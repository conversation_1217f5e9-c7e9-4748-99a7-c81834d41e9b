/**
 * Test for LinkedIn author title extraction
 */

import { LinkedInService } from '../platforms/linkedin/LinkedInService';

// Mock DOM environment
const mockElement = (html: string): HTMLElement => {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.firstElementChild as HTMLElement;
};

// Mock LinkedIn post HTML with author title
const createMockLinkedInPost = (authorName: string, authorTitle: string): string => {
  return `
    <div class="feed-shared-update-v2">
      <div class="update-components-actor">
        <div class="update-components-actor__name">${authorName}</div>
        <div class="update-components-actor__description">${authorTitle}</div>
        <div class="update-components-actor__avatar">
          <img src="https://example.com/avatar.jpg" alt="Profile photo" />
        </div>
      </div>
      <div class="update-components-text">
        <span>This is a test post content</span>
      </div>
      <a href="https://linkedin.com/posts/test-123" class="update-components-update-v2__permalink-link"></a>
    </div>
  `;
};

describe('LinkedIn Author Title Extraction', () => {
  let linkedInService: LinkedInService;

  beforeEach(() => {
    linkedInService = new LinkedInService();
    
    // Mock DOM environment
    Object.defineProperty(global, 'document', {
      value: {
        createElement: (tagName: string) => {
          const element = {
            tagName: tagName.toUpperCase(),
            innerHTML: '',
            querySelector: function(selector: string) {
              // Simple mock implementation
              if (selector.includes('actor__name')) {
                return { textContent: 'Rami Al Zoueiny' };
              }
              if (selector.includes('actor__description')) {
                return { textContent: 'Senior Graphic Designer | Video Editor | Motion Graphics | Advertis...' };
              }
              if (selector.includes('avatar') && selector.includes('img')) {
                return { src: 'https://example.com/avatar.jpg', alt: 'Profile photo' };
              }
              if (selector.includes('update-components-text')) {
                return { textContent: 'Test post content' };
              }
              if (selector.includes('permalink')) {
                return { href: 'https://linkedin.com/posts/test-123' };
              }
              return null;
            },
            querySelectorAll: function() { return []; },
            textContent: '',
            firstElementChild: null
          };
          return element;
        }
      },
      writable: true
    });
  });

  test('should extract author title from LinkedIn post', async () => {
    const mockHtml = createMockLinkedInPost(
      'Rami Al Zoueiny',
      'Senior Graphic Designer | Video Editor | Motion Graphics | Advertis...'
    );
    
    const element = mockElement(mockHtml);
    
    // Mock the querySelector method to return our test data
    element.querySelector = function(selector: string) {
      if (selector.includes('actor__name')) {
        return { textContent: 'Rami Al Zoueiny' } as any;
      }
      if (selector.includes('actor__description')) {
        return { textContent: 'Senior Graphic Designer | Video Editor | Motion Graphics | Advertis...' } as any;
      }
      if (selector.includes('avatar') && selector.includes('img')) {
        return { src: 'https://example.com/avatar.jpg', alt: 'Profile photo' } as any;
      }
      if (selector.includes('update-components-text')) {
        return { textContent: 'Test post content' } as any;
      }
      if (selector.includes('permalink')) {
        return { href: 'https://linkedin.com/posts/test-123' } as any;
      }
      return null;
    };

    const result = await linkedInService.extractPostData(element);
    
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.post.authorName).toBe('Rami Al Zoueiny');
      expect(result.post.authorTitle).toBe('Senior Graphic Designer | Video Editor | Motion Graphics | Advertis...');
    }
  });

  test('should handle missing author title gracefully', async () => {
    const mockHtml = createMockLinkedInPost('John Doe', '');
    const element = mockElement(mockHtml);
    
    // Mock querySelector to return null for author title
    element.querySelector = function(selector: string) {
      if (selector.includes('actor__name')) {
        return { textContent: 'John Doe' } as any;
      }
      if (selector.includes('actor__description')) {
        return null; // No title found
      }
      if (selector.includes('update-components-text')) {
        return { textContent: 'Test post content' } as any;
      }
      if (selector.includes('permalink')) {
        return { href: 'https://linkedin.com/posts/test-456' } as any;
      }
      return null;
    };

    const result = await linkedInService.extractPostData(element);
    
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.post.authorName).toBe('John Doe');
      expect(result.post.authorTitle).toBe(''); // Should be empty string when not found
    }
  });
});
