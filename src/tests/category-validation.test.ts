import { storage } from '../storage';
import { CategoryService } from '../services/categoryService';
import { CategorySyncService } from '../services/categorySync';

const createTestPosts = async (count: number, categoryId: string) => {
  const posts = Array.from({ length: count }, (_, i) => ({
    id: `test-post-${i}`,
    title: `Test Post ${i}`,
    categoryIds: [categoryId],
    content: 'Test content',
    createdAt: new Date().toISOString()
  }));
  
  await storage.set('posts', posts);
  return posts;
};

describe('Category System Validation', () => {
  const categoryService = CategoryService.getInstance();
  const syncService = CategorySyncService.getInstance();
  
  beforeEach(async () => {
    await storage.clear();
  });
  
  test('prevents duplicate category names', async () => {
    const result1 = await categoryService.addCategory('Marketing');
    const result2 = await categoryService.addCategory('marketing');
    const result3 = await categoryService.addCategory('Marketing  ');
    
    expect('error' in result2).toBe(true);
    expect('error' in result3).toBe(true);
    
    const categories = await categoryService.getAllCategories();
    expect(categories.length).toBe(1);
  });
  
  test('handles category deletion correctly', async () => {
    // Create category and posts
    const result = await categoryService.addCategory('TestCategory');
    if ('error' in result) throw new Error('Failed to create category');
    
    await createTestPosts(50, result.id);
    
    // Delete category
    await categoryService.deleteCategory(result.id);
    
    // Verify cleanup
    const posts = await storage.get('posts') || [];
    const categories = await categoryService.getAllCategories();
    
    expect(categories.length).toBe(0);
    expect(posts.every(p => p.categoryIds.length === 0)).toBe(true);
  });
  
  test('maintains sort order after operations', async () => {
    // Create multiple categories
    const cats = await Promise.all([
      categoryService.addCategory('First'),
      categoryService.addCategory('Second'),
      categoryService.addCategory('Third')
    ]);
    
    // Delete middle category
    if (!('error' in cats[1])) {
      await categoryService.deleteCategory(cats[1].id);
    }
    
    // Verify order
    const categories = await categoryService.getAllCategories();
    expect(categories.length).toBe(2);
    expect(categories[0].sortOrder).toBe(0);
    expect(categories[1].sortOrder).toBe(1);
  });
  
  test('merges categories atomically', async () => {
    // Create source and target categories
    const source = await categoryService.addCategory('Source');
    const target = await categoryService.addCategory('Target');
    if ('error' in source || 'error' in target) {
      throw new Error('Failed to create test categories');
    }
    
    // Create posts for source category
    await createTestPosts(25, source.id);
    
    // Merge categories
    await categoryService.mergeCategories(source.id, target.id);
    
    // Verify results
    const categories = await categoryService.getAllCategories();
    const posts = await storage.get('posts') || [];
    
    expect(categories.length).toBe(1);
    expect(categories[0].id).toBe(target.id);
    expect(categories[0].postCount).toBe(25);
    expect(posts.every(p => !p.categoryIds.includes(source.id))).toBe(true);
    expect(posts.filter(p => p.categoryIds.includes(target.id)).length).toBe(25);
  });
}); 