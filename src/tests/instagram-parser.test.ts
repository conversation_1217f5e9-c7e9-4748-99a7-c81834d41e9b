/**
 * Test file for Instagram parsing functionality
 * This tests the parseCount method and selector logic
 */

import { InstagramServiceV2 } from '../platforms/instagram/InstagramServiceV2';

// Mock DOM environment for testing
const mockDocument = {
  createElement: (tagName: string) => ({
    tagName: tagName.toUpperCase(),
    textContent: '',
    innerHTML: '',
    querySelector: () => null,
    querySelectorAll: () => [],
    appendChild: () => {},
    setAttribute: () => {},
    getAttribute: () => null,
  }),
  querySelector: () => null,
  querySelectorAll: () => [],
};

// Mock global document
(global as any).document = mockDocument;

describe('Instagram Parser Tests', () => {
  let instagramService: InstagramServiceV2;

  beforeEach(() => {
    instagramService = new InstagramServiceV2();
  });

  describe('parseCount method', () => {
    test('should parse regular numbers', () => {
      // Access private method for testing
      const parseCount = (instagramService as any).parseCount.bind(instagramService);
      
      expect(parseCount('1838')).toBe(1838);
      expect(parseCount('9')).toBe(9);
      expect(parseCount('0')).toBe(0);
    });

    test('should parse numbers with commas', () => {
      const parseCount = (instagramService as any).parseCount.bind(instagramService);
      
      expect(parseCount('1,838')).toBe(1838);
      expect(parseCount('12,345')).toBe(12345);
      expect(parseCount('1,234,567')).toBe(1234567);
    });

    test('should parse K suffix', () => {
      const parseCount = (instagramService as any).parseCount.bind(instagramService);
      
      expect(parseCount('1.8K')).toBe(1800);
      expect(parseCount('12K')).toBe(12000);
      expect(parseCount('1.2k')).toBe(1200);
    });

    test('should parse M suffix', () => {
      const parseCount = (instagramService as any).parseCount.bind(instagramService);
      
      expect(parseCount('1.5M')).toBe(1500000);
      expect(parseCount('2M')).toBe(2000000);
      expect(parseCount('1.2m')).toBe(1200000);
    });

    test('should handle text with numbers', () => {
      const parseCount = (instagramService as any).parseCount.bind(instagramService);
      
      expect(parseCount('1,838 likes')).toBe(1838);
      expect(parseCount('9 comments')).toBe(9);
      expect(parseCount('View all 9 comments')).toBe(9);
      expect(parseCount('1.8K likes')).toBe(1800);
    });

    test('should handle null/undefined/empty', () => {
      const parseCount = (instagramService as any).parseCount.bind(instagramService);
      
      expect(parseCount(null)).toBe(0);
      expect(parseCount(undefined)).toBe(0);
      expect(parseCount('')).toBe(0);
      expect(parseCount('   ')).toBe(0);
    });

    test('should handle invalid input', () => {
      const parseCount = (instagramService as any).parseCount.bind(instagramService);
      
      expect(parseCount('no numbers here')).toBe(0);
      expect(parseCount('abc')).toBe(0);
      expect(parseCount('likes')).toBe(0);
    });
  });

  describe('HTML structure parsing', () => {
    test('should create mock HTML elements for testing', () => {
      // Create mock HTML structure similar to Instagram
      const mockElement = {
        querySelector: jest.fn(),
        querySelectorAll: jest.fn(),
        textContent: '',
      };

      // Mock likes element
      const mockLikesSpan = {
        textContent: '1,838',
        parentElement: {
          querySelector: jest.fn().mockReturnValue({
            textContent: '1,838'
          })
        }
      };

      // Mock comments element
      const mockCommentsSpan = {
        textContent: '9',
        parentElement: {
          querySelector: jest.fn().mockReturnValue({
            textContent: '9'
          })
        }
      };

      mockElement.querySelector
        .mockReturnValueOnce(null) // First call for likes (old selector)
        .mockReturnValueOnce(null); // Second call for comments (old selector)

      mockElement.querySelectorAll
        .mockReturnValueOnce([{ textContent: '1,838 likes', parentElement: { querySelector: () => mockLikesSpan } }])
        .mockReturnValueOnce([{ textContent: 'View all 9 comments', parentElement: { querySelector: () => mockCommentsSpan } }]);

      // This would be part of the actual extraction logic
      const likesElements = Array.from(mockElement.querySelectorAll('span')).filter(span => 
        span.textContent && span.textContent.includes('likes')
      );
      
      expect(likesElements).toHaveLength(1);
      expect(likesElements[0].textContent).toBe('1,838 likes');
    });
  });
});
