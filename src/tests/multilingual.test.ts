/**
 * Tests for the multilingual system
 */

import t, { SUPPORTED_LOCALES, isSupportedLocale, getBrowserLocale } from '../utils/translation';
import { getPromptTemplate, generateLocalizedPrompt } from '../services/aiPromptService';

// Mock Chrome API for testing
const mockChrome = {
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn()
    }
  }
};

// @ts-ignore
global.chrome = mockChrome;

describe('Translation System', () => {
  describe('Basic Translation Function', () => {
    test('should return correct translation for existing key', () => {
      expect(t('dashboard.title', 'en')).toBe('Dashboard');
      expect(t('dashboard.title', 'tr')).toBe('Gösterge Paneli');
      expect(t('dashboard.title', 'fr')).toBe('Tableau de bord');
      expect(t('dashboard.title', 'de')).toBe('Dashboard');
      expect(t('dashboard.title', 'es')).toBe('Panel de control');
    });

    test('should fallback to English for unsupported locale', () => {
      expect(t('dashboard.title', 'invalid')).toBe('Dashboard');
      expect(t('dashboard.title', 'zh')).toBe('Dashboard');
    });

    test('should return key for non-existent translation', () => {
      expect(t('nonexistent.key', 'en')).toBe('nonexistent.key');
      expect(t('nonexistent.key', 'tr')).toBe('nonexistent.key');
    });

    test('should handle empty or null keys gracefully', () => {
      expect(t('', 'en')).toBe('');
      expect(t(null as any, 'en')).toBe(null);
    });
  });

  describe('Locale Validation', () => {
    test('should validate supported locales correctly', () => {
      expect(isSupportedLocale('en')).toBe(true);
      expect(isSupportedLocale('tr')).toBe(true);
      expect(isSupportedLocale('fr')).toBe(true);
      expect(isSupportedLocale('de')).toBe(true);
      expect(isSupportedLocale('es')).toBe(true);
      
      expect(isSupportedLocale('zh')).toBe(false);
      expect(isSupportedLocale('invalid')).toBe(false);
      expect(isSupportedLocale('')).toBe(false);
    });

    test('should return all supported locales', () => {
      expect(SUPPORTED_LOCALES).toEqual(['en', 'tr', 'fr', 'de', 'es']);
      expect(SUPPORTED_LOCALES.length).toBe(5);
    });
  });

  describe('Browser Locale Detection', () => {
    const originalNavigator = global.navigator;

    afterEach(() => {
      Object.defineProperty(global, 'navigator', {
        value: originalNavigator,
        writable: true
      });
    });

    test('should detect supported browser locale', () => {
      Object.defineProperty(global, 'navigator', {
        value: { language: 'tr-TR' },
        writable: true
      });
      expect(getBrowserLocale()).toBe('tr');

      Object.defineProperty(global, 'navigator', {
        value: { language: 'fr-FR' },
        writable: true
      });
      expect(getBrowserLocale()).toBe('fr');
    });

    test('should fallback to English for unsupported browser locale', () => {
      Object.defineProperty(global, 'navigator', {
        value: { language: 'zh-CN' },
        writable: true
      });
      expect(getBrowserLocale()).toBe('en');

      Object.defineProperty(global, 'navigator', {
        value: { language: 'invalid' },
        writable: true
      });
      expect(getBrowserLocale()).toBe('en');
    });

    test('should handle missing navigator gracefully', () => {
      Object.defineProperty(global, 'navigator', {
        value: undefined,
        writable: true
      });
      expect(getBrowserLocale()).toBe('en');
    });
  });
});

describe('AI Prompt Multilingual System', () => {
  describe('Prompt Template Retrieval', () => {
    test('should return correct prompt template for supported locales', () => {
      const englishPrompt = getPromptTemplate('tagging', 'en');
      const turkishPrompt = getPromptTemplate('tagging', 'tr');
      
      expect(englishPrompt).toContain('You are an expert social media content analyst');
      expect(turkishPrompt).toContain('Sosyal medya içerik analisti uzmanısınız');
      
      expect(englishPrompt).not.toBe(turkishPrompt);
    });

    test('should fallback to English for unsupported locales', () => {
      const englishPrompt = getPromptTemplate('tagging', 'en');
      const fallbackPrompt = getPromptTemplate('tagging', 'invalid');
      
      expect(fallbackPrompt).toBe(englishPrompt);
    });

    test('should support all prompt types', () => {
      const promptTypes = ['categorization', 'tagging', 'insight', 'fastTake', 'snapNote', 'contentIdeas'] as const;
      
      promptTypes.forEach(type => {
        const prompt = getPromptTemplate(type, 'en');
        expect(prompt).toBeTruthy();
        expect(typeof prompt).toBe('string');
        expect(prompt.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Localized Prompt Generation', () => {
    test('should generate prompts with replaced parameters', () => {
      const prompt = generateLocalizedPrompt('tagging', 'en', {
        postContent: 'Test content',
        maxTags: '5'
      });
      
      expect(prompt).toContain('Test content');
      expect(prompt).toContain('5');
      expect(prompt).not.toContain('{postContent}');
      expect(prompt).not.toContain('{maxTags}');
    });

    test('should generate different prompts for different locales', () => {
      const params = { postContent: 'Test content', maxTags: '5' };
      
      const englishPrompt = generateLocalizedPrompt('tagging', 'en', params);
      const turkishPrompt = generateLocalizedPrompt('tagging', 'tr', params);
      
      expect(englishPrompt).not.toBe(turkishPrompt);
      expect(englishPrompt).toContain('Test content');
      expect(turkishPrompt).toContain('Test content');
    });

    test('should handle missing parameters gracefully', () => {
      const prompt = generateLocalizedPrompt('tagging', 'en', {});
      
      expect(prompt).toBeTruthy();
      // Should still contain placeholder if not replaced
      expect(prompt).toContain('{postContent}');
    });
  });
});

describe('Translation Key Coverage', () => {
  test('should have all required UI translation keys', () => {
    const requiredKeys = [
      'app.name',
      'dashboard.title',
      'dashboard.loading',
      'dashboard.welcome',
      'dashboard.noPosts',
      'auth.login',
      'auth.logout',
      'settings.title',
      'settings.language',
      'error.generic',
      'error.network',
      'success.saved'
    ];

    requiredKeys.forEach(key => {
      SUPPORTED_LOCALES.forEach(locale => {
        const translation = t(key, locale);
        expect(translation).toBeTruthy();
        expect(translation).not.toBe(key); // Should not return the key itself
      });
    });
  });

  test('should have consistent translations across all locales', () => {
    const testKeys = ['dashboard.title', 'auth.login', 'settings.title'];
    
    testKeys.forEach(key => {
      const translations = SUPPORTED_LOCALES.map(locale => t(key, locale));
      
      // All translations should be different (no duplicates)
      const uniqueTranslations = new Set(translations);
      expect(uniqueTranslations.size).toBe(SUPPORTED_LOCALES.length);
      
      // All translations should be non-empty strings
      translations.forEach(translation => {
        expect(typeof translation).toBe('string');
        expect(translation.length).toBeGreaterThan(0);
      });
    });
  });
});

describe('Performance and Caching', () => {
  test('should cache translation results', () => {
    // First call
    const start1 = performance.now();
    const result1 = t('dashboard.title', 'en');
    const end1 = performance.now();
    
    // Second call (should be faster due to caching)
    const start2 = performance.now();
    const result2 = t('dashboard.title', 'en');
    const end2 = performance.now();
    
    expect(result1).toBe(result2);
    // Second call should be faster (cached)
    expect(end2 - start2).toBeLessThanOrEqual(end1 - start1);
  });

  test('should handle large number of translation calls efficiently', () => {
    const start = performance.now();
    
    // Make 1000 translation calls
    for (let i = 0; i < 1000; i++) {
      t('dashboard.title', 'en');
      t('auth.login', 'tr');
      t('settings.title', 'fr');
    }
    
    const end = performance.now();
    const duration = end - start;
    
    // Should complete within reasonable time (less than 100ms)
    expect(duration).toBeLessThan(100);
  });
});
