/**
 * Twitter/X Content Script - REFACTORED
 *
 * This script now uses the unified BaseContentScript architecture.
 * All common functionality has been moved to shared modules.
 */

// Import the new refactored Twitter content script
import { TwitterContentScript } from './platforms/TwitterContentScript';

// Initialize the refactored Twitter content script
const twitterScript = new TwitterContentScript();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => twitterScript.initialize());
} else {
  twitterScript.initialize();
}

// All functionality has been moved to the refactored TwitterContentScript class
// This file now serves as a simple entry point that initializes the new architecture




