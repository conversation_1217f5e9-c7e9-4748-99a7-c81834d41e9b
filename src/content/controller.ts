/**
 * Content Script Controller
 *
 * This module provides a unified interface for content scripts to interact with
 * the platform services. It handles the extraction and saving of posts from
 * different social media platforms.
 */

import { Post, Platform } from '../types';
import { getPlatformService, detectCurrentPlatform, ExtractionOptions, SaveOptions } from '../platforms';

/**
 * Extract post data from a DOM element
 * @param element The DOM element containing the post
 * @param platform The platform to extract from (optional, will be detected if not provided)
 * @param options Options for extraction
 * @returns The extracted post data or null if extraction failed
 */
export async function extractPostData(
  element: HTMLElement,
  platform?: Platform,
  options?: ExtractionOptions & { useCloudService?: boolean }
): Promise<Post | null> {
  try {
    // Detect platform if not provided
    const currentPlatform = platform || detectCurrentPlatform();
    if (!currentPlatform) {
      console.error('Could not detect platform');
      return null;
    }

    // Get the platform service
    const service = getPlatformService(currentPlatform, {
      useCloudService: options?.useCloudService
    });

    // Extract post data
    const result = await service.extractPostData(element, options);
    if (!result.success || !result.post) {
      console.error('Failed to extract post data:', result.error);
      return null;
    }

    return result.post;
  } catch (error) {
    console.error('Error extracting post data:', error);
    return null;
  }
}

// savePost function removed - all saves now go through background script via SAVE_POST_REQUEST

/**
 * Extract and save a post in one operation
 * @param element The DOM element containing the post
 * @param platform The platform to extract from (optional, will be detected if not provided)
 * @param extractionOptions Options for extraction
 * @param saveOptions Options for saving
 * @returns Object containing success status and error message if applicable
 */
export async function extractAndSavePost(
  element: HTMLElement,
  platform?: Platform,
  extractionOptions?: ExtractionOptions,
  saveOptions?: SaveOptions & { useCloudService?: boolean }
): Promise<{ success: boolean; error?: string; postId?: string }> {
  try {
    // Extract post data
    const post = await extractPostData(element, platform, {
      ...extractionOptions,
      useCloudService: saveOptions?.useCloudService
    });

    if (!post) {
      return {
        success: false,
        error: 'Failed to extract post data'
      };
    }

    // Send to background for processing and save
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: post
      });

      return {
        success: response?.status === 'success',
        error: response?.message || (response?.status !== 'success' ? 'Background processing failed' : undefined),
        postId: post.id
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send to background',
        postId: post.id
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Send a message to the background script
 * @param action The action to perform
 * @param data The data to send
 * @returns Promise that resolves with the response from the background script
 */
export function sendMessageToBackground(
  action: string,
  data: any
): Promise<any> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action, data }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error sending message to background:', chrome.runtime.lastError.message);
        resolve({ success: false, error: chrome.runtime.lastError.message });
      } else {
        resolve(response);
      }
    });
  });
}

/**
 * Create a port connection to the background script
 * @param name The name of the port
 * @returns The port object
 */
export function connectToBackground(name: string): chrome.runtime.Port {
  return chrome.runtime.connect({ name });
}
