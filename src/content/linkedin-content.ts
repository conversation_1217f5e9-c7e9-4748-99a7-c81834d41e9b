/**
 * LinkedIn Content Script
 *
 * This script handles the extraction and saving of LinkedIn posts.
 * It uses the modular platform architecture to handle LinkedIn-specific logic.
 */

import { extractPostData } from './controller';
import { NotificationManager } from './shared/NotificationManager';

// Constants
const SAVE_BUTTON_CLASS = 'notely-save-button';
const PROCESSED_ELEMENT_CLASS = 'notely-processed';
const POST_SELECTOR = '.feed-shared-update-v2, .update-components-actor, .feed-shared-card, .artdeco-card, .scaffold-finite-scroll__content > div';

// Debug mode for additional logging
const DEBUG = true;

// Set of processed elements to avoid duplicate processing
const processedElements = new Set<Element>();

/**
 * Initialize the content script
 */
function initialize() {
  console.log('[Notely] LinkedIn content script initialized');

  // Start scanning for posts
  scanForPosts();

  // Set up mutation observer to detect new posts
  setupMutationObserver();

  // Add periodic scanning as a fallback
  setInterval(scanForPosts, 3000);
}

/**
 * Scan the page for LinkedIn posts
 */
function scanForPosts() {
  // Find all posts on the page
  const posts = document.querySelectorAll(POST_SELECTOR);

  if (DEBUG) {
    console.log(`[Notely] Found ${posts.length} potential LinkedIn posts`);
  }

  // Process each post
  let processedCount = 0;
  posts.forEach(post => {
    // Skip elements that are too small to be posts
    if (post.clientHeight < 100) {
      return;
    }

    // Skip elements that don't have any content
    if (!hasPostContent(post as HTMLElement)) {
      return;
    }

    if (!processedElements.has(post) && !post.classList.contains(PROCESSED_ELEMENT_CLASS)) {
      processedElements.add(post);
      post.classList.add(PROCESSED_ELEMENT_CLASS);
      addSaveButton(post as HTMLElement);
      processedCount++;
    }
  });

  if (DEBUG && processedCount > 0) {
    console.log(`[Notely] Added save buttons to ${processedCount} new LinkedIn posts`);
  }
}

/**
 * Check if an element has post content
 */
function hasPostContent(element: HTMLElement): boolean {
  // Check if the element has an author name
  const hasAuthor = !!element.querySelector('.update-components-actor__name, .feed-shared-actor__name, .update-components-actor__title');

  // Check if the element has text content
  const hasContent = !!element.querySelector('.update-components-text, .feed-shared-update-v2__description, .feed-shared-text');

  // Check if the element has images
  const hasImages = !!element.querySelector('img[src*="media.licdn.com"]');

  // Return true if the element has at least an author and either content or images
  return hasAuthor && (hasContent || hasImages);
}

/**
 * Add a save button to a LinkedIn post
 */
function addSaveButton(post: HTMLElement) {
  // Check if the post already has a save button
  if (post.querySelector(`.${SAVE_BUTTON_CLASS}`)) {
    return;
  }

  // Create the save button
  const saveButton = document.createElement('button');
  saveButton.className = SAVE_BUTTON_CLASS;
  saveButton.textContent = 'Save to Notely';
  saveButton.style.cssText = `
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    margin: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: background-color 0.3s ease;
  `;

  // Add click event listener
  saveButton.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();
    handleSaveButtonClick(post, saveButton);
  });

  // Try multiple selectors to find the appropriate location to insert the button
  const footerSelectors = [
    '.update-components-footer',
    '.feed-shared-social-actions',
    '.social-details-social-actions',
    '.feed-shared-social-action-bar',
    '.feed-shared-update-v2__social-actions',
    '.feed-shared-update-v2__footer',
    '.feed-shared-update-v2__comments-container',
    '.feed-shared-control-menu'
  ];

  let footer = null;
  for (const selector of footerSelectors) {
    footer = post.querySelector(selector);
    if (footer) {
      break;
    }
  }

  if (footer) {
    // Create a container for the button to match LinkedIn's style
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
      display: inline-flex;
      align-items: center;
      margin-left: 8px;
    `;
    buttonContainer.appendChild(saveButton);
    footer.appendChild(buttonContainer);

    if (DEBUG) {
      console.log('[Notely] Added save button to LinkedIn post footer');
    }
  } else {
    // Fallback: position the button with absolute positioning
    saveButton.style.cssText += `
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 9999;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    `;

    // Make sure the post has relative positioning for absolute positioning to work
    const computedStyle = window.getComputedStyle(post);
    if (computedStyle.position === 'static') {
      post.style.position = 'relative';
    }

    post.appendChild(saveButton);

    if (DEBUG) {
      console.log('[Notely] Added save button to LinkedIn post with absolute positioning');
    }
  }
}

/**
 * Handle save button click
 */
async function handleSaveButtonClick(post: HTMLElement, button: HTMLButtonElement) {
  // Update button state
  button.textContent = 'Saving...';
  button.disabled = true;
  button.style.backgroundColor = '#999999';

  if (DEBUG) {
    console.log('[Notely] LinkedIn save button clicked, extracting post data...');
  }

  try {
    // First extract the post data with images for queue processing
    const postData = await extractPostData(post, 'LinkedIn', { includeImages: true });

    if (!postData) {
      button.textContent = 'Error';
      button.style.backgroundColor = '#e0245e';
      console.error('[Notely] Failed to extract LinkedIn post data');

      setTimeout(() => {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.backgroundColor = '#4CAF50';
        button.style.opacity = '1';
      }, 3000);
      return;
    }

    // Send to background for AI processing, local save, and cloud sync
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: postData
      });

      if (response?.status === 'success') {
        button.textContent = 'Saved!';
        button.style.backgroundColor = '#17bf63';
        console.log(`[Notely] LinkedIn post ${postData.id} sent for processing and save`);

        // Show success notification
        NotificationManager.showSimpleNotification('Post saved to Notely!', 'success');

        // Keep the button in "Saved" state
        setTimeout(() => {
          // Change to a subtle success state after a few seconds
          button.style.backgroundColor = '#4CAF50';
          button.style.opacity = '0.7';
        }, 2000);
      } else {
        button.textContent = 'Error';
        button.style.backgroundColor = '#e0245e';
        console.error(`[Notely] Background processing failed for LinkedIn post: ${response?.message}`);

        // Re-enable button after error
        setTimeout(() => {
          button.textContent = 'Save to Notely';
          button.disabled = false;
          button.style.backgroundColor = '#4CAF50';
          button.style.opacity = '1';
        }, 3000);
      }
    } catch (backgroundError) {
      button.textContent = 'Error';
      button.style.backgroundColor = '#e0245e';
      console.error(`[Notely] Error sending LinkedIn post to background:`, backgroundError);

      // Re-enable button after error
      setTimeout(() => {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.backgroundColor = '#4CAF50';
        button.style.opacity = '1';
      }, 3000);
    }
  } catch (error) {
    button.textContent = 'Error';
    button.style.backgroundColor = '#e0245e';
    console.error('[Notely] Error in handleSaveButtonClick:', error);

    // Re-enable button after error
    setTimeout(() => {
      button.textContent = 'Save to Notely';
      button.disabled = false;
      button.style.backgroundColor = '#4CAF50';
      button.style.opacity = '1';
    }, 3000);
  }
}

/**
 * Set up mutation observer to detect new posts
 */
function setupMutationObserver() {
  // Use a debounce mechanism to avoid excessive scanning
  let debounceTimer: number | null = null;
  const debounceDelay = 500; // ms

  const observer = new MutationObserver((mutations) => {
    let shouldScan = false;

    // Check if any of the mutations are relevant
    for (const mutation of mutations) {
      // Check for added nodes
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any of the added nodes match our post selectors or could contain posts
        for (const node of Array.from(mutation.addedNodes)) {
          if (node instanceof HTMLElement) {
            // If the node itself is a post or contains posts
            if (node.matches(POST_SELECTOR) || node.querySelector(POST_SELECTOR)) {
              shouldScan = true;
              break;
            }
          }
        }
      }

      if (shouldScan) {
        break;
      }
    }

    // If we should scan, debounce the scan to avoid multiple rapid scans
    if (shouldScan) {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      debounceTimer = window.setTimeout(() => {
        if (DEBUG) {
          console.log('[Notely] LinkedIn mutation detected, scanning for new posts');
        }
        scanForPosts();
        debounceTimer = null;
      }, debounceDelay);
    }
  });

  // Observe the main feed container if possible, otherwise the body
  const feedContainer = document.querySelector('.scaffold-finite-scroll__content') || document.body;

  observer.observe(feedContainer, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false
  });

  console.log('[Notely] Mutation observer set up for LinkedIn on',
    feedContainer === document.body ? 'document body' : 'feed container');

  // Also observe the body for structural changes that might affect the feed
  if (feedContainer !== document.body) {
    const bodyObserver = new MutationObserver((mutations) => {
      // Check if the feed container has been replaced or modified
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          const newFeedContainer = document.querySelector('.scaffold-finite-scroll__content');
          if (newFeedContainer && newFeedContainer !== feedContainer) {
            // Re-initialize if the feed container has changed
            console.log('[Notely] LinkedIn feed container changed, re-initializing');
            observer.disconnect();
            bodyObserver.disconnect();
            initialize();
            return;
          }
        }
      }
    });

    bodyObserver.observe(document.body, {
      childList: true,
      subtree: false
    });
  }
}

/**
 * Handle page navigation events
 */
function handleNavigation() {
  // LinkedIn uses client-side routing, so we need to detect URL changes
  let lastUrl = location.href;

  // Create a new observer to watch for URL changes
  const urlObserver = new MutationObserver(() => {
    if (location.href !== lastUrl) {
      lastUrl = location.href;
      console.log('[Notely] LinkedIn URL changed, re-initializing');

      // Reset processed elements and re-scan
      processedElements.clear();
      scanForPosts();
    }
  });

  // Observe the document title for changes (often changes with navigation)
  urlObserver.observe(document.querySelector('title') || document.head, {
    childList: true,
    subtree: true,
    characterData: true
  });
}

// Initialize the content script when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    initialize();
    handleNavigation();
  });
} else {
  initialize();
  handleNavigation();
}
