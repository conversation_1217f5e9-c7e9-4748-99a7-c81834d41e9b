/**
 * Web Content Script
 *
 * This script handles the extraction and saving of web content from any website.
 * It responds to context menu actions and extracts text, images, and page metadata.
 */

import { extractPostData } from './controller';
import { Post } from '../types';

// Debug mode for additional logging
const DEBUG = true;

/**
 * Initialize the web content script
 */
function initialize() {
  // Set up message listener for context menu actions
  setupMessageListener();
}

/**
 * Set up message listener for background script communication
 */
function setupMessageListener() {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.action) {
      case 'PING':
        sendResponse({ status: 'pong' });
        return false;

      case 'EXTRACT_AND_SAVE_SELECTION':
        handleExtractAndSaveSelection(message.data)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Async response

      case 'EXTRACT_AND_SAVE_IMAGE':
        handleExtractAndSaveImage(message.data)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Async response

      case 'EXTRACT_AND_SAVE_PAGE':
        handleExtractAndSavePage(message.data)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Async response

      case 'CONTEXT_MENU_CLICKED':
        handleContextMenuClick(message)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Async response

      default:
        return false;
    }
  });
}

/**
 * Handle context menu click and route to appropriate handler
 */
async function handleContextMenuClick(message: any): Promise<{ success: boolean; error?: string }> {
  try {
    switch (message.menuItemId) {
      case 'add-text-to-notely':
        if (!message.selectionText) {
          throw new Error('No text selected');
        }
        return await handleExtractAndSaveSelection({
          selectionText: message.selectionText,
          pageUrl: message.pageUrl
        });

      case 'add-image-to-notely':
        if (!message.srcUrl) {
          throw new Error('No image source URL');
        }
        return await handleExtractAndSaveImage({
          srcUrl: message.srcUrl,
          pageUrl: message.pageUrl
        });

      case 'add-page-to-notely':
        return await handleExtractAndSavePage({
          pageUrl: message.pageUrl
        });

      default:
        throw new Error(`Unknown menu item: ${message.menuItemId}`);
    }
  } catch (error) {
    console.error('[Notely] Error handling context menu click:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle extracting and saving selected text
 */
async function handleExtractAndSaveSelection(data: any): Promise<{ success: boolean; error?: string }> {
  try {
    // Create a temporary element to represent the selection
    const tempElement = document.createElement('div');
    tempElement.textContent = data.selectionText;

    // Extract post data using the web platform service
    const post = await extractPostData(tempElement, 'Web', {
      selectedText: data.selectionText,
      pageUrl: data.pageUrl || window.location.href
    });

    if (!post) {
      throw new Error('Failed to extract post data from selection');
    }

    // Create a unique ID for the text selection post
    const textId = `web_text_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;

    // Enhance the post with selection-specific data
    const enhancedPost: Post = {
      ...post,
      id: textId,
      permalink: `${window.location.href}#text-${textId}`, // Make permalink unique
      content: data.selectionText,
      textContent: data.selectionText,
      title: `Selected text from ${document.title}`,
      notes: `Selected from: ${window.location.href}`
    };

    // Send to background for processing and save
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: enhancedPost
      });

      if (response?.status === 'success') {
        showNotification('Text saved to Notely!', 'success');
      } else {
        throw new Error(response?.message || 'Failed to save text');
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to save text');
    }

    return { success: true };

  } catch (error) {
    console.error('[Notely] Error extracting and saving selection:', error);
    showNotification('Failed to save text to Notely', 'error');
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle extracting and saving an image
 */
async function handleExtractAndSaveImage(data: any): Promise<{ success: boolean; error?: string }> {
  try {
    // Find the image element - try multiple approaches
    let img: HTMLImageElement | null = null;

    // First try exact src match
    img = document.querySelector(`img[src="${data.srcUrl}"]`) as HTMLImageElement;

    // If not found, try to find by partial URL match
    if (!img) {
      const allImages = document.querySelectorAll('img');
      for (const image of allImages) {
        if (image.src === data.srcUrl ||
            image.currentSrc === data.srcUrl ||
            (image.srcset && image.srcset.includes(data.srcUrl))) {
          img = image;
          break;
        }
      }
    }

    // If still not found, create a virtual image element with the URL
    if (!img) {
      img = document.createElement('img');
      img.src = data.srcUrl;
      img.alt = `Image from ${window.location.hostname}`;
    }

    // Extract post data using the web platform service
    const post = await extractPostData(img, 'Web', {
      imageUrl: data.srcUrl,
      pageUrl: data.pageUrl || window.location.href
    });

    if (!post) {
      throw new Error('Failed to extract post data from image');
    }

    // Create a unique ID for the image post
    const imageId = `web_image_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;

    // Enhance the post with image-specific data
    const enhancedPost: Post = {
      ...post,
      id: imageId,
      permalink: `${window.location.href}#image-${imageId}`, // Make permalink unique
      url: data.srcUrl, // Use the image URL as the post URL
      title: `Image from ${document.title}`,
      content: img.alt || `Image saved from ${window.location.hostname}`,
      textContent: img.alt || `Image saved from ${window.location.hostname}`,
      notes: `Image saved from: ${window.location.href}`,
      media: [{
        type: 'image' as const,
        url: data.srcUrl,
        alt: img.alt || '',
        title: img.title || ''
      }]
    };

    // Send to background for processing and save
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: enhancedPost
      });

      if (response?.status === 'success') {
        showNotification('Image saved to Notely!', 'success');
      } else {
        throw new Error(response?.message || 'Failed to save image');
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to save image');
    }

    return { success: true };

  } catch (error) {
    console.error('[Notely] Error extracting and saving image:', error);
    showNotification('Failed to save image to Notely', 'error');
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Handle extracting and saving page content
 */
async function handleExtractAndSavePage(data: any): Promise<{ success: boolean; error?: string }> {
  try {
    // Use the document body as the element
    const bodyElement = document.body;

    // Extract post data using the web platform service
    const post = await extractPostData(bodyElement, 'Web', {
      pageUrl: data.pageUrl || window.location.href,
      fullPage: true
    });

    if (!post) {
      throw new Error('Failed to extract post data from page');
    }

    // Create a unique ID for the page post
    const pageId = `web_page_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;

    // Enhance the post with page-specific data
    const enhancedPost: Post = {
      ...post,
      id: pageId,
      permalink: `${window.location.href}#page-${pageId}`, // Make permalink unique
      title: document.title,
      content: getPageSummary(),
      textContent: getPageSummary(),
      notes: `Page saved from: ${window.location.href}`
    };

    // Send to background for processing and save
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: enhancedPost
      });

      if (response?.status === 'success') {
        showNotification('Page saved to Notely!', 'success');
      } else {
        throw new Error(response?.message || 'Failed to save page');
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to save page');
    }

    return { success: true };

  } catch (error) {
    console.error('[Notely] Error extracting and saving page:', error);
    showNotification('Failed to save page to Notely', 'error');
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get a summary of the page content
 */
function getPageSummary(): string {
  // Try to get meta description first
  const metaDescription = document.querySelector('meta[name="description"]') as HTMLMetaElement;
  if (metaDescription && metaDescription.content) {
    return metaDescription.content;
  }

  // Try to get the first paragraph
  const firstParagraph = document.querySelector('p');
  if (firstParagraph && firstParagraph.textContent) {
    const text = firstParagraph.textContent.trim();
    if (text.length > 50) {
      return text.length > 250 ? text.substring(0, 250) + '...' : text;
    }
  }

  // Fallback to page title
  return document.title || window.location.href;
}

/**
 * Show a notification to the user
 */
function showNotification(message: string, type: 'success' | 'error' = 'success') {
  // Create notification element
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    background-color: ${type === 'success' ? '#10B981' : '#EF4444'};
  `;

  notification.textContent = message;
  document.body.appendChild(notification);

  // Remove notification after 3 seconds
  setTimeout(() => {
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Initialize the content script when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}
