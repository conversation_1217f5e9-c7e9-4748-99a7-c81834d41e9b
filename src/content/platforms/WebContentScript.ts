/**
 * Web Content Script - Refactored
 * 
 * This script handles the extraction and saving of web content from any website.
 * It responds to context menu actions and extracts text, images, and page metadata.
 * Unlike social media platforms, this doesn't scan for posts but responds to user actions.
 */

import { SaveHandler } from '../shared/SaveHandler';
import { NotificationManager } from '../shared/NotificationManager';

export class WebContentScript {
  private debug: boolean = true;

  constructor() {
    this.initialize();
  }

  /**
   * Initialize the web content script
   */
  private initialize(): void {
    this.log('Web content script initialized on:', window.location.href);
    this.setupMessageListener();
  }

  /**
   * Set up message listener for background script communication
   */
  private setupMessageListener(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.log('Web content script received message:', message.action);

      switch (message.action) {
        case 'PING':
          sendResponse({ status: 'pong' });
          return false;

        case 'EXTRACT_AND_SAVE_SELECTION':
          this.handleExtractAndSaveSelection(message.data)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true; // Async response

        case 'EXTRACT_AND_SAVE_IMAGE':
          this.handleExtractAndSaveImage(message.data)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true; // Async response

        case 'EXTRACT_AND_SAVE_PAGE':
          this.handleExtractAndSavePage(message.data)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true; // Async response

        case 'CONTEXT_MENU_CLICKED':
          this.handleContextMenuClick(message)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true; // Async response

        default:
          this.log('Unknown action:', message.action);
          return false;
      }
    });
  }

  /**
   * Handle context menu click and route to appropriate handler
   */
  private async handleContextMenuClick(message: any): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('Handling context menu click:', message.menuItemId);

      switch (message.menuItemId) {
        case 'add-text-to-notely':
          if (!message.selectionText) {
            throw new Error('No text selected');
          }
          return await this.handleExtractAndSaveSelection({
            selectionText: message.selectionText,
            pageUrl: message.pageUrl
          });

        case 'add-image-to-notely':
          if (!message.srcUrl) {
            throw new Error('No image source URL');
          }
          return await this.handleExtractAndSaveImage({
            srcUrl: message.srcUrl,
            pageUrl: message.pageUrl
          });

        case 'add-page-to-notely':
          return await this.handleExtractAndSavePage({
            pageUrl: message.pageUrl
          });

        default:
          throw new Error(`Unknown menu item: ${message.menuItemId}`);
      }
    } catch (error) {
      console.error('[Notely] Error handling context menu click:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Handle extracting and saving selected text
   */
  private async handleExtractAndSaveSelection(data: any): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('Extracting and saving selected text:', data.selectionText);

      const result = await SaveHandler.handleWebContentSave(data, 'text');
      
      if (result.success) {
        NotificationManager.showSuccess('Text saved to Notely!');
      } else {
        NotificationManager.showError('Failed to save text to Notely');
      }

      return result;

    } catch (error) {
      console.error('[Notely] Error extracting and saving selection:', error);
      NotificationManager.showError('Failed to save text to Notely');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Handle extracting and saving an image
   */
  private async handleExtractAndSaveImage(data: any): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('Extracting and saving image:', data.srcUrl);

      const result = await SaveHandler.handleWebContentSave(data, 'image');
      
      if (result.success) {
        NotificationManager.showSuccess('Image saved to Notely!');
      } else {
        NotificationManager.showError('Failed to save image to Notely');
      }

      return result;

    } catch (error) {
      console.error('[Notely] Error extracting and saving image:', error);
      NotificationManager.showError('Failed to save image to Notely');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Handle extracting and saving page content
   */
  private async handleExtractAndSavePage(data: any): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('Extracting and saving page content');

      const result = await SaveHandler.handleWebContentSave(data, 'page');
      
      if (result.success) {
        NotificationManager.showSuccess('Page saved to Notely!');
      } else {
        NotificationManager.showError('Failed to save page to Notely');
      }

      return result;

    } catch (error) {
      console.error('[Notely] Error extracting and saving page:', error);
      NotificationManager.showError('Failed to save page to Notely');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Log messages with web content prefix
   */
  private log(message: string, ...args: any[]): void {
    if (this.debug) {
      console.log(`[Notely Web] ${message}`, ...args);
    }
  }

  /**
   * Static method to initialize when DOM is ready
   */
  static initializeWhenReady(): WebContentScript {
    const script = new WebContentScript();
    return script;
  }
}

// Initialize the web content script
export const webScript = WebContentScript.initializeWhenReady();
