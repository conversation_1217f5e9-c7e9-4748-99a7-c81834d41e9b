/**
 * LinkedIn Content Script - Refactored
 * 
 * This script handles the extraction and saving of LinkedIn posts using the
 * unified BaseContentScript architecture. It contains only LinkedIn-specific logic.
 */

import { BaseContentScript, ContentScriptConfig } from '../shared/BaseContentScript';

export class LinkedIn<PERSON>ontentScript extends BaseContentScript {
  constructor() {
    const config: ContentScriptConfig = {
      platform: 'LinkedIn',
      postSelectors: [
        '.feed-shared-update-v2:not(.comments-comment-item)',
        '.scaffold-finite-scroll__content > div[data-id*="urn:li:activity:"]',
        '.artdeco-card.feed-shared-update-v2',
        '.feed-shared-card:not(.comments-comment-item)'
      ],
      buttonConfig: {
        position: 'inline',
        platform: 'LinkedIn'
      },
      saveOptions: {
        includeImages: true,
        useCloudService: true
      },
      mutationConfig: {
        debounceDelay: 500,
        targetContainer: '.scaffold-finite-scroll__content'
      },
      enablePeriodicScan: true,
      periodicScanInterval: 3000,
      debug: true
    };

    super(config);
  }

  /**
   * Validate if a LinkedIn post element should be processed
   */
  protected validatePost(post: HTMLElement): boolean {
    // Skip elements that are too small to be posts
    if (post.clientHeight < 100) {
      return false;
    }

    // Skip comment sections and reply elements
    if (post.classList.contains('comments-comment-item') ||
        post.classList.contains('comments-comment-texteditor') ||
        post.classList.contains('comment-thread') ||
        post.querySelector('.comments-comment-item') ||
        post.querySelector('.comment-thread')) {
      this.log('Skipping comment element');
      return false;
    }

    // Skip elements that don't have the main post structure
    if (!post.classList.contains('feed-shared-update-v2') && 
        !post.querySelector('.feed-shared-update-v2')) {
      // Only allow if it's a direct post container
      const hasPostStructure = post.querySelector('.update-components-actor') && 
                               post.querySelector('.feed-shared-social-action-bar, .social-details-social-counts');
      if (!hasPostStructure) {
        return false;
      }
    }

    // Check if the element has an author name
    const hasAuthor = !!post.querySelector('.update-components-actor__name, .feed-shared-actor__name, .update-components-actor__title');

    // Check if the element has text content
    const hasContent = !!post.querySelector('.update-components-text, .feed-shared-update-v2__description, .feed-shared-text');

    // Check if the element has images
    const hasImages = !!post.querySelector('img[src*="media.licdn.com"]');

    // Check if this is a document carousel (which we should skip)
    const hasDocumentIframe = !!post.querySelector('iframe[src*="native-document"]');
    const hasDocumentIndicators = post.innerHTML.includes('document') ||
                                post.innerHTML.includes('PDF') ||
                                post.innerHTML.includes('slides') ||
                                post.innerHTML.includes('feedshare-document');

    // Skip document carousels entirely
    if (hasDocumentIframe || hasDocumentIndicators) {
      this.log('Skipping document carousel post - no save button will be shown');
      return false;
    }

    // Return true if the element has at least an author and either content or images
    return hasAuthor && (hasContent || hasImages);
  }

  /**
   * Find the best insertion point for the save button in a LinkedIn post
   */
  protected findButtonInsertionPoint(post: HTMLElement): {
    container: HTMLElement;
    method: 'append' | 'prepend' | 'before' | 'after' | 'absolute';
  } {
    // Try multiple selectors to find the appropriate footer location
    const footerSelectors = [
      '.update-components-footer',
      '.feed-shared-social-actions',
      '.social-details-social-actions',
      '.feed-shared-social-action-bar',
      '.feed-shared-update-v2__social-actions',
      '.feed-shared-update-v2__footer',
      '.feed-shared-update-v2__comments-container',
      '.feed-shared-control-menu'
    ];

    for (const selector of footerSelectors) {
      const footer = post.querySelector(selector) as HTMLElement;
      if (footer) {
        return { container: footer, method: 'append' };
      }
    }

    // Fallback to absolute positioning
    return { container: post, method: 'absolute' };
  }

  /**
   * Insert the button at the specified location
   */
  protected insertButton(
    button: HTMLButtonElement,
    insertionPoint: { container: HTMLElement; method: string },
    post: HTMLElement
  ): void {
    const { container, method } = insertionPoint;

    try {
      if (method === 'append' && this.isFooterContainer(container)) {
        // Create a container for the button to match LinkedIn's style
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
          display: inline-flex;
          align-items: center;
          margin-left: 8px;
        `;
        buttonContainer.appendChild(button);
        container.appendChild(buttonContainer);

        this.log('Added save button to LinkedIn post footer');
      } else {
        // Use absolute positioning
        this.useAbsolutePositioning(button, post);
      }
    } catch (error) {
      console.error('[Notely] Error inserting button in LinkedIn post:', error);
      this.useAbsolutePositioning(button, post);
    }
  }

  /**
   * Check if the container is a footer element
   */
  private isFooterContainer(container: HTMLElement): boolean {
    const footerClasses = [
      'update-components-footer',
      'feed-shared-social-actions',
      'social-details-social-actions',
      'feed-shared-social-action-bar'
    ];

    return footerClasses.some(className => container.classList.contains(className));
  }

  /**
   * Use absolute positioning as fallback
   */
  private useAbsolutePositioning(button: HTMLButtonElement, post: HTMLElement): void {
    this.log('Using absolute positioning for LinkedIn button');
    
    // Apply absolute positioning styles
    button.style.cssText += `
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 9999;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    `;

    // Make sure the post has relative positioning
    const computedStyle = window.getComputedStyle(post);
    if (computedStyle.position === 'static') {
      post.style.position = 'relative';
    }

    post.appendChild(button);
  }

  /**
   * Override setupMutationObserver to add LinkedIn-specific container monitoring
   */
  protected setupMutationObserver(): void {
    super.setupMutationObserver();
    
    // Set up additional observer for LinkedIn's dynamic feed container
    this.setupFeedContainerObserver();
  }

  /**
   * Set up observer for LinkedIn's feed container changes
   */
  private setupFeedContainerObserver(): void {
    const feedContainer = document.querySelector('.scaffold-finite-scroll__content');
    
    if (feedContainer) {
      const containerObserver = new MutationObserver((mutations) => {
        // Check if the feed container has been replaced or modified
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            const newFeedContainer = document.querySelector('.scaffold-finite-scroll__content');
            if (newFeedContainer && newFeedContainer !== feedContainer) {
              this.log('LinkedIn feed container changed, re-initializing');
              this.handleContainerChange();
              return;
            }
          }
        }
      });

      containerObserver.observe(document.body, {
        childList: true,
        subtree: false
      });
    }
  }

  /**
   * Handle LinkedIn-specific URL changes
   */
  protected onUrlChange(newUrl: string): void {
    this.log(`LinkedIn URL changed to: ${newUrl}`);
    
    // LinkedIn uses client-side routing
    // Reset processed elements and re-scan
    this.processedElements.clear();
    this.scanForPosts();
  }

  /**
   * Handle LinkedIn-specific container changes
   */
  protected onContainerChange(): void {
    this.log('LinkedIn container changed');
    
    // Restart the mutation observer and re-scan
    this.mutationManager?.restart();
    
    setTimeout(() => {
      this.scanForPosts();
    }, 200);
  }

  /**
   * Override scanForPosts to add LinkedIn-specific filtering
   */
  protected scanForPosts(): void {
    try {
      const posts = this.findPosts();
      this.log(`Found ${posts.length} potential LinkedIn posts`);

      // Process each post with additional validation
      let processedCount = 0;
      posts.forEach(post => {
        if (this.shouldProcessPost(post)) {
          this.processPost(post);
          processedCount++;
        }
      });

      if (processedCount > 0) {
        this.log(`Added save buttons to ${processedCount} new LinkedIn posts`);
      }
    } catch (error) {
      console.error('[Notely] Error scanning for LinkedIn posts:', error);
    }
  }

  /**
   * Override handleSaveButtonClick to add LinkedIn-specific behavior
   */
  protected async handleSaveButtonClick(post: HTMLElement, button: HTMLButtonElement): Promise<void> {
    this.log('LinkedIn save button clicked, extracting post data...');
    
    try {
      await super.handleSaveButtonClick(post, button);
      
      // Keep the button in a subtle success state after save
      setTimeout(() => {
        if (button.parentNode) {
          button.style.backgroundColor = '#4CAF50';
          button.style.opacity = '0.7';
        }
      }, 2000);
    } catch (error) {
      console.error('[Notely] Error in LinkedIn save handler:', error);
      throw error;
    }
  }
}

// Initialize the LinkedIn content script
export const linkedInScript = BaseContentScript.initializeWhenReady(LinkedInContentScript);
