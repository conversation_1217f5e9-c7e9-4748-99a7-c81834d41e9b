/**
 * Reddit Content Script
 *
 * This script handles the extraction and saving of Reddit posts.
 * It uses the modular platform architecture to handle Reddit-specific logic.
 */

import { extractPostData } from './controller';
import { getStandardButtonStyle, getSavingButtonStyle, getSavedButtonStyle, getErrorButtonStyle, getAbsoluteButtonStyle } from '../styles/button-styles';
import { NotificationManager } from './shared/NotificationManager';

// Constants
const SAVE_BUTTON_CLASS = 'notely-save-button';
const PROCESSED_ELEMENT_CLASS = 'notely-processed';
// Updated selectors for modern Reddit (2024)
const POST_SELECTOR = [
  // New Reddit selectors
  'shreddit-post',
  'article[data-testid="post-container"]',
  'div[data-testid="post-container"]',
  'div[slot="full-post-link"]',
  'faceplate-tracker[data-faceplate-tracking-context*="post"]',
  // Old Reddit selectors (fallback)
  'div.Post',
  'div.scrollerItem',
  'div._1oQyIsiPHYt6nx7VOmd1sz',
  'article.Post',
  // Additional modern selectors
  'div[data-click-id="body"]',
  'div[data-adclicklocation="body"]'
].join(', ');

// Set of processed elements to avoid duplicate processing
const processedElements = new Set<Element>();

/**
 * Initialize the content script
 */
function initialize() {
  console.log('[Notely] Reddit content script initialized');

  // Start scanning for posts
  scanForPosts();

  // Set up mutation observer to detect new posts
  setupMutationObserver();
}

/**
 * Scan the page for Reddit posts
 */
function scanForPosts() {
  // Debug: Log what we're looking for
  console.log(`[Notely] Scanning for Reddit posts with selector: ${POST_SELECTOR}`);

  // Find all posts on the page
  const posts = document.querySelectorAll(POST_SELECTOR);
  console.log(`[Notely] Found ${posts.length} Reddit posts`);

  // Debug: If no posts found, try to identify what elements are available
  if (posts.length === 0) {
    console.log('[Notely] No posts found. Debugging available elements...');

    // Check for common Reddit elements
    const debugSelectors = [
      'shreddit-post',
      'article',
      'div[data-testid]',
      'faceplate-tracker',
      'div[slot]'
    ];


  }

  // Process each post
  posts.forEach(post => {
    if (!processedElements.has(post) && !post.classList.contains(PROCESSED_ELEMENT_CLASS)) {
      processedElements.add(post);
      post.classList.add(PROCESSED_ELEMENT_CLASS);
      addSaveButton(post as HTMLElement);
    }
  });
}

/**
 * Add a save button to a Reddit post
 */
function addSaveButton(post: HTMLElement) {
  // Create the save button with standardized styling
  const saveButton = document.createElement('button');
  saveButton.className = SAVE_BUTTON_CLASS;
  saveButton.textContent = 'Save to Notely';
  saveButton.style.cssText = getStandardButtonStyle('Reddit');

  // Add click event listener
  saveButton.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();
    handleSaveButtonClick(post, saveButton);
  });

  // Find the appropriate location to insert the button (updated for modern Reddit)
  // First, try to find the actual engagement area where share, comments buttons are
  let actionBar = null;
  
  // Look for the Share button area specifically (modern Reddit 2024)
  const shareDropdown = post.querySelector('faceplate-dropdown-menu.share-dropdown-menu, [slot="ssr-share-button"]');
  if (shareDropdown) {
    actionBar = shareDropdown.parentElement;
    console.log('[Notely] Found Reddit share dropdown, using its parent as action bar');
  }

  // If no share dropdown found, try other engagement selectors
  if (!actionBar) {
    const engagementSelectors = [
      // Modern Reddit post engagement area (based on actual HTML)
      'div[slot="actionRow"]',
      'shreddit-post-engagement',
      'div[data-testid="post-engagement-bar"]',
      'div[data-testid="post-social-buttons"]',
      'faceplate-partial[src*="engagement"]',
      // Try to find containers near known Reddit elements
      'faceplate-share-button',
      // Generic engagement patterns from actual Reddit HTML
      'div.flex.items-center.gap-xs',
      'div.flex.items-center[class*="gap"]',
      // Legacy selectors
      '.reddit-infobar',
      '.bottom-row',
      '[data-testid="post-comment-header"]',
      '.post-buttons',
      '.action-buttons',
      '.ButtonGroup',
      '._3-ITD33yeeJXxZ8g6Z_IRd'
    ];

    // Try each selector
    for (const selector of engagementSelectors) {
      const found = post.querySelector(selector);
      if (found) {
        // If we found faceplate-share-button, use its parent
        if (selector === 'faceplate-share-button') {
          actionBar = found.parentElement;
        } else {
          actionBar = found;
        }
        console.log(`[Notely] Found Reddit action bar with selector: ${selector}`);
        break;
      }
    }
  }

  // Final fallback: look for the share button specifically and insert next to it
  if (!actionBar) {
    const shareButton = post.querySelector([
      'faceplate-dropdown-menu button:has(svg.icon-share)',
      'button[aria-label*="share"]',
      'button[data-testid*="share"]',
      '[data-click-id="share"]'
    ].join(', '));

    if (shareButton && shareButton.parentElement) {
      actionBar = shareButton.parentElement;
      console.log('[Notely] Found Reddit share button, using its parent as action bar');
    }
  }

  if (actionBar) {
    // Apply styling to match Reddit's Share button design
    saveButton.style.cssText = `
      color: white;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.3s;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      display: inline-flex;
      align-items: center;
      margin-left: 8px;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 12px;
      background-color: rgb(76, 175, 80);
      box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      height: var(--size-button-sm-h);
    `;
    
    actionBar.appendChild(saveButton);
    console.log('[Notely] Successfully added Reddit save button to action bar');
  } else {
    // Last resort: find a good container and use better absolute positioning
    console.log('[Notely] Could not find Reddit action bar, using fallback positioning');
    
    // Find the post content area for better positioning
    const container = post.querySelector([
      'div[slot="title"]',
      'div[slot="text-body"]', 
      'div[data-testid="post-content"]',
      '.top-matter',
      '.post-header'
    ].join(', ')) || post;

    // Apply the same exact CSS styling for fallback positioning
    saveButton.style.cssText = `
      color: white;
      margin-top: 0px;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.3s;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      position: absolute;
      bottom: 4px;
      right: 8px;
      z-index: 1000;
      font-size: 12px;
      padding: 0px 8px;
      border-radius: 12px;
      background-color: rgb(76, 175, 80);
      box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    `;

    if (container instanceof HTMLElement) {
      container.style.position = 'relative';
      container.appendChild(saveButton);
    } else {
      post.appendChild(saveButton);
    }
  }
}

/**
 * Handle save button click
 */
async function handleSaveButtonClick(post: HTMLElement, button: HTMLButtonElement) {
  // Update button state
  button.textContent = 'Saving...';
  button.disabled = true;
  button.style.cssText += getSavingButtonStyle();

  try {
    // First extract the post data with images for queue processing
    const postData = await extractPostData(post, 'Reddit', { includeImages: true });

    if (!postData) {
      button.textContent = 'Error';
      button.style.cssText += getErrorButtonStyle();
      console.error('[Notely] Failed to extract Reddit post data');

      setTimeout(() => {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.cssText = getStandardButtonStyle('Reddit');
      }, 3000);
      return;
    }

    // Send to background for AI processing, local save, and cloud sync
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'SAVE_POST_REQUEST',
        data: postData
      });

      if (response?.status === 'success') {
        button.textContent = 'Saved!';
        button.style.cssText += getSavedButtonStyle();
        console.log(`[Notely] Reddit post ${postData.id} sent for processing and save`);

        // Show success notification
        NotificationManager.showSimpleNotification('Post saved to Notely!', 'success');
      } else {
        button.textContent = 'Error';
        button.style.cssText += getErrorButtonStyle();
        console.error(`[Notely] Background processing failed for Reddit post: ${response?.message}`);

        // Re-enable button after error
        setTimeout(() => {
          button.textContent = 'Save to Notely';
          button.disabled = false;
          button.style.cssText = getStandardButtonStyle('Reddit');
        }, 3000);
      }
    } catch (backgroundError) {
      button.textContent = 'Error';
      button.style.cssText += getErrorButtonStyle();
      console.error(`[Notely] Error sending Reddit post to background:`, backgroundError);

      // Re-enable button after error
      setTimeout(() => {
        button.textContent = 'Save to Notely';
        button.disabled = false;
        button.style.cssText = getStandardButtonStyle('Reddit');
      }, 3000);
    }
  } catch (error) {
    button.textContent = 'Error';
    button.style.cssText += getErrorButtonStyle();
    console.error('[Notely] Error in handleSaveButtonClick:', error);

    // Re-enable button after error
    setTimeout(() => {
      button.textContent = 'Save to Notely';
      button.disabled = false;
      button.style.cssText = getStandardButtonStyle('Reddit');
    }, 3000);
  }
}

/**
 * Set up mutation observer to detect new posts
 */
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    let shouldScan = false;

    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldScan = true;
        break;
      }
    }

    if (shouldScan) {
      scanForPosts();
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('[Notely] Mutation observer set up for Reddit');
}

// Initialize the content script when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}
