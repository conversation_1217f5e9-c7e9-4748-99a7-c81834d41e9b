/**
 * Instagram Content Script - REFACTORED
 *
 * This script now uses the unified BaseContentScript architecture.
 * All common functionality has been moved to shared modules.
 */

// Import the new refactored Instagram content script
import { InstagramContentScript } from './platforms/InstagramContentScript';

// Initialize the refactored Instagram content script
const instagramScript = new InstagramContentScript();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => instagramScript.initialize());
} else {
  instagramScript.initialize();
}

// All functionality has been moved to the refactored InstagramContentScript class
// This file now serves as a simple entry point that initializes the new architecture






