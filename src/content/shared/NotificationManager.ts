/**
 * Notification Manager - Unified notification system for all platforms
 * 
 * This module provides consistent user notifications across all content scripts.
 * It handles success messages, error messages, and progress indicators.
 */

export interface NotificationOptions {
  duration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  showIcon?: boolean;
  allowDismiss?: boolean;
}

export class NotificationManager {
  private static readonly DEFAULT_OPTIONS: Required<NotificationOptions> = {
    duration: 3000,
    position: 'top-right',
    showIcon: true,
    allowDismiss: true
  };

  private static activeNotifications = new Set<HTMLElement>();

  /**
   * Show a success notification
   */
  static showSuccess(message: string, options: NotificationOptions = {}): void {
    this.showNotification(message, 'success', options);
  }

  /**
   * Show an error notification
   */
  static showError(message: string, options: NotificationOptions = {}): void {
    this.showNotification(message, 'error', { ...options, duration: 5000 });
  }

  /**
   * Show a simple notification matching the web content style
   * This is the same style as "Text saved to <PERSON>ly" notifications
   */
  static showSimpleNotification(message: string, type: 'success' | 'error' = 'success'): void {
    // Create notification element with exact same styling as web-content.ts
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      background-color: ${type === 'success' ? '#10B981' : '#EF4444'};
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // Remove notification after 3 seconds with same animation
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  /**
   * Show a warning notification
   */
  static showWarning(message: string, options: NotificationOptions = {}): void {
    this.showNotification(message, 'warning', options);
  }

  /**
   * Show an info notification
   */
  static showInfo(message: string, options: NotificationOptions = {}): void {
    this.showNotification(message, 'info', options);
  }

  /**
   * Show a notification with specified type
   */
  private static showNotification(
    message: string, 
    type: 'success' | 'error' | 'warning' | 'info',
    options: NotificationOptions = {}
  ): void {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    // Create notification element
    const notification = this.createNotificationElement(message, type, finalOptions);
    
    // Add to active notifications
    this.activeNotifications.add(notification);
    
    // Add to DOM
    document.body.appendChild(notification);
    
    // Trigger entrance animation
    requestAnimationFrame(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    });

    // Auto-remove after duration
    if (finalOptions.duration > 0) {
      setTimeout(() => {
        this.removeNotification(notification);
      }, finalOptions.duration);
    }
  }

  /**
   * Create the notification DOM element
   */
  private static createNotificationElement(
    message: string,
    type: 'success' | 'error' | 'warning' | 'info',
    options: Required<NotificationOptions>
  ): HTMLElement {
    const notification = document.createElement('div');
    
    // Set base styles
    notification.style.cssText = this.getNotificationStyles(type, options);
    
    // Create content container
    const content = document.createElement('div');
    content.style.cssText = `
      display: flex;
      align-items: center;
      gap: 8px;
    `;

    // Add icon if enabled
    if (options.showIcon) {
      const icon = this.createIcon(type);
      content.appendChild(icon);
    }

    // Add message text
    const messageElement = document.createElement('span');
    messageElement.textContent = message;
    messageElement.style.cssText = `
      flex: 1;
      line-height: 1.4;
    `;
    content.appendChild(messageElement);

    // Add dismiss button if enabled
    if (options.allowDismiss) {
      const dismissButton = this.createDismissButton();
      dismissButton.addEventListener('click', () => {
        this.removeNotification(notification);
      });
      content.appendChild(dismissButton);
    }

    notification.appendChild(content);
    
    return notification;
  }

  /**
   * Get notification styles based on type and options
   */
  private static getNotificationStyles(
    type: 'success' | 'error' | 'warning' | 'info',
    options: Required<NotificationOptions>
  ): string {
    const colors = this.getTypeColors(type);
    const position = this.getPositionStyles(options.position);
    
    return `
      position: fixed;
      ${position}
      z-index: 10000;
      min-width: 300px;
      max-width: 400px;
      padding: 12px 16px;
      border-radius: 8px;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      opacity: 0;
      transform: translateX(${options.position.includes('right') ? '100%' : '-100%'});
      background-color: ${colors.background};
      border-left: 4px solid ${colors.accent};
    `;
  }

  /**
   * Get colors for notification type
   */
  private static getTypeColors(type: 'success' | 'error' | 'warning' | 'info') {
    switch (type) {
      case 'success':
        return { background: '#10B981', accent: '#059669' };
      case 'error':
        return { background: '#EF4444', accent: '#DC2626' };
      case 'warning':
        return { background: '#F59E0B', accent: '#D97706' };
      case 'info':
        return { background: '#3B82F6', accent: '#2563EB' };
      default:
        return { background: '#6B7280', accent: '#4B5563' };
    }
  }

  /**
   * Get position styles
   */
  private static getPositionStyles(position: NotificationOptions['position']): string {
    switch (position) {
      case 'top-right':
        return 'top: 20px; right: 20px;';
      case 'top-left':
        return 'top: 20px; left: 20px;';
      case 'bottom-right':
        return 'bottom: 20px; right: 20px;';
      case 'bottom-left':
        return 'bottom: 20px; left: 20px;';
      default:
        return 'top: 20px; right: 20px;';
    }
  }

  /**
   * Create an icon element for the notification type
   */
  private static createIcon(type: 'success' | 'error' | 'warning' | 'info'): HTMLElement {
    const icon = document.createElement('div');
    icon.style.cssText = `
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      background-color: rgba(255, 255, 255, 0.2);
    `;

    switch (type) {
      case 'success':
        icon.textContent = '✓';
        break;
      case 'error':
        icon.textContent = '✕';
        break;
      case 'warning':
        icon.textContent = '⚠';
        break;
      case 'info':
        icon.textContent = 'i';
        break;
    }

    return icon;
  }

  /**
   * Create a dismiss button
   */
  private static createDismissButton(): HTMLElement {
    const button = document.createElement('button');
    button.style.cssText = `
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      font-size: 16px;
      line-height: 1;
      opacity: 0.7;
      transition: opacity 0.2s ease;
    `;
    button.textContent = '×';
    
    button.addEventListener('mouseenter', () => {
      button.style.opacity = '1';
      button.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.opacity = '0.7';
      button.style.backgroundColor = 'transparent';
    });

    return button;
  }

  /**
   * Remove a notification with animation
   */
  private static removeNotification(notification: HTMLElement): void {
    if (!this.activeNotifications.has(notification)) {
      return;
    }

    // Remove from active set
    this.activeNotifications.delete(notification);

    // Trigger exit animation
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(100%)';

    // Remove from DOM after animation
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  /**
   * Clear all active notifications
   */
  static clearAll(): void {
    this.activeNotifications.forEach(notification => {
      this.removeNotification(notification);
    });
  }

  /**
   * Show a progress notification that can be updated
   */
  static showProgress(message: string, options: NotificationOptions = {}): {
    update: (message: string) => void;
    complete: (message?: string) => void;
    error: (message?: string) => void;
  } {
    const notification = this.createNotificationElement(
      message, 
      'info', 
      { ...this.DEFAULT_OPTIONS, ...options, duration: 0 }
    );
    
    this.activeNotifications.add(notification);
    document.body.appendChild(notification);
    
    // Trigger entrance animation
    requestAnimationFrame(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    });

    return {
      update: (newMessage: string) => {
        const messageElement = notification.querySelector('span');
        if (messageElement) {
          messageElement.textContent = newMessage;
        }
      },
      complete: (successMessage?: string) => {
        if (successMessage) {
          const messageElement = notification.querySelector('span');
          if (messageElement) {
            messageElement.textContent = successMessage;
          }
        }
        // Change to success styling
        const colors = this.getTypeColors('success');
        notification.style.backgroundColor = colors.background;
        notification.style.borderLeftColor = colors.accent;
        
        // Auto-remove after delay
        setTimeout(() => this.removeNotification(notification), 2000);
      },
      error: (errorMessage?: string) => {
        if (errorMessage) {
          const messageElement = notification.querySelector('span');
          if (messageElement) {
            messageElement.textContent = errorMessage;
          }
        }
        // Change to error styling
        const colors = this.getTypeColors('error');
        notification.style.backgroundColor = colors.background;
        notification.style.borderLeftColor = colors.accent;
        
        // Auto-remove after delay
        setTimeout(() => this.removeNotification(notification), 3000);
      }
    };
  }
}
