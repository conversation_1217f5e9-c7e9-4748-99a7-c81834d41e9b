/**
 * Base Content Script - Foundation for all platform-specific content scripts
 * 
 * This abstract class provides common functionality and structure for all social media
 * platform content scripts. It handles initialization, button management, mutation
 * observation, and save operations in a consistent way.
 */

import { Platform } from '../../types';
import { ButtonManager, ButtonConfig } from './ButtonManager';
import { <PERSON><PERSON><PERSON>ler, SaveOptions, SaveResult } from './SaveHandler';
import { MutationManager, MutationConfig } from './MutationManager';
import { NotificationManager } from './NotificationManager';

export interface ContentScriptConfig {
  platform: Platform;
  postSelectors: string[];
  buttonConfig?: Partial<ButtonConfig>;
  saveOptions?: Partial<SaveOptions>;
  mutationConfig?: Partial<MutationConfig>;
  enablePeriodicScan?: boolean;
  periodicScanInterval?: number;
  debug?: boolean;
}

export abstract class BaseContentScript {
  protected config: Required<ContentScriptConfig>;
  protected mutationManager: MutationManager | null = null;
  protected processedElements = new Set<Element>();
  protected periodicScanTimer: number | null = null;

  private static readonly DEFAULT_CONFIG: Required<ContentScriptConfig> = {
    platform: 'Web',
    postSelectors: [],
    buttonConfig: {},
    saveOptions: {},
    mutationConfig: {},
    enablePeriodicScan: false,
    periodicScanInterval: 3000,
    debug: false
  };

  constructor(config: ContentScriptConfig) {
    this.config = { ...BaseContentScript.DEFAULT_CONFIG, ...config };
    this.log('Content script created');
  }

  /**
   * Initialize the content script
   */
  initialize(): void {
    this.log('Initializing content script');

    // Initial scan for posts
    this.scanForPosts();

    // Set up mutation observer
    this.setupMutationObserver();

    // Set up periodic scanning if enabled
    if (this.config.enablePeriodicScan) {
      this.setupPeriodicScan();
    }

    // Set up additional platform-specific initialization
    this.onInitialize();

    this.log('Content script initialized');
  }

  /**
   * Cleanup and stop the content script
   */
  destroy(): void {
    this.log('Destroying content script');

    // Stop mutation observer
    this.mutationManager?.stop();

    // Stop periodic scanning
    if (this.periodicScanTimer) {
      clearInterval(this.periodicScanTimer);
      this.periodicScanTimer = null;
    }

    // Clear processed elements
    this.processedElements.clear();

    // Platform-specific cleanup
    this.onDestroy();

    this.log('Content script destroyed');
  }

  /**
   * Scan the page for posts and add save buttons
   */
  protected scanForPosts(): void {
    try {
      const posts = this.findPosts();

      let processedCount = 0;
      posts.forEach(post => {
        if (this.shouldProcessPost(post)) {
          this.processPost(post);
          processedCount++;
        }
      });
    } catch (error) {
      console.error(`[Notely] Error scanning for ${this.config.platform} posts:`, error);
    }
  }

  /**
   * Find posts on the page using configured selectors
   */
  protected findPosts(): HTMLElement[] {
    const posts: HTMLElement[] = [];

    for (const selector of this.config.postSelectors) {
      try {
        const elements = document.querySelectorAll(selector);
        posts.push(...Array.from(elements) as HTMLElement[]);
      } catch (error) {
        console.warn(`[Notely] Invalid selector for ${this.config.platform}: ${selector}`, error);
      }
    }

    // Remove duplicates
    return Array.from(new Set(posts));
  }

  /**
   * Check if a post should be processed
   */
  protected shouldProcessPost(post: HTMLElement): boolean {
    // Skip if already processed
    if (this.processedElements.has(post) || ButtonManager.isProcessed(post)) {
      return false;
    }

    // Skip if already has a save button
    if (post.querySelector(`.${ButtonManager.getSaveButtonClass()}`)) {
      return false;
    }

    // Platform-specific validation
    return this.validatePost(post);
  }

  /**
   * Process a single post by adding a save button
   */
  protected processPost(post: HTMLElement): void {
    // Mark as processed
    this.processedElements.add(post);
    ButtonManager.markAsProcessed(post);

    // Add save button
    this.addSaveButton(post);
  }

  /**
   * Add a save button to a post
   */
  protected addSaveButton(post: HTMLElement): void {
    // Check if button already exists to prevent duplicates
    const existingButton = post.querySelector('.notely-save-btn');
    if (existingButton) {
      this.log('Save button already exists, skipping');
      return;
    }

    // Create button with platform-specific config
    const buttonConfig: ButtonConfig = {
      ...this.config.buttonConfig,
      platform: this.config.platform
    };

    const button = ButtonManager.createSaveButton(buttonConfig);

    // Add click handler
    ButtonManager.addClickHandler(button, () => {
      this.handleSaveButtonClick(post, button);
    });

    // Find insertion point and add button
    const insertionPoint = this.findButtonInsertionPoint(post);
    this.insertButton(button, insertionPoint, post);

    this.log('Save button added to post');
  }

  /**
   * Handle save button click
   */
  protected async handleSaveButtonClick(post: HTMLElement, button: HTMLButtonElement): Promise<SaveResult> {
    return await SaveHandler.handleSaveButtonClick(
      post,
      button,
      this.config.platform,
      this.config.saveOptions
    );
  }

  /**
   * Set up mutation observer
   */
  protected setupMutationObserver(): void {
    const mutationConfig: MutationConfig = {
      ...this.config.mutationConfig,
      selectors: this.config.postSelectors,
      platform: this.config.platform
    };

    this.mutationManager = MutationManager.createForPlatform(
      this.config.platform,
      this.config.postSelectors,
      {
        onNewContent: () => this.scanForPosts(),
        onUrlChange: (newUrl) => this.handleUrlChange(newUrl),
        onContainerChange: () => this.handleContainerChange()
      },
      mutationConfig
    );

    this.mutationManager.start();
  }

  /**
   * Set up periodic scanning
   */
  protected setupPeriodicScan(): void {
    this.periodicScanTimer = window.setInterval(() => {
      this.scanForPosts();
    }, this.config.periodicScanInterval);

    this.log(`Periodic scanning enabled (${this.config.periodicScanInterval}ms interval)`);
  }

  /**
   * Handle URL changes (for SPAs)
   */
  protected handleUrlChange(newUrl: string): void {
    this.log(`URL changed to: ${newUrl}`);
    
    // Clear processed elements and re-scan
    this.processedElements.clear();
    this.scanForPosts();
    
    // Platform-specific URL change handling
    this.onUrlChange(newUrl);
  }

  /**
   * Handle container changes
   */
  protected handleContainerChange(): void {
    // Restart mutation observer
    this.mutationManager?.restart();

    // Platform-specific container change handling
    this.onContainerChange();
  }

  /**
   * Log messages with platform prefix
   */
  protected log(message: string, ...args: any[]): void {
    // Debug logging removed for production
  }

  // Abstract methods to be implemented by platform-specific scripts

  /**
   * Validate if a post element should be processed
   * Platform-specific validation logic
   */
  protected abstract validatePost(post: HTMLElement): boolean;

  /**
   * Find the best insertion point for the save button
   * Platform-specific button placement logic
   */
  protected abstract findButtonInsertionPoint(post: HTMLElement): {
    container: HTMLElement;
    method: 'append' | 'prepend' | 'before' | 'after' | 'absolute';
  };

  /**
   * Insert the button at the specified insertion point
   */
  protected abstract insertButton(
    button: HTMLButtonElement,
    insertionPoint: { container: HTMLElement; method: string },
    post: HTMLElement
  ): void;

  // Optional hooks for platform-specific behavior

  /**
   * Called after initialization is complete
   * Override for platform-specific setup
   */
  protected onInitialize(): void {
    // Default: no additional initialization
  }

  /**
   * Called before destruction
   * Override for platform-specific cleanup
   */
  protected onDestroy(): void {
    // Default: no additional cleanup
  }

  /**
   * Called when URL changes
   * Override for platform-specific URL change handling
   */
  protected onUrlChange(newUrl: string): void {
    // Default: no additional handling
  }

  /**
   * Called when container changes
   * Override for platform-specific container change handling
   */
  protected onContainerChange(): void {
    // Default: no additional handling
  }

  /**
   * Static method to initialize content script when DOM is ready
   */
  static initializeWhenReady<T extends BaseContentScript>(
    scriptClass: new (...args: any[]) => T,
    ...args: any[]
  ): T {
    const script = new scriptClass(...args);

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => script.initialize());
    } else {
      script.initialize();
    }

    return script;
  }
}
