/**
 * Mutation Manager - Unified mutation observer handling for all platforms
 * 
 * This module provides consistent mutation observation across all social media platforms.
 * It handles detecting new content, debouncing scans, and managing observer lifecycle.
 */

import { Platform } from '../../types';

export interface MutationConfig {
  selectors: string[];
  debounceDelay?: number;
  observeAttributes?: boolean;
  observeCharacterData?: boolean;
  targetContainer?: string;
  platform?: Platform;
}

export interface MutationCallbacks {
  onNewContent: () => void;
  onUrlChange?: (newUrl: string) => void;
  onContainerChange?: () => void;
}

export class MutationManager {
  private observer: MutationObserver | null = null;
  private urlObserver: MutationObserver | null = null;
  private containerObserver: MutationObserver | null = null;
  private debounceTimer: number | null = null;
  private lastUrl: string = '';
  private config: Required<MutationConfig>;
  private callbacks: MutationCallbacks;

  private static readonly DEFAULT_CONFIG: Required<MutationConfig> = {
    selectors: [],
    debounceDelay: 500,
    observeAttributes: false,
    observeCharacterData: false,
    targetContainer: '',
    platform: 'Web'
  };

  constructor(config: MutationConfig, callbacks: MutationCallbacks) {
    this.config = { ...MutationManager.DEFAULT_CONFIG, ...config };
    this.callbacks = callbacks;
    this.lastUrl = window.location.href;
  }

  /**
   * Start observing mutations
   */
  start(): void {
    this.setupContentObserver();
    
    if (this.callbacks.onUrlChange) {
      this.setupUrlObserver();
    }
    
    if (this.config.targetContainer && this.callbacks.onContainerChange) {
      this.setupContainerObserver();
    }

    console.log(`[Notely] Mutation manager started for ${this.config.platform}`);
  }

  /**
   * Stop observing mutations
   */
  stop(): void {
    this.observer?.disconnect();
    this.urlObserver?.disconnect();
    this.containerObserver?.disconnect();
    
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    console.log(`[Notely] Mutation manager stopped for ${this.config.platform}`);
  }

  /**
   * Restart the mutation observer (useful for dynamic content changes)
   */
  restart(): void {
    this.stop();
    this.start();
  }

  /**
   * Set up the main content observer
   */
  private setupContentObserver(): void {
    this.observer = new MutationObserver((mutations) => {
      if (this.shouldTriggerScan(mutations)) {
        this.debouncedScan();
      }
    });

    // Determine target container
    const targetContainer = this.getTargetContainer();
    
    this.observer.observe(targetContainer, {
      childList: true,
      subtree: true,
      attributes: this.config.observeAttributes,
      characterData: this.config.observeCharacterData
    });

    console.log(`[Notely] Content observer set up for ${this.config.platform} on`, 
      targetContainer === document.body ? 'document body' : 'target container');
  }

  /**
   * Set up URL change observer for SPAs
   */
  private setupUrlObserver(): void {
    this.urlObserver = new MutationObserver(() => {
      const currentUrl = window.location.href;
      if (currentUrl !== this.lastUrl) {
        this.lastUrl = currentUrl;
        console.log(`[Notely] URL changed for ${this.config.platform}, triggering callback`);
        this.callbacks.onUrlChange?.(currentUrl);
      }
    });

    // Observe title changes (often indicates navigation)
    const titleElement = document.querySelector('title') || document.head;
    this.urlObserver.observe(titleElement, {
      childList: true,
      subtree: true,
      characterData: true
    });
  }

  /**
   * Set up container observer to detect when the main container changes
   */
  private setupContainerObserver(): void {
    if (!this.config.targetContainer) return;

    this.containerObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          const newContainer = document.querySelector(this.config.targetContainer);
          const currentTarget = this.observer?.takeRecords();
          
          if (newContainer && newContainer !== mutation.target) {
            console.log(`[Notely] Container changed for ${this.config.platform}, restarting observer`);
            this.callbacks.onContainerChange?.();
            return;
          }
        }
      }
    });

    this.containerObserver.observe(document.body, {
      childList: true,
      subtree: false
    });
  }

  /**
   * Get the target container for observation
   */
  private getTargetContainer(): Element {
    if (this.config.targetContainer) {
      const container = document.querySelector(this.config.targetContainer);
      if (container) {
        return container;
      }
    }

    // Platform-specific fallbacks
    const platformContainer = this.getPlatformContainer();
    if (platformContainer) {
      return platformContainer;
    }

    // Ultimate fallback
    return document.body;
  }

  /**
   * Get platform-specific container selectors
   */
  private getPlatformContainer(): Element | null {
    const selectors = this.getPlatformContainerSelectors();
    
    for (const selector of selectors) {
      const container = document.querySelector(selector);
      if (container) {
        return container;
      }
    }
    
    return null;
  }

  /**
   * Get platform-specific container selectors
   */
  private getPlatformContainerSelectors(): string[] {
    switch (this.config.platform) {
      case 'X/Twitter':
        return [
          '[data-testid="primaryColumn"]',
          '[data-testid="timeline"]',
          'main[role="main"]'
        ];
      case 'Instagram':
        return [
          'main[role="main"]',
          'section[role="main"]',
          '[data-testid="feed"]'
        ];
      case 'LinkedIn':
        return [
          '.scaffold-finite-scroll__content',
          '.feed-container',
          'main'
        ];
      case 'Pinterest':
        return [
          '[data-test-id="grid"]',
          '[data-test-id="masonry-container"]'
        ];
      case 'Reddit':
        return [
          '[data-testid="post-container"]',
          '.Post'
        ];
      default:
        return ['main', '[role="main"]'];
    }
  }

  /**
   * Determine if mutations should trigger a content scan
   */
  private shouldTriggerScan(mutations: MutationRecord[]): boolean {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes match our selectors or could contain matching content
        for (const node of Array.from(mutation.addedNodes)) {
          if (node instanceof HTMLElement) {
            // Check if the node itself matches any selector
            if (this.nodeMatchesSelectors(node)) {
              return true;
            }
            
            // Check if the node contains any matching elements
            if (this.nodeContainsMatchingElements(node)) {
              return true;
            }
          }
        }
      }
      
      // For attribute changes, check if the changed element is relevant
      if (mutation.type === 'attributes' && mutation.target instanceof HTMLElement) {
        if (this.nodeMatchesSelectors(mutation.target)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * Check if a node matches any of our selectors
   */
  private nodeMatchesSelectors(node: HTMLElement): boolean {
    return this.config.selectors.some(selector => {
      try {
        return node.matches(selector);
      } catch (e) {
        console.warn(`[Notely] Invalid selector: ${selector}`, e);
        return false;
      }
    });
  }

  /**
   * Check if a node contains elements matching our selectors
   */
  private nodeContainsMatchingElements(node: HTMLElement): boolean {
    return this.config.selectors.some(selector => {
      try {
        return node.querySelector(selector) !== null;
      } catch (e) {
        console.warn(`[Notely] Invalid selector: ${selector}`, e);
        return false;
      }
    });
  }

  /**
   * Debounced scan to avoid excessive scanning
   */
  private debouncedScan(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = window.setTimeout(() => {
      console.log(`[Notely] Mutation detected for ${this.config.platform}, triggering content scan`);
      this.callbacks.onNewContent();
      this.debounceTimer = null;
    }, this.config.debounceDelay);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<MutationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.restart();
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<MutationConfig> {
    return { ...this.config };
  }

  /**
   * Check if the observer is currently active
   */
  isActive(): boolean {
    return this.observer !== null;
  }

  /**
   * Force trigger a content scan
   */
  forceScan(): void {
    this.callbacks.onNewContent();
  }

  /**
   * Create a mutation manager with platform-specific defaults
   */
  static createForPlatform(
    platform: Platform,
    selectors: string[],
    callbacks: MutationCallbacks,
    customConfig: Partial<MutationConfig> = {}
  ): MutationManager {
    const platformDefaults = this.getPlatformDefaults(platform);
    
    const config: MutationConfig = {
      ...platformDefaults,
      ...customConfig,
      selectors,
      platform
    };

    return new MutationManager(config, callbacks);
  }

  /**
   * Get platform-specific default configuration
   */
  private static getPlatformDefaults(platform: Platform): Partial<MutationConfig> {
    switch (platform) {
      case 'X/Twitter':
        return {
          debounceDelay: 300,
          targetContainer: '[data-testid="primaryColumn"]'
        };
      case 'Instagram':
        return {
          debounceDelay: 500,
          targetContainer: 'main[role="main"]'
        };
      case 'LinkedIn':
        return {
          debounceDelay: 500,
          targetContainer: '.scaffold-finite-scroll__content'
        };
      case 'Pinterest':
        return {
          debounceDelay: 300,
          targetContainer: '[data-test-id="grid"]'
        };
      case 'Reddit':
        return {
          debounceDelay: 400,
          targetContainer: '[data-testid="post-container"]'
        };
      default:
        return {
          debounceDelay: 500
        };
    }
  }
}
