/**
 * Save Handler - Unified save logic for all platforms
 * 
 * This module provides consistent save behavior across all social media platforms.
 * It handles the complete save flow: extraction, local save, cloud sync, and UI feedback.
 */

import { Platform, Post } from '../../types';
import { extractPostData } from '../controller';
import { ButtonManager } from './ButtonManager';
import { NotificationManager } from './NotificationManager';

export interface SaveOptions {
  includeImages?: boolean;
  useCloudService?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export interface SaveResult {
  success: boolean;
  error?: string;
  postId?: string;
}

export class SaveHandler {
  private static readonly DEFAULT_OPTIONS: Required<SaveOptions> = {
    includeImages: true,
    useCloudService: true,
    maxRetries: 2,
    retryDelay: 1000
  };

  /**
   * Handle save button click with unified logic
   */
  static async handleSaveButtonClick(
    postElement: HTMLElement,
    button: HTMLButtonElement,
    platform: Platform,
    options: SaveOptions = {}
  ): Promise<SaveResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    // Update button to saving state
    ButtonManager.updateButtonState(button, 'saving');

    let lastError = '';
    let retryCount = 0;

    while (retryCount <= finalOptions.maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`[Notely] Retry attempt ${retryCount}/${finalOptions.maxRetries} for ${platform} post`);
          ButtonManager.updateButtonState(button, 'saving', `Retry ${retryCount}/${finalOptions.maxRetries}...`);
        }

        // Extract post data
        console.log(`[Notely] Extracting ${platform} post data...`);
        const postData = await extractPostData(postElement, platform, {
          includeImages: finalOptions.includeImages,
          useCloudService: finalOptions.useCloudService
        });

        if (!postData) {
          lastError = `Failed to extract ${platform} post data`;
          console.error(`[Notely] ${lastError}`);
          
          if (this.shouldRetry(lastError, retryCount, finalOptions.maxRetries)) {
            retryCount++;
            await this.delay(finalOptions.retryDelay);
            continue;
          }
          break;
        }

        console.log(`[Notely] Successfully extracted ${platform} post data:`, {
          id: postData.id,
          author: postData.author,
          content: postData.content?.substring(0, 100),
          mediaCount: postData.media?.length || 0,
          stats: postData.stats,
          interactions: (postData as any).interactions,
          hasStats: !!postData.stats,
          hasInteractions: !!(postData as any).interactions
        });

        // Send to background for AI processing, local save, and cloud sync
        try {
          // Check if Chrome runtime is available
          if (!chrome?.runtime?.id) {
            throw new Error('Extension context invalidated. Please reload the page and try again.');
          }

          const response = await chrome.runtime.sendMessage({
            action: 'SAVE_POST_REQUEST',
            data: postData
          });

          if (response?.status === 'success') {
            // Update button to saved state
            ButtonManager.updateButtonState(button, 'saved');
            console.log(`[Notely] ${platform} post ${postData.id} sent for processing and save`);

            // Show success notification
            NotificationManager.showSimpleNotification('Post saved to Notely!', 'success');

            // Reset button after delay
            ButtonManager.resetButtonAfterDelay(button, 2000, platform);

            return { success: true, postId: postData.id };
          } else {
            lastError = response?.message || 'Background processing failed';
            console.error(`[Notely] Background processing failed for ${platform} post: ${lastError}`);

            if (this.shouldRetry(lastError, retryCount, finalOptions.maxRetries)) {
              retryCount++;
              await this.delay(finalOptions.retryDelay);
              continue;
            }
            break;
          }
        } catch (backgroundError) {
          lastError = backgroundError instanceof Error ? backgroundError.message : String(backgroundError);
          console.error(`[Notely] Error sending ${platform} post to background:`, backgroundError);

          if (this.shouldRetry(lastError, retryCount, finalOptions.maxRetries)) {
            retryCount++;
            await this.delay(finalOptions.retryDelay);
            continue;
          }
          break;
        }

      } catch (error) {
        lastError = error instanceof Error ? error.message : String(error);
        console.error(`[Notely] Error in save handler for ${platform}:`, error);
        
        if (retryCount < finalOptions.maxRetries) {
          retryCount++;
          await this.delay(finalOptions.retryDelay);
          continue;
        }
        break;
      }
    }

    // If we get here, all retries failed
    ButtonManager.updateButtonState(button, 'error');
    
    // Show error notification
    NotificationManager.showError(`Failed to save ${platform} post: ${lastError}`);
    
    // Reset button after delay
    ButtonManager.resetButtonAfterDelay(button, 3000, platform);

    return { success: false, error: lastError };
  }

  /**
   * Determine if an error is worth retrying
   */
  private static shouldRetry(error: string, currentRetry: number, maxRetries: number): boolean {
    if (currentRetry >= maxRetries) {
      return false;
    }

    // Retry for image-related errors
    const retryableErrors = [
      'no media',
      'no valid image',
      'No images found',
      'Failed to fetch',
      'Network error',
      'timeout'
    ];

    return retryableErrors.some(retryableError => 
      error.toLowerCase().includes(retryableError.toLowerCase())
    );
  }

  /**
   * Delay execution for retry logic
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Handle save for web content (text, image, page)
   */
  static async handleWebContentSave(
    data: any,
    type: 'text' | 'image' | 'page'
  ): Promise<SaveResult> {
    try {
      let post: Post;
      let element: HTMLElement;

      switch (type) {
        case 'text':
          element = document.createElement('div');
          element.textContent = data.selectionText;
          post = await this.createWebTextPost(data, element);
          break;
        case 'image':
          element = this.findImageElement(data.srcUrl) || this.createVirtualImageElement(data.srcUrl);
          post = await this.createWebImagePost(data, element);
          break;
        case 'page':
          element = document.body;
          post = await this.createWebPagePost(data, element);
          break;
        default:
          throw new Error(`Unknown web content type: ${type}`);
      }

      // Send to background for processing and save
      try {
        // Check if Chrome runtime is available
        if (!chrome?.runtime?.id) {
          throw new Error('Extension context invalidated. Please reload the page and try again.');
        }

        const response = await chrome.runtime.sendMessage({
          action: 'SAVE_POST_REQUEST',
          data: post
        });

        if (response?.status === 'success') {
          NotificationManager.showSuccess(`${type.charAt(0).toUpperCase() + type.slice(1)} saved to Notely!`);
          return { success: true, postId: post.id };
        } else {
          throw new Error(response?.message || `Failed to save ${type}`);
        }
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : `Failed to save ${type}`);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`[Notely] Error saving web ${type}:`, error);
      NotificationManager.showError(`Failed to save ${type} to Notely`);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Create a post object for web text content
   */
  private static async createWebTextPost(data: any, element: HTMLElement): Promise<Post> {
    const postData = await extractPostData(element, 'Web', {
      selectedText: data.selectionText,
      pageUrl: data.pageUrl || window.location.href
    });

    if (!postData) {
      throw new Error('Failed to extract post data from selection');
    }

    const textId = `web_text_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;

    return {
      ...postData,
      id: textId,
      permalink: `${window.location.href}#text-${textId}`,
      content: data.selectionText,
      textContent: data.selectionText,
      title: `Selected text from ${document.title}`,
      notes: `Selected from: ${window.location.href}`
    };
  }

  /**
   * Create a post object for web image content
   */
  private static async createWebImagePost(data: any, element: HTMLElement): Promise<Post> {
    const postData = await extractPostData(element, 'Web', {
      imageUrl: data.srcUrl,
      pageUrl: data.pageUrl || window.location.href
    });

    if (!postData) {
      throw new Error('Failed to extract post data from image');
    }

    const imageId = `web_image_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;
    const img = element as HTMLImageElement;

    return {
      ...postData,
      id: imageId,
      permalink: `${window.location.href}#image-${imageId}`,
      url: data.srcUrl,
      title: `Image from ${document.title}`,
      content: img.alt || `Image saved from ${window.location.hostname}`,
      textContent: img.alt || `Image saved from ${window.location.hostname}`,
      notes: `Image saved from: ${window.location.href}`,
      media: [{
        type: 'image' as const,
        url: data.srcUrl,
        alt: img.alt || '',
        title: img.title || ''
      }]
    };
  }

  /**
   * Create a post object for web page content
   */
  private static async createWebPagePost(data: any, element: HTMLElement): Promise<Post> {
    const postData = await extractPostData(element, 'Web', {
      pageUrl: data.pageUrl || window.location.href,
      fullPage: true
    });

    if (!postData) {
      throw new Error('Failed to extract post data from page');
    }

    const pageId = `web_page_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;

    return {
      ...postData,
      id: pageId,
      permalink: `${window.location.href}#page-${pageId}`,
      title: document.title,
      content: this.getPageSummary(),
      textContent: this.getPageSummary(),
      notes: `Page saved from: ${window.location.href}`
    };
  }

  /**
   * Find an image element on the page
   */
  private static findImageElement(srcUrl: string): HTMLImageElement | null {
    // Try exact src match first
    let img = document.querySelector(`img[src="${srcUrl}"]`) as HTMLImageElement;
    
    if (!img) {
      // Try partial URL match
      const allImages = document.querySelectorAll('img');
      for (const image of allImages) {
        if (image.src === srcUrl || 
            image.currentSrc === srcUrl || 
            (image.srcset && image.srcset.includes(srcUrl))) {
          img = image;
          break;
        }
      }
    }
    
    return img;
  }

  /**
   * Create a virtual image element
   */
  private static createVirtualImageElement(srcUrl: string): HTMLImageElement {
    const img = document.createElement('img');
    img.src = srcUrl;
    img.alt = `Image from ${window.location.hostname}`;
    return img;
  }

  /**
   * Get a summary of the page content
   */
  private static getPageSummary(): string {
    // Try meta description first
    const metaDescription = document.querySelector('meta[name="description"]') as HTMLMetaElement;
    if (metaDescription?.content) {
      return metaDescription.content;
    }

    // Try first paragraph
    const firstParagraph = document.querySelector('p');
    if (firstParagraph?.textContent) {
      const text = firstParagraph.textContent.trim();
      if (text.length > 50) {
        return text.length > 250 ? text.substring(0, 250) + '...' : text;
      }
    }

    // Fallback to page title
    return document.title || window.location.href;
  }
}
