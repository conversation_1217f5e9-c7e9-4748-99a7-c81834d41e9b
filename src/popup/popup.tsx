import React, { useState, useEffect } from 'react';
import <PERSON>actDOM from 'react-dom/client';
import { LocaleProvider } from '../contexts/LocaleProvider';
import { useTranslation } from '../hooks/useTranslation';
import LanguageSelector from '../components/LanguageSelector';
import ThemeToggle from '../components/ThemeToggle';
import { initializeTheme } from '../utils/themeUtils';
import '../index.css';
import '../styles/notely-theme.css';

interface AuthStatus {
  isAuthenticated: boolean;
  user?: {
    name?: string;
    email?: string;
  };
}

const PopupContent: React.FC = () => {
  const { t } = useTranslation();
  const [authStatus, setAuthStatus] = useState<AuthStatus>({ isAuthenticated: false });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // Check authentication status
  const checkAuthStatus = async () => {
    try {
      setLoading(true);
      const response = await chrome.runtime.sendMessage({ action: 'check_auth_status' });

      if (response && response.isAuthenticated) {
        setAuthStatus({
          isAuthenticated: true,
          user: response.user
        });
      } else {
        setAuthStatus({ isAuthenticated: false });
      }
    } catch (err) {
      console.error('[Popup] Error checking auth status:', err);
      setError(t('error.auth'));
    } finally {
      setLoading(false);
    }
  };

  // Handle login
  const handleLogin = async () => {
    try {
      setIsLoggingIn(true);
      setError(null);

      const response = await chrome.runtime.sendMessage({ action: 'login' });

      if (response && response.success) {
        await checkAuthStatus(); // Refresh status
      } else {
        const errorMsg = response?.error || t('error.generic');
        setError(errorMsg);
      }
    } catch (err) {
      console.error('[Popup] Login error:', err);
      setError(t('error.network'));
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await chrome.storage.local.remove(['token', 'authToken', 'userInfo']);
      setAuthStatus({ isAuthenticated: false });
    } catch (err) {
      console.error('[Popup] Logout error:', err);
      setError(t('error.generic'));
    }
  };

  // Open dashboard
  const openDashboard = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('dashboard.html') });
    window.close();
  };

  // Open settings
  const openSettings = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
    window.close();
  };

  useEffect(() => {
    // Initialize theme
    initializeTheme();

    checkAuthStatus();
  }, []);

  if (loading) {
    return (
      <div className="w-80 bg-gradient-to-br from-zinc-950 via-black to-zinc-900 text-white notely-filter-transition relative overflow-hidden">
        {/* Ambient background effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 right-1/4 w-32 h-32 bg-purple-500/20 rounded-full blur-2xl animate-pulse" />
          <div className="absolute bottom-1/4 left-1/4 w-40 h-40 bg-pink-500/20 rounded-full blur-2xl animate-pulse delay-1000" />
        </div>

        <div className="relative p-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500/30 border-t-purple-500"></div>
            <span className="ml-3 text-gray-300 font-medium">{t('common.loading')}...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-gradient-to-br from-zinc-950 via-black to-zinc-900 shadow-2xl notely-filter-transition relative overflow-hidden">
      {/* Ambient background effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 right-1/4 w-32 h-32 bg-purple-500/20 rounded-full blur-2xl animate-pulse" />
        <div className="absolute bottom-1/4 left-1/4 w-40 h-40 bg-pink-500/20 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-indigo-500/10 rounded-full blur-3xl animate-float" />
      </div>

      {/* Header with glassmorphism */}
      <div className="relative bg-black/20 backdrop-blur-xl border-b border-purple-500/20 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center group">
            <div className="relative">
              <img src="/notely-dark.svg" alt="Notely" className="h-8 w-8 mr-3 drop-shadow-lg group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400" style={{ fontFamily: 'Space Grotesk, sans-serif' }}>
                {t('app.name')}
              </h1>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <ThemeToggle className="mr-2" />
            <LanguageSelector showLabel={false} className="text-white" />
          </div>
        </div>
        <p className="text-gray-300 text-sm leading-relaxed mt-2 font-medium">{t('app.tagline')}</p>
      </div>

      {/* Content */}
      <div className="relative p-6">
        {error && (
          <div className="mb-4 p-3 bg-red-500/10 backdrop-blur-md border border-red-500/30 text-red-400 rounded-xl notely-filter-transition">
            {error}
          </div>
        )}

        {authStatus.isAuthenticated ? (
          // Authenticated state
          <div className="space-y-6 notely-stagger-item">
            <div className="text-center">
              <div className="text-green-400 mb-3">
                <svg className="w-10 h-10 mx-auto drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-sm text-gray-300 font-medium">
                {t('dashboard.welcome')}, <span className="text-white font-semibold">{authStatus.user?.name || authStatus.user?.email || 'User'}</span>!
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={openDashboard}
                className="group relative w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300" />
                <span className="relative">{t('dashboard.title')}</span>
              </button>

              <button
                onClick={openSettings}
                className="w-full bg-white/5 backdrop-blur-md border border-white/10 text-white px-6 py-3 rounded-xl font-medium hover:bg-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105"
              >
                {t('settings.title')}
              </button>

              <button
                onClick={handleLogout}
                className="w-full bg-red-500/10 backdrop-blur-md border border-red-500/30 text-red-400 px-6 py-3 rounded-xl font-medium hover:bg-red-500/20 hover:border-red-500/50 hover:text-red-300 transition-all duration-300 hover:scale-105"
              >
                {t('auth.logout')}
              </button>
            </div>
          </div>
        ) : (
          // Not authenticated state
          <div className="space-y-6 notely-stagger-item">
            <div className="text-center">
              <p className="text-gray-300 mb-6 font-medium">
                {t('auth.noAccount')}
              </p>

              <button
                onClick={handleLogin}
                disabled={isLoggingIn}
                className="group relative w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300" />
                <span className="relative">
                  {isLoggingIn ? t('auth.loggingIn') : t('auth.loginWith')}
                </span>
              </button>
            </div>

            <div className="border-t border-white/10 pt-6">
              <button
                onClick={openDashboard}
                className="w-full bg-white/5 backdrop-blur-md border border-white/10 text-white px-6 py-3 rounded-xl font-medium hover:bg-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105"
              >
                {t('dashboard.title')} (Local)
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="relative bg-black/20 backdrop-blur-xl border-t border-white/10 px-6 py-4">
        <div className="flex justify-between items-center text-xs text-gray-400">
          <span className="font-medium">v1.0.0</span>
          <button
            onClick={openSettings}
            className="text-gray-400 hover:text-white transition-colors duration-300 font-medium"
          >
            {t('settings.title')}
          </button>
        </div>
      </div>
    </div>
  );
};

const Popup: React.FC = () => {
  return (
    <LocaleProvider>
      <PopupContent />
    </LocaleProvider>
  );
};

// Render the popup
const container = document.getElementById('root');
if (container) {
  const root = ReactDOM.createRoot(container);
  root.render(<Popup />);
}
