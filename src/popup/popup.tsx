import React, { useState, useEffect } from 'react';
import <PERSON>actDOM from 'react-dom/client';
import { LocaleProvider } from '../contexts/LocaleProvider';
import { useTranslation } from '../hooks/useTranslation';
import LanguageSelector from '../components/LanguageSelector';
import ThemeToggle from '../components/ThemeToggle';
import { initializeTheme } from '../utils/themeUtils';
import '../index.css';
import '../styles/notely-theme.css';

interface AuthStatus {
  isAuthenticated: boolean;
  user?: {
    name?: string;
    email?: string;
  };
}

const PopupContent: React.FC = () => {
  const { t } = useTranslation();
  const [authStatus, setAuthStatus] = useState<AuthStatus>({ isAuthenticated: false });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // Check authentication status
  const checkAuthStatus = async () => {
    try {
      setLoading(true);
      const response = await chrome.runtime.sendMessage({ action: 'check_auth_status' });

      if (response && response.isAuthenticated) {
        setAuthStatus({
          isAuthenticated: true,
          user: response.user
        });
      } else {
        setAuthStatus({ isAuthenticated: false });
      }
    } catch (err) {
      console.error('[Popup] Error checking auth status:', err);
      setError(t('error.auth'));
    } finally {
      setLoading(false);
    }
  };

  // Handle login
  const handleLogin = async () => {
    try {
      setIsLoggingIn(true);
      setError(null);

      const response = await chrome.runtime.sendMessage({ action: 'login' });

      if (response && response.success) {
        await checkAuthStatus(); // Refresh status
      } else {
        const errorMsg = response?.error || t('error.generic');
        setError(errorMsg);
      }
    } catch (err) {
      console.error('[Popup] Login error:', err);
      setError(t('error.network'));
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await chrome.storage.local.remove(['token', 'authToken', 'userInfo']);
      setAuthStatus({ isAuthenticated: false });
    } catch (err) {
      console.error('[Popup] Logout error:', err);
      setError(t('error.generic'));
    }
  };

  // Open dashboard
  const openDashboard = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('dashboard.html') });
    window.close();
  };

  // Open settings
  const openSettings = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
    window.close();
  };

  useEffect(() => {
    // Initialize theme
    initializeTheme();

    checkAuthStatus();
  }, []);

  if (loading) {
    return (
      <div className="w-80 p-4 bg-notely-bg text-notely-text-primary notely-filter-transition">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent"></div>
          <span className="ml-2 text-notely-text-secondary">{t('common.loading')}...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-notely-bg shadow-notely-xl notely-filter-transition">
      {/* Header */}
      <div className="bg-gradient-to-r from-notely-accent to-notely-accent-secondary text-white notely-breathing-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <img src="/notely.svg" alt="Notely" className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-semibold leading-tight">{t('app.name')}</h1>
          </div>
          <div className="flex items-center space-x-2">
            <ThemeToggle className="mr-2" />
            <LanguageSelector showLabel={false} className="text-white" />
          </div>
        </div>
        <p className="text-notely-accent-light text-sm leading-relaxed mt-1">{t('app.tagline')}</p>
      </div>

      {/* Content */}
      <div className="notely-breathing-xl">
        {error && (
          <div className="mb-4 p-3 bg-notely-error-bg border border-notely-error text-notely-error rounded-lg notely-filter-transition">
            {error}
          </div>
        )}

        {authStatus.isAuthenticated ? (
          // Authenticated state
          <div className="space-y-6 notely-stagger-item">
            <div className="text-center">
              <div className="text-notely-success mb-3">
                <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-sm text-notely-text-secondary">
                {t('dashboard.welcome')}, {authStatus.user?.name || authStatus.user?.email || 'User'}!
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={openDashboard}
                className="notely-btn-primary w-full notely-filter-transition hover:scale-105"
              >
                {t('dashboard.title')}
              </button>

              <button
                onClick={openSettings}
                className="notely-btn-secondary w-full notely-filter-transition hover:scale-105"
              >
                {t('settings.title')}
              </button>

              <button
                onClick={handleLogout}
                className="w-full px-4 py-2 bg-notely-error-bg text-notely-error rounded-lg hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-notely-error notely-filter-transition hover:scale-105"
              >
                {t('auth.logout')}
              </button>
            </div>
          </div>
        ) : (
          // Not authenticated state
          <div className="space-y-6 notely-stagger-item">
            <div className="text-center">
              <p className="text-notely-text-secondary mb-6">
                {t('auth.noAccount')}
              </p>

              <button
                onClick={handleLogin}
                disabled={isLoggingIn}
                className="notely-btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed notely-filter-transition hover:scale-105"
              >
                {isLoggingIn ? t('auth.loggingIn') : t('auth.loginWith')}
              </button>
            </div>

            <div className="border-t border-notely-border pt-6">
              <button
                onClick={openDashboard}
                className="notely-btn-secondary w-full notely-filter-transition hover:scale-105"
              >
                {t('dashboard.title')} (Local)
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-notely-surface px-4 py-3 border-t border-notely-border">
        <div className="flex justify-between items-center text-xs text-notely-text-tertiary">
          <span>v1.0.0</span>
          <button
            onClick={openSettings}
            className="hover:text-notely-text-secondary notely-filter-transition"
          >
            {t('settings.title')}
          </button>
        </div>
      </div>
    </div>
  );
};

const Popup: React.FC = () => {
  return (
    <LocaleProvider>
      <PopupContent />
    </LocaleProvider>
  );
};

// Render the popup
const container = document.getElementById('root');
if (container) {
  const root = ReactDOM.createRoot(container);
  root.render(<Popup />);
}
