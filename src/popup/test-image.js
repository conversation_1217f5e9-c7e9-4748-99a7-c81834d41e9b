document.addEventListener('DOMContentLoaded', () => {
  const imageUrlInput = document.getElementById('imageUrl');
  const fetchBtn = document.getElementById('fetchBtn');
  const resultDiv = document.getElementById('result');

  fetchBtn.addEventListener('click', async () => {
    const imageUrl = imageUrlInput.value.trim();
    if (!imageUrl) {
      showError('Please enter an image URL');
      return;
    }

    try {
      resultDiv.innerHTML = 'Fetching image...';
      
      // Send message to background script to fetch image
      const response = await chrome.runtime.sendMessage({
        action: 'FETCH_IMAGE',
        url: imageUrl
      });

      if (response.status === 'error') {
        throw new Error(response.message);
      }

      // Display the fetched image
      resultDiv.innerHTML = `
        <p>Image fetched successfully!</p>
        <img src="${response.dataUrl}" alt="Fetched image">
      `;
    } catch (error) {
      showError(`Error fetching image: ${error.message}`);
    }
  });

  function showError(message) {
    resultDiv.innerHTML = `<div class="error">${message}</div>`;
  }
});
