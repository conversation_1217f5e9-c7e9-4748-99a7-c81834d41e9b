<!DOCTYPE html>
<html>
<head>
  <title>Image Fetch Test</title>
  <style>
    body {
      width: 400px;
      padding: 20px;
      font-family: system-ui, -apple-system, sans-serif;
    }
    .result {
      margin-top: 20px;
      max-width: 100%;
    }
    .error {
      color: red;
      margin-top: 10px;
    }
    img {
      max-width: 100%;
      height: auto;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <h3>Image Fetch Test</h3>
  <input type="text" id="imageUrl" placeholder="Enter Instagram image URL" style="width: 100%">
  <button id="fetchBtn">Fetch Image</button>
  <div id="result" class="result"></div>

  <script src="test-image.js"></script>
</body>
</html>
