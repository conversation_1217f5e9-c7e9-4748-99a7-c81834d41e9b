import { defineManifest } from '@crxjs/vite-plugin';
import { ConfigEnv } from 'vite';
import packageJson from '../package.json';

const { version } = packageJson;

// Convert from Semver (example: 0.1.0-beta6)
const [major, minor, patch, label = '0'] = version
  .replace(/[^\d.-]/g, '')
  .split(/[.-]/);

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default defineManifest(async (_env: ConfigEnv) => ({
  manifest_version: 3,
  name: 'Notely Social',
  description: 'Save and organize social media posts and content',
  version: `${major}.${minor}.${patch}.${label}`,
  version_name: version,
  icons: {
    '16': 'icons/logo-16.png',
    '32': 'icons/logo-32.png',
    '48': 'icons/logo-48.png',
    '128': 'icons/logo-128.png'
  },
  action: {
    default_icon: {
      '16': 'icons/logo-16.png',
      '32': 'icons/logo-32.png',
      '48': 'icons/logo-48.png',
      '128': 'icons/logo-128.png'
    },
    default_title: 'Notely Social'
  },
  background: {
    service_worker: 'src/background.js',
    type: 'module'
  },
  content_scripts: [
    {
      matches: ['https://*.twitter.com/*', 'https://*.x.com/*'],
      js: ['src/content/content.js']
    },
    {
      matches: ['https://*.linkedin.com/*'],
      js: ['src/content/linkedin.js']
    },
    {
      matches: ['https://*.reddit.com/*'],
      js: ['src/content/reddit-content.ts']
    },
    {
      matches: ['https://*.facebook.com/*'],
      js: ['assets/content/facebook.js']
    },
    {
      matches: ['https://*.instagram.com/*'],
      js: ['assets/content/instagram.js']
    },
    {
      matches: ['https://*.pinterest.com/*', 'https://*.pinterest.ca/*', 'https://*.pinterest.co.uk/*'],
      js: ['assets/content/pinterest.js']
    }
  ],
  web_accessible_resources: [
    {
      resources: [
        'icons/logo-16.png',
        'icons/logo-32.png',
        'icons/logo-48.png',
        'icons/logo-128.png',
        'assets/injected/facebook.js',
        'assets/injected/instagram.js'
      ],
      matches: ['<all_urls>']
    }
  ],
  host_permissions: [
    'https://*.twitter.com/*',
    'https://*.x.com/*',
    'https://*.linkedin.com/*',
    'https://*.facebook.com/*',
    'https://*.instagram.com/*',
    'https://*.reddit.com/*',
    'https://*.pinterest.com/*',
    'https://*.pinterest.ca/*',
    'https://*.pinterest.co.uk/*',
    'http://*/*',
    'https://*/*'
  ],
  permissions: [
    'storage',
    'activeTab',
    'tabs',
    'identity',
    'contextMenus',
    'webRequest',
    'scripting'
  ],
  content_security_policy: {
    extension_pages: "script-src 'self' 'unsafe-inline'; object-src 'self'"
  }
}));