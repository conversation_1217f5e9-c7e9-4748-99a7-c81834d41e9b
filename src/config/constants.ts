export const CORE_CATEGORIES = {
  "news": ["world", "politics", "business", "science", "climate", "health"],
  "culture_lifestyle": ["entertainment", "music", "movies_tv", "fashion_style",
                        "food_drink", "travel", "arts_design", "books"],
  "tech_innovation": ["technology", "gaming_esports", "ai_data"],
  "sports": ["general_sports", "football_soccer", "basketball", "motorsport"],
  "life_wellness": ["fitness", "wellness_mentalHealth", "relationships"],
  "internet": ["memes", "pop_culture"],
  "education_career": ["learning", "career_productivity"],
  "misc": ["pets_animals", "parenting_family", "diy_crafting", "photography"]
} as const;

export type CoreCategoryMain = keyof typeof CORE_CATEGORIES;
export type CoreSubCategory<T extends CoreCategoryMain> = typeof CORE_CATEGORIES[T][number];

// General type for any sub-category slug
export type CoreSubCategorySlug = { 
  [K in CoreCategoryMain]: typeof CORE_CATEGORIES[K][number] 
}[CoreCategoryMain];

export const MAX_CATEGORIES = 3;
export const MAX_TAGS = 6;
export const MIN_POSTS_FOR_BIAS = 30;
export const BIAS_THRESHOLD_PERCENTAGE = 0.60; // 60%

// For GPT suggestions to core slugs
export const CATEGORY_SIMILARITY_THRESHOLD = 0.8;

// For user-typed categories matching existing slugs via cosine similarity
export const USER_TYPED_CATEGORY_SIMILARITY_THRESHOLD = 0.85;

// Default retry mechanism settings for API calls
export const DEFAULT_RETRY_ATTEMPTS = 3;
export const DEFAULT_RETRY_DELAY_MS = 1000; // 1 second
