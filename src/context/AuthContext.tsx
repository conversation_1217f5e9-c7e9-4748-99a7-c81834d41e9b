import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import * as authService from '../services/authService';
import * as apiService from '../services/apiService';

interface User {
  id: string;
  email: string;
  name: string;
  displayName?: string;
  role: 'user' | 'admin';
  plan: 'free' | 'premium';
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  storageUsed: number;
  storageLimit: number;
  adsDisabled: boolean;
  emailVerified: boolean;
  picture?: string;
  googleId?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  setUser: (user: User | null) => void;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  getToken: () => Promise<string | null>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<User | null>(null);

  const refreshUser = async (): Promise<void> => {
    try {
      const userData = await apiService.getUserProfile();
      setUser(userData);
    } catch (error) {
      console.error('Error refreshing user data:', error);
      // If we can't refresh, try to get from local storage
      const localUserData = await authService.getUser();
      setUser(localUserData);
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await authService.initAuth();
        const authenticated = await authService.isAuthenticated();
        setIsAuthenticated(authenticated);

        if (authenticated) {
          // Try to get fresh data from API first
          try {
            const userData = await apiService.getUserProfile();
            setUser(userData);
          } catch (error) {
            console.error('Failed to fetch user from API, falling back to local storage:', error);
            // Fallback to local storage if API fails
            const localUserData = await authService.getUser();
            setUser(localUserData);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (): Promise<void> => {
    setIsLoading(true);
    try {
      await authService.login();
      setIsAuthenticated(true);
      const userData = await apiService.getUserProfile();
      setUser(userData);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    try {
      await authService.logout();
      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getToken = async (): Promise<string | null> => {
    return await authService.getToken();
  };

  const value = {
    isAuthenticated,
    isLoading,
    user,
    setUser,
    login,
    logout,
    getToken,
    refreshUser
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};