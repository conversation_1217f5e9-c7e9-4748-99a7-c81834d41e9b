/**
 * Usage Tracking Service
 *
 * Tracks daily AI usage for free tier users and enforces limits.
 * Premium users have unlimited usage.
 */

export interface DailyUsage {
  date: string; // YYYY-MM-DD format
  aiCalls: number;
  lastReset: string; // ISO timestamp
}

const USAGE_STORAGE_KEY = 'aiUsageTracking';
const FREE_TIER_DAILY_LIMIT = 100; // Increased for testing

/**
 * Get today's date in YYYY-MM-DD format
 */
const getTodayString = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Get current usage data from storage
 */
export const getCurrentUsage = async (): Promise<DailyUsage> => {
  try {
    const result = await chrome.storage.local.get([USAGE_STORAGE_KEY]);
    const stored = result[USAGE_STORAGE_KEY];
    const today = getTodayString();

    // If no data or different date, reset usage
    if (!stored || stored.date !== today) {
      const newUsage: DailyUsage = {
        date: today,
        aiCalls: 0,
        lastReset: new Date().toISOString()
      };
      await chrome.storage.local.set({ [USAGE_STORAGE_KEY]: newUsage });
      return newUsage;
    }

    return stored;
  } catch (error) {
    console.error('Error getting current usage:', error);
    // Return default usage on error
    return {
      date: getTodayString(),
      aiCalls: 0,
      lastReset: new Date().toISOString()
    };
  }
};

/**
 * Increment AI call count
 */
export const incrementAIUsage = async (): Promise<DailyUsage> => {
  const currentUsage = await getCurrentUsage();
  const updatedUsage: DailyUsage = {
    ...currentUsage,
    aiCalls: currentUsage.aiCalls + 1
  };

  await chrome.storage.local.set({ [USAGE_STORAGE_KEY]: updatedUsage });
  return updatedUsage;
};

/**
 * Check if user can make AI calls based on their plan and current usage
 */
export const canMakeAICall = async (userPlan: 'free' | 'premium' | null): Promise<{
  canCall: boolean;
  reason?: string;
  remainingCalls?: number;
}> => {
  // Premium users have unlimited access
  if (userPlan === 'premium') {
    return { canCall: true };
  }

  // Logged out users are treated as free users with daily limits
  // Free users (logged in or out) have daily limits
  const usage = await getCurrentUsage();
  const remainingCalls = Math.max(0, FREE_TIER_DAILY_LIMIT - usage.aiCalls);

  if (usage.aiCalls >= FREE_TIER_DAILY_LIMIT) {
    return {
      canCall: false,
      reason: userPlan === null
        ? 'Daily AI limit reached. Log in and upgrade to Premium for unlimited access.'
        : 'Daily AI limit reached. Upgrade to Premium for unlimited access.',
      remainingCalls: 0
    };
  }

  return {
    canCall: true,
    remainingCalls
  };
};

/**
 * Get usage statistics for display
 */
export const getUsageStats = async (userPlan: 'free' | 'premium' | null): Promise<{
  used: number;
  limit: number | null; // null for unlimited
  remaining: number | null;
  resetTime: string;
}> => {
  if (userPlan === 'premium') {
    return {
      used: 0,
      limit: null,
      remaining: null,
      resetTime: ''
    };
  }

  // Logged out users and free users both have daily limits
  const usage = await getCurrentUsage();
  const remaining = Math.max(0, FREE_TIER_DAILY_LIMIT - usage.aiCalls);

  // Calculate next reset time (midnight)
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);

  return {
    used: usage.aiCalls,
    limit: FREE_TIER_DAILY_LIMIT,
    remaining,
    resetTime: tomorrow.toISOString()
  };
};

/**
 * Reset usage (for testing or manual reset)
 */
export const resetUsage = async (): Promise<void> => {
  const newUsage: DailyUsage = {
    date: getTodayString(),
    aiCalls: 0,
    lastReset: new Date().toISOString()
  };
  await chrome.storage.local.set({ [USAGE_STORAGE_KEY]: newUsage });
};

// Make resetUsage available globally for testing
if (typeof window !== 'undefined') {
  (window as any).resetAIUsage = resetUsage;
}
