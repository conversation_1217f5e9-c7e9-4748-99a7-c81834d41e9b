import { storage } from '../storage';
import { sanitizeCategoryName, generateUniqueName } from '../utils/sanitization';
import { validateCategoryName } from '../utils/categoryValidation';

export interface Category {
  id: string;
  name: string;
  sortOrder: number;
  postCount: number;
  isUserCreated: boolean;
}

export class CategoryService {
  private static instance: CategoryService;
  
  private constructor() {}
  
  static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService();
    }
    return CategoryService.instance;
  }

  async getAllCategories(): Promise<Category[]> {
    const categories = await storage.get('categories') || [];
    return categories.sort((a, b) => a.sortOrder - b.sortOrder);
  }

  async addCategory(name: string): Promise<Category | { error: string }> {
    const validation = validateCategoryName(name);
    if (!validation.isValid) {
      return { error: validation.error! };
    }

    const categories = await this.getAllCategories();
    const existingNames = categories.map(c => c.name);
    const uniqueName = generateUniqueName(name, existingNames);

    const newCategory: Category = {
      id: crypto.randomUUID(),
      name: uniqueName,
      sortOrder: categories.length,
      postCount: 0,
      isUserCreated: true
    };

    await storage.set('categories', [...categories, newCategory]);
    return newCategory;
  }

  async deleteCategory(categoryId: string): Promise<void> {
    const categories = await this.getAllCategories();
    const posts = await storage.get('posts') || [];
    
    // Remove category from all posts
    const updatedPosts = posts.map(post => ({
      ...post,
      categoryIds: post.categoryIds.filter(id => id !== categoryId)
    }));
    
    // Remove category and reorder remaining categories
    const updatedCategories = categories
      .filter(cat => cat.id !== categoryId)
      .map((cat, idx) => ({ ...cat, sortOrder: idx }));
    
    // Atomic update
    await Promise.all([
      storage.set('categories', updatedCategories),
      storage.set('posts', updatedPosts)
    ]);
  }

  async updateCategoryOrder(orderedIds: string[]): Promise<void> {
    const categories = await this.getAllCategories();
    const updatedCategories = categories.map(cat => ({
      ...cat,
      sortOrder: orderedIds.indexOf(cat.id)
    }));
    
    await storage.set('categories', updatedCategories);
  }

  async mergeCategories(sourceId: string, targetId: string): Promise<void> {
    const categories = await this.getAllCategories();
    const posts = await storage.get('posts') || [];
    
    // Update all posts with sourceId to use targetId
    const updatedPosts = posts.map(post => ({
      ...post,
      categoryIds: [...new Set([
        ...post.categoryIds.filter(id => id !== sourceId),
        ...(post.categoryIds.includes(sourceId) ? [targetId] : [])
      ])]
    }));
    
    // Remove source category and update target category count
    const updatedCategories = categories
      .filter(cat => cat.id !== sourceId)
      .map(cat => {
        if (cat.id === targetId) {
          return {
            ...cat,
            postCount: updatedPosts.filter(p => 
              p.categoryIds.includes(targetId)
            ).length
          };
        }
        return cat;
      });
    
    // Atomic update
    await Promise.all([
      storage.set('categories', updatedCategories),
      storage.set('posts', updatedPosts)
    ]);
  }
} 