import { ContentConversionStats } from '../types/contentSuggestions';

const CONVERSION_STORAGE_KEY = 'content_conversions';

/**
 * Service to track content conversions (bookmarks turned into generated content)
 */

/**
 * Get current content conversion statistics
 */
export const getContentConversionStats = async (): Promise<ContentConversionStats> => {
  try {
    const result = await chrome.storage.local.get([CONVERSION_STORAGE_KEY]);
    const stored = result[CONVERSION_STORAGE_KEY];

    if (!stored) {
      return {
        totalConversions: 0,
        weeklyConversions: 0,
        conversionsByType: {},
        lastConversionDate: undefined
      };
    }

    // Calculate weekly conversions from stored data
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const weeklyConversions = stored.conversions?.filter((conversion: any) => {
      const conversionDate = new Date(conversion.date);
      return conversionDate >= oneWeekAgo;
    }).length || 0;

    // Count conversions by type
    const conversionsByType: Record<string, number> = {};
    stored.conversions?.forEach((conversion: any) => {
      conversionsByType[conversion.type] = (conversionsByType[conversion.type] || 0) + 1;
    });

    return {
      totalConversions: stored.conversions?.length || 0,
      weeklyConversions,
      conversionsByType,
      lastConversionDate: stored.conversions?.length > 0 
        ? new Date(stored.conversions[stored.conversions.length - 1].date)
        : undefined
    };
  } catch (error) {
    console.error('Error getting content conversion stats:', error);
    return {
      totalConversions: 0,
      weeklyConversions: 0,
      conversionsByType: {},
      lastConversionDate: undefined
    };
  }
};

/**
 * Track a new content conversion
 */
export const trackContentConversion = async (
  type: 'thread' | 'caption' | 'newsletter' | 'quote' | 'poll' | 'story',
  originalPostId: string,
  generatedContent: string
): Promise<void> => {
  try {
    const result = await chrome.storage.local.get([CONVERSION_STORAGE_KEY]);
    const stored = result[CONVERSION_STORAGE_KEY] || { conversions: [] };

    const newConversion = {
      id: `${originalPostId}-${type}-${Date.now()}`,
      type,
      originalPostId,
      generatedContent: generatedContent.substring(0, 200), // Store first 200 chars
      date: new Date().toISOString(),
      timestamp: Date.now()
    };

    stored.conversions.push(newConversion);

    // Keep only last 100 conversions to prevent storage bloat
    if (stored.conversions.length > 100) {
      stored.conversions = stored.conversions.slice(-100);
    }

    await chrome.storage.local.set({ [CONVERSION_STORAGE_KEY]: stored });
    console.log(`[ContentConversion] Tracked ${type} conversion for post ${originalPostId}`);
  } catch (error) {
    console.error('Error tracking content conversion:', error);
  }
};

/**
 * Get conversion trends for analytics
 */
export const getConversionTrends = async (): Promise<{
  dailyTrend: number[];
  popularTypes: Array<{ type: string; count: number }>;
  weekOverWeekGrowth: number;
}> => {
  try {
    const result = await chrome.storage.local.get([CONVERSION_STORAGE_KEY]);
    const stored = result[CONVERSION_STORAGE_KEY];

    if (!stored?.conversions) {
      return {
        dailyTrend: [],
        popularTypes: [],
        weekOverWeekGrowth: 0
      };
    }

    const conversions = stored.conversions;
    const now = new Date();

    // Calculate daily trend for last 7 days
    const dailyTrend: number[] = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);
      
      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      const dayCount = conversions.filter((conv: any) => {
        const convDate = new Date(conv.date);
        return convDate >= date && convDate < nextDate;
      }).length;

      dailyTrend.push(dayCount);
    }

    // Calculate popular types
    const typeCounts: Record<string, number> = {};
    conversions.forEach((conv: any) => {
      typeCounts[conv.type] = (typeCounts[conv.type] || 0) + 1;
    });

    const popularTypes = Object.entries(typeCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);

    // Calculate week-over-week growth
    const thisWeek = new Date();
    thisWeek.setDate(thisWeek.getDate() - 7);
    
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 14);

    const thisWeekCount = conversions.filter((conv: any) => {
      const convDate = new Date(conv.date);
      return convDate >= thisWeek;
    }).length;

    const lastWeekCount = conversions.filter((conv: any) => {
      const convDate = new Date(conv.date);
      return convDate >= lastWeek && convDate < thisWeek;
    }).length;

    const weekOverWeekGrowth = lastWeekCount > 0 
      ? Math.round(((thisWeekCount - lastWeekCount) / lastWeekCount) * 100)
      : thisWeekCount > 0 ? 100 : 0;

    return {
      dailyTrend,
      popularTypes,
      weekOverWeekGrowth
    };
  } catch (error) {
    console.error('Error getting conversion trends:', error);
    return {
      dailyTrend: [],
      popularTypes: [],
      weekOverWeekGrowth: 0
    };
  }
};

/**
 * Clear old conversion data (for maintenance)
 */
export const clearOldConversions = async (daysToKeep: number = 30): Promise<void> => {
  try {
    const result = await chrome.storage.local.get([CONVERSION_STORAGE_KEY]);
    const stored = result[CONVERSION_STORAGE_KEY];

    if (!stored?.conversions) {
      return;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const filteredConversions = stored.conversions.filter((conv: any) => {
      const convDate = new Date(conv.date);
      return convDate >= cutoffDate;
    });

    stored.conversions = filteredConversions;
    await chrome.storage.local.set({ [CONVERSION_STORAGE_KEY]: stored });
    
    console.log(`[ContentConversion] Cleared old conversions, kept ${filteredConversions.length} recent ones`);
  } catch (error) {
    console.error('Error clearing old conversions:', error);
  }
};

/**
 * Get most successful content types based on user behavior
 */
export const getMostSuccessfulContentTypes = async (): Promise<Array<{
  type: string;
  count: number;
  successRate: number;
  avgLength: number;
}>> => {
  try {
    const result = await chrome.storage.local.get([CONVERSION_STORAGE_KEY]);
    const stored = result[CONVERSION_STORAGE_KEY];

    if (!stored?.conversions) {
      return [];
    }

    const typeStats: Record<string, {
      count: number;
      totalLength: number;
      conversions: any[];
    }> = {};

    stored.conversions.forEach((conv: any) => {
      if (!typeStats[conv.type]) {
        typeStats[conv.type] = {
          count: 0,
          totalLength: 0,
          conversions: []
        };
      }
      
      typeStats[conv.type].count++;
      typeStats[conv.type].totalLength += conv.generatedContent?.length || 0;
      typeStats[conv.type].conversions.push(conv);
    });

    return Object.entries(typeStats)
      .map(([type, stats]) => ({
        type,
        count: stats.count,
        successRate: stats.count > 0 ? 100 : 0, // Simplified success rate
        avgLength: stats.count > 0 ? Math.round(stats.totalLength / stats.count) : 0
      }))
      .sort((a, b) => b.count - a.count);
  } catch (error) {
    console.error('Error getting successful content types:', error);
    return [];
  }
};
