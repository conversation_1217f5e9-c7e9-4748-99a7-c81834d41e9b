import {
  CoreSubCategorySlug,
  DEFAULT_RETRY_ATTEMPTS,
  DEFAULT_RETRY_DELAY_MS,
  MAX_CATEGORIES,
  MAX_TAGS,
} from '../config/constants';

// --- Environment Variables ---
// IMPORTANT: The user must set this environment variable.
// For Vite, this would be in a .env file: VITE_OPENAI_API_KEY=your_key_here
// For Chrome extensions, this needs to be managed carefully, potentially fetched from a secure server
// or configured by the user in options. For now, we'll assume it's available via an env-like system.
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY || process.env.OPENAI_API_KEY;
console.log('[analysisService] VITE_OPENAI_API_KEY at definition:', import.meta.env.VITE_OPENAI_API_KEY);
console.log('[analysisService] OPENAI_API_KEY resolved to:', OPENAI_API_KEY);
const GPT4O_TURBO_MODEL = 'gpt-4o'; // GPT-4o-Turbo is usually aliased as gpt-4o, but confirm latest model ID
const TEXT_EMBEDDING_3_SMALL_MODEL = 'text-embedding-3-small';
const OPENAI_API_URL = 'https://api.openai.com/v1';

// --- Types ---
interface Gpt4oVisionRequest {
  text?: string;
  imageUrl?: string;
  coreSubCategorySlugs: CoreSubCategorySlug[];
}

export interface Gpt4oVisionResponse {
  snapNote: string; // Now always required
  categories: CoreSubCategorySlug[]; // Max 3, from coreSubCategorySlugs
  tags: string[]; // Max 6, not overlapping with categories
}


export interface EmbeddingResponse {
  embedding: number[];
}

interface OpenAIError {
  error: {
    message: string;
    type: string;
    param: string | null;
    code: string | null;
  };
}

// --- Helper: Fetch with Retry ---
async function fetchWithRetry<T>(
  url: string,
  options: RequestInit,
  retries = DEFAULT_RETRY_ATTEMPTS,
  delayMs = DEFAULT_RETRY_DELAY_MS
): Promise<T> {
  if (!OPENAI_API_KEY) {
    throw new Error('OpenAI API key is not configured.');
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${OPENAI_API_KEY}`,
    ...(options.headers || {}),
  };

  try {
    const response = await fetch(url, { ...options, headers });

    if (response.status === 429) { // Rate limit or quota exceeded
      if (retries > 0) {
        const retryAfterHeader = response.headers.get('retry-after');
        let waitTime = delayMs;
        if (retryAfterHeader) {
          const retryAfterSeconds = parseInt(retryAfterHeader, 10);
          if (!isNaN(retryAfterSeconds)) {
            waitTime = retryAfterSeconds * 1000;
          }
        }
        console.warn(`OpenAI API: Rate limited (429). Retrying in ${waitTime / 1000}s... (${retries} retries left)`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        return fetchWithRetry(url, options, retries - 1, delayMs * 2); // Exponential backoff for subsequent retries
      }
      const errorData = await response.json() as OpenAIError;
      throw new Error(`OpenAI API Error (429 - Too Many Requests): ${(errorData.error && errorData.error.message) || 'Rate limit exceeded after multiple retries.'}`);
    }

    if (!response.ok) {
      const errorData = await response.json() as OpenAIError;
      console.error('OpenAI API Error Response:', errorData);
      throw new Error(`OpenAI API Error (${response.status}): ${(errorData.error && errorData.error.message) || response.statusText}`);
    }
    return await response.json() as T;
  } catch (error: unknown) {
    if (error instanceof Error && error.message.startsWith('OpenAI API Error')) throw error; // Already an API error
    console.error('Network or other error calling OpenAI API:', error instanceof Error ? error.message : error);
    if (retries > 0) {
      console.warn(`Retrying in ${delayMs / 1000}s... (${retries} retries left)`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
      return fetchWithRetry(url, options, retries - 1, delayMs * 2);
    }
    throw new Error(`Failed to fetch from OpenAI API after multiple retries: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// --- API Functions ---

/**
 * Calls GPT-4o (Vision-enabled) to analyze text and/or an image.
 * Generates a snapNote, suggests categories, and tags.
 */
export async function callGpt4oVision(
  request: Gpt4oVisionRequest
): Promise<Gpt4oVisionResponse> {
  console.log('[analysisService] Inside callGpt4oVision. OPENAI_API_KEY:', OPENAI_API_KEY);
  const { text, imageUrl, coreSubCategorySlugs } = request;

  if (!text && !imageUrl) {
    throw new Error('Either text or imageUrl must be provided to callGpt4oVision.');
  }

  const messages: any[] = [
    {
      role: 'system',
      content: `You are an expert content analyzer. Your task is to analyze the provided text and/or image.
      1. Generate a concise 'snapNote' (a micro-caption, 1-2 sentences) for the content. The snapNote is ALWAYS required and should capture the essence of the content in a way that's useful for quick reference and organization. Maximum 150 characters.
      2. Suggest up to ${MAX_CATEGORIES} relevant categories for the content from the following list of valid category slugs: [${coreSubCategorySlugs.join(', ')}]. Choose only from this list. Return the category slugs themselves, not the main categories.
      3. Suggest up to ${MAX_TAGS} relevant tags (keywords or short phrases, 1-3 words each) for the content. Tags should be distinct from the chosen categories and from each other. Tags should be lowercase.
      Respond ONLY with a JSON object with the following structure: { "snapNote": "string", "categories": ["slug1", "slug2", ...], "tags": ["tag1", "tag2", ...] }. Do not include any other text, explanations, or markdown formatting around the JSON.`
    },
    {
      role: 'user',
      content: []
    }
  ];

  const userContent: any[] = [];
  if (text) {
    userContent.push({ type: 'text', text: text });
  }
  if (imageUrl) {
    if (!imageUrl.startsWith('data:image/')) {
        console.warn('Image URL does not seem to be a data URL. Ensure it is accessible by OpenAI. For local files, convert to data URL first.');
    }
    userContent.push({ type: 'image_url', image_url: { url: imageUrl, detail: 'low' } }); // Use 'low' detail for cost/speed unless high detail is critical
  }
  messages[1].content = userContent;

  const body = {
    model: GPT4O_TURBO_MODEL,
    messages: messages,
    max_tokens: 300, // Adjust as needed, but keep it reasonable for structured JSON output
    temperature: 0.3, // Lower temperature for more deterministic category/tag selection
    response_format: { type: 'json_object' },
  };

  console.log('[analysisService] Calling GPT-4o Vision with request:', JSON.stringify(body, null, 2));

  try {
    const response = await fetchWithRetry<any>(`${OPENAI_API_URL}/chat/completions`, {
      method: 'POST',
      body: JSON.stringify(body),
    });

    const assistantResponse = response.choices[0]?.message?.content;
    if (!assistantResponse) {
      throw new Error('No content in assistant response from GPT-4o.');
    }

    console.log('[analysisService] Raw GPT-4o response string:', assistantResponse);
    let parsedResponse: Gpt4oVisionResponse;
    try {
        parsedResponse = JSON.parse(assistantResponse);
    } catch (e) {
        console.error("[analysisService] Failed to parse GPT-4o JSON response:", assistantResponse, e);
        throw new Error('Failed to parse JSON response from GPT-4o. Response was not valid JSON.');
    }
    

    // Validate and sanitize the response
    const finalSnapNote = typeof parsedResponse.snapNote === 'string' && parsedResponse.snapNote.trim() 
      ? parsedResponse.snapNote.trim() 
      : 'Content saved for reference'; // Fallback if snapNote is missing or empty
    
    // Log if we had to use fallback
    if (!parsedResponse.snapNote || typeof parsedResponse.snapNote !== 'string' || !parsedResponse.snapNote.trim()) {
      console.warn('[analysisService] GPT-4o did not provide a valid snapNote, using fallback');
    }

    const finalCategories = Array.isArray(parsedResponse.categories) 
      ? parsedResponse.categories
          .filter(cat => typeof cat === 'string' && coreSubCategorySlugs.includes(cat as CoreSubCategorySlug))
          .slice(0, MAX_CATEGORIES) 
      : [];

    const finalTags = Array.isArray(parsedResponse.tags)
      ? [...new Set(parsedResponse.tags
          .filter(tag => typeof tag === 'string' && tag.trim().length > 0)
          .map(tag => tag.toLowerCase().trim()))]
          .filter(tag => !finalCategories.includes(tag as CoreSubCategorySlug)) // Ensure tags don't overlap with final chosen categories
          .slice(0, MAX_TAGS)
      : [];

    return {
      snapNote: finalSnapNote,
      categories: finalCategories as CoreSubCategorySlug[],
      tags: finalTags,
    };

  } catch (error: unknown) {
    console.error('[analysisService] Error in callGpt4oVision:', error instanceof Error ? error.message : error);
    // Fallback or re-throw. For now, re-throw to be handled by the caller.
    // Consider returning a default error structure if needed by the application flow.
    throw error;
  }
}

/**
 * Generates an embedding for the given text using text-embedding-3-small.
 */
export async function embedText(text: string): Promise<EmbeddingResponse> {
  if (!text || typeof text !== 'string' || text.trim().length === 0) {
    throw new Error('Text must be a non-empty string to generate embeddings.');
  }

  const body = {
    input: text.trim(),
    model: TEXT_EMBEDDING_3_SMALL_MODEL,
    // dimensions: 256, // Optional: Can specify dimensions for text-embedding-3-small (e.g., 256, 512, 1024, 1536 default)
                    // Choose based on your vector DB and performance needs. Smaller is faster & cheaper.
  };

  console.log('[analysisService] Calling OpenAI Embedding API...');

  try {
    const response = await fetchWithRetry<any>(`${OPENAI_API_URL}/embeddings`, {
      method: 'POST',
      body: JSON.stringify(body),
    });

    if (!response.data || !response.data[0] || !response.data[0].embedding) {
      console.error('Invalid embedding response structure:', response);
      throw new Error('Invalid embedding response from OpenAI.');
    }

    return { embedding: response.data[0].embedding };
  } catch (error: unknown) {
    console.error('[analysisService] Error in embedText:', error instanceof Error ? error.message : error);
    throw error; // Re-throw to be handled by the caller
  }
}

// TODO: Add unit tests for fetchWithRetry, callGpt4oVision, and embedText (likely requiring mocks for fetch).

// --- NEW ORCHESTRATION FUNCTION ---
import {
  generateAIInsight,
  generateAIFastTake,
  generateAIContentIdeas,
  InSightData
} from './aiService'; // Ensure correct path if different
import type { Post } from '../types'; // Ensure correct path for types

export interface AIEnrichmentData {
  snapNote: string | null;
  categories: CoreSubCategorySlug[];
  tags: string[];
  inSight: InSightData | null;
  fastTake: string | null;
  contentIdeas: string[] | null;
  analyzedAt: string; 
}

/**
 * Orchestrates the AI enrichment process for a given post.
 * Calls various AI services to generate snapNote, categories, tags, insight, fastTake, and contentIdeas.
 * Handles errors from individual AI calls gracefully.
 */
export async function enrichPostWithAI(
  post: Post,
  availableCoreSubCategorySlugs: CoreSubCategorySlug[]
): Promise<Partial<AIEnrichmentData>> {
  const aiData: Partial<AIEnrichmentData> = {
    snapNote: null,
    categories: post.categories as CoreSubCategorySlug[] || [], // Start with existing or empty
    tags: post.tags || [], // Start with existing or empty
    inSight: null,
    fastTake: null,
    contentIdeas: [],
  };

  const postTextContent = post.content || post.textContent || '';
  const postImageUrl = post.media?.find(m => m.type === 'image')?.url || post.savedImage; // Prefer media URL, fallback to savedImage

  // 1. Get SnapNote, Categories, and Tags (potentially using vision)
  try {
    console.log(`[analysisService] enrichPostWithAI: Calling callGpt4oVision for post ID: ${post.id}`);
    const visionResponse = await callGpt4oVision({
      text: postTextContent,
      imageUrl: postImageUrl, // Pass undefined if no image, callGpt4oVision handles it
      coreSubCategorySlugs: availableCoreSubCategorySlugs,
    });
    aiData.snapNote = visionResponse.snapNote;
    aiData.categories = visionResponse.categories;
    aiData.tags = visionResponse.tags;
    console.log(`[analysisService] enrichPostWithAI: callGpt4oVision successful for post ID: ${post.id}`, visionResponse);
  } catch (error: unknown) {
    console.error(`[analysisService] enrichPostWithAI: Error calling callGpt4oVision for post ID ${post.id}:`, error instanceof Error ? error.message : error);
    // Continue with other enrichments even if this fails
    // Categories and tags will remain as they were from the original post or empty
  }

  // 2. Get AI Insight (text-based)
  if (postTextContent) {
    try {
      console.log(`[analysisService] enrichPostWithAI: Calling generateAIInsight for post ID: ${post.id}`);
      aiData.inSight = await generateAIInsight(postTextContent);
      console.log(`[analysisService] enrichPostWithAI: generateAIInsight successful for post ID: ${post.id}`, aiData.inSight);
    } catch (error: unknown) {
      console.error(`[analysisService] enrichPostWithAI: Error calling generateAIInsight for post ID ${post.id}:`, error instanceof Error ? error.message : error);
    }
  }

  // 3. Get AI Fast Take (text-based)
  if (postTextContent) {
    try {
      console.log(`[analysisService] enrichPostWithAI: Calling generateAIFastTake for post ID: ${post.id}`);
      aiData.fastTake = await generateAIFastTake(postTextContent);
      console.log(`[analysisService] enrichPostWithAI: generateAIFastTake successful for post ID: ${post.id}`, aiData.fastTake);
    } catch (error: unknown) {
      console.error(`[analysisService] enrichPostWithAI: Error calling generateAIFastTake for post ID ${post.id}:`, error instanceof Error ? error.message : error);
    }
  }

  // 4. Get AI Content Ideas (text-based)
  if (postTextContent) {
    try {
      console.log(`[analysisService] enrichPostWithAI: Calling generateAIContentIdeas for post ID: ${post.id}`);
      aiData.contentIdeas = await generateAIContentIdeas(postTextContent);
      console.log(`[analysisService] enrichPostWithAI: generateAIContentIdeas successful for post ID: ${post.id}`, aiData.contentIdeas);
    } catch (error: unknown) {
      console.error(`[analysisService] enrichPostWithAI: Error calling generateAIContentIdeas for post ID ${post.id}:`, error instanceof Error ? error.message : error);
    }
  }
  
  aiData.analyzedAt = new Date().toISOString();

  console.log(`[analysisService] enrichPostWithAI: Final AI data for post ID ${post.id}:`, JSON.parse(JSON.stringify(aiData)));
  return aiData;
}
