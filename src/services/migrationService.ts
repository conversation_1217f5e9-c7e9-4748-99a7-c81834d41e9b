/**
 * Migration Service
 * Handles migration from fragmented storage to unified storage system
 */

import { unifiedStorage, STORAGE_KEYS } from './unifiedStorageService';
import { categoryStorage } from './categoryStorageManager';
import { chatStorage } from './chatStorageManager';
import { bookmarkStorage } from './bookmarkStorageManager';

const MIGRATION_VERSION_KEY = 'migrationVersion';
const CURRENT_MIGRATION_VERSION = 1;

export class MigrationService {
  private static instance: MigrationService;
  
  private constructor() {}
  
  static getInstance(): MigrationService {
    if (!MigrationService.instance) {
      MigrationService.instance = new MigrationService();
    }
    return MigrationService.instance;
  }

  /**
   * Run all necessary migrations
   */
  async runMigrations(): Promise<void> {
    try {
      console.log('[MigrationService] Starting data migration process...');
      
      const currentVersion = await unifiedStorage.get<number>(MIGRATION_VERSION_KEY) || 0;
      
      if (currentVersion >= CURRENT_MIGRATION_VERSION) {
        console.log('[MigrationService] No migration needed, already at latest version');
        return;
      }

      // Run migrations in sequence
      if (currentVersion < 1) {
        await this.migrateToVersion1();
      }

      // Update migration version
      await unifiedStorage.set(MIGRATION_VERSION_KEY, CURRENT_MIGRATION_VERSION);
      console.log('[MigrationService] Migration completed successfully');
    } catch (error) {
      console.error('[MigrationService] Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migration to version 1: Unify all storage systems
   */
  private async migrateToVersion1(): Promise<void> {
    console.log('[MigrationService] Running migration to version 1...');

    try {
      // 1. Migrate categories
      await this.migrateCategories();
      
      // 2. Migrate chat conversations
      await this.migrateChats();
      
      // 3. Migrate bookmarks (already using chrome.storage.local, but ensure consistency)
      await this.migrateBookmarks();
      
      // 4. Clean up old storage
      await this.cleanupOldStorage();
      
      console.log('[MigrationService] Version 1 migration completed');
    } catch (error) {
      console.error('[MigrationService] Version 1 migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate category data from multiple sources
   */
  private async migrateCategories(): Promise<void> {
    console.log('[MigrationService] Migrating categories...');

    try {
      // Migrate from chrome.storage.sync to chrome.storage.local
      if (typeof chrome !== 'undefined' && chrome.storage) {
        try {
          const syncResult = await chrome.storage.sync.get(['userCategories']);
          if (syncResult.userCategories) {
            await unifiedStorage.set(STORAGE_KEYS.USER_CATEGORIES, syncResult.userCategories);
            await chrome.storage.sync.remove(['userCategories']);
            console.log('[MigrationService] Migrated userCategories from sync to local storage');
          }
        } catch (error) {
          console.warn('[MigrationService] Could not migrate from sync storage:', error);
        }
      }

      // Ensure bookmark categories are properly formatted
      const bookmarkCategories = await unifiedStorage.get(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES);
      if (bookmarkCategories && Array.isArray(bookmarkCategories)) {
        // Validate and clean up bookmark categories
        const cleanedCategories = bookmarkCategories.filter(cat => 
          cat && typeof cat === 'object' && cat.name && cat.emoji && Array.isArray(cat.tags)
        );
        
        if (cleanedCategories.length !== bookmarkCategories.length) {
          await unifiedStorage.set(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES, cleanedCategories);
          console.log('[MigrationService] Cleaned up bookmark categories');
        }
      }

      console.log('[MigrationService] Category migration completed');
    } catch (error) {
      console.error('[MigrationService] Category migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate chat conversations from localStorage to unified storage
   */
  private async migrateChats(): Promise<void> {
    console.log('[MigrationService] Migrating chat conversations...');

    try {
      const migratedConversations = [];
      const keysToRemove = [];

      // Find all localStorage keys that look like category chats
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('category-chat-')) {
          try {
            const data = localStorage.getItem(key);
            if (data) {
              const conversations = JSON.parse(data);
              if (Array.isArray(conversations)) {
                // Extract category name from key
                const category = key.replace('category-chat-', '').replace(/-/g, ' ');
                const categoryName = category.charAt(0).toUpperCase() + category.slice(1);

                // Add category to each conversation
                const categorizedConversations = conversations.map(conv => ({
                  ...conv,
                  category: categoryName,
                  createdAt: conv.createdAt || new Date(),
                  updatedAt: conv.updatedAt || new Date()
                }));

                migratedConversations.push(...categorizedConversations);
                keysToRemove.push(key);
              }
            }
          } catch (error) {
            console.warn(`[MigrationService] Could not migrate chat data from ${key}:`, error);
          }
        }
      }

      // Save migrated conversations to unified storage
      if (migratedConversations.length > 0) {
        const existingConversations = await unifiedStorage.get(STORAGE_KEYS.CHAT_CONVERSATIONS) || [];
        const allConversations = [...migratedConversations, ...existingConversations];
        
        // Remove duplicates based on ID
        const uniqueConversations = allConversations.filter((conv, index, arr) => 
          arr.findIndex(c => c.id === conv.id) === index
        );

        await unifiedStorage.set(STORAGE_KEYS.CHAT_CONVERSATIONS, uniqueConversations);
        console.log(`[MigrationService] Migrated ${migratedConversations.length} chat conversations`);
      }

      // Remove old localStorage entries
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      console.log('[MigrationService] Chat migration completed');
    } catch (error) {
      console.error('[MigrationService] Chat migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate and validate bookmark data
   */
  private async migrateBookmarks(): Promise<void> {
    console.log('[MigrationService] Migrating bookmarks...');

    try {
      // Bookmarks are already using chrome.storage.local, but let's validate the data
      const manualBookmarks = await unifiedStorage.get(STORAGE_KEYS.MANUAL_BOOKMARKS) || [];
      const processedPosts = await unifiedStorage.get(STORAGE_KEYS.PROCESSED_POSTS) || [];

      // Validate bookmark structure
      if (Array.isArray(manualBookmarks)) {
        const validatedBookmarks = manualBookmarks.filter(bookmark => 
          bookmark && 
          typeof bookmark === 'object' && 
          bookmark.id && 
          bookmark.url && 
          bookmark.title
        ).map(bookmark => ({
          ...bookmark,
          createdAt: bookmark.createdAt || new Date(),
          updatedAt: bookmark.updatedAt || new Date(),
          isManual: bookmark.isManual !== undefined ? bookmark.isManual : true
        }));

        if (validatedBookmarks.length !== manualBookmarks.length) {
          await unifiedStorage.set(STORAGE_KEYS.MANUAL_BOOKMARKS, validatedBookmarks);
          console.log('[MigrationService] Validated and cleaned bookmark data');
        }
      }

      // Validate processed posts
      if (Array.isArray(processedPosts)) {
        const validatedProcessedPosts = processedPosts.filter(id => 
          typeof id === 'string' && id.trim() !== ''
        );

        if (validatedProcessedPosts.length !== processedPosts.length) {
          await unifiedStorage.set(STORAGE_KEYS.PROCESSED_POSTS, validatedProcessedPosts);
          console.log('[MigrationService] Validated processed posts data');
        }
      }

      console.log('[MigrationService] Bookmark migration completed');
    } catch (error) {
      console.error('[MigrationService] Bookmark migration failed:', error);
      throw error;
    }
  }

  /**
   * Clean up old storage entries that are no longer needed
   */
  private async cleanupOldStorage(): Promise<void> {
    console.log('[MigrationService] Cleaning up old storage...');

    try {
      // Clean up localStorage entries that have been migrated
      const localStorageKeysToRemove = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.startsWith('category-chat-') ||
          key.startsWith('migrated-') ||
          key === 'oldUserCategories'
        )) {
          localStorageKeysToRemove.push(key);
        }
      }

      localStorageKeysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      if (localStorageKeysToRemove.length > 0) {
        console.log(`[MigrationService] Removed ${localStorageKeysToRemove.length} old localStorage entries`);
      }

      // Clean up chrome.storage.sync entries that have been migrated
      if (typeof chrome !== 'undefined' && chrome.storage) {
        try {
          const syncKeys = ['userCategories', 'oldUserTags', 'migrationFlags'];
          await chrome.storage.sync.remove(syncKeys);
          console.log('[MigrationService] Cleaned up old sync storage entries');
        } catch (error) {
          console.warn('[MigrationService] Could not clean up sync storage:', error);
        }
      }

      console.log('[MigrationService] Storage cleanup completed');
    } catch (error) {
      console.error('[MigrationService] Storage cleanup failed:', error);
      // Don't throw here - cleanup failure shouldn't break the migration
    }
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<{
    currentVersion: number;
    latestVersion: number;
    needsMigration: boolean;
    storageInfo: any;
  }> {
    try {
      const currentVersion = await unifiedStorage.get<number>(MIGRATION_VERSION_KEY) || 0;
      const storageInfo = await unifiedStorage.getStorageInfo();

      return {
        currentVersion,
        latestVersion: CURRENT_MIGRATION_VERSION,
        needsMigration: currentVersion < CURRENT_MIGRATION_VERSION,
        storageInfo
      };
    } catch (error) {
      console.error('[MigrationService] Error getting migration status:', error);
      return {
        currentVersion: 0,
        latestVersion: CURRENT_MIGRATION_VERSION,
        needsMigration: true,
        storageInfo: null
      };
    }
  }

  /**
   * Force re-run migration (for debugging)
   */
  async forceMigration(): Promise<void> {
    console.log('[MigrationService] Forcing migration re-run...');
    await unifiedStorage.set(MIGRATION_VERSION_KEY, 0);
    await this.runMigrations();
  }
}

// Export singleton instance
export const migrationService = MigrationService.getInstance();
