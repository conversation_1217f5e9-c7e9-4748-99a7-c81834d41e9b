import { Post } from '../types';
import { WisdomQuote } from '../types/wisdom';
import { getSavedPosts } from '../storage';
import { getOpenAIApiKey } from './aiService';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for extracting wisdom quotes from user's saved posts
 */

interface ExtractedWisdom {
  text: string;
  author: string;
  source_link: string;
  tags: string[];
  originalPost: Post;
}

/**
 * Extracts potential wisdom quotes from saved posts
 * Looks for posts with meaningful content that can be turned into wisdom
 */
export async function extractWisdomFromPosts(maxQuotes: number = 5): Promise<WisdomQuote[]> {
  try {
    const savedPosts = await getSavedPosts();

    if (savedPosts.length === 0) {
      return [];
    }

    // Filter posts that are suitable for wisdom extraction
    const suitablePosts = savedPosts.filter(post => {
      const content = post.content || post.textContent || post.text || '';
      const hasContent = content.length > 10; // More permissive length requirement
      const hasAuthor = post.author || post.authorName || 'Unknown Author';
      // Remove time restriction - allow all posts regardless of age
      
      console.log('wisdomExtraction: Checking post:', {
        id: post.id,
        platform: post.platform,
        contentLength: content.length,
        hasAuthor: !!hasAuthor,
        content: content.substring(0, 100) + '...'
      });
      
      return hasContent; // Much more permissive - just needs some content
    });

    if (suitablePosts.length === 0) {
      return [];
    }

    // Sort by engagement and recency
    const sortedPosts = suitablePosts.sort((a, b) => {
      const aEngagement = (a.stats?.likes || 0) + (a.stats?.shares || 0) + (a.stats?.comments || 0);
      const bEngagement = (b.stats?.likes || 0) + (b.stats?.shares || 0) + (b.stats?.comments || 0);
      const aDate = new Date(a.savedAt || a.timestamp || 0).getTime();
      const bDate = new Date(b.savedAt || b.timestamp || 0).getTime();
      
      // Combine engagement and recency scores
      const aScore = aEngagement * 0.7 + (aDate / 1000000000) * 0.3;
      const bScore = bEngagement * 0.7 + (bDate / 1000000000) * 0.3;
      
      return bScore - aScore;
    });

    // Extract wisdom from top posts
    const extractedWisdom: WisdomQuote[] = [];
    const targetCount = Math.min(maxQuotes, sortedPosts.length);

    for (let i = 0; i < targetCount; i++) {
      const post = sortedPosts[i];
      const wisdom = await extractWisdomFromPost(post);
      
      if (wisdom) {
        extractedWisdom.push(wisdom);
      }
    }

    return extractedWisdom;

  } catch (error) {
    console.error('wisdomExtraction: Error extracting wisdom from posts:', error);
    return [];
  }
}

/**
 * Extracts a wisdom quote from a single post
 */
async function extractWisdomFromPost(post: Post): Promise<WisdomQuote | null> {
  try {
    const content = post.content || post.textContent || post.text || '';

    console.log('wisdomExtraction: Processing post:', {
      id: post.id,
      originalPostId: post.originalPostId,
      platform: post.platform,
      author: post.author,
      permalink: post.permalink,
      source_link: post.source_link,
      contentLength: content.length
    });

    if (!content || content.length < 5) {
      console.log('wisdomExtraction: Skipping post - insufficient content');
      return null;
    }

    // Transform content into actual wisdom
    const wisdomText = await transformContentToWisdom(content);

    if (!wisdomText) {
      return null;
    }

    // Determine author
    let author = 'From Post';
    if (post.author && post.author !== 'Unknown') {
      author = post.author;
    } else if (post.authorName && post.authorName !== 'Unknown') {
      author = post.authorName;
    }

    // Generate tags based on post categories and content
    const tags = generateWisdomTags(post, wisdomText);

    // Get the best link to the original post
    const sourceLink = post.permalink || post.source_link || '';

    // Store all possible IDs for better matching later
    const relatedIds = [
      post.id,
      post.originalPostId,
      // Extract ID from permalink if possible
      ...(post.permalink ? [post.permalink.split('/').pop()] : []),
      // Extract ID from source_link if possible
      ...(post.source_link ? [post.source_link.split('/').pop()] : [])
    ].filter(Boolean); // Remove any undefined/null/empty values

    // Create wisdom quote
    const wisdomQuote: WisdomQuote = {
      id: uuidv4(),
      text: wisdomText,
      author: author,
      source: `${post.platform} Post`,
      source_link: sourceLink,
      categories: post.categories || ['Inspiration'],
      tags: tags,
      createdAt: new Date().toISOString(),
      extractedFrom: 'post',
      relatedPostIds: relatedIds
    };

    console.log('wisdomExtraction: Created wisdom quote with IDs:', {
      quoteId: wisdomQuote.id,
      relatedIds: wisdomQuote.relatedPostIds,
      sourceLink: wisdomQuote.source_link
    });

    return wisdomQuote;

  } catch (error) {
    console.error('wisdomExtraction: Error extracting wisdom from individual post:', error);
    return null;
  }
}

/**
 * Transforms content into wisdom by distilling the core insight
 */
async function transformContentToWisdom(content: string): Promise<string | null> {
  // First clean the content
  let cleaned = cleanRawContent(content);

  if (!cleaned) {
    return null;
  }

  // For now, skip the wisdom-worthy check to be more permissive
  // if (!isWisdomWorthy(cleaned)) {
  //   return null;
  // }

  // Try to extract wisdom using AI transformation
  try {
    const transformedWisdom = await transformWithAI(cleaned);
    if (transformedWisdom) {
      return transformedWisdom;
    }
  } catch (error) {
    console.error('wisdomExtraction: AI transformation failed:', error);
  }

  // Fallback to manual transformation
  return manualWisdomTransformation(cleaned);
}

/**
 * Cleans raw content by removing promotional elements
 */
function cleanRawContent(content: string): string | null {
  // Remove URLs
  let cleaned = content.replace(/https?:\/\/[^\s]+/g, '');

  // Remove mentions and hashtags
  cleaned = cleaned.replace(/@\w+/g, '');
  cleaned = cleaned.replace(/#\w+/g, '');

  // Remove emojis
  cleaned = cleaned.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '');

  // Remove promotional phrases
  const promotionalPhrases = [
    /here's the prompt/gi,
    /this tool will/gi,
    /click the link/gi,
    /follow for more/gi,
    /link in bio/gi,
    /swipe up/gi,
    /dm me/gi,
    /check out/gi,
    /sign up/gi,
    /get started/gi,
    /try this/gi,
    /use this/gi,
    /download/gi,
    /subscribe/gi,
    /like and share/gi,
    /comment below/gi,
    /what do you think/gi,
    /let me know/gi,
    /thread \d+\/\d+/gi,
    /\d+\/\d+$/gi
  ];

  promotionalPhrases.forEach(phrase => {
    cleaned = cleaned.replace(phrase, '');
  });

  // Remove extra whitespace and punctuation
  cleaned = cleaned.replace(/\s+/g, ' ').trim();
  cleaned = cleaned.replace(/[.]{2,}/g, '.');

  // Remove quotes if the entire content is quoted
  if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
    cleaned = cleaned.slice(1, -1).trim();
  }

  if (!cleaned || cleaned.length < 5) {
    return null;
  }

  return cleaned;
}

/**
 * Checks if content has wisdom potential
 */
function isWisdomWorthy(content: string): boolean {
  const contentLower = content.toLowerCase();

  // Skip if it's clearly promotional or instructional
  const skipPatterns = [
    /step \d+/,
    /how to/,
    /tutorial/,
    /guide/,
    /tips for/,
    /ways to/,
    /you can now/,
    /here's how/,
    /this will help/,
    /use this to/,
    /rip .* marketers/,
    /\d+ reasons/,
    /top \d+/,
    /best \d+/
  ];

  if (skipPatterns.some(pattern => pattern.test(contentLower))) {
    return false;
  }

  // Look for wisdom indicators
  const wisdomIndicators = [
    /\b(wisdom|truth|principle|insight|understanding|perspective)\b/i,
    /\b(learn|grow|journey|experience|reflection|realize)\b/i,
    /\b(life|success|failure|challenge|opportunity|change)\b/i,
    /\b(always|never|remember|important|essential|meaningful)\b/i,
    /\b(people|everyone|we|us|human|person)\b/i
  ];

  return wisdomIndicators.some(pattern => pattern.test(content));
}

/**
 * Manual transformation of content into wisdom format
 */
function manualWisdomTransformation(content: string): string | null {
  // Transform common patterns into wisdom
  let wisdom = content;

  // Transform "You can now..." to wisdom
  wisdom = wisdom.replace(/you can now\s+(.+)/gi, (match, action) => {
    return `The tools we need are often already within reach.`;
  });

  // Transform "Here's how..." to wisdom
  wisdom = wisdom.replace(/here's how\s+(.+)/gi, (match, method) => {
    return `Every solution begins with understanding the problem.`;
  });

  // Transform "This will help..." to wisdom
  wisdom = wisdom.replace(/this will help\s+(.+)/gi, (match, benefit) => {
    return `Progress comes from taking the first step.`;
  });

  // Transform tips into principles
  wisdom = wisdom.replace(/tip:\s*(.+)/gi, (match, tip) => {
    return tip.charAt(0).toUpperCase() + tip.slice(1);
  });

  // Transform "X is dead" statements
  wisdom = wisdom.replace(/rip\s+(.+?)\?/gi, (match, subject) => {
    return `Change is the only constant in our evolving world.`;
  });

  // Ensure it ends with proper punctuation
  if (wisdom && !wisdom.match(/[.!?]$/)) {
    wisdom += '.';
  }

  // Ensure it starts with capital letter
  if (wisdom) {
    wisdom = wisdom.charAt(0).toUpperCase() + wisdom.slice(1);
  }

  // Final validation
  if (!wisdom || wisdom.length < 15 || wisdom.length > 200) {
    return null;
  }

  return wisdom;
}

/**
 * Transforms content into wisdom using AI
 */
async function transformWithAI(content: string): Promise<string | null> {
  try {
    const apiKey = await getOpenAIApiKey();
    if (!apiKey) {
      return null;
    }

    const prompt = `Transform this social media content into a timeless wisdom quote. Extract the core insight and rephrase it as something a wise mentor would say. Remove all promotional language, marketing hooks, and calls to action. Make it calm, inspirational, and evergreen.

Original content: "${content}"

Requirements:
- 1-2 sentences maximum
- No promotional language or marketing speak
- No "here's how" or "this will help" phrases
- Make it timeless and universal
- Focus on the underlying principle or truth
- Should feel like wisdom, not information

Respond with only the transformed wisdom quote, nothing else.`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are a wisdom curator who transforms information into timeless insights. You extract the deeper truth from content and express it as calm, inspirational wisdom.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 100,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    const transformedWisdom = data.choices?.[0]?.message?.content?.trim();

    if (!transformedWisdom || transformedWisdom.length < 10 || transformedWisdom.length > 200) {
      return null;
    }

    // Clean up any remaining quotes
    let cleaned = transformedWisdom;
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.slice(1, -1).trim();
    }

    return cleaned;

  } catch (error) {
    console.error('wisdomExtraction: AI transformation error:', error);
    return null;
  }
}

/**
 * Generates relevant tags for wisdom based on post content and metadata
 */
function generateWisdomTags(post: Post, content: string): string[] {
  const tags: string[] = [];

  // Add content-based tags (human-readable format)
  const contentLower = content.toLowerCase();

  const tagMappings = {
    'Growth': ['grow', 'development', 'progress', 'improve', 'better'],
    'Success': ['success', 'achieve', 'goal', 'win', 'accomplish'],
    'Mindset': ['mindset', 'think', 'believe', 'perspective', 'attitude'],
    'Leadership': ['lead', 'leader', 'team', 'manage', 'guide'],
    'Motivation': ['motivat', 'inspir', 'drive', 'passion', 'energy'],
    'Wisdom': ['wisdom', 'learn', 'lesson', 'insight', 'truth'],
    'Life': ['life', 'living', 'experience', 'journey', 'path'],
    'Business': ['business', 'work', 'career', 'professional', 'company'],
    'Creativity': ['creative', 'innovation', 'idea', 'design', 'art'],
    'Focus': ['productive', 'efficient', 'time', 'focus', 'organize']
  };

  for (const [tag, keywords] of Object.entries(tagMappings)) {
    if (keywords.some(keyword => contentLower.includes(keyword))) {
      tags.push(tag);
    }
  }

  // Add platform tag in readable format
  const platformTags = {
    'X/Twitter': 'Social Media',
    'LinkedIn': 'Professional',
    'Reddit': 'Community',
    'Instagram': 'Visual',
    'facebook': 'Social',
    'pinterest': 'Creative'
  };

  if (platformTags[post.platform as keyof typeof platformTags]) {
    tags.push(platformTags[post.platform as keyof typeof platformTags]);
  }

  // Remove duplicates and limit to 3 tags
  return [...new Set(tags)].slice(0, 3);
}

/**
 * Checks if a post is suitable for wisdom extraction
 */
export function isPostSuitableForWisdom(post: Post): boolean {
  const content = post.content || post.textContent || '';

  // Basic content requirements
  if (!content || content.length < 20 || content.length > 500) {
    return false;
  }

  // Must have some author information
  if (!post.author && !post.authorName) {
    return false;
  }

  // Clean the content first
  const cleaned = cleanRawContent(content);
  if (!cleaned) {
    return false;
  }

  // Check if it has wisdom potential
  return isWisdomWorthy(cleaned);
}
