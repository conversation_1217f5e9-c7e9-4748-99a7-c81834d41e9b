import { Post } from '../types';

const API_URL = 'https://api.notely.social';

export interface SearchResult {
  post: {
    id: string;
    content?: string;
    title?: string;
    authorName?: string;
    platform: string;
    savedAt: string;
    categories?: string[];
    tags?: string[];
    permalink: string;
    media?: any[];
  };
  similarity?: number; // For backward compatibility
  score: number; // New field from MongoDB Atlas Vector Search
}

export interface SearchSummary {
  overview: string;
  keyFindings: string[];
  relatedTopics: string[];
  suggestedActions: string[];
  totalPosts: number;
}

export interface SemanticSearchResponse {
  results: SearchResult[];
  summary: SearchSummary;
  totalResults: number;
  query: string;
  searchType?: 'vector' | 'hybrid' | 'cosine_fallback' | 'text_fallback' | 'text';
  stats?: {
    averageScore?: number;
    vectorSearchAvailable?: boolean;
    totalPosts?: number;
    postsWithEmbeddings?: number;
    embeddingCoverage?: number;
  };
}

/**
 * Perform semantic search on user's saved posts
 */
export async function performSemanticSearch(
  query: string,
  limit: number = 10
): Promise<SemanticSearchResponse> {
  try {
    // Get auth token
    const token = await getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_URL}/api/posts/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: query.trim(),
        limit
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`[SemanticSearch] Error response:`, errorData);
      throw new Error(errorData.message || `Search failed: ${response.status}`);
    }

    const data = await response.json();
    return data;

  } catch (error) {
    console.error('Semantic search error:', error);
    throw error;
  }
}

/**
 * Get search suggestions based on user's content
 */
export async function getSearchSuggestions(): Promise<string[]> {
  try {
    const token = await getAuthToken();
    if (!token) {
      return getDefaultSuggestions();
    }

    const response = await fetch(`${API_URL}/api/posts/search/suggestions`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      return getDefaultSuggestions();
    }

    const data = await response.json();
    return data.suggestions || getDefaultSuggestions();

  } catch (error) {
    console.error('Error fetching search suggestions:', error);
    return getDefaultSuggestions();
  }
}

/**
 * Test search functionality and get system status
 */
export async function testSearchSystem(): Promise<{
  status: string;
  vectorSearchAvailable: boolean;
  totalPosts: number;
  postsWithEmbeddings: number;
  embeddingCoverage: number;
  hasOpenAIKey: boolean;
}> {
  try {
    const token = await getAuthToken();
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_URL}/api/posts/search/test`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Test failed: ${response.status}`);
    }

    const data = await response.json();
    return {
      status: data.status,
      vectorSearchAvailable: data.vectorSearch?.available || false,
      totalPosts: data.totalPosts || 0,
      postsWithEmbeddings: data.postsWithEmbeddings || 0,
      embeddingCoverage: data.embeddingCoverage || 0,
      hasOpenAIKey: data.hasOpenAIKey || false
    };

  } catch (error) {
    console.error('Error testing search system:', error);
    return {
      status: 'error',
      vectorSearchAvailable: false,
      totalPosts: 0,
      postsWithEmbeddings: 0,
      embeddingCoverage: 0,
      hasOpenAIKey: false
    };
  }
}

/**
 * Get authentication token from Chrome storage
 */
async function getAuthToken(): Promise<string | null> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(
      { action: 'GET_AUTH_TOKEN' },
      (response) => {
        if (chrome.runtime.lastError) {
          console.error('Error getting auth token:', chrome.runtime.lastError);
          resolve(null);
          return;
        }
        resolve(response?.token || null);
      }
    );
  });
}

/**
 * Default search suggestions when API is unavailable
 */
function getDefaultSuggestions(): string[] {
  return [
    'AI tools and prompts',
    'productivity tips',
    'design inspiration',
    'technology trends',
    'business insights',
    'creative ideas',
    'learning resources',
    'industry news'
  ];
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Highlight search terms in text
 */
export function highlightSearchTerms(text: string, query: string): string {
  if (!query.trim()) return text;

  const terms = query.toLowerCase().split(' ').filter(term => term.length > 2);
  let highlightedText = text;

  terms.forEach(term => {
    const regex = new RegExp(`(${term})`, 'gi');
    highlightedText = highlightedText.replace(
      regex,
      '<mark class="bg-notely-mint bg-opacity-30 text-notely-text-primary">$1</mark>'
    );
  });

  return highlightedText;
}

/**
 * Format similarity score as percentage
 */
export function formatSimilarity(similarity: number): string {
  return `${Math.round(similarity * 100)}%`;
}

/**
 * Get platform icon for search results
 */
export function getPlatformIcon(platform: string): string {
  const icons: Record<string, string> = {
    'X/Twitter': '𝕏',
    'LinkedIn': '💼',
    'Instagram': '📷',
    'Reddit': '🔴',
    'pinterest': '📌',
    'Web': '🌐',
    'facebook': '📘'
  };

  return icons[platform] || '📄';
}

/**
 * Truncate text for display
 */
export function truncateText(text: string, maxLength: number = 150): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

/**
 * Format date for display
 */
export function formatSearchDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;

    return date.toLocaleDateString();
  } catch (error) {
    return 'Unknown date';
  }
}

/**
 * Extract key phrases from search query for better matching
 */
export function extractKeyPhrases(query: string): string[] {
  // Simple extraction - can be enhanced with NLP
  const words = query.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2);

  const stopWords = new Set(['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way']);

  return words.filter(word => !stopWords.has(word));
}
