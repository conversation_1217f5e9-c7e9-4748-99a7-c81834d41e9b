/**
 * Chat Storage Service
 * Handles saving and loading chat conversations and history
 */

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export interface ChatConversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatHistoryItem {
  id: string;
  title: string;
  date: string;
  messageCount: number;
  lastMessage: string;
}

const CHAT_STORAGE_KEY = 'chatConversations';
const MAX_CONVERSATIONS = 50; // Limit to prevent storage bloat

/**
 * Get all chat conversations from storage
 */
export const getChatConversations = async (): Promise<ChatConversation[]> => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const result = await chrome.storage.local.get([CHAT_STORAGE_KEY]);
      return result[CHAT_STORAGE_KEY] || [];
    }
    return [];
  } catch (error) {
    console.error('Error getting chat conversations:', error);
    return [];
  }
};

/**
 * Save a chat conversation
 */
export const saveChatConversation = async (conversation: ChatConversation): Promise<void> => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const conversations = await getChatConversations();
      
      // Find existing conversation or add new one
      const existingIndex = conversations.findIndex(c => c.id === conversation.id);
      if (existingIndex >= 0) {
        conversations[existingIndex] = conversation;
      } else {
        conversations.unshift(conversation); // Add to beginning
      }
      
      // Limit number of conversations
      const limitedConversations = conversations.slice(0, MAX_CONVERSATIONS);
      
      await chrome.storage.local.set({
        [CHAT_STORAGE_KEY]: limitedConversations
      });
    }
  } catch (error) {
    console.error('Error saving chat conversation:', error);
  }
};

/**
 * Get a specific conversation by ID
 */
export const getChatConversation = async (id: string): Promise<ChatConversation | null> => {
  try {
    const conversations = await getChatConversations();
    return conversations.find(c => c.id === id) || null;
  } catch (error) {
    console.error('Error getting chat conversation:', error);
    return null;
  }
};

/**
 * Delete a conversation
 */
export const deleteChatConversation = async (id: string): Promise<void> => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const conversations = await getChatConversations();
      const filteredConversations = conversations.filter(c => c.id !== id);
      
      await chrome.storage.local.set({
        [CHAT_STORAGE_KEY]: filteredConversations
      });
    }
  } catch (error) {
    console.error('Error deleting chat conversation:', error);
  }
};

/**
 * Get chat history items for display
 */
export const getChatHistory = async (): Promise<ChatHistoryItem[]> => {
  try {
    const conversations = await getChatConversations();
    
    return conversations.map(conversation => {
      const lastMessage = conversation.messages[conversation.messages.length - 1];
      const date = new Date(conversation.updatedAt);
      
      return {
        id: conversation.id,
        title: conversation.title,
        date: formatDate(date),
        messageCount: conversation.messages.length,
        lastMessage: lastMessage?.content.substring(0, 50) + '...' || ''
      };
    });
  } catch (error) {
    console.error('Error getting chat history:', error);
    return [];
  }
};

/**
 * Generate a conversation title from the first user message
 */
export const generateConversationTitle = (firstUserMessage: string): string => {
  // Smart title generation based on content
  const message = firstUserMessage.toLowerCase();

  // Check for common patterns and generate appropriate titles
  if (message.includes('learn') && message.includes('posts')) {
    return 'Learning from Posts';
  } else if (message.includes('extract') && message.includes('tips')) {
    return 'Extracting Tips';
  } else if (message.includes('organize') && message.includes('smarter')) {
    return 'Smart Organization';
  } else if (message.includes('tools') && message.includes('resources')) {
    return 'Tools & Resources';
  } else if (message.includes('create') && message.includes('content')) {
    return 'Content Creation';
  } else if (message.includes('analyze') || message.includes('analysis')) {
    return 'Content Analysis';
  } else if (message.includes('pattern') || message.includes('trend')) {
    return 'Pattern Analysis';
  } else if (message.includes('insight') || message.includes('understand')) {
    return 'Content Insights';
  } else if (message.includes('categorize') || message.includes('category')) {
    return 'Categorization Help';
  } else if (message.includes('summary') || message.includes('summarize')) {
    return 'Content Summary';
  } else {
    // Fallback: take first few meaningful words
    const words = firstUserMessage.split(' ').filter(word => word.length > 2);
    const title = words.slice(0, 3).join(' ');
    return title.length > 25 ? title.substring(0, 25) + '...' : title;
  }
};

/**
 * Format date for display
 */
const formatDate = (date: Date | string): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Today';
    }

    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - dateObj.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return dateObj.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Today';
  }
};

/**
 * Clear all chat history
 */
export const clearAllChatHistory = async (): Promise<void> => {
  try {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      await chrome.storage.local.set({
        [CHAT_STORAGE_KEY]: []
      });
    }
  } catch (error) {
    console.error('Error clearing chat history:', error);
  }
};

/**
 * Create a new conversation
 */
export const createNewConversation = (): ChatConversation => {
  return {
    id: Date.now().toString(),
    title: 'New Conversation',
    messages: [],
    createdAt: new Date(),
    updatedAt: new Date()
  };
};
