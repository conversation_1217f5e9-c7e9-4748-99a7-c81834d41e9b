interface UserCategoryDecision {
  originalName: string;
  newName: string | null; // null means category was deleted
  timestamp: number;
  type: 'rename' | 'delete' | 'merge' | 'keep';
}

interface CategoryMergeHistory {
  sourceCategory: string;
  targetCategory: string;
  timestamp: number;
  isUserInitiated: boolean;
}

export class UserCategoryPreferences {
  private static async getUserDecisions(): Promise<UserCategoryDecision[]> {
    const result = await chrome.storage.local.get('userCategoryDecisions');
    return result.userCategoryDecisions || [];
  }

  private static async getMergeHistory(): Promise<CategoryMergeHistory[]> {
    const result = await chrome.storage.local.get('categoryMergeHistory');
    return result.categoryMergeHistory || [];
  }

  static async recordUserDecision(decision: UserCategoryDecision): Promise<void> {
    const decisions = await this.getUserDecisions();
    decisions.push(decision);
    await chrome.storage.local.set({ userCategoryDecisions: decisions });
  }

  static async recordMerge(merge: CategoryMergeHistory): Promise<void> {
    const history = await this.getMergeHistory();
    history.push(merge);
    await chrome.storage.local.set({ categoryMergeHistory: history });
  }

  static async shouldRespectCategory(categoryName: string): Promise<boolean> {
    const decisions = await this.getUserDecisions();
    const latestDecision = decisions
      .filter(d => d.originalName === categoryName || d.newName === categoryName)
      .sort((a, b) => b.timestamp - a.timestamp)[0];
    
    return latestDecision?.type === 'keep' || latestDecision?.newName === categoryName;
  }

  static async getPreferredName(categoryName: string): Promise<string | null> {
    const decisions = await this.getUserDecisions();
    const latestDecision = decisions
      .filter(d => d.originalName === categoryName)
      .sort((a, b) => b.timestamp - a.timestamp)[0];
    
    return latestDecision?.newName || categoryName;
  }

  static async canMergeCategories(category1: string, category2: string): Promise<boolean> {
    const history = await this.getMergeHistory();
    const userInitiatedMerges = history.filter(m => m.isUserInitiated);
    
    // Don't merge if user has explicitly merged either category differently
    const hasConflictingMerge = userInitiatedMerges.some(
      m => (m.sourceCategory === category1 && m.targetCategory !== category2) ||
          (m.sourceCategory === category2 && m.targetCategory !== category1)
    );
    
    return !hasConflictingMerge;
  }
} 