/**
 * Universal Cloud Sync Service
 *
 * This service provides a unified interface for uploading posts to the cloud
 * across all platforms. It handles authentication, retries, and error handling.
 */

import { Post, AnalyzedPost } from '../types';

// Helper type for parsing JSON error responses
interface ErrorResponseData {
  message?: string;
  error?: string; // Common alternative to message
  details?: unknown; // For any other details
  existingPost?: Partial<Post & { _id?: string; id?: string }>; // For 409 conflicts
  id?: string; // Sometimes the ID might be directly on the error data for 409
  _id?: string; // Alternative for id
}

// For successful post creation/update responses
interface SuccessPostResponseData extends Partial<Post> {
  _id?: string;
  id?: string;
  // Potentially other fields returned by your backend on success
}

interface CloudPostIdentifiable {
  _id?: string;
  id?: string;
  // Add other common fields if your GET /api/posts?permalink=... endpoint returns them consistently
}


// For the data field in CloudSyncResult
type CloudSyncResultDataType = 
  | SuccessPostResponseData 
  | ErrorResponseData 
  | { errorData?: ErrorResponseData, updateErrorText?: string } 
  | { statusCode?: number, body?: string } 
  | { rawResponse?: string } // For the case where 409 parsing fails
  | null;


const API_URL = 'https://api.notely.social';

export interface CloudSyncResult {
  success: boolean;
  status?: 'success' | 'conflict' | 'error'; // More specific status
  error?: string;
  retryable?: boolean;
  data?: CloudSyncResultDataType; // Replaces 'any'
}

/**
 * Update a local post with its cloud ID after successful upload
 * @param postId The local post ID
 * @param cloudId The cloud ID returned from the server
 */
async function updateLocalPostWithCloudId(postId: string, cloudId: string): Promise<void> {
  try {
    // Try to update in local storage first
    const localResult = await chrome.storage.local.get('localSavedPosts');
    const localPosts: Post[] = localResult.localSavedPosts || [];
    const localIndex = localPosts.findIndex(p => p.id === postId);

    if (localIndex !== -1) {
      localPosts[localIndex]._id = cloudId;
      await chrome.storage.local.set({ localSavedPosts: localPosts });
      console.log(`[CloudSync] Updated local post ${postId} with cloud ID ${cloudId}`);
    }

    // Try to update in sync storage as well
    try {
      const syncResult = await chrome.storage.sync.get('savedPosts');
      const syncPosts: Post[] = syncResult.savedPosts || [];
      const syncIndex = syncPosts.findIndex(p => p.id === postId);

      if (syncIndex !== -1) {
        syncPosts[syncIndex]._id = cloudId;
        await chrome.storage.sync.set({ savedPosts: syncPosts });
        console.log(`[CloudSync] Updated sync post ${postId} with cloud ID ${cloudId}`);
      }
    } catch (syncError) {
      console.warn(`[CloudSync] Could not update sync storage for post ${postId}:`, syncError);
    }
  } catch (error) {
    console.error(`[CloudSync] Error updating local post ${postId} with cloud ID ${cloudId}:`, error);
  }
}

/**
 * Upload a post to the cloud
 * @param post The post to upload
 * @param token Authentication token (optional, will be fetched if not provided)
 * @returns Promise<CloudSyncResult>
 */
export async function syncToCloud(post: AnalyzedPost, token?: string): Promise<CloudSyncResult> {
  let authToken = token;
  try {
    console.log(`[CloudSync] Starting cloud upload for post: ${post.id} (${post.platform})`);

    if (!authToken) {
      const tokenResult = await chrome.storage.local.get('authToken');
      authToken = tokenResult?.authToken;
    }

    if (!authToken) {
      console.log(`[CloudSync] User not logged in. Skipping cloud upload for post: ${post.id}`);
      return {
        success: false,
        status: 'error',
        error: 'User not logged in',
        retryable: false
      };
    }

    console.log('[CloudSync] Post object received by syncToCloud:', JSON.stringify(post, null, 2));
    const postToUpload = {
      ...post,
      originalPostId: post.id, // Keep original extension-generated ID
      savedAt: new Date().toISOString(),
      uploadStatus: 'uploading',
      content: post.content || post.textContent || '',
      interactions: post.stats ? {
        likes: post.stats.likes || 0,
        replies: post.stats.comments || 0,
        reposts: post.stats.shares || 0
      } : undefined
    };
    delete postToUpload.stats;
    delete postToUpload.textContent;

    // For Instagram posts with base64 data, we need to handle large payloads
    if (post.platform === 'Instagram') {
      // Store original URLs for base64 images to avoid 413 Payload Too Large
      if (postToUpload.media && Array.isArray(postToUpload.media)) {
        postToUpload.media = postToUpload.media.map(mediaItem => {
          if (mediaItem.url && mediaItem.url.startsWith('data:image/')) {
            // For base64 images, keep the base64 data but add a flag for the backend
            return {
              ...mediaItem,
              isBase64: true,
              originalUrl: mediaItem.originalUrl || mediaItem.url // Keep original URL if available
            };
          }
          return mediaItem;
        });
      }

      // Handle base64 author avatar
      if (postToUpload.authorAvatar && postToUpload.authorAvatar.startsWith('data:image/')) {
        // Keep base64 author avatar as-is since it's smaller and needed for display
        console.log(`[CloudSync] Instagram post ${post.id} has base64 author avatar (${Math.round(postToUpload.authorAvatar.length / 1024)}KB)`);
    }
    }

    // Use the general /api/posts endpoint for all platforms (including Instagram)
    // This endpoint has the base64 image processing fix
    let apiUrl = `${API_URL}/api/posts`;

    console.log(`[CloudSync] Uploading ${post.platform} post ${post.id} to ${apiUrl} with data:`, postToUpload);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout
    let response: Response;

    console.log('[CloudSync] Payload to be sent (postToUpload):', JSON.stringify(postToUpload, null, 2));

    try {
      // eslint-disable-next-line no-useless-catch
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(postToUpload),
        signal: controller.signal
    });
    } catch (fetchError: unknown) {
      clearTimeout(timeoutId);
      console.error(`[CloudSync] Fetch error during POST to ${apiUrl} for post ${post.id}:`, fetchError);
      return {
        success: false,
        status: 'error',
        error: fetchError instanceof Error && fetchError.name === 'AbortError' ? 'Request timed out' : (fetchError instanceof Error ? fetchError.message : 'Network error during POST'),
        retryable: true
      };
    }
    clearTimeout(timeoutId);

    if (response.ok) {
      let responseData: SuccessPostResponseData | undefined;
      try {
        responseData = await response.json() as SuccessPostResponseData;
        const cloudId = responseData?._id || responseData?.id;
        if (cloudId) {
          console.log(`[CloudSync] Successfully uploaded post ${post.id}. Cloud ID: ${cloudId}. Updating local storage.`);
          await updateLocalPostWithCloudId(post.id, cloudId); // Use original post.id for local mapping
        } else {
          console.warn(`[CloudSync] Post ${post.id} uploaded successfully, but no cloud ID found in response:`, responseData);
        }
        return { success: true, status: 'success', data: responseData };
      } catch (parseError: unknown) {
        console.warn(`[CloudSync] Successfully uploaded post ${post.id}, but failed to parse response JSON:`, parseError instanceof Error ? parseError.message : parseError);
        return { success: true, status: 'success', error: 'Failed to parse success response' }; // Success, but couldn't get ID
      }
    } else if (response.status === 409) {
      let errorData: ErrorResponseData | null = null;
      try {
        errorData = await response.json() as ErrorResponseData;
        console.log(`[CloudSync] 409 Conflict for post ${post.id}. Response data:`, errorData);
      } catch (parseError: unknown) {
        console.warn(`[CloudSync] 409 Conflict for post ${post.id}, but failed to parse error response JSON:`, parseError instanceof Error ? parseError.message : parseError);
        // Still return 'conflict' so background can try to find by permalink
        return {
          success: false,
          status: 'conflict',
          error: 'Post already exists (409), failed to parse 409 response body.',
          retryable: false,
          data: { rawResponse: await response.text().catch(() => 'Could not get raw 409 text') }
        };
      }

      const existingId = errorData?.existingPost?._id || errorData?.existingPost?.id || errorData?.id;

      if (existingId) {
        console.log(`[CloudSync] Existing cloud ID ${existingId} found for post ${post.id}. Attempting to update with AI data.`);
        
        // Prepare AI data for PUT request - ensure we only send AI-related fields and other updatable fields
        const aiUpdateData = {
          snapNote: post.snapNote,
          inSight: post.inSight,
          fastTake: post.fastTake,
          contentIdeas: post.contentIdeas,
          categories: post.categories,
          tags: post.tags,
          embeddingVector: post.embeddingVector, // if available
          analyzedAt: post.analyzedAt, // if available
          // Include any other fields that should be updatable on conflict
          textContent: post.textContent, // User might have edited this
          media: post.media // Media might have been updated
        };

        const updateController = new AbortController();
        const updateTimeoutId = setTimeout(() => updateController.abort(), 20000); // 20s timeout for PUT
        let updateResponse: Response;

        try {
          updateResponse = await fetch(`${API_URL}/api/posts/${existingId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authToken}`,
            },
            body: JSON.stringify(aiUpdateData),
            signal: updateController.signal
          });
        } catch (putFetchError: unknown) {
          clearTimeout(updateTimeoutId);
          console.error(`[CloudSync] Fetch error during PUT to ${API_URL}/api/posts/${existingId} for post ${post.id}:`, putFetchError);
          return {
            success: false,
            status: 'error',
            error: putFetchError instanceof Error && putFetchError.name === 'AbortError' ? 'PUT request timed out' : (putFetchError instanceof Error ? putFetchError.message : 'Network error during PUT'),
            data: errorData, // Original 409 data
            retryable: true
          };
        }
        clearTimeout(updateTimeoutId);

        if (updateResponse.ok) {
          console.log(`[CloudSync] Successfully updated existing post ${existingId} with AI data.`);
          await updateLocalPostWithCloudId(post.id, existingId); // Use original post.id for local mapping
          return { success: true, status: 'success', data: await updateResponse.json().catch(() => null) as SuccessPostResponseData | null };
        } else {
          const updateErrorText = await updateResponse.text().catch(() => 'Could not get PUT error text');
          console.warn(`[CloudSync] Failed to update existing post ${existingId}. Status: ${updateResponse.status}. Body: ${updateErrorText}`);
          // Even if PUT fails, we know the cloud ID. Update local mapping.
          await updateLocalPostWithCloudId(post.id, existingId);
          return {
            success: false,
            status: 'error',
            error: `Failed to update existing post ${existingId} after 409. Status: ${updateResponse.status}.`,
            data: { errorData, updateErrorText }, // Include original 409 and PUT error info
            retryable: true 
          };
        }
      } else {
        // 409 occurred, but no existingId found in the response body.
        // This is the key case for the frontend to handle by permalink.
        console.warn(`[CloudSync] 409 Conflict for post ${post.id}, but NO existing cloud ID found in response. Attempting to fetch by permalink. Data:`, errorData);

        // Use the general API endpoint for all platforms (including Instagram)
        let baseApiUrl = `${API_URL}/api/posts`;

        try {
          console.log(`[CloudSync] Attempting GET request to ${baseApiUrl}?permalink=${encodeURIComponent(post.permalink)}`);
          const getResponse = await fetch(`${baseApiUrl}?permalink=${encodeURIComponent(post.permalink)}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${authToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (getResponse.ok) {
            const existingPosts = await getResponse.json(); // Assuming API returns an array or single object
            let foundPost: CloudPostIdentifiable | null = null;
            if (Array.isArray(existingPosts) && existingPosts.length > 0) {
              foundPost = existingPosts[0]; // Take the first match
            } else if (!Array.isArray(existingPosts) && existingPosts) {
              foundPost = existingPosts; // API returned a single object
            }

            const foundCloudId = foundPost?._id || foundPost?.id;

            if (foundCloudId) {
              console.log(`[CloudSync] Successfully fetched existing post by permalink. Cloud ID: ${foundCloudId}. Attempting PUT to update.`);
              
              const updateApiUrl = `${baseApiUrl}/${foundCloudId}`;
              const putController = new AbortController();
              const putTimeoutId = setTimeout(() => putController.abort(), 20000);
              let putResponse: Response;

              try {
                putResponse = await fetch(updateApiUrl, {
                  method: 'PUT',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                  },
                  body: JSON.stringify(postToUpload), // postToUpload is defined earlier in syncToCloud
                  signal: putController.signal
                });
              } catch (putFetchError: unknown) {
                clearTimeout(putTimeoutId);
                console.error(`[CloudSync] Fetch error during PUT to ${updateApiUrl} for post ${post.id}:`, putFetchError);
                return { success: false, status: 'error', error: putFetchError instanceof Error && putFetchError.name === 'AbortError' ? 'Update request timed out' : (putFetchError instanceof Error ? putFetchError.message : 'Network error during PUT'), retryable: true };
              }
              clearTimeout(putTimeoutId);

              if (putResponse.ok) {
                const putResponseData = await putResponse.json() as SuccessPostResponseData;
                console.log(`[CloudSync] Successfully updated post ${post.id} (Cloud ID: ${foundCloudId}) via PUT.`);
                await updateLocalPostWithCloudId(post.id, foundCloudId); // Update local mapping
                return { success: true, status: 'success', data: putResponseData };
              } else {
                const putErrorText = await putResponse.text();
                console.error(`[CloudSync] Failed to update post ${post.id} (Cloud ID: ${foundCloudId}) via PUT. Status: ${putResponse.status}. Response:`, putErrorText);
                return { success: false, status: 'error', error: `Failed to update post via PUT (status ${putResponse.status})`, data: {rawResponse: putErrorText}, retryable: putResponse.status >= 500 };
              }
            } else {
              console.warn(`[CloudSync] GET by permalink for ${post.permalink} succeeded but no post found or no ID in response.`);
            }
          } else {
            console.warn(`[CloudSync] GET request by permalink for ${post.permalink} failed with status ${getResponse.status}.`);
          }
        } catch (getByPermalinkError: unknown) {
          console.error(`[CloudSync] Error during GET by permalink for ${post.permalink}:`, getByPermalinkError);
        }
        
        // Fallback: If GET by permalink fails or doesn't lead to an update, return original-style conflict error
        console.warn(`[CloudSync] Falling back to original conflict error for post ${post.id} after permalink check.`);
        return {
          success: false,
          status: 'conflict',
          error: 'Post already exists (409), and attempt to update via permalink lookup failed.',
          retryable: false,
          data: errorData
        };
      }
    } else {
      // Handle other non-OK, non-409 responses
      const errorText = await response.text().catch(() => 'Failed to retrieve error response text');
      console.error(`[CloudSync] Failed to upload post ${post.id}. Status: ${response.status}. Body: ${errorText}`);
      return {
        success: false,
        status: 'error',
        error: `Cloud sync failed: ${response.status} ${response.statusText}`,
        data: { statusCode: response.status, body: errorText },
        retryable: [500, 502, 503, 504].includes(response.status) // Retry typical server errors
      };
    }
  } catch (error: unknown) {
    console.error(`[CloudSync] Unexpected error during cloud sync for post ${post.id}:`, error);
    return {
      success: false,
      status: 'error',
      error: error instanceof Error ? error.message : 'An unexpected error occurred during cloud sync.',
      retryable: true // Typically retry unexpected errors
    };
  }
}

/**
 * Upload a post to cloud in the background with retry logic
 * @param post The post to upload
 * @param maxRetries Maximum number of retry attempts
 * @returns Promise<void>
 */
export async function syncToCloudInBackground(post: AnalyzedPost, maxRetries: number = 3): Promise<void> {
  let retryCount = 0;

  while (retryCount <= maxRetries) {
    try {
      const result = await syncToCloud(post);

      if (result.success) {
        console.log(`[CloudSync] Background upload successful for ${post.platform} post ${post.id}`);
        return;
      }

      if (!result.retryable) {
        console.log(`[CloudSync] Background upload failed (non-retryable) for ${post.platform} post ${post.id}: ${result.error}`);
        return;
      }

      retryCount++;
      if (retryCount <= maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 10000); // Exponential backoff, max 10s
        console.log(`[CloudSync] Retrying upload for ${post.platform} post ${post.id} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } catch (error) {
      console.error(`[CloudSync] Unexpected error in background upload for ${post.platform} post ${post.id}:`, error);
      retryCount++;
      if (retryCount <= maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.error(`[CloudSync] Background upload failed after ${maxRetries} retries for ${post.platform} post ${post.id}`);
}

/**
 * Check if a user is logged in and has cloud sync enabled
 * @returns Promise<boolean>
 */
export async function isCloudSyncEnabled(): Promise<boolean> {
  try {
    const tokenResult = await chrome.storage.local.get('authToken');
    return !!tokenResult?.authToken;
  } catch (error) {
    console.error('[CloudSync] Error checking cloud sync status:', error);
    return false;
  }
}

/**
 * Update post upload status in local storage
 * @param postId The post ID to update
 * @param status The new upload status
 */
export async function updatePostUploadStatus(
  postId: string,
  status: 'pending' | 'uploading' | 'uploaded' | 'failed' | 'local-only'
): Promise<void> {
  try {
    // This would need to be implemented based on your storage system
    // For now, just log the status update
    console.log(`[CloudSync] Post ${postId} upload status updated to: ${status}`);
  } catch (error) {
    console.error(`[CloudSync] Error updating upload status for post ${postId}:`, error);
  }
}

// ===== UNIFIED DATA SYNC EXTENSION =====

import { unifiedStorage, STORAGE_KEYS } from './unifiedStorageService';

export interface UnifiedSyncData {
  version: number;
  timestamp: number;
  deviceId: string;
  data: {
    categories: any[];
    customBookmarkCategories: any[];
    bookmarks: any[];
    conversations: any[];
    processedPosts: string[];
    userSettings: any;
  };
}

export interface SyncStatus {
  enabled: boolean;
  lastSync: Date | null;
  version: number;
  pendingChanges: boolean;
  error: string | null;
  deviceId: string;
}

const UNIFIED_SYNC_VERSION_KEY = 'unifiedSyncVersion';
const UNIFIED_LAST_SYNC_KEY = 'lastUnifiedSyncTimestamp';
const UNIFIED_SYNC_ENABLED_KEY = 'unifiedCloudSyncEnabled';

/**
 * Generate a unique device ID for sync tracking
 */
function generateDeviceId(): string {
  const stored = localStorage.getItem('notelyDeviceId');
  if (stored) return stored;

  const deviceId = `device-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  localStorage.setItem('notelyDeviceId', deviceId);
  return deviceId;
}

/**
 * Check if unified cloud sync is enabled
 */
export async function isUnifiedSyncEnabled(): Promise<boolean> {
  try {
    const enabled = await unifiedStorage.get<boolean>(UNIFIED_SYNC_ENABLED_KEY);
    // Enable by default if not set
    if (enabled === null || enabled === undefined) {
      await setUnifiedSyncEnabled(true);
      return true;
    }
    return enabled === true;
  } catch (error) {
    console.error('[CloudSync] Error checking unified sync status:', error);
    return false;
  }
}

/**
 * Enable or disable unified cloud sync
 */
export async function setUnifiedSyncEnabled(enabled: boolean): Promise<void> {
  try {
    await unifiedStorage.set(UNIFIED_SYNC_ENABLED_KEY, enabled);

    if (enabled && await isCloudSyncEnabled()) {
      // Trigger initial sync when enabling
      setTimeout(() => syncUnifiedDataToCloud(), 1000);
    }
  } catch (error) {
    console.error('[CloudSync] Error setting unified sync status:', error);
    throw error;
  }
}

/**
 * Get unified sync status
 */
export async function getUnifiedSyncStatus(): Promise<SyncStatus> {
  try {
    const [enabled, lastSyncTimestamp, version] = await Promise.all([
      isUnifiedSyncEnabled(),
      unifiedStorage.get<number>(UNIFIED_LAST_SYNC_KEY),
      unifiedStorage.get<number>(UNIFIED_SYNC_VERSION_KEY) || 0
    ]);

    return {
      enabled,
      lastSync: lastSyncTimestamp ? new Date(lastSyncTimestamp) : null,
      version,
      pendingChanges: await hasUnifiedPendingChanges(),
      error: null,
      deviceId: generateDeviceId()
    };
  } catch (error) {
    console.error('[CloudSync] Error getting unified sync status:', error);
    return {
      enabled: false,
      lastSync: null,
      version: 0,
      pendingChanges: false,
      error: error.message,
      deviceId: generateDeviceId()
    };
  }
}

/**
 * Sync unified data (categories, bookmarks, chat) to cloud
 */
export async function syncUnifiedDataToCloud(): Promise<CloudSyncResult> {
  try {
    console.log('[CloudSync] Starting unified data sync to cloud...');

    // Check prerequisites
    if (!await isCloudSyncEnabled()) {
      return { success: false, error: 'Cloud sync not enabled' };
    }

    if (!await isUnifiedSyncEnabled()) {
      return { success: false, error: 'Unified sync not enabled' };
    }

    // Collect unified data
    const syncData = await collectUnifiedSyncData();

    // Use existing auth token
    const tokenResult = await chrome.storage.local.get('authToken');
    const authToken = tokenResult?.authToken;

    if (!authToken) {
      return { success: false, error: 'No auth token available' };
    }

    // Send to existing cloud endpoint (extend it to handle unified data)
    const response = await fetch(`${API_URL}/api/unified-sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(syncData)
    });

    if (!response.ok) {
      throw new Error(`Unified sync failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    // Update sync metadata
    await Promise.all([
      unifiedStorage.set(UNIFIED_SYNC_VERSION_KEY, result.version || syncData.version + 1),
      unifiedStorage.set(UNIFIED_LAST_SYNC_KEY, Date.now())
    ]);

    console.log('[CloudSync] Unified data sync completed successfully');
    return { success: true, status: 'success' };
  } catch (error) {
    console.error('[CloudSync] Unified data sync failed:', error);
    return { success: false, error: error.message, retryable: true };
  }
}

/**
 * Sync unified data from cloud
 */
export async function syncUnifiedDataFromCloud(): Promise<CloudSyncResult> {
  try {
    console.log('[CloudSync] Starting unified data sync from cloud...');

    // Check prerequisites
    if (!await isCloudSyncEnabled()) {
      return { success: false, error: 'Cloud sync not enabled' };
    }

    if (!await isUnifiedSyncEnabled()) {
      return { success: false, error: 'Unified sync not enabled' };
    }

    // Get current version
    const currentVersion = await unifiedStorage.get<number>(UNIFIED_SYNC_VERSION_KEY) || 0;
    const deviceId = generateDeviceId();

    // Use existing auth token
    const tokenResult = await chrome.storage.local.get('authToken');
    const authToken = tokenResult?.authToken;

    if (!authToken) {
      return { success: false, error: 'No auth token available' };
    }

    // Fetch from cloud
    const response = await fetch(`${API_URL}/api/unified-sync?version=${currentVersion}&deviceId=${deviceId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (!response.ok) {
      if (response.status === 304) {
        console.log('[CloudSync] No unified data updates available from cloud');
        return { success: true, status: 'success' };
      }
      throw new Error(`Unified sync failed: ${response.status} ${response.statusText}`);
    }

    const cloudData: UnifiedSyncData = await response.json();

    // Apply cloud data if newer
    if (cloudData.version > currentVersion) {
      await applyUnifiedSyncData(cloudData);

      // Update sync metadata
      await Promise.all([
        unifiedStorage.set(UNIFIED_SYNC_VERSION_KEY, cloudData.version),
        unifiedStorage.set(UNIFIED_LAST_SYNC_KEY, Date.now())
      ]);

      console.log(`[CloudSync] Applied unified cloud data version ${cloudData.version}`);
    }

    return { success: true, status: 'success' };
  } catch (error) {
    console.error('[CloudSync] Unified data sync from cloud failed:', error);
    return { success: false, error: error.message, retryable: true };
  }
}

// Helper functions for unified sync
async function collectUnifiedSyncData(): Promise<UnifiedSyncData> {
  const [
    categories,
    customBookmarkCategories,
    bookmarks,
    conversations,
    processedPosts,
    userSettings
  ] = await Promise.all([
    unifiedStorage.get(STORAGE_KEYS.USER_CATEGORIES) || [],
    unifiedStorage.get(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES) || [],
    unifiedStorage.get(STORAGE_KEYS.MANUAL_BOOKMARKS) || [],
    unifiedStorage.get(STORAGE_KEYS.CHAT_CONVERSATIONS) || [],
    unifiedStorage.get(STORAGE_KEYS.PROCESSED_POSTS) || [],
    unifiedStorage.get(STORAGE_KEYS.USER_SETTINGS) || {}
  ]);

  const currentVersion = await unifiedStorage.get<number>(UNIFIED_SYNC_VERSION_KEY) || 0;

  return {
    version: currentVersion + 1,
    timestamp: Date.now(),
    deviceId: generateDeviceId(),
    data: {
      categories,
      customBookmarkCategories,
      bookmarks,
      conversations,
      processedPosts,
      userSettings
    }
  };
}

async function applyUnifiedSyncData(syncData: UnifiedSyncData): Promise<void> {
  const { data } = syncData;

  await unifiedStorage.setMultiple({
    [STORAGE_KEYS.USER_CATEGORIES]: data.categories,
    [STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES]: data.customBookmarkCategories,
    [STORAGE_KEYS.MANUAL_BOOKMARKS]: data.bookmarks,
    [STORAGE_KEYS.CHAT_CONVERSATIONS]: data.conversations,
    [STORAGE_KEYS.PROCESSED_POSTS]: data.processedPosts,
    [STORAGE_KEYS.USER_SETTINGS]: data.userSettings
  });

  console.log('[CloudSync] Applied unified sync data to local storage');
}

async function hasUnifiedPendingChanges(): Promise<boolean> {
  const lastSync = await unifiedStorage.get<number>(UNIFIED_LAST_SYNC_KEY) || 0;
  const hasData = await hasUnifiedLocalData();

  // If we have data but haven't synced in the last hour, consider it pending
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  return hasData && lastSync < oneHourAgo;
}

async function hasUnifiedLocalData(): Promise<boolean> {
  const [categories, bookmarks, conversations] = await Promise.all([
    unifiedStorage.get(STORAGE_KEYS.USER_CATEGORIES),
    unifiedStorage.get(STORAGE_KEYS.MANUAL_BOOKMARKS),
    unifiedStorage.get(STORAGE_KEYS.CHAT_CONVERSATIONS)
  ]);

  return !!(categories?.length || bookmarks?.length || conversations?.length);
}
