/**
 * Chat Storage Manager
 * Unified chat and conversation management
 */

import { unifiedStorage, STORAGE_KEYS } from './unifiedStorageService';

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export interface ChatConversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  category?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatHistoryItem {
  id: string;
  title: string;
  date: string;
  messageCount: number;
  lastMessage: string;
  category?: string;
}

const MAX_CONVERSATIONS = 50;
const MAX_MESSAGES_PER_CONVERSATION = 100;

export class ChatStorageManager {
  private static instance: ChatStorageManager;
  
  private constructor() {}
  
  static getInstance(): ChatStorageManager {
    if (!ChatStorageManager.instance) {
      ChatStorageManager.instance = new ChatStorageManager();
    }
    return ChatStorageManager.instance;
  }

  /**
   * Get all chat conversations
   */
  async getAllConversations(): Promise<ChatConversation[]> {
    try {
      const conversations = await unifiedStorage.get<ChatConversation[]>(STORAGE_KEYS.CHAT_CONVERSATIONS) || [];
      return conversations.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    } catch (error) {
      console.error('[ChatStorageManager] Error getting conversations:', error);
      return [];
    }
  }

  /**
   * Get conversations for a specific category
   */
  async getCategoryConversations(category: string): Promise<ChatConversation[]> {
    try {
      // First try the new unified storage
      const allConversations = await this.getAllConversations();
      const categoryConversations = allConversations.filter(conv => conv.category === category);

      // If no conversations found, try legacy category-specific storage
      if (categoryConversations.length === 0) {
        const legacyKey = `${STORAGE_KEYS.CATEGORY_CHAT_PREFIX}${category.toLowerCase().replace(/\s+/g, '-')}`;
        const legacyConversations = await unifiedStorage.get<ChatConversation[]>(legacyKey) || [];
        
        // Migrate legacy conversations to unified storage
        if (legacyConversations.length > 0) {
          await this.migrateCategoryConversations(category, legacyConversations);
          return legacyConversations;
        }
      }

      return categoryConversations;
    } catch (error) {
      console.error('[ChatStorageManager] Error getting category conversations:', error);
      return [];
    }
  }

  /**
   * Save a conversation
   */
  async saveConversation(conversation: ChatConversation): Promise<void> {
    try {
      const conversations = await this.getAllConversations();
      const existingIndex = conversations.findIndex(c => c.id === conversation.id);

      // Update timestamp
      conversation.updatedAt = new Date();

      // Limit messages per conversation
      if (conversation.messages.length > MAX_MESSAGES_PER_CONVERSATION) {
        conversation.messages = conversation.messages.slice(-MAX_MESSAGES_PER_CONVERSATION);
      }

      if (existingIndex >= 0) {
        conversations[existingIndex] = conversation;
      } else {
        conversations.unshift(conversation);
      }

      // Limit total conversations
      const limitedConversations = conversations.slice(0, MAX_CONVERSATIONS);

      await unifiedStorage.set(STORAGE_KEYS.CHAT_CONVERSATIONS, limitedConversations);

      // Also save to category-specific storage for backward compatibility
      if (conversation.category) {
        await this.saveToCategoryStorage(conversation);
      }
    } catch (error) {
      console.error('[ChatStorageManager] Error saving conversation:', error);
      throw error;
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<void> {
    try {
      const conversations = await this.getAllConversations();
      const conversation = conversations.find(c => c.id === conversationId);
      
      // Remove from main storage
      const filteredConversations = conversations.filter(c => c.id !== conversationId);
      await unifiedStorage.set(STORAGE_KEYS.CHAT_CONVERSATIONS, filteredConversations);

      // Remove from category-specific storage if applicable
      if (conversation?.category) {
        await this.removeFromCategoryStorage(conversation);
      }
    } catch (error) {
      console.error('[ChatStorageManager] Error deleting conversation:', error);
      throw error;
    }
  }

  /**
   * Clear all conversations for a category
   */
  async clearCategoryConversations(category: string): Promise<void> {
    try {
      // Remove from main storage
      const conversations = await this.getAllConversations();
      const filteredConversations = conversations.filter(c => c.category !== category);
      await unifiedStorage.set(STORAGE_KEYS.CHAT_CONVERSATIONS, filteredConversations);

      // Remove category-specific storage
      const categoryKey = `${STORAGE_KEYS.CATEGORY_CHAT_PREFIX}${category.toLowerCase().replace(/\s+/g, '-')}`;
      await unifiedStorage.remove(categoryKey);
    } catch (error) {
      console.error('[ChatStorageManager] Error clearing category conversations:', error);
      throw error;
    }
  }

  /**
   * Clear all chat history
   */
  async clearAllConversations(): Promise<void> {
    try {
      await unifiedStorage.set(STORAGE_KEYS.CHAT_CONVERSATIONS, []);
      
      // Also clear any legacy category-specific storage
      const allKeys = await this.getAllStorageKeys();
      const categoryKeys = allKeys.filter(key => key.startsWith(STORAGE_KEYS.CATEGORY_CHAT_PREFIX));
      
      for (const key of categoryKeys) {
        await unifiedStorage.remove(key);
      }
    } catch (error) {
      console.error('[ChatStorageManager] Error clearing all conversations:', error);
      throw error;
    }
  }

  /**
   * Get chat history summary
   */
  async getChatHistory(): Promise<ChatHistoryItem[]> {
    try {
      const conversations = await this.getAllConversations();
      
      return conversations.map(conv => ({
        id: conv.id,
        title: conv.title,
        date: new Date(conv.updatedAt).toLocaleDateString(),
        messageCount: conv.messages.length,
        lastMessage: conv.messages.length > 0 
          ? conv.messages[conv.messages.length - 1].content.substring(0, 100) + '...'
          : 'No messages',
        category: conv.category
      }));
    } catch (error) {
      console.error('[ChatStorageManager] Error getting chat history:', error);
      return [];
    }
  }

  /**
   * Create a new conversation
   */
  createNewConversation(category?: string): ChatConversation {
    return {
      id: `conv-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: 'New Conversation',
      messages: [],
      category,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Subscribe to conversation changes
   */
  subscribeToChanges(callback: (conversations: ChatConversation[]) => void): () => void {
    return unifiedStorage.subscribe(STORAGE_KEYS.CHAT_CONVERSATIONS, async () => {
      const conversations = await this.getAllConversations();
      callback(conversations);
    });
  }

  /**
   * Subscribe to category-specific conversation changes
   */
  subscribeToCategoryChanges(category: string, callback: (conversations: ChatConversation[]) => void): () => void {
    const unsubscribers = [
      // Subscribe to main conversations
      unifiedStorage.subscribe(STORAGE_KEYS.CHAT_CONVERSATIONS, async () => {
        const conversations = await this.getCategoryConversations(category);
        callback(conversations);
      }),
      // Subscribe to category-specific storage (for backward compatibility)
      unifiedStorage.subscribe(
        `${STORAGE_KEYS.CATEGORY_CHAT_PREFIX}${category.toLowerCase().replace(/\s+/g, '-')}`,
        async () => {
          const conversations = await this.getCategoryConversations(category);
          callback(conversations);
        }
      )
    ];

    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  }

  /**
   * Migrate existing chat data to unified format
   */
  async migrateExistingData(): Promise<void> {
    try {
      console.log('[ChatStorageManager] Starting chat data migration...');

      // Get all localStorage keys that look like category chats
      const localStorageKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('category-chat-')
      );

      for (const key of localStorageKeys) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const conversations = JSON.parse(data) as ChatConversation[];
            const category = key.replace('category-chat-', '').replace(/-/g, ' ');
            
            // Add category to conversations and migrate
            const migratedConversations = conversations.map(conv => ({
              ...conv,
              category: category.charAt(0).toUpperCase() + category.slice(1)
            }));

            await this.migrateCategoryConversations(category, migratedConversations);
            localStorage.removeItem(key);
          }
        } catch (error) {
          console.error(`[ChatStorageManager] Error migrating ${key}:`, error);
        }
      }

      console.log('[ChatStorageManager] Chat migration completed');
    } catch (error) {
      console.error('[ChatStorageManager] Error during chat migration:', error);
    }
  }

  // Private helper methods
  private async saveToCategoryStorage(conversation: ChatConversation): Promise<void> {
    if (!conversation.category) return;

    const categoryKey = `${STORAGE_KEYS.CATEGORY_CHAT_PREFIX}${conversation.category.toLowerCase().replace(/\s+/g, '-')}`;
    const categoryConversations = await unifiedStorage.get<ChatConversation[]>(categoryKey) || [];
    
    const existingIndex = categoryConversations.findIndex(c => c.id === conversation.id);
    if (existingIndex >= 0) {
      categoryConversations[existingIndex] = conversation;
    } else {
      categoryConversations.unshift(conversation);
    }

    // Limit category conversations
    const limitedConversations = categoryConversations.slice(0, 20);
    await unifiedStorage.set(categoryKey, limitedConversations);
  }

  private async removeFromCategoryStorage(conversation: ChatConversation): Promise<void> {
    if (!conversation.category) return;

    const categoryKey = `${STORAGE_KEYS.CATEGORY_CHAT_PREFIX}${conversation.category.toLowerCase().replace(/\s+/g, '-')}`;
    const categoryConversations = await unifiedStorage.get<ChatConversation[]>(categoryKey) || [];
    const filteredConversations = categoryConversations.filter(c => c.id !== conversation.id);
    
    if (filteredConversations.length > 0) {
      await unifiedStorage.set(categoryKey, filteredConversations);
    } else {
      await unifiedStorage.remove(categoryKey);
    }
  }

  private async migrateCategoryConversations(category: string, conversations: ChatConversation[]): Promise<void> {
    // Add conversations to main storage
    const allConversations = await this.getAllConversations();
    const migratedConversations = conversations.map(conv => ({
      ...conv,
      category: category.charAt(0).toUpperCase() + category.slice(1)
    }));

    const updatedConversations = [...migratedConversations, ...allConversations];
    const limitedConversations = updatedConversations.slice(0, MAX_CONVERSATIONS);
    
    await unifiedStorage.set(STORAGE_KEYS.CHAT_CONVERSATIONS, limitedConversations);
  }

  private async getAllStorageKeys(): Promise<string[]> {
    // This is a simplified version - in a real implementation,
    // you might need to use chrome.storage.local.get(null) to get all keys
    return Object.values(STORAGE_KEYS);
  }
}

// Export singleton instance
export const chatStorage = ChatStorageManager.getInstance();
