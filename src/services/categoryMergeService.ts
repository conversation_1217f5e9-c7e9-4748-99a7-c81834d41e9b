import { UserCategoryPreferences } from './userCategoryPreferences';
import { CORE_CATEGORIES } from '../config/constants';
import { Post } from '../types';

interface CategoryStats {
  name: string;
  postCount: number;
  posts: Set<string>;
  isCore: boolean;
}

export interface MergeResult {
  originalCategory: string;
  mergedIntoCategory: string;
  affectedPosts: number;
}

const TARGET_CATEGORY_RATIO = 5; // One category per 5 posts
const OVERLAP_THRESHOLD = 0.6; // 60% content overlap for merging
const MIN_POSTS_FOR_CATEGORY = 2; // Minimum posts to maintain a category

function calculateOverlap(set1: Set<string>, set2: Set<string>): number {
  const intersection = new Set([...set1].filter(x => set2.has(x)));
  return intersection.size / Math.min(set1.size, set2.size);
}

export async function analyzeCategoriesForMerging(posts: Post[]): Promise<MergeResult[]> {
  // Build category statistics
  const categoryStats = new Map<string, CategoryStats>();
  
  // Populate initial stats
  for (const post of posts) {
    for (const category of post.categories || []) {
      if (!categoryStats.has(category)) {
        categoryStats.set(category, {
          name: category,
          postCount: 0,
          posts: new Set(),
          isCore: Object.values(CORE_CATEGORIES).flat().includes(category)
        });
      }
      const stats = categoryStats.get(category)!;
      stats.postCount++;
      stats.posts.add(post.id);
    }
  }

  const mergeResults: MergeResult[] = [];
  const processedCategories = new Set<string>();

  // Calculate target number of categories
  const targetCategories = Math.max(
    Math.ceil(posts.length / TARGET_CATEGORY_RATIO),
    3 // Minimum 3 categories
  );

  // Sort categories by post count (descending)
  const sortedCategories = Array.from(categoryStats.values())
    .sort((a, b) => b.postCount - a.postCount);

  // If we're already at or below target, no need to merge
  if (sortedCategories.length <= targetCategories) {
    return mergeResults;
  }

  // Process categories until we reach target number
  while (sortedCategories.length - processedCategories.size > targetCategories) {
    for (const category of sortedCategories) {
      if (processedCategories.has(category.name)) continue;

      // Check if this category should be respected based on user decisions
      const shouldRespect = await UserCategoryPreferences.shouldRespectCategory(category.name);
      if (shouldRespect || (category.isCore && category.postCount >= MIN_POSTS_FOR_CATEGORY)) {
        processedCategories.add(category.name);
        continue;
      }

      // Find best merge candidate
      let bestMatch: string | null = null;
      let bestOverlap = 0;

      for (const candidate of sortedCategories) {
        if (
          candidate.name === category.name || 
          processedCategories.has(candidate.name)
        ) continue;

        // Check if merging these categories is allowed by user preferences
        const canMerge = await UserCategoryPreferences.canMergeCategories(
          category.name, 
          candidate.name
        );
        
        if (!canMerge) continue;

        const overlap = calculateOverlap(category.posts, candidate.posts);
        if (overlap > OVERLAP_THRESHOLD && overlap > bestOverlap) {
          bestOverlap = overlap;
          bestMatch = candidate.name;
        }
      }

      if (bestMatch) {
        // Record the merge
        await UserCategoryPreferences.recordMerge({
          sourceCategory: category.name,
          targetCategory: bestMatch,
          timestamp: Date.now(),
          isUserInitiated: false
        });

        mergeResults.push({
          originalCategory: category.name,
          mergedIntoCategory: bestMatch,
          affectedPosts: category.postCount
        });
        processedCategories.add(category.name);

        // If we've reached target categories, stop merging
        if (sortedCategories.length - processedCategories.size <= targetCategories) {
          break;
        }
      } else {
        // If we can't find a merge candidate, respect this category
        processedCategories.add(category.name);
      }
    }
  }

  return mergeResults;
} 