import * as authService from './authService';

const API_URL = 'https://api.notely.social';

/**
 * Make an authenticated API request
 */
export const fetchWithAuth = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<any> => {
  try {
    const token = await authService.getToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    };

    const response = await fetch(`${API_URL}${endpoint}`, {
      ...options,
      headers
    });

    // Handle token expiration
    if (response.status === 401) {
      await authService.logout(); // Clear auth data on token expiration
      throw new Error('Authentication token expired');
    }

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

/**
 * Get user profile from the API
 */
export const getUserProfile = async (): Promise<any> => {
  try {
    const profile = await fetchWithAuth('/auth/me');
    // Update local storage with latest profile data
    await chrome.storage.local.set({ user_profile: profile });
    return profile;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

/**
 * Update user profile
 */
export const updateUserProfile = async (profileData: {
  name?: string;
  displayName?: string | null;
}): Promise<any> => {
  console.log('[apiService] Updating user profile with data:', profileData);
  
  try {
    const updatedProfile = await fetchWithAuth('/auth/me', {
      method: 'PUT',
      body: JSON.stringify(profileData)
    });
    
    console.log('[apiService] Profile update response:', updatedProfile);
    
    // Update local storage
    await chrome.storage.local.set({ user_profile: updatedProfile });
    
    return updatedProfile;
  } catch (error) {
    console.error('[apiService] Error updating profile:', error);
    throw error;
  }
};

/**
 * Get user subscription info
 */
export const getUserSubscription = async (): Promise<any> => {
  return fetchWithAuth('/user/subscription');
};