import { WisdomQuote } from '../types/wisdom';
import { Post } from '../types';
import { getOpenAIApiKey } from './aiService';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for generating AI-powered wisdom quotes based on trending content
 */

const AI_API_ENDPOINT = 'https://api.openai.com/v1/chat/completions';
const AI_MODEL = 'gpt-4o';

interface GeneratedWisdom {
  quote: string;
  author: string;
  tags: string[];
  category: string;
}

/**
 * Generates AI wisdom quotes to fill remaining slots in 5-day batch
 */
export async function generateAIWisdom(
  neededCount: number,
  userPosts: Post[] = [],
  existingWisdom: WisdomQuote[] = []
): Promise<WisdomQuote[]> {
  try {
    if (neededCount <= 0) {
      return [];
    }

    const apiKey = await getOpenAIApiKey();
    if (!apiKey) {
      console.error('wisdomGeneration: OpenAI API key is missing');
      return [];
    }

    // Analyze user's content patterns for personalized wisdom
    const contentContext = analyzeUserContentPatterns(userPosts);
    
    // Generate wisdom quotes
    const generatedWisdom = await generateWisdomWithAI(apiKey, neededCount, contentContext, existingWisdom);
    
    // Convert to WisdomQuote format
    const wisdomQuotes: WisdomQuote[] = generatedWisdom.map((wisdom, index) => ({
      id: uuidv4(),
      text: wisdom.quote,
      author: 'Generated',
      source: 'AI Generated',
      categories: [wisdom.category],
      tags: wisdom.tags,
      createdAt: new Date().toISOString(),
      extractedFrom: 'ai-generated',
      dayIndex: index
    }));

    return wisdomQuotes;

  } catch (error) {
    console.error('wisdomGeneration: Error generating AI wisdom:', error);
    return [];
  }
}

/**
 * Analyzes user's saved posts to understand their interests and content patterns
 */
function analyzeUserContentPatterns(posts: Post[]): string {
  if (!posts || posts.length === 0) {
    return 'general productivity, success, and personal growth topics';
  }

  // Extract common themes from user's posts
  const themes: string[] = [];
  const platforms: string[] = [];
  const categories: string[] = [];
  
  posts.slice(0, 20).forEach(post => { // Analyze recent 20 posts
    // Collect platforms
    if (post.platform) {
      platforms.push(post.platform);
    }
    
    // Collect categories
    if (post.categories) {
      categories.push(...post.categories);
    }
    
    // Extract themes from content
    const content = (post.content || post.textContent || '').toLowerCase();
    
    const themeKeywords = {
      'technology': ['tech', 'ai', 'software', 'digital', 'innovation', 'startup'],
      'business': ['business', 'entrepreneur', 'marketing', 'sales', 'strategy'],
      'productivity': ['productive', 'efficiency', 'time', 'focus', 'organize'],
      'leadership': ['leadership', 'team', 'manage', 'lead', 'culture'],
      'creativity': ['creative', 'design', 'art', 'innovation', 'idea'],
      'personal growth': ['growth', 'learn', 'develop', 'improve', 'mindset'],
      'career': ['career', 'job', 'work', 'professional', 'skill'],
      'finance': ['money', 'invest', 'finance', 'wealth', 'economic'],
      'health': ['health', 'fitness', 'wellness', 'mental', 'exercise'],
      'social media': ['social', 'content', 'audience', 'engagement', 'viral']
    };
    
    for (const [theme, keywords] of Object.entries(themeKeywords)) {
      if (keywords.some(keyword => content.includes(keyword))) {
        themes.push(theme);
      }
    }
  });

  // Get most common themes
  const themeCount = themes.reduce((acc, theme) => {
    acc[theme] = (acc[theme] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const topThemes = Object.entries(themeCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([theme]) => theme);

  const topPlatforms = [...new Set(platforms)].slice(0, 2);
  
  return `The user is interested in ${topThemes.join(', ')} and actively saves content from ${topPlatforms.join(' and ')}. Focus on wisdom related to these areas.`;
}

/**
 * Generates wisdom quotes using AI based on user context
 */
async function generateWisdomWithAI(
  apiKey: string,
  count: number,
  userContext: string,
  existingWisdom: WisdomQuote[]
): Promise<GeneratedWisdom[]> {
  
  const existingQuotes = existingWisdom.map(w => w.text).join('\n- ');
  
  const prompt = `Generate ${count} unique wisdom insights for a daily wisdom feature. These should be timeless truths, not information or tips.

User Context: ${userContext}

Requirements:
- Each wisdom should be 1-2 sentences maximum
- Focus on timeless principles and deeper truths
- Avoid promotional language, marketing speak, or instructional content
- No "how to" or "here's what" or "this will help" phrases
- Make them feel like something a wise mentor would whisper to someone stuck
- Should be calming, inspirational, and evergreen
- Avoid clichés and overused phrases
- Don't repeat or paraphrase these existing quotes:
${existingQuotes}

Think of wisdom as distilled truth that feels timeless and meaningful, not actionable tips or information.

For each wisdom, also provide:
- A relevant category (Growth, Success, Creativity, Leadership, Productivity, Mindset, Innovation, or Life)
- 2-3 relevant tags

Respond with a JSON array of objects with this structure:
{
  "quote": "The actual wisdom text",
  "author": "Generated",
  "tags": ["tag1", "tag2", "tag3"],
  "category": "Category"
}

Example:
[
  {
    "quote": "What we resist in others often reflects what we haven't yet accepted in ourselves.",
    "author": "Generated",
    "tags": ["Self-awareness", "Growth", "Reflection"],
    "category": "Growth"
  }
]`;

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are a wisdom curator and motivational expert. Generate inspiring, actionable quotes that resonate with modern professionals and creators. Always respond with valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.8, // Higher creativity for varied wisdom
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from AI API');
    }

    const parsed = JSON.parse(content);
    
    // Handle both array and object responses
    let wisdomArray: GeneratedWisdom[];
    if (Array.isArray(parsed)) {
      wisdomArray = parsed;
    } else if (parsed.quotes && Array.isArray(parsed.quotes)) {
      wisdomArray = parsed.quotes;
    } else {
      throw new Error('Invalid response format from AI API');
    }

    // Validate and clean the generated wisdom
    const validWisdom = wisdomArray
      .filter(w => w.quote && w.quote.length > 10 && w.quote.length < 300)
      .slice(0, count) // Ensure we don't exceed requested count
      .map(w => ({
        quote: w.quote.trim(),
        author: 'Generated',
        tags: Array.isArray(w.tags) ? w.tags.slice(0, 3) : ['Wisdom'],
        category: w.category || 'Growth'
      }));

    return validWisdom;

  } catch (error) {
    console.error('wisdomGeneration: Error calling AI API:', error);
    
    // Fallback to predefined wisdom if AI fails
    return generateFallbackWisdom(count);
  }
}

/**
 * Fallback wisdom quotes if AI generation fails
 */
function generateFallbackWisdom(count: number): GeneratedWisdom[] {
  const fallbackQuotes: GeneratedWisdom[] = [
    {
      quote: "What we resist in others often reflects what we haven't yet accepted in ourselves.",
      author: "Generated",
      tags: ["Self-awareness", "Growth", "Reflection"],
      category: "Growth"
    },
    {
      quote: "The space between stimulus and response is where our freedom lies.",
      author: "Generated",
      tags: ["Choice", "Mindset", "Freedom"],
      category: "Mindset"
    },
    {
      quote: "Every master was once a beginner who refused to give up.",
      author: "Generated",
      tags: ["Persistence", "Mastery", "Journey"],
      category: "Growth"
    },
    {
      quote: "The quality of our questions determines the quality of our lives.",
      author: "Generated",
      tags: ["Curiosity", "Wisdom", "Life"],
      category: "Life"
    },
    {
      quote: "We cannot direct the wind, but we can adjust our sails.",
      author: "Generated",
      tags: ["Adaptability", "Control", "Resilience"],
      category: "Life"
    },
    {
      quote: "The teacher appears when the student is ready to learn.",
      author: "Generated",
      tags: ["Learning", "Readiness", "Growth"],
      category: "Growth"
    },
    {
      quote: "Comparison is the thief of joy and the enemy of progress.",
      author: "Generated",
      tags: ["Comparison", "Joy", "Progress"],
      category: "Mindset"
    }
  ];

  return fallbackQuotes.slice(0, count);
}
