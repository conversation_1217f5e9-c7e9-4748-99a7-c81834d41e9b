/**
 * Unified Storage Service
 * Centralizes all storage operations to ensure synchronization across components
 */

// Storage keys - centralized to prevent conflicts
export const STORAGE_KEYS = {
  // Categories
  USER_CATEGORIES: 'userCategories',
  CUSTOM_BOOKMARK_CATEGORIES: 'custom_categories',
  
  // Chat & Conversations
  CHAT_CONVERSATIONS: 'chatConversations',
  CATEGORY_CHAT_PREFIX: 'category-chat-',
  
  // Bookmarks
  MANUAL_BOOKMARKS: 'manual_bookmarks',
  PROCESSED_POSTS: 'bookmark_processed_posts',
  
  // Posts & Content
  POSTS: 'posts',
  TAGS: 'userTags',
  
  // Settings
  USER_SETTINGS: 'userSettings',
  THEME_SETTINGS: 'themeSettings'
} as const;

// Storage change listeners
type StorageChangeListener = (changes: { [key: string]: any }) => void;
const storageListeners = new Map<string, Set<StorageChangeListener>>();

/**
 * Unified storage interface that uses chrome.storage.local consistently
 */
export class UnifiedStorageService {
  private static instance: UnifiedStorageService;
  
  private constructor() {
    // Listen for storage changes and notify subscribers
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === 'local') {
          Object.keys(changes).forEach(key => {
            const listeners = storageListeners.get(key);
            if (listeners) {
              listeners.forEach(listener => {
                listener({ [key]: changes[key] });
              });
            }
          });
        }
      });
    }
  }
  
  static getInstance(): UnifiedStorageService {
    if (!UnifiedStorageService.instance) {
      UnifiedStorageService.instance = new UnifiedStorageService();
    }
    return UnifiedStorageService.instance;
  }

  /**
   * Get data from storage
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get([key]);
        return result[key] || null;
      }
      return null;
    } catch (error) {
      console.error(`[UnifiedStorage] Error getting ${key}:`, error);
      return null;
    }
  }

  /**
   * Set data in storage
   */
  async set<T>(key: string, value: T): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ [key]: value });
      }
    } catch (error) {
      console.error(`[UnifiedStorage] Error setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * Remove data from storage
   */
  async remove(key: string): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.remove([key]);
      }
    } catch (error) {
      console.error(`[UnifiedStorage] Error removing ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get multiple keys at once
   */
  async getMultiple<T>(keys: string[]): Promise<{ [key: string]: T }> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(keys);
        return result as { [key: string]: T };
      }
      return {};
    } catch (error) {
      console.error(`[UnifiedStorage] Error getting multiple keys:`, error);
      return {};
    }
  }

  /**
   * Set multiple key-value pairs at once
   */
  async setMultiple(data: { [key: string]: any }): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set(data);
      }
    } catch (error) {
      console.error(`[UnifiedStorage] Error setting multiple keys:`, error);
      throw error;
    }
  }

  /**
   * Subscribe to storage changes for a specific key
   */
  subscribe(key: string, listener: StorageChangeListener): () => void {
    if (!storageListeners.has(key)) {
      storageListeners.set(key, new Set());
    }
    storageListeners.get(key)!.add(listener);
    
    // Return unsubscribe function
    return () => {
      const listeners = storageListeners.get(key);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          storageListeners.delete(key);
        }
      }
    };
  }

  /**
   * Migrate data from localStorage to chrome.storage.local
   */
  async migrateFromLocalStorage(localStorageKey: string, chromeStorageKey: string): Promise<void> {
    try {
      // Check if data exists in localStorage
      const localData = localStorage.getItem(localStorageKey);
      if (localData) {
        // Parse and save to chrome storage
        const parsedData = JSON.parse(localData);
        await this.set(chromeStorageKey, parsedData);
        
        // Remove from localStorage after successful migration
        localStorage.removeItem(localStorageKey);
        console.log(`[UnifiedStorage] Migrated ${localStorageKey} to ${chromeStorageKey}`);
      }
    } catch (error) {
      console.error(`[UnifiedStorage] Error migrating ${localStorageKey}:`, error);
    }
  }

  /**
   * Migrate data from chrome.storage.sync to chrome.storage.local
   */
  async migrateFromSync(syncKey: string, localKey: string): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        // Get data from sync storage
        const syncResult = await chrome.storage.sync.get([syncKey]);
        if (syncResult[syncKey]) {
          // Save to local storage
          await this.set(localKey, syncResult[syncKey]);
          
          // Remove from sync storage after successful migration
          await chrome.storage.sync.remove([syncKey]);
          console.log(`[UnifiedStorage] Migrated ${syncKey} from sync to local storage as ${localKey}`);
        }
      }
    } catch (error) {
      console.error(`[UnifiedStorage] Error migrating from sync ${syncKey}:`, error);
    }
  }

  /**
   * Clear all storage (for debugging/reset purposes)
   */
  async clearAll(): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.clear();
        console.log('[UnifiedStorage] All storage cleared');
      }
    } catch (error) {
      console.error('[UnifiedStorage] Error clearing storage:', error);
      throw error;
    }
  }

  /**
   * Get storage usage info
   */
  async getStorageInfo(): Promise<{ bytesInUse: number; quota: number }> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const bytesInUse = await chrome.storage.local.getBytesInUse();
        return {
          bytesInUse,
          quota: chrome.storage.local.QUOTA_BYTES || 5242880 // 5MB default
        };
      }
      return { bytesInUse: 0, quota: 0 };
    } catch (error) {
      console.error('[UnifiedStorage] Error getting storage info:', error);
      return { bytesInUse: 0, quota: 0 };
    }
  }
}

// Export singleton instance
export const unifiedStorage = UnifiedStorageService.getInstance();

// Export specific storage managers for different data types
export * from './categoryStorageManager';
export * from './chatStorageManager';
export * from './bookmarkStorageManager';
