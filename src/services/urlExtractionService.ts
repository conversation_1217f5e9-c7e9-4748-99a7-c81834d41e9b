/**
 * URL Extraction Service
 * 
 * This service handles:
 * 1. Extracting URLs from post content using regex patterns
 * 2. Fetching website metadata (title, description) from URLs
 * 3. Normalizing URLs and preventing duplicates
 * 4. Filtering out social media and internal links
 */

export interface ExtractedUrl {
  url: string;
  normalizedUrl: string;
  domain: string;
}

export interface WebsiteMetadata {
  title: string;
  description: string;
  favicon?: string;
  ogImage?: string;
}

export interface BookmarkCandidate {
  url: string;
  normalizedUrl: string;
  domain: string;
  title: string;
  description: string;
  favicon: string;
}

// Social media domains to exclude from bookmark extraction
const EXCLUDED_DOMAINS = new Set([
  'twitter.com',
  'x.com',
  't.co',
  'linkedin.com',
  'lnkd.in',
  'instagram.com',
  'facebook.com',
  'fb.me',
  'reddit.com',
  'redd.it',
  'pinterest.com',
  'pin.it',
  'tiktok.com',
  'youtube.com',
  'youtu.be',
  'bit.ly',
  'tinyurl.com',
  'short.link',
  'ow.ly',
  'buff.ly'
]);

// URL regex pattern to match various URL formats - improved to be more strict
const URL_REGEX = /(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?:\/[^\s]*)?/g;

/**
 * Extract URLs from post content using regex patterns
 */
export function extractUrlsFromContent(content: string): ExtractedUrl[] {
  if (!content || typeof content !== 'string') {
    return [];
  }

  const urls: ExtractedUrl[] = [];
  const matches = content.match(URL_REGEX);

  if (!matches) {
    return [];
  }

  for (const match of matches) {
    try {
      const normalizedUrl = normalizeUrl(match);
      
      // Additional validation for URL completeness
      if (!isValidUrl(normalizedUrl)) {
        console.debug('[urlExtractionService] Skipping invalid URL format:', match);
        continue;
      }
      
      const urlObj = new URL(normalizedUrl);
      const domain = urlObj.hostname.replace('www.', '');

      // Skip excluded domains
      if (EXCLUDED_DOMAINS.has(domain)) {
        continue;
      }

      // Skip if already found (prevent duplicates)
      if (urls.some(u => u.normalizedUrl === normalizedUrl)) {
        continue;
      }

      urls.push({
        url: match,
        normalizedUrl,
        domain
      });
    } catch (error) {
      // Skip invalid URLs
      console.debug('[urlExtractionService] Skipping invalid URL:', match, error.message);
    }
  }

  return urls;
}

/**
 * Validate if a URL is properly formatted and accessible
 */
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    
    // Must have a proper hostname with at least one dot and valid TLD
    const hostname = urlObj.hostname;
    
    // Skip if hostname doesn't contain a dot (incomplete URLs)
    if (!hostname.includes('.')) {
      return false;
    }
    
    // Skip if hostname ends with a slash or is incomplete
    if (hostname.endsWith('/') || hostname.length < 3) {
      return false;
    }
    
    // Must have a valid TLD (at least 2 characters)
    const parts = hostname.split('.');
    const tld = parts[parts.length - 1];
    if (tld.length < 2) {
      return false;
    }
    
    // Skip common incomplete patterns
    const invalidPatterns = [
      /^[a-zA-Z]+\.?$/,  // Just word with optional dot (like "developer.")
      /^[a-zA-Z]+\.[a-zA-Z]$/,  // Single letter TLD
      /localhost/,
      /^\d+\.\d+\.\d+$/  // Incomplete IP
    ];
    
    for (const pattern of invalidPatterns) {
      if (pattern.test(hostname)) {
        return false;
      }
    }
    
    return true;
  } catch {
    return false;
  }
}

/**
 * Normalize URL by adding protocol and removing tracking parameters
 */
export function normalizeUrl(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }

  let normalized = url.trim();

  // Add protocol if missing
  if (!normalized.startsWith('http://') && !normalized.startsWith('https://')) {
    normalized = `https://${normalized}`;
  }

  try {
    const urlObj = new URL(normalized);
    
    // Remove common tracking parameters
    const trackingParams = [
      'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
      'fbclid', 'gclid', 'msclkid', 'ref', 'source', 'campaign'
    ];
    
    trackingParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });

    // Remove fragment (hash) for consistency
    urlObj.hash = '';

    return urlObj.toString();
  } catch (error) {
    return normalized;
  }
}

/**
 * Check if a URL is a duplicate of existing bookmarks
 */
export function isDuplicateUrl(url: string, existingUrls: string[]): boolean {
  const normalizedUrl = normalizeUrl(url);
  const normalizedExisting = existingUrls.map(u => normalizeUrl(u));
  
  return normalizedExisting.includes(normalizedUrl);
}

/**
 * Fetch website metadata using a CORS proxy or direct fetch
 */
export async function fetchWebsiteMetadata(url: string): Promise<WebsiteMetadata> {
  const normalizedUrl = normalizeUrl(url);
  
  try {
    // Try direct fetch first (will work for CORS-enabled sites)
    let response: Response;
    
    try {
      response = await fetch(normalizedUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; Notely/1.0; +https://notely.social)'
        },
        mode: 'cors'
      });
    } catch (corsError) {
      // If CORS fails, try with a CORS proxy
      const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(normalizedUrl)}`;
      try {
        response = await fetch(proxyUrl);
        
        if (response.ok) {
          const data = await response.json();
          response = new Response(data.contents, { status: 200 });
        } else {
          throw new Error(`Proxy failed with status ${response.status}`);
        }
      } catch (proxyError) {
        throw new Error(`Both direct fetch and proxy failed: ${corsError.message}, ${proxyError.message}`);
      }
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const html = await response.text();
    return parseHtmlMetadata(html, normalizedUrl);
    
  } catch (error) {
    // Only log non-network errors as warnings to reduce noise
    if (!error.message?.includes('fetch') && !error.message?.includes('CORS') && !error.message?.includes('HTTP 400')) {
      console.warn('[urlExtractionService] Failed to fetch metadata for:', normalizedUrl, error.message);
    } else {
      console.debug('[urlExtractionService] Using fallback metadata for:', normalizedUrl, '(network/CORS issue)');
    }
    
    // Return fallback metadata
    const domain = new URL(normalizedUrl).hostname.replace('www.', '');
    return {
      title: domain.charAt(0).toUpperCase() + domain.slice(1),
      description: `Website: ${domain}`,
      favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=16`
    };
  }
}

/**
 * Parse HTML content to extract metadata
 */
function parseHtmlMetadata(html: string, url: string): WebsiteMetadata {
  const domain = new URL(url).hostname.replace('www.', '');
  
  // Create a temporary DOM parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  
  // Extract title
  let title = '';
  const ogTitle = doc.querySelector('meta[property="og:title"]')?.getAttribute('content');
  const titleTag = doc.querySelector('title')?.textContent;
  title = ogTitle || titleTag || domain;
  
  // Clean up title
  title = title.trim().substring(0, 100);
  if (!title) {
    title = domain.charAt(0).toUpperCase() + domain.slice(1);
  }
  
  // Extract description
  let description = '';
  const ogDescription = doc.querySelector('meta[property="og:description"]')?.getAttribute('content');
  const metaDescription = doc.querySelector('meta[name="description"]')?.getAttribute('content');
  description = ogDescription || metaDescription || `Website: ${domain}`;
  
  // Clean up description
  description = description.trim().substring(0, 200);
  if (!description) {
    description = `Website: ${domain}`;
  }
  
  // Extract favicon
  let favicon = `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
  const faviconLink = doc.querySelector('link[rel="icon"], link[rel="shortcut icon"]')?.getAttribute('href');
  if (faviconLink) {
    try {
      favicon = new URL(faviconLink, url).toString();
    } catch {
      // Keep default favicon
    }
  }
  
  // Extract OG image
  let ogImage = '';
  const ogImageTag = doc.querySelector('meta[property="og:image"]')?.getAttribute('content');
  if (ogImageTag) {
    try {
      ogImage = new URL(ogImageTag, url).toString();
    } catch {
      // Skip invalid OG image
    }
  }
  
  return {
    title,
    description,
    favicon,
    ogImage
  };
}

/**
 * Extract bookmark candidates from post content
 * This combines URL extraction with metadata fetching
 */
export async function extractBookmarkCandidates(content: string): Promise<BookmarkCandidate[]> {
  const urls = extractUrlsFromContent(content);
  
  if (urls.length === 0) {
    return [];
  }
  
  console.log(`[urlExtractionService] 🔍 Found ${urls.length} URLs in content:`, urls.map(u => u.domain));
  console.log(`[urlExtractionService] 📡 Fetching metadata for ${urls.length} URLs...`);
  
  const candidates: BookmarkCandidate[] = [];
  
  // Process URLs in parallel but limit concurrency
  const batchSize = 3;
  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (urlData) => {
      try {
        const metadata = await fetchWebsiteMetadata(urlData.normalizedUrl);
        return {
          url: urlData.url,
          normalizedUrl: urlData.normalizedUrl,
          domain: urlData.domain,
          title: metadata.title,
          description: metadata.description,
          favicon: metadata.favicon || `https://www.google.com/s2/favicons?domain=${urlData.domain}&sz=16`
        };
      } catch (error) {
        console.debug('[urlExtractionService] Failed to process URL:', urlData.normalizedUrl, 'using fallback');
        // Return fallback bookmark candidate instead of null
        return {
          url: urlData.url,
          normalizedUrl: urlData.normalizedUrl,
          domain: urlData.domain,
          title: urlData.domain.charAt(0).toUpperCase() + urlData.domain.slice(1),
          description: `Website: ${urlData.domain}`,
          favicon: `https://www.google.com/s2/favicons?domain=${urlData.domain}&sz=16`
        };
      }
    });
    
    const batchResults = await Promise.all(batchPromises);
    candidates.push(...batchResults as BookmarkCandidate[]);
  }
  
  console.log(`[urlExtractionService] ✅ Successfully processed ${candidates.length} bookmark candidates`);
  if (candidates.length > 0) {
    console.log(`[urlExtractionService] 📚 Extracted bookmarks:`, candidates.map(c => `${c.title} (${c.domain})`));
  }
  return candidates;
}
