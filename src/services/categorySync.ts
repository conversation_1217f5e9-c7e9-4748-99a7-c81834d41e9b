import { storage } from '../storage';
import type { Category } from './categoryService';

export class CategorySyncService {
  private static instance: CategorySyncService;
  
  private constructor() {}
  
  static getInstance(): CategorySyncService {
    if (!CategorySyncService.instance) {
      CategorySyncService.instance = new CategorySyncService();
    }
    return CategorySyncService.instance;
  }

  async syncCategoryCounts(): Promise<void> {
    const categories = await storage.get('categories') || [];
    const posts = await storage.get('posts') || [];
    
    const updatedCategories = categories.map(cat => ({
      ...cat,
      postCount: posts.filter(p => p.categoryIds.includes(cat.id)).length
    }));
    
    await storage.set('categories', updatedCategories);
  }

  async cleanupOrphanedCategories(): Promise<void> {
    const categories = await storage.get('categories') || [];
    const posts = await storage.get('posts') || [];
    
    // Find categories with no posts
    const orphanedIds = categories
      .filter(cat => !cat.isUserCreated) // Only auto-cleanup non-user categories
      .filter(cat => !posts.some(p => p.categoryIds.includes(cat.id)))
      .map(cat => cat.id);
    
    if (orphanedIds.length === 0) return;
    
    const updatedCategories = categories
      .filter(cat => !orphanedIds.includes(cat.id))
      .map((cat, idx) => ({ ...cat, sortOrder: idx }));
    
    await storage.set('categories', updatedCategories);
  }

  async validateCategoryIntegrity(): Promise<{
    hasIssues: boolean;
    issues: string[];
  }> {
    const categories = await storage.get('categories') || [];
    const posts = await storage.get('posts') || [];
    const issues: string[] = [];

    // Check for duplicate names
    const names = categories.map(c => c.name.toLowerCase());
    const duplicates = names.filter(
      (name, idx) => names.indexOf(name) !== idx
    );
    if (duplicates.length > 0) {
      issues.push(`Found duplicate category names: ${duplicates.join(', ')}`);
    }

    // Check for invalid sort orders
    const sortOrders = categories.map(c => c.sortOrder);
    const expectedOrders = Array.from(
      { length: categories.length }, 
      (_, i) => i
    );
    const hasInvalidOrder = !sortOrders.every(
      order => expectedOrders.includes(order)
    );
    if (hasInvalidOrder) {
      issues.push('Found invalid category sort orders');
    }

    // Check for mismatched post counts
    for (const category of categories) {
      const actualCount = posts.filter(
        p => p.categoryIds.includes(category.id)
      ).length;
      if (actualCount !== category.postCount) {
        issues.push(
          `Category "${category.name}" has incorrect post count: ` +
          `${category.postCount} stored vs ${actualCount} actual`
        );
      }
    }

    return {
      hasIssues: issues.length > 0,
      issues
    };
  }
} 