import { Post, AnalyzedPost, CoreSubCategorySlug, InSightData } from './types';

// API URL for cloud sync
const API_URL = 'https://api.notely.social';

// --- Promise-based wrappers (Keep these as they might be useful elsewhere) ---
const storageGet = (key: string | string[] | { [key: string]: unknown } | null): Promise<{[key: string]: unknown}> => {
    // ... implementation ...
  return new Promise((resolve, reject) => {
    chrome.storage.sync.get(key, (result) => {
      if (chrome.runtime.lastError) {
        return reject(chrome.runtime.lastError);
      }
      resolve(result);
    });
  });
};

const storageLocalGet = (key: string | string[] | { [key: string]: unknown } | null): Promise<{[key: string]: unknown}> => {
    // ... implementation ...
  return new Promise((resolve, reject) => {
    chrome.storage.local.get(key, (result) => {
      if (chrome.runtime.lastError) {
        return reject(chrome.runtime.lastError);
      }
      resolve(result);
    });
  });
};

const storageSet = (items: { [key: string]: unknown }): Promise<void> => {
    // ... implementation ...
  return new Promise((resolve, reject) => {
    chrome.storage.sync.set(items, () => {
      if (chrome.runtime.lastError) {
        return reject(chrome.runtime.lastError);
      }
      resolve();
    });
  });
};

const storageLocalSet = (items: { [key: string]: unknown }): Promise<void> => {
    // ... implementation ...
  return new Promise((resolve, reject) => {
    chrome.storage.local.set(items, () => {
      if (chrome.runtime.lastError) {
        return reject(chrome.runtime.lastError);
      }
      resolve();
    });
  });
};

// --- Robust Save Post Function (Handles local save/update) ---
export const savePost = async (post: Post): Promise<{ status: 'success' | 'duplicate' | 'error', message: string }> => {
  console.log('[storage.ts] Attempting to save/update post locally:', { id: post.id, platform: post.platform });

  // Debug: Check if this is a thread post
  if (post.isThread) {
    console.log('[storage.ts] Saving thread post:', {
      id: post.id,
      isThread: post.isThread,
      threadId: post.threadId,
      threadPosition: post.threadPosition,
      threadLength: post.threadLength
    });
  }

  try {
    // Ensure core fields exist
    if (!post.id || !post.platform || !post.permalink) {
        console.error('[storage.ts] Post missing essential fields (id, platform, permalink):', post);
        return { status: 'error', message: 'Post is missing essential fields.' };
    }

    // Initialize arrays and savedAt if they don't exist
    post.savedAt = post.savedAt || new Date().toISOString();
    post.categories = post.categories || [];
    post.tags = post.tags || [];

    // Get current local posts
    const localResult = await storageLocalGet('localSavedPosts');
    const localPosts: Post[] = Array.isArray(localResult.localSavedPosts) ? localResult.localSavedPosts : [];

    const existingPostIndexById = localPosts.findIndex(p => p.id === post.id);

    if (existingPostIndexById !== -1) {
      // Post with the same ID exists, update it
      console.log(`[storage.ts] Updating existing post with ID: ${post.id} in local storage.`);
      localPosts[existingPostIndexById] = post;
    } else {
      // No post with this ID exists. Check for permalink duplicates before adding as new.
      if (localPosts.some(p => p.permalink === post.permalink)) {
        console.log(`[storage.ts] A different post with permalink '${post.permalink}' already exists in local storage. Cannot save new post with ID: ${post.id}.`);
        return { status: 'duplicate', message: `A different post with permalink '${post.permalink}' already exists.` };
      }
      console.log(`[storage.ts] Adding new post with ID: ${post.id} to local storage.`);
      localPosts.push(post);
    }

    // Save media items to IndexedDB if present
    if (post.media && post.media.length > 0) {
      console.log(`[storage.ts] Saving ${post.media.length} media items to IndexedDB for post ${post.id}`);
      const mediaPromises = post.media.map(async (mediaItem) => {
        if (mediaItem.url) {
          try {
            const isBase64 = mediaItem.isBase64 === true || mediaItem.url.startsWith('data:');
            await saveImageToIndexedDB(post.id, mediaItem.url, isBase64);
            return true;
          } catch (mediaError) {
            console.error(`[storage.ts] Error saving media item to IndexedDB for post ${post.id}, item ${mediaItem.url}:`, mediaError);
            return false; // Continue saving other media items
          }
        }
        return false;
      });
      const mediaResults = await Promise.all(mediaPromises);
      // Note: We proceed with saving post metadata even if some media items failed.
      // Error handling for media save failures should be managed by the caller or UI if critical.
    }

    // Commit changes to chrome.storage.local
    await storageLocalSet({ localSavedPosts: localPosts });
    return { status: 'success', message: 'Post saved/updated successfully in local storage.' };

  } catch (error) {
    console.error('[storage.ts] General error during local savePost process for ID:', post.id, error);
    return { status: 'error', message: `Failed to save/update post locally: ${error instanceof Error ? error.message : String(error)}` };
  }
};

// --- Get Saved Posts (Combine sync, local, legacy) ---
export const getSavedPosts = async (): Promise<Post[]> => {
  try {
    const syncResult = await storageGet('savedPosts');
    const syncPosts: Post[] = syncResult.savedPosts || [];

    const localResult = await storageLocalGet('localSavedPosts');
    const localPosts: Post[] = localResult.localSavedPosts || [];
    console.log('[storage.ts] Found additional posts in local storage:', localPosts.length);



    // Combine posts, prioritizing sync, then local, avoiding duplicates by permalink
    const allPostsMap = new Map<string, Post>();

    [...localPosts, ...syncPosts].forEach((post: Post) => { // Removed legacyPosts, added type for post
        if (post && typeof post.id === 'string' && post.id.trim() !== '' && post.permalink) {
            allPostsMap.set(post.permalink, post); // Newer entries overwrite older ones
        } else {
            console.warn('[storage.ts] getSavedPosts: Skipping post due to missing id or permalink:', post);
        }
    });

    const allPosts = Array.from(allPostsMap.values());
    const threadPostsInResult = allPosts.filter(p => p.isThread);
    console.log('[storage.ts] Thread posts in final result:', threadPostsInResult.length);

    console.log(`[storage.ts] Returning ${allPosts.length} unique posts after merging.`);
    return allPosts;

  } catch (error) {
    console.error('[storage.ts] Error retrieving posts:', error);
    // Basic fallback - consider trying only local or legacy if sync fails?
    return [];
  }
};

// --- Delete Post (from all locations) ---
export const deletePost = async (postId: string): Promise<boolean> => {
    console.log('[storage.ts] Attempting to delete post:', postId);
    if (!postId) {
        console.error('[storage.ts] Cannot delete post: No postId provided');
        return false;
    }

    let deleted = false;

    try {
        // Helper function to delete from a posts array
        const deleteFromPosts = (posts: Post[], id: string): { updatedPosts: Post[]; deleted: boolean } => {
            let deletedAny = false;

            // First try exact match
            let updatedPosts = posts.filter(post => {
                if (post.id === id) {
                    deletedAny = true;
                    return false;
                }
                return true;
            });

            // If no exact match, try platform-specific formats
            if (!deletedAny) {
                // Handle Pinterest format (pinterest_12345 or just 12345)
                if (id.startsWith('pinterest_')) {
                    const numericId = id.replace('pinterest_', '');
                    updatedPosts = posts.filter(post => {
                        if (post.id === numericId || post.id === id) {
                            deletedAny = true;
                            return false;
                        }
                        return true;
                    });
                } else if (/^\d+$/.test(id)) {
                    // If it's a numeric ID, try with pinterest_ prefix
                    const pinterestId = `pinterest_${id}`;
                    updatedPosts = posts.filter(post => {
                        if (post.id === pinterestId) {
                            deletedAny = true;
                            return false;
                        }
                        return true;
                    });
                }
            }

            return { updatedPosts, deleted: deletedAny };
        };

        // Process sync storage
        const syncResult = await storageGet('savedPosts');
        const syncPosts: Post[] = syncResult.savedPosts || [];
        const { updatedPosts: updatedSyncPosts, deleted: deletedFromSync } = deleteFromPosts(syncPosts, postId);

        if (deletedFromSync) {
            console.log('[storage.ts] Deleted from sync storage');
            await storageSet({ savedPosts: updatedSyncPosts });
            deleted = true;
        }

        // Process local storage
        const localResult = await storageLocalGet('localSavedPosts');
        const localPosts: Post[] = localResult.localSavedPosts || [];
        const { updatedPosts: updatedLocalPosts, deleted: deletedFromLocal } = deleteFromPosts(localPosts, postId);

        if (deletedFromLocal) {
            console.log('[storage.ts] Deleted from local storage');
            await storageLocalSet({ localSavedPosts: updatedLocalPosts });
            deleted = true;
        }

        if (!deleted) {
            console.log(`[storage.ts] Post ${postId} not found in any storage`);
        } else {
            console.log(`[storage.ts] Successfully deleted post: ${postId}`);
        }

        return deleted;
    } catch (error) {
        console.error('[storage.ts] Error deleting post:', error);
        throw error; // Re-throw to maintain backward compatibility
    }
};

// --- Check If Post Exists (Checks all locations by ID) ---
export const checkIfPostExists = async (postId: string): Promise<boolean> => {
  try {
    // Check sync storage
    const syncResult = await storageGet('savedPosts');
    const syncPosts: Post[] = syncResult.savedPosts || [];
    if (syncPosts.some((post: Post) => post.id === postId)) {
      return true;
    }

    // Check local storage
    const localResult = await storageLocalGet('localSavedPosts');
    const localPosts: Post[] = localResult.localSavedPosts || [];
    if (localPosts.some((post: Post) => post.id === postId)) {
      return true;
    }

    console.log('[storage.ts] Post not found in any storage.');
    return false;
  } catch (error) {
    console.error('[storage.ts] Error checking if post exists:', error);
    return false; // Default to false on error
  }
};

// --- Save Image to IndexedDB (Moved from reddit.ts) ---
export const saveImageToIndexedDB = async (postId: string, imageUrl: string, isBase64: boolean = false): Promise<void> => {
  console.log(`[storage.ts] Saving image to IndexedDB for post ${postId}: ${isBase64 ? 'base64 image' : imageUrl.substring(0, 50) + '...'}`);

  // Maximum number of retries for fetching images
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000; // 1 second delay between retries

  // Helper function to wait between retries
  const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // Helper function to attempt fetch with retries
  const fetchWithRetry = async (url: string, retries = MAX_RETRIES): Promise<Response> => {
    try {
      // Use a timeout to prevent hanging on slow connections
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      // Try different fetch options to handle CORS issues
      const fetchOptions: RequestInit = {
        method: 'GET',
        mode: 'cors',
        credentials: 'omit',
        signal: controller.signal,
        headers: {
          'Accept': 'image/*, */*',
        }
      };

      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }

      return response;
    } catch (error) {
      // If we have retries left and it's a network error, retry
      if (retries > 0 && (
          error instanceof TypeError || // Network error
          (error instanceof DOMException && error.name === 'AbortError') || // Timeout
          (error instanceof Error && error.message.includes('Failed to fetch'))
      )) {
        await wait(RETRY_DELAY);
        return fetchWithRetry(url, retries - 1);
      }

      // If it's a CORS error, try a different approach with a proxy
      if (error instanceof TypeError && error.message.includes('CORS')) {
        // Try using an image element as a fallback for CORS issues
        return await fetchViaImgElement(url);
      }

      // Otherwise, rethrow
      throw error;
    }
  };

  // Fallback method using Image element for CORS issues
  const fetchViaImgElement = (url: string): Promise<Response> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        // Create a canvas to convert the image to a blob
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          return reject(new Error('Failed to get canvas context'));
        }

        ctx.drawImage(img, 0, 0);

        // Convert canvas to blob
        canvas.toBlob(blob => {
          if (!blob) {
            return reject(new Error('Failed to convert image to blob'));
          }

          // Create a mock response
          const mockResponse = new Response(blob, {
            status: 200,
            statusText: 'OK',
            headers: new Headers({
              'Content-Type': blob.type,
              'Content-Length': String(blob.size)
            })
          });

          resolve(mockResponse);
        }, 'image/png');
      };

      img.onerror = () => {
        reject(new Error(`Failed to load image via Image element: ${url}`));
      };

      img.src = url;
    });
  };

  try {
    let blob: Blob;

    // Handle base64 images directly
    if (isBase64 && imageUrl.startsWith('data:')) {
      // Convert base64 to blob
      const base64Response = await fetch(imageUrl);
      blob = await base64Response.blob();
    } else {
      // Try to fetch the image with retry logic
      const response = await fetchWithRetry(imageUrl);
      blob = await response.blob();
    }

    // If the blob is empty or too small, it might be an error page
    if (blob.size < 100) { // Less than 100 bytes is suspicious
      // Image might be an error page
    }

    const dbPromise = new Promise<IDBDatabase>((resolve, reject) => {
      const request = indexedDB.open('social-saver-images', 2); // Increment version to force upgrade
      request.onerror = (event) => {
        console.error("[storage.ts] IndexedDB error:", event);
        reject(new Error("Failed to open IndexedDB"));
      };
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        console.log("[storage.ts] IndexedDB upgrade needed, creating/updating schema");

        // Delete existing object store if it exists (to ensure clean schema)
        if (db.objectStoreNames.contains('images')) {
          db.deleteObjectStore('images');
        }

        // Create the images object store with proper schema
        const imageStore = db.createObjectStore('images', { keyPath: 'id' });

        // Add indexes for better querying
        imageStore.createIndex('postId', 'postId', { unique: false });
        imageStore.createIndex('createdAt', 'createdAt', { unique: false });

        console.log("[storage.ts] IndexedDB schema updated successfully");
      };
      request.onsuccess = (event) => {
        resolve((event.target as IDBOpenDBRequest).result);
      };
    });

    const db = await dbPromise;
    const transaction = db.transaction(['images'], 'readwrite');
    const store = transaction.objectStore('images');

    // Create a more robust ID, less likely to collide if saved quickly
    // For base64 images, use a shorter ID to avoid exceeding IndexedDB key size limits
    const imageId = isBase64
      ? `${postId}_base64_${Date.now()}`
      : `${postId}_${imageUrl.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 100)}_${Date.now()}`;

    const storeRequest = store.put({
      id: imageId,
      postId: postId,
      blob: blob,
      url: imageUrl,
      isBase64: isBase64,
      createdAt: new Date().toISOString()
    });

    return new Promise<void>((resolve, reject) => {
      storeRequest.onsuccess = () => {
        resolve();
      };
      storeRequest.onerror = (event) => {
        console.error("[storage.ts] Error saving image to IndexedDB store:", event);
        reject(new Error("Failed to save image to IndexedDB store"));
      };
      transaction.oncomplete = () => {
        db.close();
      };
      transaction.onerror = (event) => {
        console.error("[storage.ts] IndexedDB transaction error:", event);
        // Don't reject here as storeRequest.onerror would have already rejected
        db.close();
      };
    });
  } catch (error) {
    console.error(`[storage.ts] Error in saveImageToIndexedDB for ${imageUrl}:`, error);
    // Log the error but don't throw to prevent stopping the post save process
    // We'll consider the image save as optional - the post data is more important
  }
};

// --- Function to retrieve image data URL from IndexedDB ---
export const getImageDataUrl = (imageId: string): Promise<string | null> => {
  return new Promise((resolve, reject) => {
    const dbPromise = new Promise<IDBDatabase>((resolveDB, rejectDB) => {
      const request = indexedDB.open('social-saver-images', 2); // Use same version as saveImageToIndexedDB
      request.onerror = (event) => {
        console.error("[storage.ts] IndexedDB error opening DB for getImageDataUrl:", event);
        rejectDB(new Error("Failed to open IndexedDB for getImageDataUrl"));
      };
      request.onupgradeneeded = (event) => { // Ensure store exists if called before save
        const db = (event.target as IDBOpenDBRequest).result;
        console.log("[storage.ts] IndexedDB upgrade needed in getImageDataUrl, creating/updating schema");

        // Delete existing object store if it exists (to ensure clean schema)
        if (db.objectStoreNames.contains('images')) {
          db.deleteObjectStore('images');
        }

        // Create the images object store with proper schema
        const imageStore = db.createObjectStore('images', { keyPath: 'id' });

        // Add indexes for better querying
        imageStore.createIndex('postId', 'postId', { unique: false });
        imageStore.createIndex('createdAt', 'createdAt', { unique: false });

        console.log("[storage.ts] IndexedDB schema updated successfully in getImageDataUrl");
      };
      request.onsuccess = (event) => {
        resolveDB((event.target as IDBOpenDBRequest).result);
      };
    });

    dbPromise.then(db => {
      if (!db.objectStoreNames.contains('images')) {
        db.close(); // Close DB before rejecting
        resolve(null); // Or reject, depending on desired behavior for missing store
        return;
      }
      const transaction = db.transaction('images', 'readonly');
      const store = transaction.objectStore('images');

      // Check if this is a post ID rather than an image ID
      if (imageId.indexOf('_') === -1) {
        // This might be a post ID, try to find all images for this post
        console.log(`[storage.ts] Looking for images by post ID: ${imageId}`);
        const getAllRequest = store.getAll();

        getAllRequest.onsuccess = () => {
          const images = getAllRequest.result;
          // Filter images by postId
          const postImages = images.filter(img => img.postId === imageId);

          if (postImages.length > 0) {
            console.log(`[storage.ts] Found ${postImages.length} images for post ID: ${imageId}`);
            // Use the first image
            const firstImage = postImages[0];

            if (firstImage.blob) {
              const reader = new FileReader();
              reader.onloadend = () => {
                console.log(`[storage.ts] Retrieved and converted image blob to dataURL for post ID: ${imageId}`);
                resolve(reader.result as string);
              };
              reader.onerror = (fileReaderError) => {
                console.error(`[storage.ts] FileReader error for post ID ${imageId}:`, fileReaderError);
                reject(new Error(`FileReader error for post ID ${imageId}`));
              };
              reader.readAsDataURL(firstImage.blob);
            } else if (firstImage.dataUrl) {
              console.log(`[storage.ts] Retrieved image data (as dataUrl) for post ID: ${imageId}`);
              resolve(firstImage.dataUrl);
            } else {
              console.warn(`[storage.ts] Found image record but no blob or dataUrl for post ID: ${imageId}`);
              resolve(null);
            }
          } else {
            console.warn(`[storage.ts] No images found for post ID: ${imageId}`);
            resolve(null);
          }
        };

        getAllRequest.onerror = (event) => {
          console.error(`[storage.ts] Error getting all images for post ID ${imageId}:`, event);
          reject(new Error(`Error getting all images: ${(event.target as IDBRequest)?.error?.message}`));
        };
      } else {
        // This is an image ID, get it directly
        const request = store.get(imageId);

        request.onsuccess = () => {
          if (request.result && request.result.blob) { // Assuming image is stored as blob, convert to dataURL
              const reader = new FileReader();
              reader.onloadend = () => {
                  console.log(`[storage.ts] Retrieved and converted image blob to dataURL for ID: ${imageId}`);
                  resolve(reader.result as string);
              };
              reader.onerror = (fileReaderError) => {
                  console.error(`[storage.ts] FileReader error for ID ${imageId}:`, fileReaderError);
                  reject(new Error(`FileReader error for image ID ${imageId}`));
              };
              reader.readAsDataURL(request.result.blob);
          } else if (request.result && request.result.dataUrl) { // Fallback if already stored as dataUrl
             console.log(`[storage.ts] Retrieved image data (as dataUrl) for ID: ${imageId}`);
             resolve(request.result.dataUrl);
          }
           else {
             console.warn(`[storage.ts] No image data found for ID: ${imageId}`);
             resolve(null);
          }
        };
      }

      // Add error handler for the direct get request
      if (imageId.indexOf('_') !== -1) {
        const request = store.get(imageId);
        request.onerror = (event) => {
          console.error(`[storage.ts] Error retrieving image data for ID ${imageId}:`, event);
          reject(new Error(`Error getting image data: ${(event.target as IDBRequest)?.error?.message}`));
        };
      }

      transaction.oncomplete = () => {
        db.close();
      };
      transaction.onerror = (event) => {
        console.error(`[storage.ts] Transaction error in getImageDataUrl for ID ${imageId}:`, event);
        db.close();
        // Rejecting here might be redundant if request.onerror already handled it
      };
    }).catch(dbOpenError => {
        console.error(`[storage.ts] Failed to open DB for getImageDataUrl (ID: ${imageId}):`, dbOpenError);
        reject(dbOpenError); // Propagate the error from dbPromise
    });
  });
};

// --- Helper function to sync post updates to cloud ---
const syncPostUpdateToCloud = async (postId: string, details: Partial<AnalyzedPost>): Promise<void> => {
  try {
    const tokenResult = await storageLocalGet('authToken');
    const token = tokenResult?.authToken as string | undefined;

    if (!token) {
      console.log(`[storage.ts] User not logged in. Skipping cloud sync for post ${postId} update.`);
      return;
    }

    let cloudPostId: string | undefined = undefined;
    let foundPost: Post | LocalPost | SyncPost | null = null;

    // Try to find the post in local storage to get its cloud ID
    const localResult = await storageLocalGet('localSavedPosts');
    const localPosts: AnalyzedPost[] = (Array.isArray(localResult.localSavedPosts) ? localResult.localSavedPosts : []) as AnalyzedPost[];
    const localPost = localPosts.find(p => p.id === postId);

    if (localPost && localPost._id) {
      foundPost = localPost;
      cloudPostId = typeof localPost._id === 'string' ? localPost._id : String(localPost._id);
    } else {
      // Try sync storage if not found or no cloudId in local
      const syncResult = await storageGet('savedPosts');
      const syncPosts: AnalyzedPost[] = (Array.isArray(syncResult.savedPosts) ? syncResult.savedPosts : []) as AnalyzedPost[];
      const syncPost = syncPosts.find(p => p.id === postId);
      if (syncPost && syncPost._id) {
        foundPost = syncPost;
        cloudPostId = typeof syncPost._id === 'string' ? syncPost._id : String(syncPost._id);
      }
    }

    if (!foundPost || !cloudPostId) {
      console.warn(`[storage.ts] Could not find post or its cloud ID for post ${postId}. Skipping cloud update.`);
      return;
    }

    console.log(`[storage.ts] Syncing update for post ${postId} (Cloud ID: ${cloudPostId}) with details:`, details);

    const response = await fetch(`${API_URL}/api/posts/${cloudPostId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(details),
    });

    if (response.ok) {
      console.log(`[storage.ts] Successfully synced update for post ${postId} (Cloud ID: ${cloudPostId}) to cloud.`);
      // const updatedPostData = await response.json(); // TODO: Use updatedPostData if needed for local state updates
      // Optionally, update local/sync storage with the response from PUT if needed
      // For example, if the PUT response contains the full updated post:
      // await updatePostInStorage(postId, updatedPostData, 'local');
      // await updatePostInStorage(postId, updatedPostData, 'sync');
    } else {
      const errorText = await response.text().catch(() => 'Failed to retrieve error text');
      console.error(`[storage.ts] Failed to sync update for post ${postId} (Cloud ID: ${cloudPostId}). Status: ${response.status}. Body: ${errorText}`);
    }
  } catch (error: unknown) {
    console.error(`[storage.ts] Error in syncPostUpdateToCloud for post ${postId}:`, error instanceof Error ? error.message : error);
  }
};

// --- NEW: Update Post Details (Categories, Tags, and AI Content) ---
// This function updates various details of a post, including AI-generated content.
export const updatePostDetails = async (postId: string, details: {
  categories?: CoreSubCategorySlug[];
  tags?: string[];
  snapNote?: string;
  notes?: string;
  inSight?: InSightData | null; // Changed from any
  fastTake?: string;
  contentIdeas?: string[];
  embeddingVector?: number[];
  analyzedAt?: string;
}): Promise<void> => {
  if (details.notes !== undefined && typeof window !== 'undefined' && window.location.search.includes('debug=notes')) {
    console.log(`[NOTES] Updating notes for post ${postId}:`, details.notes);
  }
  let postUpdated = false;

  const updateLogic = (post: Post): Post => {
    return {
      ...post,
      // Only update fields that are provided in the details object
      ...(details.categories !== undefined && { categories: details.categories }),
      ...(details.tags !== undefined && { tags: details.tags }),
      ...(details.snapNote !== undefined && { snapNote: details.snapNote }),
      ...(details.notes !== undefined && { notes: details.notes }),
      ...(details.inSight !== undefined && { inSight: details.inSight }),
      ...(details.fastTake !== undefined && { fastTake: details.fastTake }),
      ...(details.contentIdeas !== undefined && { contentIdeas: details.contentIdeas }),
      ...(details.embeddingVector !== undefined && { embeddingVector: details.embeddingVector }),
      ...(details.analyzedAt !== undefined && { analyzedAt: details.analyzedAt }),
    };
  };

  try {
    // Try updating in Sync Storage
    const syncResult = await storageGet('savedPosts');
    const syncPosts: Post[] = Array.isArray(syncResult.savedPosts) ? syncResult.savedPosts : [];
    const syncIndex = syncPosts.findIndex(p => p.id === postId);

    if (syncIndex !== -1) {
      if (details.notes !== undefined && typeof window !== 'undefined' && window.location.search.includes('debug=notes')) {
        console.log('[NOTES] Found post in sync storage, updating notes');
      }
      syncPosts[syncIndex] = updateLogic(syncPosts[syncIndex]);
      await storageSet({ savedPosts: syncPosts });
      postUpdated = true;
    }

    // Try updating in Local Storage
    const localResult = await storageLocalGet('localSavedPosts');
    const localPosts: Post[] = Array.isArray(localResult.localSavedPosts) ? localResult.localSavedPosts : [];
    const localIndex = localPosts.findIndex(p => p.id === postId);

    if (localIndex !== -1) {
      if (details.notes !== undefined && typeof window !== 'undefined' && window.location.search.includes('debug=notes')) {
        console.log('[NOTES] Found post in local storage, updating notes');
      }
      localPosts[localIndex] = updateLogic(localPosts[localIndex]);
      await storageLocalSet({ localSavedPosts: localPosts });
      postUpdated = true;
    }

    if (!postUpdated) {
      if (details.notes !== undefined && typeof window !== 'undefined' && window.location.search.includes('debug=notes')) {
        console.error(`[NOTES] Post ${postId} not found in any storage location`);
      }
    }

    // Sync the update to cloud (non-blocking)
    syncPostUpdateToCloud(postId, details).catch(error => {
      console.error(`[storage.ts] Failed to sync post ${postId} update to cloud:`, error);
    });

  } catch (error) {
    console.error(`[storage.ts] Error updating details for post ${postId}:`, error);
    throw error; // Re-throw to indicate failure
  }
};

// --- Category Management ---

const CATEGORIES_STORAGE_KEY = 'userCategories';

// Function to get all unique categories created by the user
export const getAllCategories = async (): Promise<string[]> => {
  try {
    const result = await storageLocalGet(CATEGORIES_STORAGE_KEY);
    const storedValue = result[CATEGORIES_STORAGE_KEY];
    const categories: string[] = Array.isArray(storedValue)
        ? storedValue.filter((item): item is string => typeof item === 'string' && item.trim() !== '')
        : [];
    return [...new Set(categories)]; // Already filtered for non-empty strings
  } catch (error) {
    console.error('[storage.ts] Error getting all categories:', error);
    return [];
  }
};

// Function to save the master list of categories
export const saveAllCategories = async (categories: string[]): Promise<void> => {
  try {
    // Ensure uniqueness and filter out empties before saving
    const uniqueCategories = [...new Set(categories)].filter(cat => typeof cat === 'string' && cat.trim() !== '');
    await storageLocalSet({ [CATEGORIES_STORAGE_KEY]: uniqueCategories });
  } catch (error) {
    console.error('[storage.ts] Error saving categories:', error);
    throw error; // Re-throw to indicate failure
  }
};

// --- Tag Management ---

const TAGS_STORAGE_KEY = 'userTags';

// Function to get all unique tags created by the user
export const getAllTags = async (): Promise<string[]> => {
  try {
    const result = await storageLocalGet(TAGS_STORAGE_KEY);
    const storedValue = result[TAGS_STORAGE_KEY];
    const tags: string[] = Array.isArray(storedValue)
        ? storedValue.filter((item): item is string => typeof item === 'string' && item.trim() !== '')
        : [];
    return [...new Set(tags)]; // Already filtered for non-empty strings
  } catch (error) {
    console.error('[storage.ts] Error getting all tags:', error);
    return [];
  }
};

// Function to save the master list of tags
export const saveAllTags = async (tags: string[]): Promise<void> => {
  try {
    // Ensure uniqueness and filter out empties before saving
    const uniqueTags = [...new Set(tags)].filter(tag => typeof tag === 'string' && tag.trim() !== '');
    await storageLocalSet({ [TAGS_STORAGE_KEY]: uniqueTags });
  } catch (error) {
    console.error('[storage.ts] Error saving tags:', error);
    throw error; // Re-throw to indicate failure
  }
};
