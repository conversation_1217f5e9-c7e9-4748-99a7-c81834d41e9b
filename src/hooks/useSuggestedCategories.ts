import { useState, useEffect, useCallback } from 'react';
import { Post } from '../types'; // Assuming Post type is available
// import { getSavedPosts } from '../storage'; // Actual function to get posts

// Mock for getSavedPosts until integrated
const MOCK_POST_COUNT_FOR_SUGGESTIONS = 35;
const getMockSavedPosts = async (): Promise<Post[]> => {
  // Simulate fetching posts with varying categories
  // This mock should be replaced with actual calls to storage
  console.log('[useSuggestedCategories] Using MOCK posts for suggestions.');
  const mockPosts: Post[] = [];
  const possibleCategories = ['news', 'tech_innovation', 'culture_lifestyle', 'sports', 'misc'];
  for (let i = 0; i < MOCK_POST_COUNT_FOR_SUGGESTIONS; i++) {
    const postCategories: string[] = [];
    // Simulate a dominant category ('news') appearing > 60% of the time
    if (Math.random() < 0.7) { // news appears 70% of the time
      postCategories.push('news');
    }
    if (Math.random() < 0.3) { // tech_innovation appears 30% of the time
      postCategories.push('tech_innovation');
    }
    if (Math.random() < 0.2) { // culture_lifestyle appears 20% of the time
      postCategories.push(possibleCategories[Math.floor(Math.random() * 3) + 2]);
    }
    mockPosts.push({
      id: `mock_post_${i}`,
      platform: 'X/Twitter',
      savedAt: new Date().toISOString(),
      permalink: `http://example.com/post/${i}`,
      categories: postCategories,
      // ... other required Post fields can be minimal for this mock
    } as Post);
  }
  return mockPosts;
};

interface CategoryFrequency {
  [category: string]: number;
}

const MIN_POSTS_FOR_SUGGESTIONS = 30;
const SUGGESTION_THRESHOLD_PERCENTAGE = 0.6; // 60%

/**
 * A React hook to suggest categories based on user's saving history.
 * Rules:
 * - Tracks category frequency after a user has saved ≥ 30 posts.
 * - Suggests a category if it appears in > 60% of their saves.
 */
export function useSuggestedCategories(userId?: string /* Optional: if suggestions are user-specific and posts are fetched per user */) {
  const [suggestedCategories, setSuggestedCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const calculateSuggestions = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // const allPosts = await getSavedPosts(); // TODO: Replace with actual post fetching
      const allPosts = await getMockSavedPosts(); // Using mock for now
      
      if (allPosts.length < MIN_POSTS_FOR_SUGGESTIONS) {
        setSuggestedCategories([]);
        setIsLoading(false);
        console.log(`[useSuggestedCategories] Not enough posts (${allPosts.length}) to make suggestions. Minimum: ${MIN_POSTS_FOR_SUGGESTIONS}`);
        return;
      }

      const frequency: CategoryFrequency = {};
      let postsWithCategoriesCount = 0;

      allPosts.forEach(post => {
        if (post.categories && post.categories.length > 0) {
          postsWithCategoriesCount++;
          post.categories.forEach(category => {
            frequency[category] = (frequency[category] || 0) + 1;
          });
        }
      });

      if (postsWithCategoriesCount === 0) {
        setSuggestedCategories([]);
        setIsLoading(false);
        console.log('[useSuggestedCategories] No posts with categories found to calculate frequency.');
        return;
      }

      const suggestions: string[] = [];
      for (const category in frequency) {
        if ((frequency[category] / postsWithCategoriesCount) > SUGGESTION_THRESHOLD_PERCENTAGE) {
          suggestions.push(category);
        }
      }
      
      console.log('[useSuggestedCategories] Calculated Frequencies:', frequency);
      console.log('[useSuggestedCategories] Suggestions:', suggestions);
      setSuggestedCategories(suggestions);

    } catch (err) {
      console.error('[useSuggestedCategories] Error calculating suggestions:', err);
      setError((err as Error).message || 'Failed to calculate suggestions');
      setSuggestedCategories([]);
    } finally {
      setIsLoading(false);
    }
  }, [userId]); // Dependency on userId if posts are fetched based on it

  useEffect(() => {
    calculateSuggestions();
  }, [calculateSuggestions]);

  return { suggestedCategories, isLoading, error, refreshSuggestions: calculateSuggestions };
} 