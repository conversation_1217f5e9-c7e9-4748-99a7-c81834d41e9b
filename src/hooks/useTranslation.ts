import { useLocale } from '../contexts/useLocale';
import t, { SupportedLocale, setLocale } from '../utils/translation';

export const useTranslation = () => {
  const { locale, setLocale: setContextLocale } = useLocale();

  const translate = (key: string): string => {
    return t(key, locale);
  };

  const changeLocale = async (newLocale: SupportedLocale) => {
    await setLocale(newLocale);
    setContextLocale(newLocale);
  };

  return {
    t: translate,
    locale: locale as SupportedLocale,
    changeLocale
  };
};

export default useTranslation;
