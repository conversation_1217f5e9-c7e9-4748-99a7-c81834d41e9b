import { useState, useCallback, useEffect } from 'react';

export interface DraggableItem {
  id: string;
  type: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  data?: any;
}

export interface UseDragAndDropReturn {
  items: DraggableItem[];
  draggedItem: DraggableItem | null;
  isDragging: boolean;
  handleDragStart: (item: DraggableItem, event: React.DragEvent) => void;
  handleDragEnd: (event: React.DragEvent) => void;
  handleDragOver: (event: React.DragEvent) => void;
  handleDrop: (event: React.DragEvent, targetId: string) => void;
  updateItemPosition: (id: string, position: { x: number; y: number }) => void;
  addItem: (item: Omit<DraggableItem, 'id'>) => void;
  removeItem: (id: string) => void;
  reorderItems: (startIndex: number, endIndex: number) => void;
}

export const useDragAndDrop = (initialItems: DraggableItem[] = []): UseDragAndDropReturn => {
  const [items, setItems] = useState<DraggableItem[]>(initialItems);
  const [draggedItem, setDraggedItem] = useState<DraggableItem | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Update items when initialItems changes
  useEffect(() => {
    setItems(initialItems);
  }, [initialItems]);

  const handleDragStart = useCallback((item: DraggableItem, event: React.DragEvent) => {
    setDraggedItem(item);
    setIsDragging(true);

    // Set drag data
    event.dataTransfer.setData('text/plain', item.id);
    event.dataTransfer.effectAllowed = 'move';

    // Create a custom drag image (optional)
    const dragImage = event.currentTarget.cloneNode(true) as HTMLElement;
    dragImage.style.opacity = '0.8';
    dragImage.style.transform = 'rotate(5deg)';
    document.body.appendChild(dragImage);
    event.dataTransfer.setDragImage(dragImage, 0, 0);

    // Clean up the drag image after a short delay
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  }, []);

  const handleDragEnd = useCallback((event: React.DragEvent) => {
    setDraggedItem(null);
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDrop = useCallback((event: React.DragEvent, targetId: string) => {
    event.preventDefault();

    const draggedId = event.dataTransfer.getData('text/plain');
    if (!draggedId || !draggedItem) return;

    const startIndex = items.findIndex(item => item.id === draggedId);
    const endIndex = items.findIndex(item => item.id === targetId);

    if (startIndex === -1 || endIndex === -1) {
      setDraggedItem(null);
      setIsDragging(false);
      return;
    }

    // Reorder items if needed
    if (startIndex !== endIndex) {
      setItems(prevItems => {
        const newItems = Array.from(prevItems);
        const [removed] = newItems.splice(startIndex, 1);
        newItems.splice(endIndex, 0, removed);
        return newItems;
      });
    }

    setDraggedItem(null);
    setIsDragging(false);
  }, [draggedItem, items]);

  const updateItemPosition = useCallback((id: string, position: { x: number; y: number }) => {
    setItems(prevItems =>
      prevItems.map(item =>
        item.id === id
          ? { ...item, position }
          : item
      )
    );
  }, []);

  const addItem = useCallback((newItem: Omit<DraggableItem, 'id'>) => {
    const item: DraggableItem = {
      ...newItem,
      id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
    setItems(prevItems => [...prevItems, item]);
  }, []);

  const removeItem = useCallback((id: string) => {
    setItems(prevItems => prevItems.filter(item => item.id !== id));
  }, []);

  const reorderItems = useCallback((startIndex: number, endIndex: number) => {
    setItems(prevItems => {
      const result = Array.from(prevItems);
      const [removed] = result.splice(startIndex, 1);
      result.splice(endIndex, 0, removed);
      return result;
    });
  }, []);

  return {
    items,
    draggedItem,
    isDragging,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDrop,
    updateItemPosition,
    addItem,
    removeItem,
    reorderItems
  };
};
