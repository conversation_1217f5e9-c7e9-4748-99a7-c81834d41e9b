import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  displayName?: string;
  role: 'user' | 'admin';
  plan: 'free' | 'premium';
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  adsDisabled: boolean;
  emailVerified: boolean;
}

interface WebAuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
  getToken: () => string | null;
}

const WebAuthContext = createContext<WebAuthContextType | null>(null);

export const useWebAuth = (): WebAuthContextType => {
  const context = useContext(WebAuthContext);
  if (!context) {
    throw new Error('useWebAuth must be used within a WebAuthProvider');
  }
  return context;
};

interface WebAuthProviderProps {
  children: ReactNode;
}

const API_URL = 'https://api.notely.social';

export const WebAuthProvider: React.FC<WebAuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check for auth token in URL (from Google OAuth redirect)
        const urlParams = new URLSearchParams(window.location.search);
        const authTokenFromUrl = urlParams.get('auth_token');

        if (authTokenFromUrl) {
          // Store the token and remove it from URL
          localStorage.setItem('authToken', authTokenFromUrl);
          // Clean up the URL
          window.history.replaceState({}, document.title, window.location.pathname);
          await fetchUserProfile(authTokenFromUrl);
        } else {
          // Check for existing token in localStorage
          const token = localStorage.getItem('authToken');
          if (token) {
            await fetchUserProfile(token);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        localStorage.removeItem('authToken');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const fetchUserProfile = async (token: string) => {
    try {
      const response = await fetch(`${API_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem('user_profile', JSON.stringify(userData));
      } else {
        throw new Error('Failed to fetch user profile');
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      localStorage.removeItem('authToken');
      localStorage.removeItem('user_profile');
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      if (data.token) {
        localStorage.setItem('authToken', data.token);
        await fetchUserProfile(data.token);
      } else {
        throw new Error('No token received');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, displayName: string): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, displayName }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      if (data.token) {
        localStorage.setItem('authToken', data.token);
        await fetchUserProfile(data.token);
      } else {
        throw new Error('No token received');
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    try {
      localStorage.removeItem('authToken');
      localStorage.removeItem('user_profile');
      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getToken = (): string | null => {
    return localStorage.getItem('authToken');
  };

  const value = {
    isAuthenticated,
    isLoading,
    user,
    login,
    register,
    logout,
    getToken,
  };

  return <WebAuthContext.Provider value={value}>{children}</WebAuthContext.Provider>;
};
