import React from 'react';
import ReactDOM from 'react-dom/client';

// Import only essential styles
import '../index.css';

// Simple landing component without any external dependencies
const SimpleLanding: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-950 via-black to-zinc-900 text-white">
      <div className="max-w-7xl mx-auto px-6 py-20">
        <h1 className="text-6xl font-bold mb-8">
          Your social media
          <br />
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-400">
            second brain
          </span>
        </h1>
        
        <p className="text-xl text-gray-300 mb-12 max-w-2xl">
          Stop losing brilliant posts in the endless scroll.
          <span className="text-white font-medium"> Save, organize, and rediscover</span> the content that matters with AI-powered intelligence.
        </p>
        
        <button className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-10 py-4 rounded-2xl font-bold text-lg hover:scale-105 transition-all duration-300">
          Add to Chrome — Free
        </button>
        
        <div className="mt-20">
          <h2 className="text-3xl font-bold mb-8">How It Works</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Click the Extension</h3>
              <p className="text-gray-300">One click to save any post from any platform.</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">AI Auto-categorizes</h3>
              <p className="text-gray-300">Our AI automatically organizes your content.</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Search & Reuse</h3>
              <p className="text-gray-300">Find exactly what you need instantly.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <SimpleLanding />
  </React.StrictMode>
); 