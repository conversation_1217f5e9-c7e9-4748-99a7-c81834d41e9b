import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css';
import '../styles/notely-theme.css';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import LandingPage from './components/LandingPage';
import WebDashboard from './components/WebDashboard';
import WebLogin from './components/WebLogin';
import WebSettings from './components/WebSettings';
import WebForgotPassword from './components/WebForgotPassword';
import WebResetPassword from './components/WebResetPassword';
import AdminOverview from './components/AdminOverview';
import { WebAuthProvider } from './context/WebAuthContext';

const App: React.FC = () => {
  return (
    <WebAuthProvider>
      <Router>
        <div className="min-h-screen bg-notely-bg">
          <Routes>
            <Route path="/" element={<LandingPage />} />
            <Route path="/dashboard" element={<WebDashboard />} />
            <Route path="/dashboard/login" element={<WebLogin />} />
            <Route path="/dashboard/register" element={<WebLogin />} />
            <Route path="/dashboard/forgot-password" element={<WebForgotPassword />} />
            <Route path="/dashboard/reset-password" element={<WebResetPassword />} />
            <Route path="/dashboard/settings" element={<WebSettings />} />
            <Route path="/admin" element={<AdminOverview />} />
            {/* Legacy routes for backwards compatibility */}
            <Route path="/login" element={<Navigate to="/dashboard/login" replace />} />
            <Route path="/register" element={<Navigate to="/dashboard/register" replace />} />
            <Route path="/settings" element={<Navigate to="/dashboard/settings" replace />} />
          </Routes>
        </div>
      </Router>
    </WebAuthProvider>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
