import React, { useState, useEffect } from 'react';
import { useWebAuth } from '../context/WebAuthContext';

interface FlaggedUser {
  _id: string;
  email: string;
  name: string;
  displayName?: string;
  plan: 'free' | 'premium';
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  isFlagged: boolean;
  lastFlaggedReason?: string;
  flaggedAt?: string;
  createdAt: string;
  lastLogin: string;
  storageUsed: number;
  storageLimit: number;
}

interface AbuseStats {
  totalFlagged: number;
  flaggedToday: number;
  flaggedThisWeek: number;
  mostCommonReasons: Array<{ reason: string; count: number }>;
}

interface UserAbuseHistory {
  userId: string;
  email: string;
  currentlyFlagged: boolean;
  lastFlaggedReason?: string;
  flaggedAt?: string;
  currentViolations: string[];
  shouldFlag: boolean;
  userStats: {
    storageUsed: number;
    storageLimit: number;
    plan: string;
    createdAt: string;
    lastLogin: string;
  };
}

const AdminFlaggedUsers: React.FC = () => {
  const { getToken } = useWebAuth();
  const [flaggedUsers, setFlaggedUsers] = useState<FlaggedUser[]>([]);
  const [abuseStats, setAbuseStats] = useState<AbuseStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserAbuseHistory | null>(null);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [showFlagForm, setShowFlagForm] = useState(false);
  const [flagForm, setFlagForm] = useState({
    userId: '',
    reason: '',
  });

  useEffect(() => {
    fetchFlaggedUsers();
    fetchAbuseStats();
  }, []);

  const fetchFlaggedUsers = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const response = await fetch('/admin/users/flagged', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFlaggedUsers(data.users);
      } else {
        throw new Error('Failed to fetch flagged users');
      }
    } catch (err) {
      console.error('Error fetching flagged users:', err);
      setError('Failed to load flagged users');
    } finally {
      setLoading(false);
    }
  };

  const fetchAbuseStats = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/abuse/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAbuseStats(data.stats);
      }
    } catch (err) {
      console.error('Error fetching abuse stats:', err);
    }
  };

  const fetchUserAbuseHistory = async (userId: string) => {
    try {
      setLoading(true);
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/abuse-history`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedUser(data.history);
        setShowUserDetails(true);
      } else {
        throw new Error('Failed to fetch user abuse history');
      }
    } catch (err) {
      console.error('Error fetching user abuse history:', err);
      setError('Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  const unflagUser = async (userId: string) => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/unflag`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        await fetchFlaggedUsers();
        await fetchAbuseStats();
        setShowUserDetails(false);
        setSelectedUser(null);
      } else {
        throw new Error('Failed to unflag user');
      }
    } catch (err) {
      console.error('Error unflagging user:', err);
      setError('Failed to unflag user');
    }
  };

  const flagUser = async (userId: string, reason: string) => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/flag`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      });

      if (response.ok) {
        await fetchFlaggedUsers();
        await fetchAbuseStats();
        setShowFlagForm(false);
        setFlagForm({ userId: '', reason: '' });
      } else {
        throw new Error('Failed to flag user');
      }
    } catch (err) {
      console.error('Error flagging user:', err);
      setError('Failed to flag user');
    }
  };

  const triggerAbuseCheck = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/abuse/check', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setError('Abuse detection check initiated. New violations will appear shortly.');
        setTimeout(() => {
          fetchFlaggedUsers();
          fetchAbuseStats();
        }, 30000); // Refresh after 30 seconds
      } else {
        throw new Error('Failed to trigger abuse check');
      }
    } catch (err) {
      console.error('Error triggering abuse check:', err);
      setError('Failed to trigger abuse check');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatStorageSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    const gb = mb / 1024;
    return gb >= 1 ? `${gb.toFixed(2)} GB` : `${mb.toFixed(0)} MB`;
  };

  const getViolationSeverity = (violations: string[]) => {
    const highSeverityKeywords = ['excessive', 'abuse', 'suspicious'];
    return violations.some(v => 
      highSeverityKeywords.some(keyword => v.toLowerCase().includes(keyword))
    ) ? 'high' : 'medium';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">🚨 Flagged Users</h2>
          <p className="text-gray-600">Monitor and manage users flagged for suspicious activity</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={triggerAbuseCheck}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            🔍 Run Abuse Check
          </button>
          <button
            onClick={() => setShowFlagForm(true)}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            🚩 Flag User
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {error.includes('initiated') ? 'Info' : 'Error'}
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistics Cards */}
      {abuseStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <span className="text-2xl">🚨</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-red-600">Total Flagged</p>
                <p className="text-2xl font-bold text-red-900">{abuseStats.totalFlagged}</p>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <span className="text-2xl">📅</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-orange-600">Flagged Today</p>
                <p className="text-2xl font-bold text-orange-900">{abuseStats.flaggedToday}</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-yellow-600">This Week</p>
                <p className="text-2xl font-bold text-yellow-900">{abuseStats.flaggedThisWeek}</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">🔍</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-blue-600">Top Reasons</p>
                <p className="text-sm text-blue-900">
                  {abuseStats.mostCommonReasons[0]?.reason?.split(':')[0] || 'None'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Flagged Users Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Flagged Users ({flaggedUsers.length})</h3>
          </div>

          {loading ? (
            <div className="text-center py-4">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              <p className="mt-2 text-gray-600">Loading flagged users...</p>
            </div>
          ) : flaggedUsers.length === 0 ? (
            <div className="text-center py-8">
              <span className="text-6xl">✅</span>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No flagged users</h3>
              <p className="mt-1 text-sm text-gray-500">All users are currently in good standing.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Flagged Reason
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Flagged Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Storage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {flaggedUsers.map((user) => (
                    <tr key={user._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {user.displayName || user.name}
                            </div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.plan === 'premium' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {user.plan}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate">
                          {user.lastFlaggedReason || 'No reason provided'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.flaggedAt ? formatDate(user.flaggedAt) : 'Unknown'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatStorageSize(user.storageUsed)} / {formatStorageSize(user.storageLimit)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => fetchUserAbuseHistory(user._id)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View Details
                        </button>
                        <button
                          onClick={() => unflagUser(user._id)}
                          className="text-green-600 hover:text-green-900"
                        >
                          Unflag
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* User Details Modal */}
      {showUserDetails && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border border-[#2F2F2F] w-11/12 max-w-4xl shadow-lg rounded-notely-xl bg-notely-card">
            <div className="flex justify-between items-center mb-4">
                              <h3 className="text-lg font-bold text-notely-text-primary">User Abuse Details</h3>
              <button
                className="text-gray-400 hover:text-gray-600"
                onClick={() => setShowUserDetails(false)}
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-6">
              {/* User Info */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">User Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Email:</span> {selectedUser.email}
                  </div>
                  <div>
                    <span className="font-medium">Plan:</span> {selectedUser.userStats.plan}
                  </div>
                  <div>
                    <span className="font-medium">Member Since:</span> {formatDate(selectedUser.userStats.createdAt)}
                  </div>
                  <div>
                    <span className="font-medium">Last Login:</span> {formatDate(selectedUser.userStats.lastLogin)}
                  </div>
                  <div>
                    <span className="font-medium">Storage:</span> {formatStorageSize(selectedUser.userStats.storageUsed)}
                  </div>
                  <div>
                    <span className="font-medium">Status:</span>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                      selectedUser.currentlyFlagged ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {selectedUser.currentlyFlagged ? 'Flagged' : 'Clean'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Current Violations */}
              {selectedUser.currentViolations.length > 0 && (
                <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                  <h4 className="font-medium text-red-900 mb-3">🚨 Current Violations</h4>
                  <ul className="space-y-2">
                    {selectedUser.currentViolations.map((violation, index) => (
                      <li key={index} className="text-sm text-red-800">
                        • {violation}
                      </li>
                    ))}
                  </ul>
                  {selectedUser.shouldFlag && !selectedUser.currentlyFlagged && (
                    <div className="mt-3 p-3 bg-yellow-100 border border-yellow-200 rounded">
                      <p className="text-sm text-yellow-800">
                        ⚠️ This user should be flagged based on current violations.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Flag History */}
              {selectedUser.currentlyFlagged && (
                <div className="bg-orange-50 border border-orange-200 p-4 rounded-lg">
                  <h4 className="font-medium text-orange-900 mb-3">🚩 Flag History</h4>
                  <div className="text-sm text-orange-800">
                    <p><span className="font-medium">Reason:</span> {selectedUser.lastFlaggedReason}</p>
                    <p><span className="font-medium">Date:</span> {selectedUser.flaggedAt ? formatDate(selectedUser.flaggedAt) : 'Unknown'}</p>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                {selectedUser.currentlyFlagged ? (
                  <button
                    onClick={() => unflagUser(selectedUser.userId)}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    ✅ Unflag User
                  </button>
                ) : selectedUser.shouldFlag && (
                  <button
                    onClick={() => {
                      setFlagForm({
                        userId: selectedUser.userId,
                        reason: selectedUser.currentViolations.join('; '),
                      });
                      setShowFlagForm(true);
                      setShowUserDetails(false);
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    🚩 Flag User
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Flag User Modal */}
      {showFlagForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border border-[#2F2F2F] w-96 shadow-lg rounded-notely-xl bg-notely-card">
            <div className="flex justify-between items-center mb-4">
                              <h3 className="text-lg font-bold text-notely-text-primary">🚩 Flag User</h3>
              <button
                className="text-gray-400 hover:text-gray-600"
                onClick={() => setShowFlagForm(false)}
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={(e) => {
              e.preventDefault();
              flagUser(flagForm.userId, flagForm.reason);
            }}>
              <div className="space-y-4">
                <div>
                  <label htmlFor="userId" className="block text-sm font-medium text-gray-700 mb-1">
                    User ID
                  </label>
                  <input
                    type="text"
                    id="userId"
                    value={flagForm.userId}
                    onChange={(e) => setFlagForm({ ...flagForm, userId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Enter user ID"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
                    Reason for Flagging
                  </label>
                  <textarea
                    id="reason"
                    value={flagForm.reason}
                    onChange={(e) => setFlagForm({ ...flagForm, reason: e.target.value })}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Describe the reason for flagging this user..."
                    required
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowFlagForm(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm font-medium"
                >
                  🚩 Flag User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminFlaggedUsers; 