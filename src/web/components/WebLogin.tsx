import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useWebAuth } from '../context/WebAuthContext';

const WebLogin: React.FC = () => {
  const { isAuthenticated, isLoading, login, register } = useWebAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRegisterMode, setIsRegisterMode] = useState(false);
  const [showPremiumOffer, setShowPremiumOffer] = useState(false);

  // Check if user is already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as { from?: Location })?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleLocalLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      await login(email, password);
      // Navigation will be handled by the useEffect above
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Login failed');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      if (!displayName.trim()) {
        throw new Error('Display name is required');
      }

      await register(email, password, displayName);
      setShowPremiumOffer(true);
      // Navigation will be handled by the useEffect above
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Registration failed');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleLogin = () => {
    setError(null);
    setIsSubmitting(true);

    // Set flags for Google auth handling
    sessionStorage.setItem('acceptGoogleAuth', 'true');
    localStorage.setItem('acceptingAuthMessages', 'true');

    try {
      const API_URL = process.env.REACT_APP_API_URL || 'https://api.notely.social';
      const googleAuthUrl = `${API_URL}/auth/google/web?prompt=select_account`;
      
      // For web, redirect directly to Google auth
      window.location.href = googleAuthUrl;
    } catch (error: unknown) {
      console.error('Error initiating Google login:', error);
      setError(error instanceof Error ? error.message : 'Failed to initiate Google login');
      setIsSubmitting(false);
      // Clear auth flags if there was an error
      sessionStorage.removeItem('acceptGoogleAuth');
      localStorage.removeItem('acceptingAuthMessages');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-notely-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-notely-lavender border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-notely-background flex items-center justify-center p-4">
      <div className="w-full max-w-sm mx-auto px-6 py-8 bg-notely-card rounded-xl shadow-lg border border-notely-border">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-lg font-semibold text-notely-text-primary mb-1">
            {isRegisterMode ? 'Create Account' : 'Welcome back'}
          </h1>
          <p className="text-sm text-notely-text-secondary">
            {isRegisterMode ? 'Join Notely today' : 'Sign in to continue'}
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
            {error}
          </div>
        )}

        {/* Premium Offer */}
        {showPremiumOffer && (
          <div className="mb-4 p-3 bg-gradient-to-r from-notely-lavender/10 to-notely-sky/10 border border-notely-lavender/20 rounded-lg">
            <p className="text-sm text-notely-text-primary font-medium mb-2">🎉 Welcome to Notely!</p>
            <p className="text-xs text-notely-text-secondary">
              Get started with our free plan or upgrade to Premium for unlimited saves and AI features.
            </p>
          </div>
        )}

        {/* Form */}
        <form onSubmit={isRegisterMode ? handleRegister : handleLocalLogin} className="space-y-4 mb-6">
          {isRegisterMode && (
            <div>
              <label htmlFor="displayName" className="block text-sm font-medium text-notely-text-primary mb-1">
                Display Name
              </label>
              <input
                type="text"
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                required
                disabled={isSubmitting}
                className="w-full px-3 py-2 border border-notely-border bg-notely-surface rounded-xl focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:border-notely-lavender disabled:opacity-50 transition-all text-sm"
                placeholder="Enter your name"
              />
            </div>
          )}
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-notely-text-primary mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-notely-border bg-notely-surface rounded-xl focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:border-notely-lavender disabled:opacity-50 transition-all text-sm"
              placeholder="Enter your email"
            />
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-notely-text-primary mb-1">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-notely-border bg-notely-surface rounded-xl focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:border-notely-lavender disabled:opacity-50 transition-all text-sm"
              placeholder={isRegisterMode ? "Min 6 characters" : "Enter password"}
            />
          </div>
          
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full py-2.5 px-4 bg-notely-lavender text-white font-medium rounded-xl hover:bg-notely-lavender/90 focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:ring-offset-2 disabled:opacity-50 transition-all text-sm"
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-2"></div>
                {isRegisterMode ? 'Creating...' : 'Signing in...'}
              </span>
            ) : (
              isRegisterMode ? 'Create Account' : 'Sign In'
            )}
          </button>
          
          {/* Forgot Password */}
          {!isRegisterMode && (
            <div className="text-center">
              <button
                type="button"
                onClick={() => navigate('/dashboard/forgot-password')}
                className="text-sm text-notely-text-muted hover:text-notely-lavender hover:underline transition-colors"
              >
                Forgot password?
              </button>
            </div>
          )}
        </form>

        {/* Google Login */}
        <button
          onClick={handleGoogleLogin}
          disabled={isSubmitting}
          className="w-full py-2.5 px-4 border border-notely-border bg-notely-surface text-notely-text-primary rounded-xl hover:bg-notely-surface/80 focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:ring-offset-2 disabled:opacity-50 transition-all text-sm font-medium flex items-center justify-center"
        >
          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          Continue with Google
        </button>

        {/* Toggle Mode */}
        <div className="mt-6 text-center">
          <p className="text-sm text-notely-text-muted">
            {isRegisterMode ? 'Already have an account?' : "Don't have an account?"}
          </p>
          <button
            onClick={() => {
              setIsRegisterMode(!isRegisterMode);
              setError(null);
              setEmail('');
              setPassword('');
              setDisplayName('');
              setShowPremiumOffer(false);
            }}
            className="text-sm text-notely-lavender hover:underline transition-colors mt-1"
          >
            {isRegisterMode ? 'Sign In' : 'Create Account'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default WebLogin;
