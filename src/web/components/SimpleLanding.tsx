import React from 'react';

const SimpleLanding: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-950 via-black to-zinc-900 text-white flex flex-col items-center justify-center p-4">
      <div className="max-w-3xl text-center">
        <h1 className="text-4xl md:text-6xl font-bold mb-6" style={{ fontFamily: 'Space Grotesk, sans-serif' }}>
          Welcome to{' '}
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400">
            Notely
          </span>
        </h1>
        
        <p className="text-xl md:text-2xl text-gray-300 mb-8">
          Save, organize, and rediscover posts from Twitter, LinkedIn, Reddit, and Instagram with AI-powered intelligence.
        </p>
        
        <button 
          onClick={() => window.open('https://chrome.google.com/webstore/detail/notely-social', '_blank')}
          className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:scale-105 transition-transform duration-300"
        >
          Add to Chrome
        </button>
        
        <p className="mt-6 text-gray-400">
          Never lose brilliant content again.
        </p>
      </div>
    </div>
  );
};

export default SimpleLanding;
