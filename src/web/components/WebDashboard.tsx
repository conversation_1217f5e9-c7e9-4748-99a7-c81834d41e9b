import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useWebAuth } from '../context/WebAuthContext';
import { PremiumBadge } from '../../components/PremiumBadge';

interface Post {
  id: string;
  title: string;
  content: string;
  platform: string;
  category?: string;
  tags: string[];
  createdAt: string;
  imageUrl?: string;
}

const WebDashboard: React.FC = () => {
  const { isAuthenticated, isLoading, user, logout } = useWebAuth();
  const navigate = useNavigate();
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoadingPosts, setIsLoadingPosts] = useState(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/dashboard/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchPosts();
    }
  }, [isAuthenticated, user]);

  const fetchPosts = async () => {
    setIsLoadingPosts(true);
    try {
      // For now, we'll show a placeholder since the Chrome extension storage
      // isn't available in the web version. In the future, this could sync
      // with a cloud database.
      setPosts([]);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setIsLoadingPosts(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/dashboard/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-notely-bg">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-notely-lavender"></div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-notely-bg">
      {/* Header */}
      <header className="bg-notely-surface border-b border-notely-border shadow-notely-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <img
                src="/notely.svg"
                alt="Notely"
                className="h-8 w-auto mr-3"
              />
              <h1 className="text-xl font-bold text-notely-text-primary">
                Notely Social
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              {user && (
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-semibold text-notely-text-primary">
                        {user.displayName || user.name}
                      </p>
                      <PremiumBadge 
                        plan={user.plan} 
                        subscriptionStatus={user.subscriptionStatus}
                        size="sm"
                      />
                    </div>
                    <p className="text-xs text-notely-text-secondary">{user.email}</p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => navigate('/dashboard/settings')}
                      className="text-sm text-notely-text-secondary hover:text-notely-accent transition-colors"
                    >
                      Settings
                    </button>
                    <button
                      onClick={handleLogout}
                      className="text-sm text-red-600 hover:text-red-800 transition-colors"
                    >
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-notely-text-primary mb-2">
            Welcome to Notely Social Web Dashboard
          </h2>
          <p className="text-notely-text-secondary">
            Manage your saved social media posts and content
          </p>
        </div>

        {/* Chrome Extension Notice */}
        <div className="bg-notely-card rounded-lg p-6 mb-8 border border-notely-border">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-notely-lavender rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-notely-text-primary mb-2">
                Get the Chrome Extension
              </h3>
              <p className="text-notely-text-secondary mb-4">
                To save and manage posts from social media platforms, install our Chrome extension. 
                Your saved posts will sync across all your devices.
              </p>
              <div className="flex space-x-4">
                <a
                  href="https://chrome.google.com/webstore/detail/notely-social"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-notely-lavender text-white rounded-md hover:bg-notely-lavender-dark transition-colors"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Install Chrome Extension
                </a>
                <a
                  href="/dashboard.html"
                  className="inline-flex items-center px-4 py-2 border border-notely-border text-notely-text-primary rounded-md hover:bg-notely-surface transition-colors"
                >
                  Open Extension Dashboard
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Posts Section */}
        <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
          <h3 className="text-lg font-semibold text-notely-text-primary mb-4">
            Your Saved Posts
          </h3>
          
          {isLoadingPosts ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-lavender"></div>
            </div>
          ) : posts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {posts.map((post) => (
                <div key={post.id} className="border border-notely-border rounded-lg p-4 hover:shadow-notely-md transition-shadow">
                  <h4 className="font-semibold text-notely-text-primary mb-2">{post.title}</h4>
                  <p className="text-notely-text-secondary text-sm mb-3">{post.content}</p>
                  <div className="flex items-center justify-between text-xs text-notely-text-muted">
                    <span className="capitalize">{post.platform}</span>
                    <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-notely-surface rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-notely-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-notely-text-primary mb-2">No posts yet</h4>
              <p className="text-notely-text-secondary mb-4">
                Install the Chrome extension to start saving posts from social media platforms.
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default WebDashboard;
