import React, { useState, useEffect } from 'react';
import { useWebAuth } from '../context/WebAuthContext';

interface Coupon {
  id: string;
  code: string;
  discountType: 'percentage' | 'fixed';
  amount: number;
  usageLimit?: number;
  usedCount: number;
  expiresAt?: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  isExpired: boolean;
  isUsable: boolean;
}

interface CouponStats {
  totalCoupons: number;
  activeCoupons: number;
  expiredCoupons: number;
  totalUsages: number;
}

const AdminCoupons: React.FC = () => {
  const { getToken } = useWebAuth();
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [couponStats, setCouponStats] = useState<CouponStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  const [filters, setFilters] = useState({
    status: '',
    search: '',
  });

  const [form, setForm] = useState({
    code: '',
    discountType: 'percentage' as 'percentage' | 'fixed',
    amount: '',
    usageLimit: '',
    expiresAt: '',
  });

  useEffect(() => {
    fetchCoupons();
    fetchCouponStats();
  }, [filters]);

  const fetchCoupons = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await fetch(`/admin/coupons?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCoupons(data.coupons);
      } else {
        throw new Error('Failed to fetch coupons');
      }
    } catch (err) {
      console.error('Error fetching coupons:', err);
      setError('Failed to load coupons');
    } finally {
      setLoading(false);
    }
  };

  const fetchCouponStats = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/coupons/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCouponStats(data.stats);
      }
    } catch (err) {
      console.error('Error fetching coupon stats:', err);
    }
  };

  const createCoupon = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const token = await getToken();
      const response = await fetch('/admin/coupons', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: form.code,
          discountType: form.discountType,
          amount: parseFloat(form.amount),
          usageLimit: form.usageLimit ? parseInt(form.usageLimit) : null,
          expiresAt: form.expiresAt || null,
        }),
      });

      if (response.ok) {
        setForm({
          code: '',
          discountType: 'percentage',
          amount: '',
          usageLimit: '',
          expiresAt: '',
        });
        setShowCreateForm(false);
        await fetchCoupons();
        await fetchCouponStats();
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to create coupon');
      }
    } catch (err) {
      console.error('Error creating coupon:', err);
      setError('Failed to create coupon');
    }
  };

  const toggleCouponStatus = async (couponId: string, isActive: boolean) => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/coupons/${couponId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !isActive }),
      });

      if (response.ok) {
        await fetchCoupons();
        await fetchCouponStats();
      } else {
        throw new Error('Failed to update coupon');
      }
    } catch (err) {
      console.error('Error updating coupon:', err);
      setError('Failed to update coupon');
    }
  };

  const deleteCoupon = async (couponId: string) => {
    if (!confirm('Are you sure you want to delete this coupon?')) return;

    try {
      const token = await getToken();
      const response = await fetch(`/admin/coupons/${couponId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        await fetchCoupons();
        await fetchCouponStats();
      } else {
        throw new Error('Failed to delete coupon');
      }
    } catch (err) {
      console.error('Error deleting coupon:', err);
      setError('Failed to delete coupon');
    }
  };

  const getStatusBadgeClass = (coupon: Coupon) => {
    if (!coupon.isActive) return 'bg-gray-100 text-gray-800';
    if (coupon.isExpired) return 'bg-red-100 text-red-800';
    if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) return 'bg-orange-100 text-orange-800';
    return 'bg-green-100 text-green-800';
  };

  const getStatusText = (coupon: Coupon) => {
    if (!coupon.isActive) return 'Inactive';
    if (coupon.isExpired) return 'Expired';
    if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) return 'Used Up';
    return 'Active';
  };

  return (
    <div>
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-notely-text-secondary">Total Coupons</p>
              <p className="text-2xl font-bold text-notely-text-primary">{couponStats?.totalCoupons || 0}</p>
            </div>
            <div className="w-12 h-12 bg-notely-sky/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-notely-sky" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-notely-text-secondary">Active Coupons</p>
              <p className="text-2xl font-bold text-notely-text-primary">{couponStats?.activeCoupons || 0}</p>
            </div>
            <div className="w-12 h-12 bg-notely-mint/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-notely-mint" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-notely-text-secondary">Expired</p>
              <p className="text-2xl font-bold text-notely-text-primary">{couponStats?.expiredCoupons || 0}</p>
            </div>
            <div className="w-12 h-12 bg-notely-coral/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-notely-coral" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-notely-text-secondary">Total Usage</p>
              <p className="text-2xl font-bold text-notely-text-primary">{couponStats?.totalUsages || 0}</p>
            </div>
            <div className="w-12 h-12 bg-notely-lavender/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-notely-lavender" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Actions and Filters */}
      <div className="bg-notely-card rounded-lg p-6 mb-6 border border-notely-border">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-notely-text-primary">Coupon Management</h2>
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="bg-notely-accent text-white px-4 py-2 rounded-lg hover:bg-notely-accent/90 transition-colors"
          >
            {showCreateForm ? 'Cancel' : 'Create Coupon'}
          </button>
        </div>

        {/* Create Coupon Form */}
        {showCreateForm && (
          <form onSubmit={createCoupon} className="mb-6 p-4 bg-notely-surface rounded-lg border border-notely-border">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                  Coupon Code
                </label>
                <input
                  type="text"
                  value={form.code}
                  onChange={(e) => setForm(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                  placeholder="SAVE10"
                  pattern="[A-Z0-9]+"
                  className="w-full bg-notely-bg border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                  Discount Type
                </label>
                <select
                  value={form.discountType}
                  onChange={(e) => setForm(prev => ({ ...prev, discountType: e.target.value as 'percentage' | 'fixed' }))}
                  className="w-full bg-notely-bg border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                  Amount {form.discountType === 'percentage' ? '(%)' : '($)'}
                </label>
                <input
                  type="number"
                  value={form.amount}
                  onChange={(e) => setForm(prev => ({ ...prev, amount: e.target.value }))}
                  min="0"
                  max={form.discountType === 'percentage' ? "100" : undefined}
                  step={form.discountType === 'percentage' ? "1" : "0.01"}
                  className="w-full bg-notely-bg border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                  Usage Limit (Optional)
                </label>
                <input
                  type="number"
                  value={form.usageLimit}
                  onChange={(e) => setForm(prev => ({ ...prev, usageLimit: e.target.value }))}
                  min="1"
                  className="w-full bg-notely-bg border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                  Expires At (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={form.expiresAt}
                  onChange={(e) => setForm(prev => ({ ...prev, expiresAt: e.target.value }))}
                  className="w-full bg-notely-bg border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                />
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <button
                type="submit"
                className="bg-notely-mint text-white px-6 py-2 rounded-lg hover:bg-notely-mint/90 transition-colors"
              >
                Create Coupon
              </button>
            </div>
          </form>
        )}

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-notely-text-secondary mb-2">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full bg-notely-surface border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
            >
              <option value="">All</option>
              <option value="active">Active</option>
              <option value="expired">Expired</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-notely-text-secondary mb-2">
              Search
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search by coupon code..."
              className="w-full bg-notely-surface border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
            />
          </div>
        </div>
      </div>

      {/* Coupons Table */}
      <div className="bg-notely-card rounded-lg border border-notely-border overflow-hidden">
        <div className="px-6 py-4 border-b border-notely-border">
          <h2 className="text-lg font-semibold text-notely-text-primary">Coupons</h2>
        </div>
        
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto"></div>
            <p className="text-notely-text-secondary mt-2">Loading coupons...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-notely-surface">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Code</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Discount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Usage</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Expires</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Created By</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-notely-border">
                {coupons.map((coupon) => (
                  <tr key={coupon.id} className="hover:bg-notely-surface/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-notely-text-primary">{coupon.code}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-notely-text-primary">
                        {coupon.discountType === 'percentage' ? `${coupon.amount}%` : `$${coupon.amount}`}
                      </div>
                      <div className="text-xs text-notely-text-secondary">
                        {coupon.discountType === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-notely-text-primary">
                      {coupon.usedCount}{coupon.usageLimit ? ` / ${coupon.usageLimit}` : ' / ∞'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-notely-text-secondary">
                      {coupon.expiresAt ? new Date(coupon.expiresAt).toLocaleDateString() : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(coupon)}`}>
                        {getStatusText(coupon)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-notely-text-secondary">
                      {coupon.createdBy}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => toggleCouponStatus(coupon.id, coupon.isActive)}
                          className="text-notely-sky hover:text-notely-sky/80 transition-colors"
                        >
                          {coupon.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                        <button
                          onClick={() => deleteCoupon(coupon.id)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminCoupons; 