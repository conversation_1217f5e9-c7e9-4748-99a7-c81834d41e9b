import React, { useState, useEffect } from 'react';
import { useWebAuth } from '../context/WebAuthContext';
import AdminCoupons from './AdminCoupons';
import AdminFlaggedUsers from './AdminFlaggedUsers';
import AdminAIProviderSettings from './AdminAIProviderSettings';
import AdminSuspiciousUsers from './AdminSuspiciousUsers';

interface AdminOverviewData {
  totalUsers: number;
  activeUsersToday: number;
  totalTokenUsage: number;
  totalStorageUsedGB: number;
}

interface User {
  _id: string;
  email: string;
  name: string;
  displayName?: string;
  plan: 'free' | 'premium';
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  adsDisabled: boolean;
  createdAt: string;
  storageUsed: number;
}

interface UserWithStats extends User {
  tokensUsed30Days?: number;
  storageUsedMB?: number;
}

interface Coupon {
  id: string;
  code: string;
  discountType: 'percentage' | 'fixed';
  amount: number;
  usageLimit?: number;
  usedCount: number;
  expiresAt?: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  isExpired: boolean;
  isUsable: boolean;
}

interface CouponStats {
  totalCoupons: number;
  activeCoupons: number;
  expiredCoupons: number;
  totalUsages: number;
}

const AdminOverview: React.FC = () => {
  const { user, getToken } = useWebAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'coupons' | 'flagged' | 'ai-settings' | 'suspicious'>('overview');
  const [overview, setOverview] = useState<AdminOverviewData | null>(null);
  const [users, setUsers] = useState<UserWithStats[]>([]);
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [couponStats, setCouponStats] = useState<CouponStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // User filters
  const [userFilters, setUserFilters] = useState({
    subscriptionStatus: '',
    plan: '',
    search: '',
  });

  // Coupon filters and form
  const [couponFilters, setCouponFilters] = useState({
    status: '',
    search: '',
  });

  const [showCreateCoupon, setShowCreateCoupon] = useState(false);
  const [couponForm, setCouponForm] = useState({
    code: '',
    discountType: 'percentage' as 'percentage' | 'fixed',
    amount: '',
    usageLimit: '',
    expiresAt: '',
  });

  // Check if user is admin
  const isAdmin = user?.role === 'admin';

  useEffect(() => {
    if (isAdmin && activeTab === 'overview') {
      fetchOverviewData();
    }
  }, [isAdmin, activeTab]);

  useEffect(() => {
    if (isAdmin && activeTab === 'users') {
      fetchUsers();
    }
  }, [isAdmin, activeTab, userFilters]);

  useEffect(() => {
    if (isAdmin && activeTab === 'coupons') {
      fetchCoupons();
      fetchCouponStats();
    }
  }, [isAdmin, activeTab, couponFilters]);

  const fetchOverviewData = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/overview', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setOverview(data.overview);
      } else {
        throw new Error('Failed to fetch overview data');
      }
    } catch (err) {
      console.error('Error fetching overview:', err);
      setError('Failed to load overview data');
    }
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const params = new URLSearchParams();
      
      Object.entries(userFilters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await fetch(`/admin/users?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        
        // Fetch usage stats for each user
        const usersWithStats = await Promise.all(
          data.users.map(async (user: User) => {
            try {
              const statsResponse = await fetch(`/admin/users/${user._id}?days=30`, {
                headers: {
                  'Authorization': `Bearer ${token}`,
                },
              });

              if (statsResponse.ok) {
                const statsData = await statsResponse.json();
                return {
                  ...user,
                  tokensUsed30Days: statsData.usage.aiTokens.total,
                  storageUsedMB: Math.round(user.storageUsed / (1024 * 1024) * 100) / 100,
                };
              }
            } catch (err) {
              console.error(`Error fetching stats for user ${user._id}:`, err);
            }
            
            return {
              ...user,
              tokensUsed30Days: 0,
              storageUsedMB: Math.round(user.storageUsed / (1024 * 1024) * 100) / 100,
            };
          })
        );

        setUsers(usersWithStats);
      } else {
        throw new Error('Failed to fetch users');
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const toggleUserAds = async (userId: string, adsDisabled: boolean) => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/ads`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ adsDisabled }),
      });

      if (response.ok) {
        await fetchUsers();
      } else {
        throw new Error('Failed to update ads setting');
      }
    } catch (err) {
      console.error('Error updating ads:', err);
      setError('Failed to update ads setting');
    }
  };

  const updateUserPlan = async (userId: string, plan: 'free' | 'premium') => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/subscription`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan }),
      });

      if (response.ok) {
        await fetchUsers();
      } else {
        throw new Error('Failed to update user plan');
      }
    } catch (err) {
      console.error('Error updating plan:', err);
      setError('Failed to update user plan');
    }
  };

  const fetchCoupons = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/coupons', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCoupons(data.coupons);
      } else {
        throw new Error('Failed to fetch coupons');
      }
    } catch (err) {
      console.error('Error fetching coupons:', err);
      setError('Failed to load coupons');
    }
  };

  const fetchCouponStats = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/coupons/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCouponStats(data.stats);
      } else {
        throw new Error('Failed to fetch coupon stats');
      }
    } catch (err) {
      console.error('Error fetching coupon stats:', err);
      setError('Failed to load coupon stats');
    }
  };

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-notely-bg flex items-center justify-center">
        <div className="bg-notely-card rounded-lg p-8 max-w-md w-full mx-4 border border-notely-border">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-notely-text-primary mb-2">Access Denied</h2>
            <p className="text-notely-text-secondary">You don't have permission to access the admin dashboard.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-notely-bg">
      {/* Header */}
      <header className="bg-notely-surface border-b border-notely-border shadow-notely-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <img
                src="/notely.svg"
                alt="Notely"
                className="h-8 w-auto mr-3"
              />
              <h1 className="text-xl font-bold text-notely-text-primary">
                Admin Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <a
                href="/dashboard"
                className="text-sm text-notely-text-secondary hover:text-notely-accent transition-colors"
              >
                Back to Dashboard
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-notely-surface border-b border-notely-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-notely-accent text-notely-accent'
                  : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary hover:border-notely-text-muted'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'users'
                  ? 'border-notely-accent text-notely-accent'
                  : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary hover:border-notely-text-muted'
              }`}
            >
              Users
            </button>
            <button
              onClick={() => setActiveTab('coupons')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'coupons'
                  ? 'border-notely-accent text-notely-accent'
                  : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary hover:border-notely-text-muted'
              }`}
            >
              Coupons
            </button>
            <button
              onClick={() => setActiveTab('flagged')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'flagged'
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary hover:border-notely-text-muted'
              }`}
            >
              🚨 Flagged Users
            </button>
            <button
              onClick={() => setActiveTab('ai-settings')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'ai-settings'
                  ? 'border-notely-accent text-notely-accent'
                  : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary hover:border-notely-text-muted'
              }`}
            >
              ⚙️ AI Provider Settings
            </button>
            <button
              onClick={() => setActiveTab('suspicious')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'suspicious'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-notely-text-secondary hover:text-notely-text-primary hover:border-notely-text-muted'
              }`}
            >
              🚨 Suspicious Users
            </button>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-notely-text-primary mb-2">
                System Overview
              </h2>
              <p className="text-notely-text-secondary">
                High-level metrics and statistics for your Notely Social platform
              </p>
            </div>

            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-notely-text-secondary">Total Users</p>
                    <p className="text-2xl font-bold text-notely-text-primary">{overview?.totalUsers || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-notely-sky/20 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-notely-sky" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-notely-text-secondary">Active Today</p>
                    <p className="text-2xl font-bold text-notely-text-primary">{overview?.activeUsersToday || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-notely-mint/20 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-notely-mint" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-notely-text-secondary">Total Tokens (30d)</p>
                    <p className="text-2xl font-bold text-notely-text-primary">{overview?.totalTokenUsage?.toLocaleString() || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-notely-lavender/20 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-notely-lavender" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-notely-text-secondary">Total Storage</p>
                    <p className="text-2xl font-bold text-notely-text-primary">{overview?.totalStorageUsedGB || 0} GB</p>
                  </div>
                  <div className="w-12 h-12 bg-notely-coral/20 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-notely-coral" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-notely-text-primary mb-2">
                User Management
              </h2>
              <p className="text-notely-text-secondary">
                Manage user accounts, subscriptions, and settings
              </p>
            </div>

            {/* Filters */}
            <div className="bg-notely-card rounded-lg p-6 mb-6 border border-notely-border">
              <h3 className="text-lg font-semibold text-notely-text-primary mb-4">Filters</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                    Subscription Status
                  </label>
                  <select
                    value={userFilters.subscriptionStatus}
                    onChange={(e) => setUserFilters(prev => ({ ...prev, subscriptionStatus: e.target.value }))}
                    className="w-full bg-notely-surface border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                  >
                    <option value="">All</option>
                    <option value="trialing">Trialing</option>
                    <option value="active">Active</option>
                    <option value="past_due">Past Due</option>
                    <option value="canceled">Canceled</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                    Plan
                  </label>
                  <select
                    value={userFilters.plan}
                    onChange={(e) => setUserFilters(prev => ({ ...prev, plan: e.target.value }))}
                    className="w-full bg-notely-surface border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                  >
                    <option value="">All</option>
                    <option value="free">Free</option>
                    <option value="premium">Premium</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-notely-text-secondary mb-2">
                    Search
                  </label>
                  <input
                    type="text"
                    value={userFilters.search}
                    onChange={(e) => setUserFilters(prev => ({ ...prev, search: e.target.value }))}
                    placeholder="Email or name..."
                    className="w-full bg-notely-surface border border-notely-border rounded-lg px-3 py-2 text-notely-text-primary focus:outline-none focus:ring-2 focus:ring-notely-accent"
                  />
                </div>
              </div>
            </div>

            {/* Users Table */}
            <div className="bg-notely-card rounded-lg border border-notely-border overflow-hidden">
              <div className="px-6 py-4 border-b border-notely-border">
                <h3 className="text-lg font-semibold text-notely-text-primary">Users</h3>
              </div>
              
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto"></div>
                  <p className="text-notely-text-secondary mt-2">Loading users...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-notely-surface">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Plan</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Tokens (30d)</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Storage</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Ads</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Created</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-notely-text-secondary uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-notely-border">
                      {users.map((user) => (
                        <tr key={user._id} className="hover:bg-notely-surface/50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-notely-text-primary">{user.email}</div>
                              <div className="text-sm text-notely-text-secondary">{user.displayName || user.name}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.plan === 'premium' 
                                ? 'bg-notely-lavender/20 text-notely-lavender' 
                                : 'bg-notely-text-muted/20 text-notely-text-muted'
                            }`}>
                              {user.plan}
                            </span>
                            <div className="text-xs text-notely-text-secondary mt-1">
                              {user.subscriptionStatus}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-notely-text-primary">
                            {user.tokensUsed30Days?.toLocaleString() || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-notely-text-primary">
                            {user.storageUsedMB} MB
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => toggleUserAds(user._id, !user.adsDisabled)}
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                user.adsDisabled 
                                  ? 'bg-red-100 text-red-800' 
                                  : 'bg-green-100 text-green-800'
                              }`}
                            >
                              {user.adsDisabled ? 'Disabled' : 'Enabled'}
                            </button>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-notely-text-secondary">
                            {new Date(user.createdAt).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => updateUserPlan(user._id, user.plan === 'free' ? 'premium' : 'free')}
                                className="text-notely-sky hover:text-notely-sky/80 transition-colors"
                              >
                                {user.plan === 'free' ? 'Upgrade' : 'Downgrade'}
                              </button>
                              <button
                                onClick={() => toggleUserAds(user._id, !user.adsDisabled)}
                                className="text-notely-lavender hover:text-notely-lavender/80 transition-colors"
                              >
                                {user.adsDisabled ? 'Enable Ads' : 'Disable Ads'}
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Coupons Tab */}
        {activeTab === 'coupons' && <AdminCoupons />}

        {/* Flagged Users Tab */}
        {activeTab === 'flagged' && <AdminFlaggedUsers />}

        {/* AI Provider Settings Tab */}
        {activeTab === 'ai-settings' && <AdminAIProviderSettings />}

        {/* Suspicious Users Tab */}
        {activeTab === 'suspicious' && <AdminSuspiciousUsers />}
      </main>
    </div>
  );
};

export default AdminOverview; 