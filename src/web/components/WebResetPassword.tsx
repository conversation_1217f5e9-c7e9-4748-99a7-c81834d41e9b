import React, { useState, useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';

const WebResetPassword: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [token, setToken] = useState('');

  useEffect(() => {
    const tokenFromUrl = searchParams.get('token');
    if (!tokenFromUrl) {
      setError('Invalid or missing reset token. Please request a new password reset.');
    } else {
      setToken(tokenFromUrl);
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return;
    }

    if (!token) {
      setError('Invalid reset token');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          token, 
          newPassword: password 
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage(data.message);
        // Redirect to login after 3 seconds
        setTimeout(() => {
          navigate('/dashboard/login');
        }, 3000);
      } else {
        setError(data.message || 'An error occurred');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setError('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!token && !error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-notely-bg">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-notely-lavender"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-notely-bg py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-notely-lavender/10">
            <svg
              className="h-6 w-6 text-notely-lavender"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"
              />
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-bold text-notely-text-primary">
            Set new password
          </h2>
          <p className="mt-2 text-center text-sm text-notely-text-secondary">
            Enter your new password below.
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="rounded-md bg-red-50 p-4 border border-red-200">
              <div className="text-sm text-red-800">{error}</div>
            </div>
          )}

          {message && (
            <div className="rounded-md bg-green-50 p-4 border border-green-200">
              <div className="text-sm text-green-800">
                {message}
                <div className="mt-2 text-xs">Redirecting to login in 3 seconds...</div>
              </div>
            </div>
          )}

          {!message && token && (
            <>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-notely-text-secondary mb-1">
                  New Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isSubmitting}
                  className="w-full px-3 py-2 border border-notely-border bg-notely-surface rounded-md focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:border-notely-lavender disabled:opacity-50 transition-all"
                  placeholder="Enter new password (min 6 characters)"
                />
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-notely-text-secondary mb-1">
                  Confirm New Password
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  disabled={isSubmitting}
                  className="w-full px-3 py-2 border border-notely-border bg-notely-surface rounded-md focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:border-notely-lavender disabled:opacity-50 transition-all"
                  placeholder="Confirm new password"
                />
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isSubmitting || !password.trim() || !confirmPassword.trim()}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-notely-lavender hover:bg-notely-lavender-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-notely-lavender disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating password...
                    </div>
                  ) : (
                    'Update password'
                  )}
                </button>
              </div>
            </>
          )}

          <div className="text-center">
            <Link
              to="/dashboard/login"
              className="text-sm text-notely-lavender hover:text-notely-lavender-dark transition-colors duration-200 underline-offset-2 hover:underline"
            >
              Back to login
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WebResetPassword; 