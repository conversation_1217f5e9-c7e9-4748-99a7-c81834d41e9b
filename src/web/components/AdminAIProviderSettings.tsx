import React, { useState, useEffect } from 'react';
import { useWebAuth } from '../context/WebAuthContext';

interface AIConfig {
  taskType: string;
  provider: string;
  model?: string;
  updatedAt: string;
}

interface Provider {
  name: string;
  models: Array<{ id: string; name: string }>;
}

interface TaskType {
  id: string;
  name: string;
  description: string;
}

interface ProvidersData {
  providers: Record<string, Provider>;
  taskTypes: TaskType[];
}

const AdminAIProviderSettings: React.FC = () => {
  const { getToken } = useWebAuth();
  const [configs, setConfigs] = useState<AIConfig[]>([]);
  const [providersData, setProvidersData] = useState<ProvidersData | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchConfigs();
    fetchProvidersData();
  }, []);

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const response = await fetch('/admin/ai-config', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setConfigs(data.configs);
      } else {
        throw new Error('Failed to fetch AI configurations');
      }
    } catch (error) {
      console.error('Error fetching AI configs:', error);
      showMessage('error', 'Failed to load AI configurations');
    } finally {
      setLoading(false);
    }
  };

  const fetchProvidersData = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/ai-config/providers', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setProvidersData(data);
      } else {
        throw new Error('Failed to fetch providers data');
      }
    } catch (error) {
      console.error('Error fetching providers data:', error);
      showMessage('error', 'Failed to load provider information');
    }
  };

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const updateConfig = async (taskType: string, provider: string, model?: string) => {
    try {
      setSaving(taskType);
      const token = await getToken();
      const response = await fetch('/admin/ai-config', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskType, provider, model }),
      });

      if (response.ok) {
        const data = await response.json();
        showMessage('success', data.message);
        
        // Update local state
        setConfigs(prev => prev.map(config => 
          config.taskType === taskType 
            ? { ...config, provider, model, updatedAt: data.config.updatedAt }
            : config
        ));
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating AI config:', error);
      showMessage('error', error instanceof Error ? error.message : 'Failed to update configuration');
    } finally {
      setSaving(null);
    }
  };

  const resetToDefaults = async () => {
    if (!window.confirm('Are you sure you want to reset all AI provider configurations to defaults? This cannot be undone.')) {
      return;
    }

    try {
      setLoading(true);
      const token = await getToken();
      const response = await fetch('/admin/ai-config/reset', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setConfigs(data.configs);
        showMessage('success', data.message);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reset configurations');
      }
    } catch (error) {
      console.error('Error resetting AI configs:', error);
      showMessage('error', error instanceof Error ? error.message : 'Failed to reset configurations');
    } finally {
      setLoading(false);
    }
  };

  const getConfigForTask = (taskType: string): AIConfig | undefined => {
    return configs.find(config => config.taskType === taskType);
  };

  const getTaskTypeInfo = (taskType: string): TaskType | undefined => {
    return providersData?.taskTypes.find(t => t.id === taskType);
  };

  if (loading && configs.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-600">Loading AI provider configurations...</div>
      </div>
    );
  }

  if (!providersData) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-600">Loading provider information...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI Provider Settings</h2>
          <p className="text-gray-600 mt-1">Configure which AI provider to use for different task types</p>
        </div>
        <button
          onClick={resetToDefaults}
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 disabled:opacity-50"
        >
          Reset to Defaults
        </button>
      </div>

      {message && (
        <div
          className={`mb-6 p-4 rounded-md ${
            message.type === 'success'
              ? 'bg-green-50 text-green-800 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}
        >
          {message.text}
        </div>
      )}

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900">Task Type Configurations</h3>
        </div>

        <div className="divide-y divide-gray-200">
          {providersData.taskTypes.map((taskType) => {
            const config = getConfigForTask(taskType.id);
            const currentProvider = config?.provider || 'openai';
            const currentModel = config?.model;
            const isUpdating = saving === taskType.id;

            return (
              <div key={taskType.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-lg font-medium text-gray-900">{taskType.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{taskType.description}</p>
                    {config?.updatedAt && (
                      <p className="text-xs text-gray-500 mt-2">
                        Last updated: {new Date(config.updatedAt).toLocaleString()}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center space-x-4 ml-6">
                    {/* Provider Selector */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Provider
                      </label>
                      <select
                        value={currentProvider}
                        onChange={(e) => {
                          const newProvider = e.target.value;
                          // Reset model when provider changes
                          updateConfig(taskType.id, newProvider, undefined);
                        }}
                        disabled={isUpdating}
                        className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:opacity-50"
                      >
                        {Object.entries(providersData.providers).map(([key, provider]) => (
                          <option key={key} value={key}>
                            {provider.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Model Selector */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Model
                      </label>
                      <select
                        value={currentModel || ''}
                        onChange={(e) => {
                          const newModel = e.target.value || undefined;
                          updateConfig(taskType.id, currentProvider, newModel);
                        }}
                        disabled={isUpdating}
                        className="block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:opacity-50"
                      >
                        <option value="">Default Model</option>
                        {providersData.providers[currentProvider]?.models.map((model) => (
                          <option key={model.id} value={model.id}>
                            {model.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Status */}
                    <div className="w-20 flex justify-center">
                      {isUpdating ? (
                        <div className="text-blue-600 text-sm">Saving...</div>
                      ) : (
                        <div className="text-green-600 text-sm">✓ Saved</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Important Notes</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Changes take effect immediately for new AI requests</li>
          <li>• OpenAI is used as fallback for any provider failures</li>
          <li>• Make sure API keys are configured for selected providers</li>
          <li>• Different models may have different capabilities and costs</li>
        </ul>
      </div>
    </div>
  );
};

export default AdminAIProviderSettings; 