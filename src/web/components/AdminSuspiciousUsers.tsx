import React, { useState, useEffect } from 'react';
import { useWebAuth } from '../context/WebAuthContext';

interface SuspiciousUser {
  _id: string;
  email: string;
  name: string;
  displayName?: string;
  plan: string;
  tokensUsed30d: number;
  storageUsedMB: number;
  estimatedCost: number;
  flaggedForAbuse: boolean;
  lastLogin: string;
  createdAt: string;
  suspiciousReasons: string[];
}

interface Statistics {
  totalUsers: number;
  flaggedUsers: number;
  totalEstimatedCost: number;
  averageEstimatedCost: number;
  thresholds: {
    TOKENS_30D: number;
    STORAGE_GB: number;
    COST_DOLLAR: number;
  };
}

interface CostSummary {
  period: string;
  tokens: {
    total: number;
    requests: number;
    cost: number;
  };
  storage: {
    totalMB: number;
    totalGB: number;
    uploads: number;
    cost: number;
  };
  total: {
    estimatedCost: number;
  };
}

const AdminSuspiciousUsers: React.FC = () => {
  const { getToken } = useWebAuth();
  const [users, setUsers] = useState<SuspiciousUser[]>([]);
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [costSummary, setCostSummary] = useState<CostSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchSuspiciousUsers();
    fetchCostSummary();
  }, []);

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const fetchSuspiciousUsers = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const response = await fetch('/admin/users/suspicious', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
        setStatistics(data.statistics);
      } else {
        throw new Error('Failed to fetch suspicious users');
      }
    } catch (error) {
      console.error('Error fetching suspicious users:', error);
      showMessage('error', 'Failed to load suspicious users');
    } finally {
      setLoading(false);
    }
  };

  const fetchCostSummary = async () => {
    try {
      const token = await getToken();
      const response = await fetch('/admin/cost-analysis/summary', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCostSummary(data.summary);
      }
    } catch (error) {
      console.error('Error fetching cost summary:', error);
    }
  };

  const markUserAsSafe = async (userId: string) => {
    try {
      setActionLoading(userId);
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/mark-safe`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        showMessage('success', data.message);
        
        // Update local state
        setUsers(prev => prev.map(user => 
          user._id === userId 
            ? { ...user, flaggedForAbuse: false }
            : user
        ));
        
        // Update statistics
        if (statistics) {
          setStatistics(prev => prev ? {
            ...prev,
            flaggedUsers: prev.flaggedUsers - 1
          } : null);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to mark user as safe');
      }
    } catch (error) {
      console.error('Error marking user as safe:', error);
      showMessage('error', error instanceof Error ? error.message : 'Failed to mark user as safe');
    } finally {
      setActionLoading(null);
    }
  };

  const exportToCSV = () => {
    const headers = ['Email', 'Name', 'Plan', 'Tokens (30d)', 'Storage (MB)', 'Estimated Cost', 'Flagged', 'Reasons'];
    const rows = users.map(user => [
      user.email,
      user.displayName || user.name,
      user.plan,
      user.tokensUsed30d.toString(),
      user.storageUsedMB.toString(),
      `$${user.estimatedCost.toFixed(3)}`,
      user.flaggedForAbuse ? 'Yes' : 'No',
      user.suspiciousReasons.join('; ')
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `suspicious-users-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading && users.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-600">Loading suspicious users...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">🚨 Suspicious Users & Cost Analysis</h2>
          <p className="text-gray-600 mt-1">Monitor users with high usage patterns and estimated costs</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={fetchSuspiciousUsers}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50"
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
          <button
            onClick={exportToCSV}
            disabled={users.length === 0}
            className="px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 disabled:opacity-50"
          >
            Export CSV
          </button>
        </div>
      </div>

      {message && (
        <div
          className={`mb-6 p-4 rounded-md ${
            message.type === 'success'
              ? 'bg-green-50 text-green-800 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}
        >
          {message.text}
        </div>
      )}

      {/* Cost Summary Cards */}
      {costSummary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total AI Cost (30d)</p>
                <p className="text-2xl font-bold text-blue-600">${costSummary.tokens.cost.toFixed(3)}</p>
                <p className="text-sm text-gray-500">{costSummary.tokens.total.toLocaleString()} tokens</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Storage Cost (30d)</p>
                <p className="text-2xl font-bold text-purple-600">${costSummary.storage.cost.toFixed(3)}</p>
                <p className="text-sm text-gray-500">{costSummary.storage.totalGB.toFixed(2)} GB</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Estimated Cost</p>
                <p className="text-2xl font-bold text-red-600">${costSummary.total.estimatedCost.toFixed(3)}</p>
                <p className="text-sm text-gray-500">Last 30 days</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <p className="text-sm font-medium text-gray-600">Total Users</p>
            <p className="text-xl font-bold text-gray-900">{statistics.totalUsers}</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <p className="text-sm font-medium text-gray-600">Flagged Users</p>
            <p className="text-xl font-bold text-red-600">{statistics.flaggedUsers}</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <p className="text-sm font-medium text-gray-600">Avg. Cost</p>
            <p className="text-xl font-bold text-blue-600">${statistics.averageEstimatedCost.toFixed(3)}</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <p className="text-sm font-medium text-gray-600">Total Est. Cost</p>
            <p className="text-xl font-bold text-purple-600">${statistics.totalEstimatedCost.toFixed(3)}</p>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900">Suspicious Users</h3>
          <p className="text-sm text-gray-600 mt-1">
            Users exceeding thresholds: {statistics?.thresholds.TOKENS_30D.toLocaleString()} tokens, {statistics?.thresholds.STORAGE_GB}GB storage, or ${statistics?.thresholds.COST_DOLLAR} cost
          </p>
        </div>

        {users.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No suspicious users found. Great job! 🎉</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tokens (30d)</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Storage</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Est. Cost</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reasons</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.email}</div>
                        <div className="text-sm text-gray-500">{user.displayName || user.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.plan === 'premium' 
                          ? 'bg-purple-100 text-purple-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {user.plan}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.tokensUsed30d.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.storageUsedMB.toFixed(1)} MB
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-semibold ${
                        user.estimatedCost > 1 ? 'text-red-600' :
                        user.estimatedCost > 0.5 ? 'text-orange-600' :
                        'text-green-600'
                      }`}>
                        ${user.estimatedCost.toFixed(3)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.flaggedForAbuse ? (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                          🚨 Flagged
                        </span>
                      ) : (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          ⚠️ Suspicious
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs">
                        {user.suspiciousReasons.map((reason, index) => (
                          <div key={index} className="mb-1">
                            • {reason}
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {user.flaggedForAbuse && (
                        <button
                          onClick={() => markUserAsSafe(user._id)}
                          disabled={actionLoading === user._id}
                          className="text-green-600 hover:text-green-900 disabled:opacity-50"
                        >
                          {actionLoading === user._id ? 'Updating...' : 'Mark as Safe'}
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Thresholds Info */}
      {statistics && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Detection Thresholds</h4>
          <div className="text-sm text-blue-800 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <strong>Tokens:</strong> {statistics.thresholds.TOKENS_30D.toLocaleString()} in 30 days
            </div>
            <div>
              <strong>Storage:</strong> {statistics.thresholds.STORAGE_GB}GB total usage
            </div>
            <div>
              <strong>Cost:</strong> ${statistics.thresholds.COST_DOLLAR} estimated monthly cost
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSuspiciousUsers; 