import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const WebForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/auth/request-password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('If an account with that email exists, we\'ve sent you a password reset link.');
        setEmail('');
      } else {
        setError(data.message || 'Failed to send reset email');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-notely-background flex items-center justify-center p-4">
      <div className="w-full max-w-sm mx-auto px-6 py-8 bg-notely-card rounded-xl shadow-lg border border-notely-border">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-lg font-semibold text-notely-text-primary mb-1">
            Reset Password
          </h1>
          <p className="text-sm text-notely-text-secondary">
            Enter your email to receive a reset link
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
            {error}
          </div>
        )}

        {message && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-lg text-sm">
            {message}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4 mb-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-notely-text-primary mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={isSubmitting}
              className="w-full px-3 py-2 border border-notely-border bg-notely-surface rounded-xl focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:border-notely-lavender disabled:opacity-50 transition-all text-sm"
              placeholder="Enter your email"
            />
          </div>
          
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full py-2.5 px-4 bg-notely-lavender text-white font-medium rounded-xl hover:bg-notely-lavender/90 focus:outline-none focus:ring-2 focus:ring-notely-lavender focus:ring-offset-2 disabled:opacity-50 transition-all text-sm"
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-2"></div>
                Sending...
              </span>
            ) : (
              'Send Reset Link'
            )}
          </button>
        </form>

        {/* Back to Login */}
        <div className="text-center">
          <Link
            to="/dashboard/login"
            className="text-sm text-notely-text-muted hover:text-notely-lavender hover:underline transition-colors"
          >
            ← Back to Sign In
          </Link>
        </div>
      </div>
    </div>
  );
};

export default WebForgotPassword; 