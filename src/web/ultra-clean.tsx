import React from 'react';
import ReactDOM from 'react-dom/client';

// Ultra minimal component with inline styles - no external dependencies
const UltraClean: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f0f23 0%, #000000 50%, #1a1a2e 100%)',
      color: 'white',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      padding: '80px 20px'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <h1 style={{
          fontSize: '4rem',
          fontWeight: 'bold',
          marginBottom: '2rem',
          lineHeight: 1.2
        }}>
          Your social media
          <br />
          <span style={{
            background: 'linear-gradient(135deg, #a855f7 0%, #6366f1 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            second brain
          </span>
        </h1>
        
        <p style={{
          fontSize: '1.5rem',
          color: '#d1d5db',
          marginBottom: '3rem',
          maxWidth: '600px',
          lineHeight: 1.6
        }}>
          Stop losing brilliant posts in the endless scroll. 
          <span style={{ color: 'white', fontWeight: '600' }}> Save, organize, and rediscover</span> the content that matters.
        </p>
        
        <button style={{
          background: 'linear-gradient(135deg, #7c3aed 0%, #4f46e5 100%)',
          color: 'white',
          padding: '16px 40px',
          borderRadius: '16px',
          fontSize: '1.125rem',
          fontWeight: 'bold',
          border: 'none',
          cursor: 'pointer',
          transition: 'transform 0.2s',
        }}>
          Add to Chrome — Free
        </button>
        
        <div style={{ marginTop: '5rem' }}>
          <h2 style={{ fontSize: '2rem', fontWeight: 'bold', marginBottom: '2rem' }}>
            How It Works
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '2rem'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{
                width: '64px',
                height: '64px',
                background: 'linear-gradient(135deg, #6366f1 0%, #7c3aed 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1.5rem auto',
                fontSize: '1.25rem',
                fontWeight: 'bold'
              }}>
                1
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.75rem' }}>
                Click the Extension
              </h3>
              <p style={{ color: '#d1d5db' }}>
                One click to save any post from any platform.
              </p>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{
                width: '64px',
                height: '64px',
                background: 'linear-gradient(135deg, #6366f1 0%, #7c3aed 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1.5rem auto',
                fontSize: '1.25rem',
                fontWeight: 'bold'
              }}>
                2
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.75rem' }}>
                AI Auto-categorizes
              </h3>
              <p style={{ color: '#d1d5db' }}>
                Our AI automatically organizes your content.
              </p>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{
                width: '64px',
                height: '64px',
                background: 'linear-gradient(135deg, #6366f1 0%, #7c3aed 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1.5rem auto',
                fontSize: '1.25rem',
                fontWeight: 'bold'
              }}>
                3
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.75rem' }}>
                Search & Reuse
              </h3>
              <p style={{ color: '#d1d5db' }}>
                Find exactly what you need instantly.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <UltraClean />
  </React.StrictMode>
); 