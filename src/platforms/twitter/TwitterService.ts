/**
 * Twitter Service
 *
 * Handles Twitter-specific post extraction and saving logic.
 */

import { Post, AnalyzedPost, CoreSubCategorySlug, MediaItem, Platform } from "../../types";
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, ExtractionResult, ImageExtractionResult } from '../base/types';
import { savePost } from '../../storage';

export class TwitterService extends BasePlatformService {
  private static instance: TwitterService;
  private videoUrlCache: Map<string, string> = new Map();
  private networkInterceptionSetup = false;

  constructor() {
    super('X/Twitter');
    TwitterService.instance = this;
    this.setupVideoUrlInterception();
  }

  /**
   * Set up network request interception to capture video URLs
   */
  private setupVideoUrlInterception(): void {
    if (this.networkInterceptionSetup) return;
    
    console.log('[Twitter] Setting up video URL interception...');
    
    try {
      // Override XMLHttpRequest to capture video URLs
      const originalOpen = XMLHttpRequest.prototype.open;
      const originalSend = XMLHttpRequest.prototype.send;
      
      XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
        (this as any)._requestUrl = url.toString();
        return originalOpen.apply(this, [method, url, ...args]);
      };

      XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null) {
        const url = (this as any)._requestUrl;
        
        // Listen for video URLs from Twitter's API - cast a wider net
        if (url && (url.includes('video') || url.includes('.mp4') || url.includes('ext_tw_video') || 
                   url.includes('twimg.com') || url.includes('amplify') || url.includes('tweet') ||
                   url.includes('media') || url.includes('adaptive') || url.includes('playlist') ||
                   url.includes('/2/timeline/') || url.includes('TweetDetail'))) {
          console.log(`[Twitter] 🌐 Intercepted request to: ${url.substring(0, 100)}...`);
          
          this.addEventListener('load', () => {
            try {
              if (this.responseText) {
                console.log(`[Twitter] 📥 Got response for: ${url.substring(0, 80)}... (length: ${this.responseText.length})`);
                
                // Try to parse as JSON first
                try {
                  const response = JSON.parse(this.responseText);
                  TwitterService.extractVideoUrlsFromResponse(response);
                } catch (jsonError) {
                  // If not JSON, search text for video URLs
                  if (this.responseText.includes('.mp4') || this.responseText.includes('video') || this.responseText.includes('.m3u8')) {
                    console.log(`[Twitter] 🔍 Parsing text response for video URLs...`);
                    TwitterService.extractVideoUrlsFromText(this.responseText);
                  }
                }
              }
            } catch (e) {
              // Direct video URL
              if (url.includes('.mp4')) {
                console.log(`[Twitter] 🎬 Direct video URL intercepted: ${url}`);
                TwitterService.cacheVideoUrl(url);
              }
            }
          });
        }
        
        return originalSend.apply(this, [body]);
      };

      // Also intercept fetch requests
      const originalFetch = window.fetch;
      window.fetch = async function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
        const url = input.toString();
        const response = await originalFetch(input, init);
        
        if (url.includes('video') || url.includes('.mp4') || url.includes('ext_tw_video') || 
            url.includes('twimg.com') || url.includes('amplify') || url.includes('tweet') ||
            url.includes('media') || url.includes('adaptive') || url.includes('playlist') ||
            url.includes('/2/timeline/') || url.includes('TweetDetail')) {
          console.log(`[Twitter] 🌐 Fetch intercepted: ${url.substring(0, 100)}...`);
          
          try {
            const clonedResponse = response.clone();
            const contentType = response.headers.get('content-type') || '';
            
            if (contentType.includes('application/json')) {
              const data = await clonedResponse.json();
              console.log(`[Twitter] 📥 Fetch JSON response for: ${url.substring(0, 80)}...`);
              TwitterService.extractVideoUrlsFromResponse(data);
            } else {
              const text = await clonedResponse.text();
              console.log(`[Twitter] 📥 Fetch text response (${text.length} chars) for: ${url.substring(0, 80)}...`);
              if (text.includes('.mp4') || text.includes('video') || text.includes('.m3u8')) {
                TwitterService.extractVideoUrlsFromText(text);
              }
            }
          } catch (e) {
            // Direct video URL
            if (url.includes('.mp4')) {
              console.log(`[Twitter] 🎬 Direct video URL from fetch: ${url}`);
              TwitterService.cacheVideoUrl(url);
            }
          }
        }
        
        return response;
      };
      
      this.networkInterceptionSetup = true;
      console.log('[Twitter] ✅ Network interception setup complete');
    } catch (error) {
      console.error('[Twitter] ❌ Failed to setup network interception:', error);
    }
  }

  /**
   * Extract video URLs from API responses
   */
  private static extractVideoUrlsFromResponse(data: any): void {
    if (!data) return;
    
    console.log(`[Twitter] 🔍 Searching response for video URLs...`);
    
    // Recursively search for video URLs
    const findVideoUrls = (obj: any) => {
      if (typeof obj === 'string' && obj.includes('.mp4') && !obj.includes('thumb') && !obj.includes('jpg') && !obj.includes('png')) {
        console.log(`[Twitter] 🎬 Found video URL in response: ${obj.substring(0, 100)}...`);
        TwitterService.cacheVideoUrl(obj);
      } else if (typeof obj === 'object' && obj !== null) {
        Object.values(obj).forEach(findVideoUrls);
      }
    };
    
    findVideoUrls(data);
  }

  /**
   * Extract video URLs from text responses (like M3U8 playlists or plain text)
   */
  private static extractVideoUrlsFromText(text: string): void {
    if (!text) return;
    
    console.log(`[Twitter] 🔍 Searching text response for video URLs...`);
    
    // Look for .mp4 URLs in the text
    const mp4Regex = /(https?:\/\/[^\s"'<>]+\.mp4[^\s"'<>]*)/gi;
    const matches = text.match(mp4Regex);
    
    if (matches) {
      matches.forEach(url => {
        if (!url.includes('thumb') && !url.includes('jpg') && !url.includes('png')) {
          console.log(`[Twitter] 🎬 Found video URL in text: ${url.substring(0, 100)}...`);
          TwitterService.cacheVideoUrl(url);
        }
      });
    }
    
    // Also look for video URLs in M3U8 format
    const m3u8Regex = /(https?:\/\/[^\s"'<>]+\.m3u8[^\s"'<>]*)/gi;
    const m3u8Matches = text.match(m3u8Regex);
    
    if (m3u8Matches) {
      m3u8Matches.forEach(url => {
        console.log(`[Twitter] 🎥 Found M3U8 URL in text: ${url.substring(0, 100)}...`);
        TwitterService.cacheVideoUrl(url);
      });
    }
  }

  /**
   * Cache a video URL for later use
   */
  private static cacheVideoUrl(url: string): void {
    if (url.includes('.mp4') && TwitterService.instance) {
      console.log(`[Twitter] 🎥 Cached video URL: ${url.substring(0, 100)}...`);
      const timestamp = Date.now().toString();
      TwitterService.instance.videoUrlCache.set(timestamp, url);
    }
  }

  static getInstance(): TwitterService {
    if (!TwitterService.instance) {
      TwitterService.instance = new TwitterService();
    }
    return TwitterService.instance;
  }

  /**
   * Extract post data from a Twitter tweet element
   * @param element The tweet element
   * @param options Options for extraction
   * @returns ExtractionResult containing the extracted post data or an error
   */
  // Helper method to extract and parse stat numbers (e.g., likes, replies)
  private extractStat(element: HTMLElement, selector: string): number {
    const statElement = element.querySelector(selector + ' [data-testid="app-text-transition-container"], ' + selector + ' span > span > span'); // Common patterns for stat numbers
    let text = statElement?.textContent?.trim();

    if (!text) {
        // Fallback for view counts that might be directly in a span after an SVG, or similar structures
        const directSpan = element.querySelector(selector + ' span[aria-hidden="true"]');
        if (directSpan) text = directSpan.textContent?.trim();
        else {
            const groupSpan = element.querySelector(selector + ' div[role="group"] span[data-testid="app-text-transition-container"] span');
            if (groupSpan) text = groupSpan.textContent?.trim();
        }
    }
    
    if (!text) return 0;

    const num = parseFloat(text.replace(/,/g, '')); // Remove commas before parsing
    if (isNaN(num)) return 0;

    if (text.toUpperCase().endsWith('K')) return num * 1000;
    if (text.toUpperCase().endsWith('M')) return num * 1000000;
    if (text.toUpperCase().endsWith('B')) return num * 1000000000;
    return num;
  }

  // Helper method to extract video sources
  private extractVideoSources(element: HTMLElement): MediaItem[] {
    const videos: MediaItem[] = [];
    
    const videoElements = element.querySelectorAll('video');
    console.log(`[Twitter] 🔍 Found ${videoElements.length} video element(s) in DOM`);
    
    videoElements.forEach((videoEl, index) => {
      if (videoEl instanceof HTMLVideoElement) {
        let src = videoEl.src;
        console.log(`[Twitter] 📺 Video ${index + 1} src: ${src ? src.substring(0, 100) + '...' : 'NO SRC'}`);
        console.log(`[Twitter] 📺 Video ${index + 1} poster: ${videoEl.poster ? videoEl.poster.substring(0, 100) + '...' : 'NO POSTER'}`);
        
        // If no src, try to get from source elements
        if (!src) {
          const sources = videoEl.querySelectorAll('source');
          console.log(`[Twitter] 🔍 Found ${sources.length} source element(s)`);
          
          for (const source of sources) {
            if (source instanceof HTMLSourceElement && source.src) {
              src = source.src;
              console.log(`[Twitter] 📺 Video ${index + 1} source src: ${src.substring(0, 100)}...`);
              break;
            }
          }
        }
        
        // If still no src, check data attributes
        if (!src) {
          const dataSrc = videoEl.getAttribute('data-src') || 
                          videoEl.getAttribute('data-video-url') ||
                          videoEl.getAttribute('data-mp4');
          if (dataSrc) {
            src = dataSrc;
            console.log(`[Twitter] 📺 Video ${index + 1} data-src: ${src.substring(0, 100)}...`);
          }
        }
        
        if (src && src.startsWith('blob:')) {
          console.log(`[Twitter] 🎥 Found blob video ${index + 1}: ${src.substring(0, 100)}...`);
          console.log(`[Twitter] 🔍 Full blob URL: ${src}`);
          console.log(`[Twitter] 🎬 Video element poster: ${videoEl.poster || 'None'}`);
          
          // Log all video element attributes for debugging
          const attrs = videoEl.getAttributeNames().map(name => `${name}="${videoEl.getAttribute(name)}"`);
          console.log(`[Twitter] 📋 Video element attributes: ${attrs.join(', ')}`);
          
          // Try to find a cached real video URL
          const cachedUrl = this.findCachedVideoUrl(videoEl);
          console.log(`[Twitter] 🔍 Cached URLs available: ${this.videoUrlCache.size}`);
          if (this.videoUrlCache.size > 0) {
            console.log(`[Twitter] 📦 Cached URLs:`, Array.from(this.videoUrlCache.values()));
          }
          
          if (cachedUrl) {
            console.log(`[Twitter] ✅ Using cached video URL: ${cachedUrl.substring(0, 100)}...`);
            videos.push({ 
              type: 'video', 
              url: cachedUrl, 
              alt: `Twitter video ${index + 1}`,
              width: videoEl.videoWidth || undefined,
              height: videoEl.videoHeight || undefined
            });
          } else {
            // Try to extract from the video element's data attributes or nearby DOM
            console.log(`[Twitter] 🔍 Trying to extract real video URL from DOM...`);
            
            // Try to trigger video loading by simulating interaction
            this.attemptVideoUrlCapture(videoEl);
            
            const realUrl = this.extractRealVideoUrl(element, videoEl);
            
            if (realUrl) {
              // Check if it's actually a video URL or a poster image
              const isActualVideo = realUrl.includes('.mp4') && !realUrl.includes('thumb');
              
              if (isActualVideo) {
                console.log(`[Twitter] ✅ Extracted real video URL: ${realUrl.substring(0, 100)}...`);
                videos.push({ 
                  type: 'video', 
                  url: realUrl, 
                  alt: `Twitter video ${index + 1}`,
                  width: videoEl.videoWidth || undefined,
                  height: videoEl.videoHeight || undefined
                });
              } else {
                console.log(`[Twitter] 📸 Using high-quality video poster: ${realUrl.substring(0, 100)}...`);
                videos.push({ 
                  type: 'image', 
                  url: realUrl, 
                  alt: `Twitter video poster ${index + 1} - Original video not extractable`,
                  width: videoEl.videoWidth || undefined,
                  height: videoEl.videoHeight || undefined
                });
              }
            } else {
              // If no realUrl found, but we have a poster, use that as a fallback
              if (videoEl.poster) {
                console.log(`[Twitter] 📸 Fallback: using video poster as image: ${videoEl.poster.substring(0, 100)}...`);
                // Use the original poster URL without transformation
                videos.push({ 
                  type: 'image', 
                  url: videoEl.poster, 
                  alt: `Twitter video poster ${index + 1} - Original video not accessible`,
                  width: videoEl.videoWidth || undefined,
                  height: videoEl.videoHeight || undefined
                });
              } else {
                console.log(`[Twitter] ⚠️ No poster available, saving blob URL: ${src.substring(0, 100)}...`);
                videos.push({ 
                  type: 'video', 
                  url: src, 
                  alt: `Twitter video ${index + 1}`,
                  width: videoEl.videoWidth || undefined,
                  height: videoEl.videoHeight || undefined
                });
              }
            }
          }
        } else if (src && !src.startsWith('blob:')) {
          // If it's not a blob URL, use the actual video
          console.log(`[Twitter] 🎥 Found real video ${index + 1}: ${src.substring(0, 100)}...`);
          videos.push({ 
            type: 'video', 
            url: src, 
            alt: `Twitter video ${index + 1}`,
            width: videoEl.videoWidth || undefined,
            height: videoEl.videoHeight || undefined
          });
        } else {
          // No src found anywhere, but we have a poster - this is a video post
          console.log(`[Twitter] ❓ Video ${index + 1} has no src attribute, checking for delayed loading...`);
          
          if (videoEl.poster) {
            console.log(`[Twitter] 🎦 Video detected but not loaded yet, using poster: ${videoEl.poster.substring(0, 100)}...`);
            
            // Try to find the real video URL from DOM/cache since we know this is a video
            const cachedUrl = this.findCachedVideoUrl(videoEl);
            const realUrl = cachedUrl || this.extractRealVideoUrl(element, videoEl);
            
            if (realUrl) {
              console.log(`[Twitter] ✅ Found video URL for unloaded video: ${realUrl.substring(0, 100)}...`);
              videos.push({ 
                type: 'video', 
                url: realUrl, 
                alt: `Twitter video ${index + 1}`,
                width: videoEl.videoWidth || undefined,
                height: videoEl.videoHeight || undefined
              });
            } else {
              console.log(`[Twitter] 📸 No video URL found, using poster as image: ${videoEl.poster.substring(0, 100)}...`);
              videos.push({ 
                type: 'image', 
                url: videoEl.poster, 
                alt: `Twitter video thumbnail ${index + 1}`,
                width: videoEl.videoWidth || undefined,
                height: videoEl.videoHeight || undefined
              });
            }
          } else {
            console.log(`[Twitter] ❌ No src and no poster, skipping this video element`);
          }
        }
      }
    });
    
    return videos;
  }

  /**
   * Find a cached video URL that matches the current video element
   */
  private findCachedVideoUrl(videoEl: HTMLVideoElement): string | null {
    // Get the most recent cached URL (simple approach)
    const urls = Array.from(this.videoUrlCache.values());
    return urls.length > 0 ? urls[urls.length - 1] : null;
  }

  /**
   * Attempt to trigger video loading by simulating user interaction
   * This might cause Twitter to make the actual video request
   */
  private attemptVideoUrlCapture(videoEl: HTMLVideoElement): void {
    try {
      console.log(`[Twitter] 🎯 Attempting to trigger video loading...`);
      
      // Store current state
      const originalAutoplay = videoEl.autoplay;
      const originalMuted = videoEl.muted;
      
      // Set properties that might trigger loading
      videoEl.muted = true;
      videoEl.preload = 'metadata';
      
      // Try to load the video
      try {
        videoEl.load();
      } catch (e) {
        console.log(`[Twitter] ⚠️ Video load() failed: ${e}`);
      }
      
      // Try playing briefly (muted)
      const playPromise = videoEl.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          console.log(`[Twitter] ▶️ Video play started, pausing...`);
          setTimeout(() => {
            try {
              videoEl.pause();
              videoEl.currentTime = 0;
            } catch (e) {
              // Ignore errors
            }
          }, 100);
        }).catch(e => {
          console.log(`[Twitter] ⚠️ Video play failed: ${e}`);
        });
      }
      
      // Restore original state
      setTimeout(() => {
        videoEl.autoplay = originalAutoplay;
        videoEl.muted = originalMuted;
      }, 200);
      
    } catch (error) {
      console.log(`[Twitter] ❌ Error in video capture attempt: ${error}`);
    }
  }

  /**
   * Try to extract the real video URL from DOM or data attributes
   */
  private extractRealVideoUrl(element: HTMLElement, videoEl: HTMLVideoElement): string | null {
    console.log(`[Twitter] 🔍 Starting real video URL extraction...`);
    
    // First, try to construct video URL from poster URL
    const poster = videoEl.poster;
    if (poster && poster.includes('amplify_video_thumb')) {
      console.log(`[Twitter] 🎬 Found amplify_video_thumb poster: ${poster.substring(0, 100)}...`);
      
      // Extract video ID from poster URL
      // Pattern: https://pbs.twimg.com/amplify_video_thumb/1937461956862218240/img/2knZotI0yHjt7xMx.jpg
      const videoIdMatch = poster.match(/amplify_video_thumb\/(\d+)/);
      if (videoIdMatch) {
        const videoId = videoIdMatch[1];
        console.log(`[Twitter] 📹 Extracted video ID: ${videoId}`);
        
        // Try to find the actual video URL from the same tweet's DOM
        console.log(`[Twitter] 🔍 Searching DOM for actual video URL with ID: ${videoId}`);
        const actualVideoUrl = this.findActualVideoUrlFromDOM(element, videoId);
        if (actualVideoUrl) {
          console.log(`[Twitter] ✅ Found actual video URL from DOM: ${actualVideoUrl}`);
          return actualVideoUrl;
        } else {
          console.log(`[Twitter] ❌ No actual video URL found in DOM for ID: ${videoId}`);
        }
        
        // Since constructing video URLs is unreliable, let's be practical:
        // Save the poster image as a high-quality image with a note that this was a video
        console.log(`[Twitter] 💡 Practical fallback: saving poster image for video ${videoId}`);
        
        // Use the original poster URL - don't try to transform it as video thumbnails 
        // have different URL patterns than regular Twitter images
        console.log(`[Twitter] 📸 Using original poster as video representation: ${poster}`);
        return poster;
      }
    }
    
    // Look for data attributes that might contain the video URL
    const dataAttrs = ['data-video-url', 'data-src', 'data-mp4', 'data-video', 'data-url'];
    for (const attr of dataAttrs) {
      const url = videoEl.getAttribute(attr);
      if (url && (url.includes('.mp4') || url.includes('video'))) {
        console.log(`[Twitter] ✅ Found video URL in ${attr}: ${url.substring(0, 100)}...`);
        return url;
      }
    }

    // Look for URLs in video element's attributes (excluding poster/thumbnail)
    const allAttrs = videoEl.getAttributeNames();
    for (const attr of allAttrs) {
      const value = videoEl.getAttribute(attr);
      if (value && value.includes('.mp4') && !value.includes('thumb') && !value.includes('jpg') && !value.includes('png')) {
        console.log(`[Twitter] ✅ Found video URL in ${attr}: ${value.substring(0, 100)}...`);
        return value;
      }
    }

    // Look in parent elements for video URLs
    let currentElement = element;
    for (let i = 0; i < 3; i++) { // Go up 3 levels
      if (!currentElement) break;
      
      const htmlContent = currentElement.innerHTML;
      const urlMatches = [
        /https:\/\/video\.twimg\.com\/amplify_video\/\d+\/vid\/[^"'\s]*\.mp4[^"'\s]*/g,
        /https:\/\/video\.twimg\.com\/ext_tw_video\/\d+\/pu\/vid\/[^"'\s]*\.mp4[^"'\s]*/g,
        /https:\/\/[^"'\s]*\.mp4[^"'\s]*/g,
        /https:\/\/video\.twimg\.com\/[^"'\s]*\.mp4[^"'\s]*/g,
        /https:\/\/[^"'\s]*video[^"'\s]*\.mp4[^"'\s]*/g
      ];
      
      for (const regex of urlMatches) {
        const matches = htmlContent.match(regex);
        if (matches && matches.length > 0) {
          // Filter out thumbnail URLs
          const videoUrl = matches.find(url => 
            !url.includes('thumb') && 
            !url.includes('jpg') && 
            !url.includes('png') &&
            url.includes('.mp4')
          );
          if (videoUrl) {
            console.log(`[Twitter] ✅ Found video URL in HTML (level ${i}): ${videoUrl.substring(0, 100)}...`);
            return videoUrl;
          }
        }
      }
      
      currentElement = currentElement.parentElement;
    }

    // Look in nearby script tags for video URLs
    const scripts = element.querySelectorAll('script');
    console.log(`[Twitter] 🔍 Checking ${scripts.length} script elements...`);
    
    for (const script of scripts) {
      const content = script.textContent || script.innerHTML;
      if (content) {
        // Look for video URLs in script content (excluding thumbnails)
        const urlMatches = [
          /https:\/\/video\.twimg\.com\/amplify_video\/\d+\/vid\/[^"'\s]*\.mp4[^"'\s]*/g,
          /https:\/\/video\.twimg\.com\/ext_tw_video\/\d+\/pu\/vid\/[^"'\s]*\.mp4[^"'\s]*/g,
          /https:\/\/video\.twimg\.com\/[^"'\s]*\.mp4[^"'\s]*/g,
          /https:\/\/[^"'\s]*\.mp4[^"'\s]*/g
        ];
        
        for (const regex of urlMatches) {
          const matches = content.match(regex);
          if (matches && matches.length > 0) {
            // Filter out thumbnail URLs
            const videoUrl = matches.find(url => 
              !url.includes('thumb') && 
              !url.includes('jpg') && 
              !url.includes('png') &&
              url.includes('.mp4')
            );
            if (videoUrl) {
              console.log(`[Twitter] ✅ Found video URL in script: ${videoUrl.substring(0, 100)}...`);
              return videoUrl;
            }
          }
        }
        
        // Try to parse as JSON
        try {
          const data = JSON.parse(content);
          const videoUrl = this.findVideoUrlInObject(data);
          if (videoUrl) {
            console.log(`[Twitter] ✅ Found video URL in JSON: ${videoUrl.substring(0, 100)}...`);
            return videoUrl;
          }
        } catch (e) {
          // Not JSON, continue
        }
      }
    }

    console.log(`[Twitter] ❌ No real video URL found in DOM extraction`);
    return null;
  }

  /**
   * Recursively search for video URLs in an object
   */
  private findVideoUrlInObject(obj: any): string | null {
    if (typeof obj === 'string' && obj.includes('.mp4') && 
        !obj.includes('thumb') && !obj.includes('jpg') && !obj.includes('png')) {
      return obj;
    }
    
    if (typeof obj === 'object' && obj !== null) {
      for (const value of Object.values(obj)) {
        const result = this.findVideoUrlInObject(value);
        if (result) {
          return result;
        }
      }
    }
    
    return null;
  }

  /**
   * Try to find the actual video URL from the DOM using various methods
   */
  private findActualVideoUrlFromDOM(element: HTMLElement, videoId: string): string | null {
    // Look for actual video URLs in network requests or script content
    // This searches more comprehensively for real video URLs that match the video ID
    
    // Search all script tags for video URLs containing our video ID
    const scripts = document.querySelectorAll('script');
    for (const script of scripts) {
      const content = script.textContent || script.innerHTML;
      if (content && content.includes(videoId)) {
        // Look for complete video URLs with our video ID
        const videoUrlRegex = new RegExp(`https://video\\.twimg\\.com/[^"'\\s]*${videoId}[^"'\\s]*\\.mp4[^"'\\s]*`, 'g');
        const matches = content.match(videoUrlRegex);
        if (matches && matches.length > 0) {
          const videoUrl = matches.find(url => 
            !url.includes('thumb') && 
            !url.includes('jpg') && 
            !url.includes('png') &&
            url.includes(videoId)
          );
          if (videoUrl) {
            console.log(`[Twitter] 🎯 Found actual video URL in script: ${videoUrl.substring(0, 100)}...`);
            return videoUrl;
          }
        }
      }
    }
    
    // Look in the tweet element's HTML for video URLs
    const tweetHtml = element.innerHTML;
    if (tweetHtml.includes(videoId)) {
      const videoUrlRegex = new RegExp(`https://video\\.twimg\\.com/[^"'\\s]*${videoId}[^"'\\s]*\\.mp4[^"'\\s]*`, 'g');
      const matches = tweetHtml.match(videoUrlRegex);
      if (matches && matches.length > 0) {
        const videoUrl = matches.find(url => 
          !url.includes('thumb') && 
          !url.includes('jpg') && 
          !url.includes('png') &&
          url.includes(videoId)
        );
        if (videoUrl) {
          console.log(`[Twitter] 🎯 Found actual video URL in tweet HTML: ${videoUrl.substring(0, 100)}...`);
          return videoUrl;
        }
      }
    }
    
    console.log(`[Twitter] 🔍 No actual video URL found in DOM for video ID: ${videoId}`);
    return null;
  }



  async extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult> {
    try {
      const savedAt = new Date().toISOString();

      // --- Author Info ---
      let authorNameElement = element.querySelector('[data-testid="User-Name"] span span');
      if (!authorNameElement) authorNameElement = element.querySelector('a[role="link"] div[dir="auto"] span span');
      let authorHandleElement = element.querySelector('[data-testid="User-Name"] div[dir="ltr"] span');
      if (!authorHandleElement) authorHandleElement = element.querySelector('a[role="link"] div[dir="ltr"] span');
      let authorAvatarElement = element.querySelector('[data-testid="Tweet-User-Avatar"] img');
      if (!authorAvatarElement) authorAvatarElement = element.querySelector('a[role="link"] img[src*="twimg.com"]');

      const authorName = authorNameElement?.textContent?.trim() || '';
      const initialAuthorHandle = authorHandleElement?.textContent?.trim().replace('@', '') || '';
      const authorAvatar = authorAvatarElement?.getAttribute('src') || '';

      // --- Tweet Content ---
      let tweetTextElement = element.querySelector('[data-testid="tweetText"]');
      if (!tweetTextElement) tweetTextElement = element.querySelector('div[lang][dir="auto"]:not([data-testid])'); // More specific to avoid grabbing quoted tweet text prematurely
      
      let content = '';
      if (tweetTextElement) {
          // Check if there's a "Show more" button and try to expand content first
          const showMoreButton = tweetTextElement.querySelector('[role="button"]');
          if (showMoreButton && (showMoreButton.textContent?.includes('Show more') || showMoreButton.textContent?.includes('Show More'))) {
            try {
              // Click the "Show more" button to expand the content
              console.log('[Twitter] Found "Show more" button, attempting to expand content');
              (showMoreButton as HTMLElement).click();
              
              // Wait a brief moment for content to expand
              await new Promise(resolve => setTimeout(resolve, 100));
              
              // Re-query the text element after expansion (it might have changed)
              tweetTextElement = element.querySelector('[data-testid="tweetText"]') || tweetTextElement;
            } catch (error) {
              console.warn('[Twitter] Failed to click "Show more" button:', error);
            }
          }

          const clonedTextElement = tweetTextElement.cloneNode(true) as HTMLElement;
          clonedTextElement.querySelectorAll('[role="button"]').forEach(btn => btn.remove()); // Remove "Show more" button after expansion
          content = clonedTextElement.textContent?.trim() || '';
          
          console.log(`[Twitter] Extracted content length: ${content.length} chars`);
      }
      if (!content) { // Fallback for some quoted tweet structures or simple text displays
          const simpleText = element.querySelector('article div[lang][dir]');
          if(simpleText && simpleText.closest('article[data-testid="tweet"]')?.isSameNode(element.closest('article[data-testid="tweet"]'))) {
            content = simpleText.textContent?.trim() || '';
          }
      }

      // --- Permalink ---
      const timeElement = element.querySelector('time');
      let permalink = timeElement?.closest('a')?.href || '';

      if (!permalink) {
        const selectorsToTry = [
          'a[href*="/status/"][aria-describedby][role="link"][target="_blank"]', // Common for main tweet link
          'a[href*="/status/"][aria-label*="ago"][role="link"]', // Links with time information
          'article[data-testid="tweet"] a[href*="/status/"]',
          'div[data-testid="tweetPhoto"] a[href*="/status/"]'
        ];
        for (const selector of selectorsToTry) {
          const linkElement = element.querySelector(selector) as HTMLAnchorElement | null;
          if (linkElement?.href && linkElement.href.includes('/status/')) {
            // Ensure it's the permalink of the current tweet, not a quoted one by checking proximity or context
            if (linkElement.closest('article[data-testid="tweet"]')?.isSameNode(element.closest('article[data-testid="tweet"]'))) {
                 permalink = linkElement.href;
                 break;
            }
          }
        }
      }
      
      if (!permalink && initialAuthorHandle) {
        const pseudoId = `pseudo-${Date.now()}`;
        permalink = `https://twitter.com/${initialAuthorHandle}/status/${pseudoId}`;
      } else if (!permalink) {
        console.error('[Notely CRITICAL] PERMALINK COULD NOT BE DETERMINED.');
        return { success: false, error: 'Failed to extract permalink.' };
      }

      // --- Author Handle and URL from Permalink ---
      let authorHandleFromPermalink = '';
      try {
        const url = new URL(permalink);
        const pathParts = url.pathname.split('/');
        if (pathParts.length >= 3 && pathParts[2] === 'status') {
          authorHandleFromPermalink = pathParts[1];
        }
      } catch { /* Ignore parsing errors */ }
      
      const finalAuthorHandle = authorHandleFromPermalink || initialAuthorHandle;
      const finalAuthorUrl = finalAuthorHandle ? `https://twitter.com/${finalAuthorHandle}` : '';

      // --- Tweet ID from Permalink ---
      let id = '';
      const idMatch = permalink.match(/status\/(\d+)/);
      if (idMatch && idMatch[1]) {
        id = idMatch[1];
      } else if (permalink.includes('/status/pseudo-')) {
        id = permalink.substring(permalink.lastIndexOf('/') + 1);
      }

      if (!id) {
        console.error('[Notely CRITICAL] TWEET ID COULD NOT BE EXTRACTED from permalink:', permalink);
        return { success: false, error: 'Failed to extract tweet ID from permalink: ' + permalink };
      }

      // --- Timestamp ---
      const timestamp = timeElement?.getAttribute('datetime') || savedAt;

      // --- Stats ---
      const stats = {
        likes: this.extractStat(element, '[data-testid="like"]'),
        comments: this.extractStat(element, '[data-testid="reply"]'),
        shares: this.extractStat(element, '[data-testid="retweet"]'),
        views: this.extractStat(element, 'a[href$="/analytics"], div[role="group"][aria-label*="views"]'),
      };

      // --- Media ---
      let mediaItems: MediaItem[] = [];
      
      // Extract videos first
      const videoItems = this.extractVideoSources(element);
      if (videoItems.length > 0) {
        console.log(`[Twitter] 🎥 Found ${videoItems.length} video(s)`);
        mediaItems = mediaItems.concat(videoItems);
      }
      
      // Extract images if requested and no videos found (to avoid duplicates)
      if (options?.includeImages !== false && videoItems.length === 0) {
        const imageResult = await this.extractImageUrls(element);
        if (imageResult.success && imageResult.images) {
          console.log(`[Twitter] 🖼️ Found ${imageResult.images.length} image(s)`);
          mediaItems = mediaItems.concat(imageResult.images);
        }
      }

      // --- Construct Post ---
      const postData: Post = {
        id: id,
        platform: this.platform, // this.platform is 'X/Twitter', correctly typed from BasePlatformService
        author: authorName || finalAuthorHandle, // Simplified author string
        authorName: authorName,
        authorHandle: finalAuthorHandle,
        authorUrl: finalAuthorUrl,
        authorAvatar: authorAvatar,
        content: content, // Main text content
        textContent: content, // Potentially enriched later
        title: content.substring(0, 100), // Basic title from content
        createdAt: timestamp || new Date(0).toISOString(), // Original post timestamp
        savedAt: savedAt,
        permalink: permalink,
        media: mediaItems, // Use the combined media items
        stats: stats,
        categories: [], // Initialize as empty array
        tags: [],       // Initialize as empty array
      };

      return {
        success: true,
        post: postData
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract image URLs from a Twitter tweet element.
   * Implements abstract method from BasePlatformService.
   * @param element The tweet HTMLElement
   * @param options Options for extraction (currently unused by this implementation)
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  async extractImageUrls(element: HTMLElement, _options?: ExtractionOptions): Promise<ImageExtractionResult> {
    try {
      const images: MediaItem[] = [];
      const uniqueUrls = new Set<string>();

      // Twitter-specific image extraction logic from DOM
      // Enhanced to better detect multi-image carousels

      // Strategy 1: Look for media containers (handles both single and multi-image posts)
      const mediaContainers = element.querySelectorAll('[data-testid="tweetPhoto"], [data-testid="videoPlayer"]');

      mediaContainers.forEach((mediaContainer) => {
        // Handle images within each media container
        const imgElements = mediaContainer.querySelectorAll('img[src*="twimg.com"]');
        imgElements.forEach((img) => {
          if (img instanceof HTMLImageElement && img.src) {
            // Skip small images (likely avatars or icons)
            if (img.width > 100 && img.height > 100) {
              // Get the highest quality version of the image
              let imageUrl = img.src;
              // Replace Twitter's image sizing parameters to get the original size
              imageUrl = imageUrl.replace(/&name=\w+/, '&name=orig');

              if (!uniqueUrls.has(imageUrl)) {
                uniqueUrls.add(imageUrl);
                images.push({
                  type: 'image',
                  url: imageUrl,
                  alt: img.alt || 'Twitter image'
                });
              }
            }
          }
        });

        // Handle videos within each media container
        const videoElements = mediaContainer.querySelectorAll('video');
        videoElements.forEach((video) => {
          if (video instanceof HTMLVideoElement && video.src) {
            if (!uniqueUrls.has(video.src)) {
              uniqueUrls.add(video.src);
              images.push({
                type: 'video',
                url: video.src,
                alt: 'Twitter video'
              });
            }
          }
        });

        // If no video source found directly, try to get the poster image
        if (videoElements.length > 0 && images.filter(img => img.type === 'video').length === 0) {
          const poster = videoElements[0].poster;
          if (poster && !uniqueUrls.has(poster)) {
            uniqueUrls.add(poster);
            images.push({
              type: 'image',
              url: poster,
              alt: 'Twitter video thumbnail'
            });
          }
        }
      });

      // Strategy 2: Look for carousel indicators or multiple image containers
      // Twitter sometimes uses different structures for multi-image posts
      if (images.length <= 1) {
        const carouselImages = element.querySelectorAll('div[role="group"] img[src*="twimg.com"], div[aria-label*="Image"] img[src*="twimg.com"]');
        carouselImages.forEach((img) => {
          if (img instanceof HTMLImageElement && img.src && img.width > 100 && img.height > 100) {
            let imageUrl = img.src.replace(/&name=\w+/, '&name=orig');
            if (!uniqueUrls.has(imageUrl)) {
              uniqueUrls.add(imageUrl);
              images.push({
                type: 'image',
                url: imageUrl,
                alt: img.alt || 'Twitter carousel image'
              });
            }
          }
        });
      }

      // Check for GIFs
      const gifContainer = element.querySelector('[data-testid="tweetGif"]');
      if (gifContainer) {
        const gifImg = gifContainer.querySelector('img[src*="twimg.com"]');
        if (gifImg instanceof HTMLImageElement && gifImg.src) {
          let gifUrl = gifImg.src;
          // Try to get the highest quality version
          gifUrl = gifUrl.replace(/&name=\w+/, '&name=orig');

          if (!uniqueUrls.has(gifUrl)) {
            uniqueUrls.add(gifUrl);
            images.push({
              type: 'image',
              url: gifUrl,
              alt: gifImg.alt || 'Twitter GIF'
            });
          }
        }
      }

      // Check for card images (e.g., link previews)
      const cardContainer = element.querySelector('[data-testid="card.wrapper"]');
      if (cardContainer) {
        const cardImg = cardContainer.querySelector('img[src*="twimg.com"]');
        if (cardImg instanceof HTMLImageElement && cardImg.src) {
          // Skip small images (likely avatars or icons)
          if (cardImg.width > 100 && cardImg.height > 100) {
            let cardImgUrl = cardImg.src;
            // Try to get the highest quality version
            cardImgUrl = cardImgUrl.replace(/&name=\w+/, '&name=orig');

            if (!uniqueUrls.has(cardImgUrl)) {
              uniqueUrls.add(cardImgUrl);
              images.push({
                type: 'image',
                url: cardImgUrl,
                alt: cardImg.alt || 'Twitter card image'
              });
            }
          }
        }
      }
      // The 'else' block that handled 'source' as a Post object has been removed.
      // The method now only processes an HTMLElement.

      return {
        success: true,
        images
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      await savePost(post);
      return true;
    } catch (error) {
      console.error('Error saving Twitter post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post): Promise<boolean> {
    try {
      const { syncToCloud } = await import('../../services/cloudSyncService');
      
      const postForUpload: AnalyzedPost = {
        ...post,
        categories: (post.categories as CoreSubCategorySlug[] || []),
        tags: post.tags || [],
        // Initialize AnalyzedPost specific fields not present on Post
        snapNote: null, 
        embeddingVector: [], 
        analyzedAt: new Date().toISOString(), 
        inSight: null,
        fastTake: null,
        contentIdeas: null,
      };

      const result = await syncToCloud(postForUpload);
      return result.success;
    } catch (error) {
      console.error('Error uploading Twitter post to cloud:', error);
      return false;
    }
  }
}
