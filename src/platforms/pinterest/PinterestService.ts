/**
 * Pinterest Service
 *
 * Handles Pinterest-specific post extraction and saving logic.
 * Completely fixes the Pinterest integration.
 */

import { Post, Platform, MediaItem } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult, ImageExtractionResult } from '../base/types';
import { savePost } from '../../storage';

export class PinterestService extends BasePlatformService {
  constructor() {
    super('pinterest');
  }

  /**
   * Extract post data from a Pinterest pin element
   * @param element The pin element
   * @param options Options for extraction
   * @returns ExtractionResult containing the extracted post data or an error
   */
  async extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult> {
    try {
      // --- Pin ID ---
      // Get Pin ID directly from the pinElement itself
      let pinId = element.dataset.testPinId;

      // If no pin ID in dataset, try to extract from a link
      if (!pinId) {
        const pinLink = element.querySelector<HTMLAnchorElement>('a[href*="/pin/"]');
        if (pinLink) {
          const pinIdMatch = pinLink.href.match(/\/pin\/(\d+)/);
          if (pinIdMatch && pinIdMatch[1]) {
            pinId = pinIdMatch[1];
          }
        }
      }

      if (!pinId) {
        console.error('Pinterest: Could not find pin ID');
        return {
          success: false,
          error: 'Could not find pin ID in Pinterest pin'
        };
      }

      // --- Permalink ---
      // Try multiple selectors to find the pin link
      const linkSelectors = [
        'a[href*="/pin/"]',
        'a[data-test-id="pinLink"]',
        'a[data-test-id="pin-link"]',
        'a[role="button"][href*="/pin/"]'
      ];

      let linkElement: HTMLAnchorElement | null = null;
      for (const selector of linkSelectors) {
        linkElement = element.querySelector<HTMLAnchorElement>(selector);
        if (linkElement && linkElement.href) {
          break;
        }
      }

      // Build permalink - prefer found link, fallback to constructed URL
      let permalink = '';
      if (linkElement?.href) {
        permalink = linkElement.href;
        // Ensure it's a full URL and preserve the current domain
        if (permalink.startsWith('/')) {
          // Use the current domain (could be tr.pinterest.com, www.pinterest.com, etc.)
          const currentDomain = window.location.origin;
          permalink = `${currentDomain}${permalink}`;
        }
      } else {
        // Use current domain for constructed URL too
        const currentDomain = window.location.origin;
        permalink = `${currentDomain}/pin/${pinId}/`;
      }

      // --- Image and Alt text ---
      // Try different selectors for images
      let imgElement = element.querySelector<HTMLImageElement>('img.hCL');

      // If not found, try other common selectors
      if (!imgElement || !imgElement.src) {
        const alternativeSelectors = [
          'div[data-test-id="pin"] img',
          'div[data-test-id="pinWrapper"] img',
          'div[data-test-id="pinCard"] img',
          'div[data-test-id="pinImageWrapper"] img',
          'img[loading="eager"]',
          'img[decoding="sync"]'
        ];

        for (const selector of alternativeSelectors) {
          imgElement = element.querySelector<HTMLImageElement>(selector);
          if (imgElement && imgElement.src) {
            break;
          }
        }
      }

      // If still not found, try any image
      if (!imgElement || !imgElement.src) {
        imgElement = element.querySelector<HTMLImageElement>('img');
      }

      if (!imgElement || !imgElement.src) {
        console.error('Pinterest: Could not find media URL');
        return {
          success: false,
          error: 'Could not find media URL in Pinterest pin'
        };
      }

      const mediaUrl = imgElement.src;
      const mediaAlt = imgElement.alt;

      // --- Pin Title and Description ---
      // Extract title from aria-label
      let pinTitle = '';
      let pinDescription = '';

      // Try to get title from aria-label
      const ariaLabel = linkElement?.getAttribute('aria-label');
      let useAriaLabelAsTitle = false;

      if (ariaLabel) {
        const trimmedAriaLabel = ariaLabel.trim();
        // Check for known non-title prefixes (case-insensitive)
        const isImageSourcePrefix = /^Görüntü kaynağı:/i.test(trimmedAriaLabel) ||
                                   /^Image source:/i.test(trimmedAriaLabel);

        if (!isImageSourcePrefix && trimmedAriaLabel.length > 0) {
          pinTitle = trimmedAriaLabel;
          useAriaLabelAsTitle = true;
        }
      }

      // Try to get title from alt text if not found in aria-label
      if (!pinTitle && mediaAlt) {
        pinTitle = mediaAlt;
      }

      // Try to get description from dedicated description elements
      const descriptionElement = element.querySelector('div[data-test-id="pinDescription"], div[data-test-id="description"]');
      if (descriptionElement) {
        pinDescription = descriptionElement.textContent?.trim() || '';
      }

      // --- Author Information ---
      // Try to find author information with updated selectors
      let authorName = '';
      let authorUrl = '';
      let authorAvatar = '';

      // Try multiple selectors for author information
      // Look in the current element and also check parent elements
      const authorSelectors = [
        'a[data-test-id="pinCreatorName"]',
        'a[data-test-id="username"]',
        'a[href*="/user/"]',
        'a[href*="/_created/"]',
        'div[data-test-id="pinCreatorName"] a',
        'div[data-test-id="creator-profile"] a',
        '[data-test-id="creator-profile-link"]',
        'a[aria-label*="profile"]',
        // Look for author links in the footer area specifically
        '[data-test-id="pinrep-footer"] a[href^="/"][href$="/"][aria-label]:not([href*="/pin/"]):not([href*="/search"])',
        '[data-test-id="pointer-events-wrapper"] a[href^="/"][href$="/"][aria-label]:not([href*="/pin/"]):not([href*="/search"])',
        // More specific selector for Pinterest author links - exclude pin links and search links
        'a[href^="/"][href$="/"][aria-label]:not([href*="/pin/"]):not([href*="/search"]):not([aria-label*="Pin sayfası"]):not([aria-label*="Pin page"])'
      ];

      let authorElement: HTMLElement | null = null;

      // First try to find in the current element
      for (const selector of authorSelectors) {
        authorElement = element.querySelector(selector);
        if (authorElement) {
          const text = authorElement.textContent?.trim();
          const ariaLabel = authorElement.getAttribute('aria-label')?.trim();
          console.log(`Pinterest: Testing selector "${selector}" - found element with text: "${text}", aria-label: "${ariaLabel}"`);

          if (text || ariaLabel) {
            console.log(`Pinterest: Found author element with selector: ${selector}`);
            break;
          }
        } else {
          console.log(`Pinterest: Selector "${selector}" found no elements`);
        }
      }

      // If not found, try looking in parent elements (sometimes author info is outside the pin element)
      if (!authorElement) {
        console.log('Pinterest: Searching in parent elements...');

        // Try multiple levels of parent elements
        const parentLevels = [
          element.parentElement,
          element.parentElement?.parentElement,
          element.parentElement?.parentElement?.parentElement,
          element.parentElement?.parentElement?.parentElement?.parentElement
        ];

        for (let i = 0; i < parentLevels.length; i++) {
          const parentElement = parentLevels[i];
          if (parentElement) {
            console.log(`Pinterest: Checking parent level ${i + 1}`);

            for (const selector of authorSelectors) {
              authorElement = parentElement.querySelector(selector);
              if (authorElement) {
                const text = authorElement.textContent?.trim();
                const ariaLabel = authorElement.getAttribute('aria-label')?.trim();
                console.log(`Pinterest: Testing parent selector "${selector}" - found element with text: "${text}", aria-label: "${ariaLabel}"`);

                if (text || ariaLabel) {
                  console.log(`Pinterest: Found author element in parent level ${i + 1} with selector: ${selector}`);
                  break;
                }
              }
            }

            if (authorElement) break;
          }
        }
      }

      // If still not found, try looking for any link that might contain a username
      if (!authorElement) {
        const allLinks = element.querySelectorAll('a');
        console.log(`Pinterest: Fallback search - found ${allLinks.length} total links in element`);

        for (const link of allLinks) {
          const text = link.textContent?.trim();
          const href = link.href;
          const ariaLabel = link.getAttribute('aria-label')?.trim();

          console.log(`Pinterest: Checking link - href: "${href}", aria-label: "${ariaLabel}", text: "${text}"`);

          // Look for links that might be usernames (not pin links, not empty)
          // Exclude links with "Pin sayfası", "Pin page", or long descriptive text
          if (href && href.match(/^https?:\/\/[^\/]*pinterest\.com\/[^\/]+\/$/) &&
              ariaLabel && ariaLabel.length > 0 && ariaLabel.length < 50 &&
              !href.includes('/pin/') && !href.includes('/search') &&
              !ariaLabel.includes('Pin sayfası') && !ariaLabel.includes('Pin page') &&
              !ariaLabel.includes('Charming') && !ariaLabel.includes('Doll') &&
              !ariaLabel.includes('Dress') && !ariaLabel.includes('Elegant')) {
            authorElement = link;
            console.log(`Pinterest: Found potential author link with aria-label: ${ariaLabel}`);
            break;
          }
        }
      }

      // If still not found, try a broader search in the document for any Pinterest user links
      if (!authorElement) {
        console.log('Pinterest: Trying broader document search for author links...');

        // Look for any Pinterest user profile links in the document
        const documentLinks = document.querySelectorAll('a[href*="pinterest.com/"][href$="/"]');
        console.log(`Pinterest: Found ${documentLinks.length} potential Pinterest profile links in document`);

        for (const link of documentLinks) {
          const href = link.href;
          const ariaLabel = link.getAttribute('aria-label')?.trim();
          const text = link.textContent?.trim();

          // Skip pin links and search links
          if (!href.includes('/pin/') && !href.includes('/search') &&
              !href.includes('/ideas/') && !href.includes('/today/') &&
              (ariaLabel || text)) {

            console.log(`Pinterest: Potential author link - href: "${href}", aria-label: "${ariaLabel}", text: "${text}"`);

            // Check if this looks like a user profile link
            const userMatch = href.match(/pinterest\.com\/([^\/]+)\/$/);
            if (userMatch && userMatch[1] &&
                !userMatch[1].includes('pin') &&
                !userMatch[1].includes('search') &&
                userMatch[1].length > 0 && userMatch[1].length < 50) {

              authorElement = link;
              console.log(`Pinterest: Found potential author via document search: ${userMatch[1]}`);
              break;
            }
          }
        }
      }

      if (authorElement) {
        // Extract author name - prioritize aria-label for Pinterest author links
        const ariaLabel = authorElement.getAttribute('aria-label')?.trim();
        const textContent = authorElement.textContent?.trim();

        console.log(`Pinterest: Found authorElement - tag: ${authorElement.tagName}, aria-label: "${ariaLabel}", textContent: "${textContent}"`);

        // Filter out CSS content and other unwanted text
        const isCSSContent = (text: string) => {
          return text.includes('{') || text.includes('}') || text.includes('font-family') ||
                 text.includes('color:') || text.includes('font-weight') || text.includes('::cue') ||
                 text.includes('BlinkMacSystemFont') || text.includes('-webkit-') || text.includes('sans-serif');
        };

        // For Pinterest author links, aria-label often contains the username
        if (ariaLabel && ariaLabel.length > 0 && !ariaLabel.toLowerCase().includes('profile') && !isCSSContent(ariaLabel)) {
          authorName = ariaLabel;
        } else if (textContent && textContent.length > 0 && !isCSSContent(textContent)) {
          authorName = textContent;
        } else if (ariaLabel && !isCSSContent(ariaLabel)) {
          // Fallback: clean up aria-label by removing "profile" suffix
          authorName = ariaLabel.replace(/profile$/i, '').trim();
        }

        // Extract author URL
        if (authorElement instanceof HTMLAnchorElement) {
          authorUrl = authorElement.href;
        } else {
          const linkElement = authorElement.querySelector('a');
          if (linkElement) {
            authorUrl = linkElement.href;
          }
        }

        console.log(`Pinterest: Extracted author: "${authorName}", URL: "${authorUrl}"`);

        // If we got CSS content, mark as not found and continue searching
        if (!authorName || isCSSContent(authorName)) {
          console.log('Pinterest: Author content appears to be CSS, marking as not found');
          authorName = '';
          authorUrl = '';
          authorElement = null;
        }
      }

      if (!authorElement) {
        console.warn('Pinterest: Could not find author information with any selector');

        // Fallback: Try to extract username from page context
        // Look for username in page header, profile info, etc.
        const pageAuthorSelectors = [
          '[data-test-id="profile-name"]',
          '[data-test-id="user-name"]',
          'h1[data-test-id="profile-name"]',
          '.profileName',
          '[data-test-id="header"] h1',
          'meta[property="profile:username"]'
        ];

        for (const selector of pageAuthorSelectors) {
          const pageAuthorElement = document.querySelector(selector);
          if (pageAuthorElement) {
            if (pageAuthorElement instanceof HTMLMetaElement) {
              authorName = pageAuthorElement.content || '';
            } else {
              authorName = pageAuthorElement.textContent?.trim() || '';
            }
            console.log(`Pinterest: Found page author with selector ${selector}: ${authorName}`);
            break;
          }
        }

        // If still no author found, try to extract from URL
        if (!authorName) {
          const urlMatch = window.location.pathname.match(/\/([^\/]+)\//);
          if (urlMatch && urlMatch[1] && urlMatch[1] !== 'pin') {
            authorName = urlMatch[1];
            console.log(`Pinterest: Extracted author from URL: ${authorName}`);
          }
        }
      }

      // Try to find author avatar with multiple selectors
      const avatarSelectors = [
        'img[data-test-id="userAvatar"]',
        'img[data-test-id="creatorAvatar"]',
        'img[data-test-id="creator-avatar"]',
        'div[data-test-id="creator-profile"] img',
        'div[data-test-id="pinCreatorName"] img',
        '[data-test-id="creator-profile-link"] img'
      ];

      let authorAvatarElement: HTMLImageElement | null = null;
      for (const selector of avatarSelectors) {
        authorAvatarElement = element.querySelector<HTMLImageElement>(selector);
        if (authorAvatarElement && authorAvatarElement.src) {
          console.log(`Pinterest: Found avatar element with selector: ${selector}`);
          break;
        }
      }

      if (authorAvatarElement) {
        authorAvatar = authorAvatarElement.src;
        console.log(`Pinterest: Found author avatar: ${authorAvatar.substring(0, 50)}...`);
      } else {
        console.warn('Pinterest: Could not find author avatar with any selector');
      }

      // --- Stats ---
      // Try to find like and comment counts with multiple selector strategies
      let likeCount = 0;
      let commentCount = 0;
      let saveCount = 0;

      // Strategy 1: Look for like count with multiple selectors
      const likeSelectors = [
        '[data-test-id="likeCount"]',
        '[data-test-id="likeButton"] span',
        '[data-test-id="reaction-count"]',
        '[aria-label*="like"]',
        'button[aria-label*="like"] span',
        'div[data-test-id="pin-action-bar"] button:first-child span',
        'span[class*="like"]',
        'span[class*="reaction"]',
        // Additional Pinterest-specific selectors
        'div[data-test-id="pin"] button[aria-label*="react"] span',
        'div[data-test-id="pin"] button[type="button"]:first-child span',
        'button[data-test-id="react-button"] span',
        'div[role="button"][aria-label*="react"] span'
      ];

      for (const selector of likeSelectors) {
        const likeElement = element.querySelector(selector);
        if (likeElement) {
          const likeText = likeElement.textContent?.trim() || '';
          const likeMatch = likeText.match(/(\d+(?:,\d+)*(?:\.\d+)?[KMB]?)/i);
          if (likeMatch && likeMatch[1]) {
            likeCount = this.parseCountText(likeMatch[1]);
            console.log(`Pinterest: Found like count using selector "${selector}": ${likeCount}`);
            break;
          }
        }
      }

      // Strategy 2: Look for comment count with multiple selectors
      const commentSelectors = [
        '[data-test-id="commentCount"]',
        '[data-test-id="commentButton"] span',
        '[aria-label*="comment"]',
        'button[aria-label*="comment"] span',
        'div[data-test-id="pin-action-bar"] button:nth-child(2) span',
        'span[class*="comment"]',
        // Additional Pinterest-specific selectors
        'div[data-test-id="pin"] button[aria-label*="comment"] span',
        'button[data-test-id="comment-button"] span',
        'div[role="button"][aria-label*="comment"] span'
      ];

      for (const selector of commentSelectors) {
        const commentElement = element.querySelector(selector);
        if (commentElement) {
          const commentText = commentElement.textContent?.trim() || '';
          const commentMatch = commentText.match(/(\d+(?:,\d+)*(?:\.\d+)?[KMB]?)/i);
          if (commentMatch && commentMatch[1]) {
            commentCount = this.parseCountText(commentMatch[1]);
            console.log(`Pinterest: Found comment count using selector "${selector}": ${commentCount}`);
            break;
          }
        }
      }

      // Strategy 3: Look for save count (Pinterest-specific metric)
      const saveSelectors = [
        '[data-test-id="saveCount"]',
        '[data-test-id="saveButton"] span',
        '[aria-label*="save"]',
        'button[aria-label*="save"] span',
        'div[data-test-id="pin-action-bar"] button:last-child span',
        'span[class*="save"]',
        // Additional Pinterest-specific selectors
        'div[data-test-id="pin"] button[aria-label*="save"] span',
        'button[data-test-id="save-button"] span',
        'div[role="button"][aria-label*="save"] span',
        'button[aria-label*="Save"] span'
      ];

      for (const selector of saveSelectors) {
        const saveElement = element.querySelector(selector);
        if (saveElement) {
          const saveText = saveElement.textContent?.trim() || '';
          const saveMatch = saveText.match(/(\d+(?:,\d+)*(?:\.\d+)?[KMB]?)/i);
          if (saveMatch && saveMatch[1]) {
            saveCount = this.parseCountText(saveMatch[1]);
            console.log(`Pinterest: Found save count using selector "${selector}": ${saveCount}`);
            break;
          }
        }
      }

      // Note: Pinterest feed elements typically don't show engagement statistics
      // Stats are usually only visible on individual pin detail pages

      // Log stats result
      if (likeCount === 0 && commentCount === 0 && saveCount === 0) {
        console.log('Pinterest: No engagement statistics found - this is normal for Pinterest feed elements');
      } else {
        console.log(`Pinterest: Successfully extracted stats - Likes: ${likeCount}, Comments: ${commentCount}, Saves: ${saveCount}`);
      }

      // Combine title and description for content and clean unwanted text
      let content = pinTitle ? (pinDescription ? `${pinTitle}\n\n${pinDescription}` : pinTitle) : pinDescription;

      // Remove unwanted text patterns
      const unwantedPatterns = [
        /Pin sayfası/gi,
        /Pin page/gi,
        /Pinterest User/gi,
        /\s+Pin\s*$/gi,  // Remove trailing "Pin"
        /^\s*Pin\s+/gi   // Remove leading "Pin"
      ];

      unwantedPatterns.forEach(pattern => {
        content = content.replace(pattern, '').trim();
      });

      // Clean up extra whitespace
      content = content.replace(/\s+/g, ' ').trim();

      // Create the post object
      const post: Post = {
        id: `pinterest_${pinId}`,
        platform: 'pinterest',
        author: authorName,
        authorName,
        authorUrl,
        authorAvatar,
        textContent: content,
        content,
        permalink,
        savedAt: new Date().toISOString(),
        media: [],
        stats: {
          likes: likeCount,
          comments: commentCount,
          shares: saveCount, // Pinterest uses "saves" instead of traditional shares
          views: 0
        },
        categories: [],
        tags: []
      };

      // Extract images if requested
      if (options?.includeImages !== false) {
        // Add the main image
        post.media = [{
          type: 'image',
          url: mediaUrl,
          alt: mediaAlt || 'Pinterest image'
        }];

        // Try to extract additional images
        const imageResult = await this.extractImageUrls(element, options);
        if (imageResult.success && imageResult.images && imageResult.images.length > 1) {
          // Replace with the full set of images if we found more than just the main one
          post.media = imageResult.images;
          console.log(`Pinterest: Extracted ${post.media.length} media items`);
        }
      }

      return {
        success: true,
        post
      };
    } catch (error) {
      console.error('Pinterest: Error extracting post data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Parse count text like "1.2K", "1,234", "1M" to actual numbers
   * @param countText The text containing the count
   * @returns The parsed number
   */
  private parseCountText(countText: string): number {
    if (!countText) return 0;

    // Remove any non-numeric characters except K, M, B, commas, and dots
    const cleanText = countText.replace(/[^\d.,KMB]/gi, '');

    // Handle K, M, B suffixes
    if (cleanText.toLowerCase().includes('k')) {
      const num = parseFloat(cleanText.replace(/[,k]/gi, ''));
      return Math.round(num * 1000);
    } else if (cleanText.toLowerCase().includes('m')) {
      const num = parseFloat(cleanText.replace(/[,m]/gi, ''));
      return Math.round(num * 1000000);
    } else if (cleanText.toLowerCase().includes('b')) {
      const num = parseFloat(cleanText.replace(/[,b]/gi, ''));
      return Math.round(num * 1000000000);
    } else {
      // Handle regular numbers with commas
      const num = parseFloat(cleanText.replace(/,/g, ''));
      return Math.round(num) || 0;
    }
  }



  /**
   * Extract image URLs from a Pinterest pin element or post data
   * @param source The pin element or post data
   * @param options Options for extraction
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  async extractImageUrls(source: Post | HTMLElement, options?: ExtractionOptions): Promise<ImageExtractionResult> {
    try {
      const images: MediaItem[] = [];
      const uniqueUrls = new Set<string>();

      if (source instanceof HTMLElement) {
        console.log('Pinterest: Extracting images from DOM element');

        // --- Strategy 1: Extract the main image ---
        // Try different selectors for the main image
        let mainImg = source.querySelector<HTMLImageElement>('img.hCL');

        // If not found, try other common selectors
        if (!mainImg || !mainImg.src) {
          const alternativeSelectors = [
            'div[data-test-id="pin"] img',
            'div[data-test-id="pinWrapper"] img',
            'div[data-test-id="pinCard"] img',
            'div[data-test-id="pinImageWrapper"] img',
            'img[loading="eager"]',
            'img[decoding="sync"]'
          ];

          for (const selector of alternativeSelectors) {
            mainImg = source.querySelector<HTMLImageElement>(selector);
            if (mainImg && mainImg.src) {
              break;
            }
          }
        }

        // If still not found, try any image
        if (!mainImg || !mainImg.src) {
          mainImg = source.querySelector<HTMLImageElement>('img');
        }

        if (mainImg?.src) {
          // Try to get the highest resolution version
          let mainImgUrl = mainImg.src;

          // If it has srcset, try to get the highest resolution
          if (mainImg.srcset) {
            const srcsetParts = mainImg.srcset.split(',');
            // Get the last part which typically has the highest resolution
            const lastPart = srcsetParts[srcsetParts.length - 1].trim();
            const urlMatch = lastPart.match(/^(.*?)\s/);
            if (urlMatch && urlMatch[1]) {
              mainImgUrl = urlMatch[1];
            }
          }

          // Add to images array
          if (!uniqueUrls.has(mainImgUrl)) {
            uniqueUrls.add(mainImgUrl);
            images.push({
              type: 'image',
              url: mainImgUrl,
              alt: mainImg.alt || 'Pinterest image'
            });
            console.log(`Pinterest: Added main image: ${mainImgUrl.substring(0, 50)}...`);
          }
        }

        // --- Strategy 2: Extract additional images (for carousel pins) ---
        const additionalImgs = source.querySelectorAll<HTMLImageElement>('img[srcset]');
        console.log(`Pinterest: Found ${additionalImgs.length} images with srcset`);

        additionalImgs.forEach(img => {
          // Skip if it's the main image we already added
          if (img !== mainImg && img.src) {
            // Try to get the highest resolution image from srcset
            let highestResUrl = img.src;
            if (img.srcset) {
              const srcsetParts = img.srcset.split(',');
              // Get the last part which typically has the highest resolution
              const lastPart = srcsetParts[srcsetParts.length - 1].trim();
              const urlMatch = lastPart.match(/^(.*?)\s/);
              if (urlMatch && urlMatch[1]) {
                highestResUrl = urlMatch[1];
              }
            }

            // Add to images array if not already added
            if (!uniqueUrls.has(highestResUrl)) {
              uniqueUrls.add(highestResUrl);
              images.push({
                type: 'image',
                url: highestResUrl,
                alt: img.alt || 'Pinterest image'
              });
              console.log(`Pinterest: Added additional image: ${highestResUrl.substring(0, 50)}...`);
            }
          }
        });

        // --- Strategy 3: Look for images in carousel dots ---
        const carouselDots = source.querySelectorAll('div[data-test-id="carousel-dot"]');
        console.log(`Pinterest: Found ${carouselDots.length} carousel dots`);

        if (carouselDots.length > 0) {
          carouselDots.forEach(dot => {
            // Check if it has a background image
            const style = window.getComputedStyle(dot);
            const backgroundImage = style.backgroundImage;

            if (backgroundImage && backgroundImage !== 'none') {
              // Extract URL from background-image: url("...")
              const match = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
              if (match && match[1]) {
                const imageUrl = match[1];

                // Add to images array if not already added
                if (!uniqueUrls.has(imageUrl)) {
                  uniqueUrls.add(imageUrl);
                  images.push({
                    type: 'image',
                    url: imageUrl,
                    alt: 'Pinterest carousel image'
                  });
                  console.log(`Pinterest: Added carousel dot image: ${imageUrl.substring(0, 50)}...`);
                }
              }
            }
          });
        }

        // --- Strategy 4: Extract video if present ---
        const videoElement = source.querySelector<HTMLVideoElement>('video');
        if (videoElement) {
          // Try to get the video source
          if (videoElement.src && !uniqueUrls.has(videoElement.src)) {
            uniqueUrls.add(videoElement.src);
            images.push({
              type: 'video',
              url: videoElement.src,
              alt: 'Pinterest video'
            });
            console.log(`Pinterest: Added video: ${videoElement.src.substring(0, 50)}...`);
          }
          // If no src, try to get sources from source elements
          else {
            const sources = videoElement.querySelectorAll('source');
            if (sources.length > 0) {
              // Get the highest quality source
              let bestSource: HTMLSourceElement | null = null;

              sources.forEach(source => {
                if (source instanceof HTMLSourceElement && source.src) {
                  bestSource = source;
                }
              });

              if (bestSource && bestSource.src && !uniqueUrls.has(bestSource.src)) {
                uniqueUrls.add(bestSource.src);
                images.push({
                  type: 'video',
                  url: bestSource.src,
                  alt: 'Pinterest video'
                });
                console.log(`Pinterest: Added video from source: ${bestSource.src.substring(0, 50)}...`);
              }
            }
            // If no sources, try to get the poster image
            else if (videoElement.poster && !uniqueUrls.has(videoElement.poster)) {
              uniqueUrls.add(videoElement.poster);
              images.push({
                type: 'image',
                url: videoElement.poster,
                alt: 'Pinterest video thumbnail'
              });
              console.log(`Pinterest: Added video poster: ${videoElement.poster.substring(0, 50)}...`);
            }
          }
        }

        // --- Strategy 5: Look for images in pin close-ups ---
        const closeupImages = source.querySelectorAll('div[data-test-id="pin-closeup"] img, div[data-test-id="pin-closeup-image"] img');
        console.log(`Pinterest: Found ${closeupImages.length} closeup images`);

        closeupImages.forEach(img => {
          if (img instanceof HTMLImageElement && img.src) {
            // Try to get the highest resolution version
            let imageUrl = img.src;

            // If it has srcset, try to get the highest resolution
            if (img.srcset) {
              const srcsetParts = img.srcset.split(',');
              // Get the last part which typically has the highest resolution
              const lastPart = srcsetParts[srcsetParts.length - 1].trim();
              const urlMatch = lastPart.match(/^(.*?)\s/);
              if (urlMatch && urlMatch[1]) {
                imageUrl = urlMatch[1];
              }
            }

            // Add to images array if not already added
            if (!uniqueUrls.has(imageUrl)) {
              uniqueUrls.add(imageUrl);
              images.push({
                type: 'image',
                url: imageUrl,
                alt: img.alt || 'Pinterest closeup image'
              });
              console.log(`Pinterest: Added closeup image: ${imageUrl.substring(0, 50)}...`);
            }
          }
        });
      } else {
        console.log('Pinterest: Extracting images from post data');

        // Extract images from post data
        if (source.media) {
          source.media.forEach(item => {
            if (!uniqueUrls.has(item.url)) {
              uniqueUrls.add(item.url);
              images.push(item);
            }
          });
          console.log(`Pinterest: Extracted ${images.length} media items from post data`);
        }
      }

      return {
        success: true,
        images
      };
    } catch (error) {
      console.error('Pinterest: Error extracting image URLs:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      // Use message passing to save the post since content scripts can't access chrome.storage directly
      return new Promise((resolve) => {
        chrome.runtime.sendMessage({
          action: 'SAVE_POST',
          data: post
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Pinterest: Error sending save message:', chrome.runtime.lastError);
            resolve(false);
          } else {
            console.log('Pinterest: Save message sent successfully');
            resolve(true);
          }
        });
      });
    } catch (error) {
      console.error('Pinterest: Error saving post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post): Promise<boolean> {
    try {
      const { syncToCloud } = await import('../../services/cloudSyncService');
      const result = await syncToCloud(post);
      return result.success;
    } catch (error) {
      console.error('Error uploading Pinterest post to cloud:', error);
      return false;
    }
  }
}
