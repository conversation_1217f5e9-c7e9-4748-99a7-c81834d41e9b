# Platform Modules for Notely.social Chrome Extension

This directory contains the modular platform-specific code for the Notely.social Chrome extension. Each platform (Twitter, LinkedIn, Instagram, Reddit, Pinterest) has its own module with standardized interfaces for extracting and saving posts.

## Architecture

The platform modules are organized as follows:

```
platforms/
  base/
    BasePlatformService.ts  (Abstract base class with common methods)
    types.ts                (Common types for platform modules)
  twitter/
    TwitterService.ts       (Twitter-specific implementation)
  linkedin/
    LinkedInService.ts      (LinkedIn-specific implementation)
  instagram/
    InstagramService.ts     (Instagram-specific implementation)
  reddit/
    RedditService.ts        (Reddit-specific implementation)
  pinterest/
    PinterestService.ts     (Pinterest-specific implementation)
  index.ts                  (Exports all platform services)
```

## Base Platform Service

The `BasePlatformService` is an abstract class that defines the common interface for all platform services:

- `extractPostData()`: Extract post data from DOM elements
- `extractImageUrls()`: Extract image URLs from post data
- `saveToLocal()`: Save post data to local storage
- `uploadToCloud()`: Upload post data to cloud storage
- `validatePost()`: Validate post data before saving

## Platform-Specific Services

Each platform has its own service that extends the base class and implements the platform-specific logic:

- **TwitterService**: Handles Twitter-specific post extraction and saving
- **LinkedInService**: Handles LinkedIn-specific post extraction and saving
- **InstagramServiceV2**: Handles Instagram-specific post extraction and saving, with fixes for image extraction and cloud upload
- **InstagramCloudService**: Alternative Instagram service for cloud-based processing
- **RedditService**: Handles Reddit-specific post extraction and saving, with fixes for image extraction
- **PinterestService**: Handles Pinterest-specific post extraction and saving, with a complete fix for the integration

## Platform Service Factory

The `getPlatformService()` function in `index.ts` returns the appropriate platform service based on the current platform:

```typescript
export function getPlatformService(platform: Platform): BasePlatformService {
  switch (platform) {
    case 'X/Twitter':
      return new TwitterService();
    case 'LinkedIn':
      return new LinkedInService();
    case 'Instagram':
      return new InstagramServiceV2();
    case 'Reddit':
      return new RedditService();
    case 'pinterest':
      return new PinterestService();
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
}
```

## Usage

### In Content Scripts

```typescript
import { extractPostData, savePost, extractAndSavePost } from '../content/controller';

// Extract post data
const post = await extractPostData(postElement);

// Save post
const result = await savePost(post);

// Or extract and save in one step
const result = await extractAndSavePost(postElement);
```

### In Background Script

```typescript
import { processAndSyncPostInBackground, handleSavePostRequest } from './platformController';

// Handle save post request
const result = await handleSavePostRequest(post);

// Process and sync post in background
await processAndSyncPostInBackground(post);
```

## Adding a New Platform

To add a new platform:

1. Create a new directory for the platform in `platforms/`
2. Create a new service class that extends `BasePlatformService`
3. Implement the required methods
4. Add the new platform to the `getPlatformService()` function in `index.ts`
5. Update the `detectCurrentPlatform()` function in `index.ts`

## Testing

Each platform service can be tested in isolation by creating a test file that mocks the DOM elements and tests the extraction and saving logic.

## Error Handling

All platform services include robust error handling to ensure that failures in one platform don't affect others. Each function returns standardized result objects with success/error information.
