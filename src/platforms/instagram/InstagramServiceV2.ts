/**
 * Instagram Service V2
 *
 * This version sends minimal data to the backend and lets the server handle
 * image fetching, S3 uploads, and database storage.
 */

import { Post, MediaItem } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { SaveOptions, ExtractionResult, SaveResult } from '../base/types';
import * as authService from '../../services/authService';

// Instagram-specific selectors
const SELECTORS = {
  // Author selectors
  AUTHOR: 'header a, a[href^="/"][role="link"], a[href^="/"] span, div[role="button"] span',
  AUTHOR_HANDLE_SPAN: 'header span, a[href^="/"] span',
  AUTHOR_AVATAR: 'img[alt$="\'s profile picture"][draggable="false"]',

  // Content selectors
  CAPTION: 'div[role="button"] + div, div[role="button"] + span, div[dir="auto"], span[dir="auto"], div._a9zs',

  // Image selectors
  IMAGE: 'article img[src]:not([src^="data:"]):not([alt*="profile picture"])',
  CAROUSEL_ITEM: 'div[role="button"] img[src]:not([src^="data:"])',

  // Interaction selectors - Updated for current Instagram structure
  LIKES_LINK: 'a[href*="liked_by"]',
  COMMENTS_LINK: 'a[href*="comments"]',
  LIKES_SPAN: 'a[href*="liked_by"] span.html-span',
  COMMENTS_SPAN: 'a[href*="comments"] span.html-span'
};

export class InstagramServiceV2 extends BasePlatformService {
  constructor() {
    super('Instagram');
  }

  /**
   * Extract post data from an Instagram post element
   * @param element The post element
   * @param options Options for extraction
   * @returns ExtractionResult containing the post data or an error
   */
  async extractPostData(element: HTMLElement): Promise<ExtractionResult> {
    try {
      console.log('Instagram V2: Extracting post data');

      // --- Post ID ---
      // Extract the post ID (shortcode) from the URL
      let shortcode = '';
      const linkElement = element.querySelector('a[href*="/p/"]');
      if (linkElement instanceof HTMLAnchorElement) {
        const match = linkElement.href.match(/\/p\/([^/?]+)/);
        if (match && match[1]) {
          shortcode = match[1];
        }
      }

      // If no shortcode found, try to find it in a time element
      if (!shortcode) {
        const timeElement = element.querySelector('time');
        if (timeElement && timeElement.parentElement instanceof HTMLAnchorElement) {
          const match = timeElement.parentElement.href.match(/\/p\/([^/?]+)/);
          if (match && match[1]) {
            shortcode = match[1];
          }
        }
      }

      if (!shortcode) {
        return {
          success: false,
          error: 'Could not extract post ID (shortcode)'
        };
      }

      // Construct the permalink
      const postUrl = `https://www.instagram.com/p/${shortcode}/`;
      console.log(`Instagram V2: Found shortcode: ${shortcode}`);

      // --- Author Info ---
      // Try different selectors for author information
      let author = '';
      let authorUrl = '';

      // First try the header link
      const authorElement = element.querySelector(SELECTORS.AUTHOR);
      if (authorElement) {
        author = authorElement.textContent?.trim() || '';
        if (authorElement instanceof HTMLAnchorElement) {
          authorUrl = authorElement.href;
        }
      }

      // If no author found, try the handle span
      if (!author) {
        const authorHandleSpan = element.querySelector(SELECTORS.AUTHOR_HANDLE_SPAN);
        if (authorHandleSpan) {
          author = authorHandleSpan.textContent?.trim() || '';
          // Try to find the parent link
          const parentLink = authorHandleSpan.closest('a');
          if (parentLink instanceof HTMLAnchorElement) {
            authorUrl = parentLink.href;
          }
        }
      }

      // Extract author avatar
      let authorAvatar = '';
      const authorAvatarElement = element.querySelector(SELECTORS.AUTHOR_AVATAR);
      if (authorAvatarElement instanceof HTMLImageElement) {
        authorAvatar = authorAvatarElement.src;
      }

      console.log(`Instagram V2: Author: ${author}, Avatar URL: ${authorAvatar ? 'Found' : 'Not found'}`);

      // --- Post Content ---
      // Extract post caption
      let caption = '';
      const captionElement = element.querySelector(SELECTORS.CAPTION);
      if (captionElement) {
        caption = captionElement.textContent?.trim() || '';
      }

      // If no caption found, try other selectors
      if (!caption) {
        const altCaptionElements = element.querySelectorAll('div[dir="auto"]');
        altCaptionElements.forEach(el => {
          // Skip if it's likely a username or timestamp
          if (el.textContent &&
              el.textContent.length > 5 &&
              !el.textContent.includes('@') &&
              !el.textContent.includes('ago')) {
            caption += el.textContent.trim() + ' ';
          }
        });
        caption = caption.trim();
      }

      // --- Timestamp ---
      // Extract the timestamp
      let timestamp = '';
      const timeElement = element.querySelector('time');
      if (timeElement) {
        timestamp = timeElement.dateTime || timeElement.getAttribute('datetime') || '';
      }

      // --- Interactions ---
      // Extract likes and comments with improved selectors
      let likeCount = 0;
      let commentCount = 0;

      // Try the most specific selectors first - target the exact HTML structure
      console.log(`Instagram V2: Looking for likes and comments using specific selectors`);

      // Look for likes using the exact structure: a[href*="liked_by"] span.html-span
      const likesSpan = element.querySelector(SELECTORS.LIKES_SPAN);
      if (likesSpan) {
        const likesText = likesSpan.textContent?.trim();
        if (likesText) {
          console.log(`Instagram V2: Found likes span with text: "${likesText}"`);
          likeCount = this.parseCount(likesText);
        }
      } else {
        console.log(`Instagram V2: No likes span found with selector: ${SELECTORS.LIKES_SPAN}`);
      }

      // Look for comments using the exact structure: a[href*="comments"] span.html-span
      const commentsSpan = element.querySelector(SELECTORS.COMMENTS_SPAN);
      if (commentsSpan) {
        const commentsText = commentsSpan.textContent?.trim();
        if (commentsText) {
          console.log(`Instagram V2: Found comments span with text: "${commentsText}"`);
          commentCount = this.parseCount(commentsText);
        }
      } else {
        console.log(`Instagram V2: No comments span found with selector: ${SELECTORS.COMMENTS_SPAN}`);
      }

      // Fallback: if still no likes/comments found, try broader search
      if (likeCount === 0) {
        console.log(`Instagram V2: Fallback - searching for likes link`);
        const likesLink = element.querySelector(SELECTORS.LIKES_LINK);
        if (likesLink) {
          console.log(`Instagram V2: Found likes link, searching for html-span inside`);
          const htmlSpan = likesLink.querySelector('span.html-span');
          if (htmlSpan) {
            console.log(`Instagram V2: Found html-span in likes link: "${htmlSpan.textContent}"`);
            likeCount = this.parseCount(htmlSpan.textContent);
          }
        }
      }

      if (commentCount === 0) {
        console.log(`Instagram V2: Fallback - searching for comments link`);
        const commentsLink = element.querySelector(SELECTORS.COMMENTS_LINK);
        if (commentsLink) {
          console.log(`Instagram V2: Found comments link, searching for html-span inside`);
          const htmlSpan = commentsLink.querySelector('span.html-span');
          if (htmlSpan) {
            console.log(`Instagram V2: Found html-span in comments link: "${htmlSpan.textContent}"`);
            commentCount = this.parseCount(htmlSpan.textContent);
          }
        }
      }

      console.log(`Instagram V2: Likes: ${likeCount}, Comments: ${commentCount}`);

      // --- Media URLs ---
      // Extract image URLs (we'll only send the URLs to the backend)
      const mediaItems: MediaItem[] = [];

      // Find all image elements
      const imgElements = element.querySelectorAll<HTMLImageElement>(SELECTORS.IMAGE);
      console.log(`Instagram V2: Found ${imgElements.length} potential image elements`);

      // Process each image
      for (const img of imgElements) {
        if (img.src &&
            !img.src.includes('data:image') &&
            !img.alt?.includes("profile picture") &&
            img.src !== authorAvatar) {

          // Use srcset if available for higher quality images
          const srcset = img.srcset;
          let bestUrl = img.src;

          if (srcset) {
            // Parse srcset to get the highest quality image
            const srcsetParts = srcset.split(',');
            let highestWidth = 0;

            srcsetParts.forEach(part => {
              const [url, width] = part.trim().split(' ');
              const numWidth = parseInt(width?.replace('w', '') || '0');
              if (numWidth > highestWidth) {
                highestWidth = numWidth;
                bestUrl = url;
              }
            });
          }

          mediaItems.push({
            type: 'image',
            url: bestUrl,
            alt: img.alt || 'Instagram image'
          });
        }
      }

      // If no images found, try carousel items
      if (mediaItems.length === 0) {
        const carouselItems = element.querySelectorAll<HTMLImageElement>(SELECTORS.CAROUSEL_ITEM);
        console.log(`Instagram V2: Found ${carouselItems.length} potential carousel items`);

        for (const img of carouselItems) {
          if (img.src &&
              !img.src.includes('data:image') &&
              !img.alt?.includes("profile picture") &&
              img.src !== authorAvatar) {

            mediaItems.push({
              type: 'image',
              url: img.src,
              alt: img.alt || 'Instagram carousel image'
            });
          }
        }
      }

      console.log(`Instagram V2: Extracted ${mediaItems.length} media items`);

      // Create the post object with minimal data
      const post: Post = {
        id: shortcode,
        platform: 'Instagram',
        author,
        authorName: author,
        authorUrl,
        authorAvatar,
        textContent: caption,
        content: caption,
        permalink: postUrl,
        timestamp,
        savedAt: new Date().toISOString(),
        media: mediaItems,
        stats: {
          likes: likeCount,
          comments: commentCount,
          shares: 0,
          views: 0
        }
      };

      return {
        success: true,
        post
      };
    } catch (error) {
      console.error('Instagram V2: Error extracting post data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Parse a count string (e.g., "1,234 likes" or "1.2K comments")
   * @param countText The count text to parse
   * @returns The parsed count as a number
   */
  private parseCount(countText: string | undefined | null): number {
    if (!countText) return 0;

    console.log(`Instagram V2: Parsing count text: "${countText}"`);

    // Remove non-numeric characters except for K, M, decimal points, and commas
    const cleanText = countText.replace(/[^0-9KkMm.,]/g, '');

    console.log(`Instagram V2: Cleaned text: "${cleanText}"`);

    // Handle K and M suffixes
    if (cleanText.match(/[Kk]$/)) {
      const number = parseFloat(cleanText.replace(/[Kk]$/, ''));
      return Math.round(number * 1000);
    } else if (cleanText.match(/[Mm]$/)) {
      const number = parseFloat(cleanText.replace(/[Mm]$/, ''));
      return Math.round(number * 1000000);
    } else {
      // Remove commas and parse as regular number
      const number = parseFloat(cleanText.replace(/,/g, ''));
      return Math.round(number) || 0;
    }
  }

  /**
   * Process and save a post
   * @param post The post to save
   * @param options Options for saving
   * @returns SaveResult containing success status and error message if applicable
   */
  async processAndSavePost(post: Post, options: SaveOptions = {}): Promise<SaveResult> {
    try {
      console.log(`Instagram V2: Processing and saving post ${post.id}`);

      // Save to local storage if requested
      if (options.saveToLocal !== false) {
        await this.saveToLocal(post);
      }

      // Upload to cloud if requested
      if (options.uploadToCloud) {
        const token = await authService.getToken();
        if (!token) {
          return {
            success: false,
            error: 'Authentication token not available',
            savedLocally: options.saveToLocal !== false
          };
        }

        const cloudSuccess = await this.uploadToCloud(post, token);
        return {
          success: true,
          postId: post.id,
          savedLocally: options.saveToLocal !== false,
          uploadedToCloud: cloudSuccess
        };
      }

      return {
        success: true,
        postId: post.id,
        savedLocally: options.saveToLocal !== false,
        uploadedToCloud: false
      };
    } catch (error) {
      console.error('Instagram V2: Error processing and saving post:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  private async saveToLocal(post: Post): Promise<boolean> {
    try {
      // Use the existing storage service
      chrome.runtime.sendMessage({
        action: 'SAVE_POST',
        data: post
      });
      return true;
    } catch (error) {
      console.error('Instagram V2: Error saving post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post to cloud
   * @param post The post data to upload
   * @param token The authentication token
   * @returns Promise<boolean> indicating success or failure
   */
  private async uploadToCloud(post: Post, token: string): Promise<boolean> {
    try {
      // Define backend URL - use the new v2 endpoint
      const API_URL = 'https://api.notely.social';
      const apiUrl = `${API_URL}/api/v2/posts`;

      console.log(`Instagram V2: Uploading post to ${apiUrl}`);

      // Prepare the post for upload - only send necessary data
      const postToUpload = {
        originalPostId: post.id,
        platform: post.platform,
        authorName: post.authorName || post.author,
        authorHandle: post.authorHandle,
        authorAvatar: post.authorAvatar,
        content: post.content || post.textContent,
        timestamp: post.timestamp,
        permalink: post.permalink,
        media: post.media,
        interactions: {
          likes: post.stats?.likes || 0,
          comments: post.stats?.comments || 0
        }
      };

      // Send the post to the backend
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(postToUpload)
      });

      if (response.ok) {
        console.log(`Instagram V2: Successfully uploaded post ${post.id} to cloud.`);
        return true;
      } else if (response.status === 409) {
        console.log(`Instagram V2: Post ${post.id} already exists in cloud.`);
        return true; // Consider this a success
      } else {
        const errorData = await response.json().catch(() => ({ message: `Cloud upload failed with status: ${response.status}` }));
        console.error(`Instagram V2: Failed to upload post ${post.id} to cloud. Status: ${response.status}`, errorData);
        return false;
      }
    } catch (error) {
      console.error('Instagram V2: Error uploading post to cloud:', error);
      return false;
    }
  }
}
