/**
 * Instagram Cloud Service
 *
 * This version sends data to the backend for server-side processing.
 * The backend handles image fetching, S3 uploads, and database storage.
 */

import { Post, MediaItem } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { SaveOptions, ExtractionResult, SaveResult } from '../base/types';
import * as authService from '../../services/authService';
import { savePost } from '../../storage';

// Instagram-specific selectors
const SELECTORS = {
  // Author selectors
  AUTHOR: 'header a, a[href^="/"][role="link"], a[href^="/"] span, div[role="button"] span',
  AUTHOR_HANDLE_SPAN: 'header span, a[href^="/"] span',
  AUTHOR_AVATAR: 'img[alt$="\'s profile picture"][draggable="false"]',

  // Content selectors
  CAPTION: 'div[role="button"] + div, div[role="button"] + span, div[dir="auto"], span[dir="auto"], div._a9zs',

  // Enhanced image selectors for better carousel detection
  IMAGE: 'article img[src]:not([src^="data:"]):not([alt*="profile picture"])',
  CAROUSEL_ITEM: 'div[role="button"] img[src]:not([src^="data:"])',
  // Additional selectors for carousel images
  ALL_POST_IMAGES: 'img[src*="cdninstagram.com"]:not([alt*="profile picture"]), img[src*="fbcdn.net"]:not([alt*="profile picture"])',
  CAROUSEL_CONTAINER: 'div[style*="transform"], div[class*="carousel"], div[class*="slider"]',
  CAROUSEL_SLIDES: 'div[role="tabpanel"], div[class*="slide"], li[class*="slide"]',

  // Interaction selectors - Updated for current Instagram structure
  LIKES_LINK: 'a[href*="liked_by"]',
  COMMENTS_LINK: 'a[href*="comments"]',
  LIKES_SPAN: 'a[href*="liked_by"] span.html-span',
  COMMENTS_SPAN: 'a[href*="comments"] span.html-span'
};

export class InstagramCloudService extends BasePlatformService {
  private static instance: InstagramCloudService;
  private videoUrlCache: Map<string, string> = new Map();
  private networkInterceptionSetup = false;

  constructor() {
    super('Instagram');
    InstagramCloudService.instance = this;
    this.setupNetworkInterception();
  }

  /**
   * Set up network request interception to capture Instagram video URLs
   * Designed specifically for Instagram's patterns to avoid crashes
   */
  private setupNetworkInterception(): void {
    if (this.networkInterceptionSetup) return;
    
    console.log('Instagram Cloud: Setting up safe network interception...');
    
    try {
      // Only intercept Instagram-specific video requests to avoid conflicts
      const originalFetch = window.fetch;
      window.fetch = async function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
        const url = input.toString();
        let response;
        
        try {
          response = await originalFetch(input, init);
          
          // Only process Instagram video-related requests
          if (url.includes('instagram.com') && 
              (url.includes('video') || url.includes('.mp4') || url.includes('media') || 
               url.includes('graphql') || url.includes('api/v1/') || url.includes('stories'))) {
            
            console.log(`Instagram Cloud: 🌐 Intercepted Instagram request: ${url.substring(0, 80)}...`);
            
            try {
              const clonedResponse = response.clone();
              const contentType = response.headers.get('content-type') || '';
              
              if (contentType.includes('application/json')) {
                const data = await clonedResponse.json();
                InstagramCloudService.extractVideoUrlsFromResponse(data);
              } else if (contentType.includes('text/') || contentType.includes('javascript')) {
                const text = await clonedResponse.text();
                if (text.includes('.mp4') || text.includes('video_url')) {
                  InstagramCloudService.extractVideoUrlsFromText(text);
                }
              }
            } catch (interceptError) {
              // Silently handle interception errors to avoid crashes
              console.log(`Instagram Cloud: ⚠️ Safe interception error (ignored): ${interceptError}`);
            }
          }
        } catch (fetchError) {
          // Let the original fetch error propagate normally
          throw fetchError;
        }
        
        return response;
      };
      
      this.networkInterceptionSetup = true;
      console.log('Instagram Cloud: ✅ Safe network interception setup complete');
    } catch (error) {
      console.log(`Instagram Cloud: ⚠️ Network interception setup failed (safe): ${error}`);
      // Don't throw - let Instagram work normally without interception
    }
  }

  /**
   * Extract video URLs from Instagram API responses
   */
  private static extractVideoUrlsFromResponse(data: any): void {
    if (!data) return;
    
    console.log(`Instagram Cloud: 🔍 Searching Instagram response for video URLs...`);
    
    // Recursively search for Instagram video URLs
    const findVideoUrls = (obj: any) => {
      if (typeof obj === 'string') {
        // Look for Instagram video URL patterns
        if (obj.includes('.mp4') && 
            (obj.includes('instagram.com') || obj.includes('cdninstagram.com') || obj.includes('fbcdn.net')) &&
            !obj.includes('thumb') && !obj.includes('jpg') && !obj.includes('png')) {
          console.log(`Instagram Cloud: 🎬 Found video URL in response: ${obj.substring(0, 80)}...`);
          InstagramCloudService.cacheVideoUrl(obj);
        }
      } else if (typeof obj === 'object' && obj !== null) {
        // Look specifically for Instagram video data structures
        if (obj.video_versions && Array.isArray(obj.video_versions)) {
          obj.video_versions.forEach((version: any) => {
            if (version.url) {
              console.log(`Instagram Cloud: 🎬 Found video version URL: ${version.url.substring(0, 80)}...`);
              InstagramCloudService.cacheVideoUrl(version.url);
            }
          });
        }
        
        // Continue recursive search
        Object.values(obj).forEach(findVideoUrls);
      }
    };
    
    findVideoUrls(data);
  }

  /**
   * Extract video URLs from Instagram text responses
   */
  private static extractVideoUrlsFromText(text: string): void {
    if (!text) return;
    
    console.log(`Instagram Cloud: 🔍 Searching Instagram text response for video URLs...`);
    
    // Instagram-specific video URL patterns
    const instagramVideoRegex = [
      /https:\/\/[^"'\s]*\.cdninstagram\.com[^"'\s]*\.mp4[^"'\s]*/gi,
      /https:\/\/[^"'\s]*\.fbcdn\.net[^"'\s]*\.mp4[^"'\s]*/gi,
      /https:\/\/[^"'\s]*instagram[^"'\s]*\.mp4[^"'\s]*/gi,
      /"video_url":"([^"]+\.mp4[^"]*)"/gi
    ];
    
    instagramVideoRegex.forEach(regex => {
      const matches = text.match(regex);
      if (matches) {
        matches.forEach(url => {
          // Clean up escaped characters
          const cleanUrl = url.replace(/\\u0026/g, '&').replace(/\\/g, '').replace(/^"video_url":"/, '').replace(/"$/, '');
          if (!cleanUrl.includes('thumb') && !cleanUrl.includes('jpg') && !cleanUrl.includes('png')) {
            console.log(`Instagram Cloud: 🎬 Found video URL in text: ${cleanUrl.substring(0, 80)}...`);
            InstagramCloudService.cacheVideoUrl(cleanUrl);
          }
        });
      }
    });
  }

  /**
   * Cache a video URL for later use
   */
  private static cacheVideoUrl(url: string): void {
    if (!InstagramCloudService.instance) return;
    
    const timestamp = Date.now().toString();
    InstagramCloudService.instance.videoUrlCache.set(timestamp, url);
    
    // Keep only the last 10 URLs to avoid memory issues
    if (InstagramCloudService.instance.videoUrlCache.size > 10) {
      const firstKey = InstagramCloudService.instance.videoUrlCache.keys().next().value;
      InstagramCloudService.instance.videoUrlCache.delete(firstKey);
    }
    
    console.log(`Instagram Cloud: 📦 Cached video URL (total: ${InstagramCloudService.instance.videoUrlCache.size})`);
  }

  /**
   * Find a cached video URL that might match the current video
   */
  private findCachedVideoUrl(): string | null {
    const urls = Array.from(this.videoUrlCache.values());
    return urls.length > 0 ? urls[urls.length - 1] : null;
  }

  /**
   * Extract post data from an Instagram post element
   * @param element The post element
   * @param options Options for extraction
   * @returns ExtractionResult containing the post data or an error
   */
  async extractPostData(element: HTMLElement): Promise<ExtractionResult> {
    try {
      console.log('Instagram Cloud: Extracting post data');

      // --- Post ID ---
      // Extract the post ID (shortcode) from the URL
      let shortcode = '';
      console.log('Instagram Cloud: Attempting to extract shortcode...');

      const linkElement = element.querySelector('a[href*="/p/"]');
      console.log('Instagram Cloud: Post link element found:', !!linkElement);

      if (linkElement instanceof HTMLAnchorElement) {
        console.log('Instagram Cloud: Post link href:', linkElement.href);
        const match = linkElement.href.match(/\/p\/([^/?]+)/);
        if (match && match[1]) {
          shortcode = match[1];
          console.log('Instagram Cloud: Shortcode from post link:', shortcode);
        }
      }

      // If no shortcode found, try to find it in a time element
      if (!shortcode) {
        console.log('Instagram Cloud: Trying time element...');
        const timeElement = element.querySelector('time');
        console.log('Instagram Cloud: Time element found:', !!timeElement);

        if (timeElement && timeElement.parentElement instanceof HTMLAnchorElement) {
          console.log('Instagram Cloud: Time parent link href:', timeElement.parentElement.href);
          const match = timeElement.parentElement.href.match(/\/p\/([^/?]+)/);
          if (match && match[1]) {
            shortcode = match[1];
            console.log('Instagram Cloud: Shortcode from time element:', shortcode);
          }
        }
      }

      // Try alternative strategies
      if (!shortcode) {
        console.log('Instagram Cloud: Trying alternative strategies...');

        // Try current URL
        const urlMatch = window.location.href.match(/\/p\/([^/?]+)/);
        if (urlMatch && urlMatch[1]) {
          shortcode = urlMatch[1];
          console.log('Instagram Cloud: Shortcode from current URL:', shortcode);
        }

        // Try reel URLs
        if (!shortcode) {
          const reelElement = element.querySelector('a[href*="/reel/"]');
          if (reelElement instanceof HTMLAnchorElement) {
            const match = reelElement.href.match(/\/reel\/([^/?]+)/);
            if (match && match[1]) {
              shortcode = match[1];
              console.log('Instagram Cloud: Shortcode from reel link:', shortcode);
            }
          }
        }
      }

      if (!shortcode) {
        console.error('Instagram Cloud: Could not extract post ID (shortcode)');
        console.error('Instagram Cloud: Element HTML snippet:', element.outerHTML.substring(0, 500));
        return {
          success: false,
          error: 'Could not extract post ID (shortcode)'
        };
      }

      // Construct the permalink
      const postUrl = `https://www.instagram.com/p/${shortcode}/`;
      console.log(`Instagram Cloud: Found shortcode: ${shortcode}`);

      // --- Author Info ---
      // Try different selectors for author information
      let author = '';
      let authorUrl = '';

      // First try the header link
      const authorElement = element.querySelector(SELECTORS.AUTHOR);
      if (authorElement) {
        author = authorElement.textContent?.trim() || '';
        if (authorElement instanceof HTMLAnchorElement) {
          authorUrl = authorElement.href;
        }
      }

      // If no author found, try the handle span
      if (!author) {
        const authorHandleSpan = element.querySelector(SELECTORS.AUTHOR_HANDLE_SPAN);
        if (authorHandleSpan) {
          author = authorHandleSpan.textContent?.trim() || '';
          // Try to find the parent link
          const parentLink = authorHandleSpan.closest('a');
          if (parentLink instanceof HTMLAnchorElement) {
            authorUrl = parentLink.href;
          }
        }
      }

      // Extract author avatar and convert to base64
      let authorAvatar = '';
      const authorAvatarElement = element.querySelector(SELECTORS.AUTHOR_AVATAR);
      if (authorAvatarElement instanceof HTMLImageElement) {
        try {
          // Convert author avatar to base64 to avoid CORS issues
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (ctx) {
            canvas.width = authorAvatarElement.naturalWidth || authorAvatarElement.width || 150;
            canvas.height = authorAvatarElement.naturalHeight || authorAvatarElement.height || 150;

            // Draw the avatar image onto the canvas
            ctx.drawImage(authorAvatarElement, 0, 0, canvas.width, canvas.height);

            // Convert canvas to data URL
            authorAvatar = canvas.toDataURL('image/jpeg', 0.9);
            console.log(`Instagram Cloud: Successfully converted author avatar to base64 (${authorAvatar.length} chars)`);
          } else {
            // Fallback to original URL if canvas fails
            authorAvatar = authorAvatarElement.src;
            console.log(`Instagram Cloud: Canvas context failed, using original avatar URL: ${authorAvatar.substring(0, 50)}...`);
          }
        } catch (error) {
          console.error('Instagram Cloud: Error converting author avatar to base64:', error);
          // Fallback to original URL if conversion fails
          authorAvatar = authorAvatarElement.src;
        }
      }

      console.log(`Instagram Cloud: Author: ${author}, Avatar URL: ${authorAvatar ? 'Found' : 'Not found'}`);

      // --- Post Content ---
      // Extract post caption with improved logic
      let caption = '';

      // Try to find the actual post caption by looking for specific patterns
      // Look for spans that contain the actual post text (usually longer content)
      const captionCandidates = element.querySelectorAll('span, div[dir="auto"]');

      for (const candidate of captionCandidates) {
        const text = candidate.textContent?.trim() || '';

        // Skip if it's likely not a caption
        if (text.length < 10) continue; // Too short
        if (text === author) continue; // Just the username
        if (text.includes('Suggested for you')) continue; // Instagram UI text
        if (text.includes('Follow')) continue; // Follow button
        if (text.includes('ago')) continue; // Timestamp
        if (text.includes('likes')) continue; // Like count
        if (text.includes('comments')) continue; // Comment count
        if (text.includes('Share')) continue; // Share button
        if (text.includes('Save')) continue; // Save button
        if (text.includes('More options')) continue; // Menu
        if (text.match(/^\d+$/)) continue; // Just numbers
        if (text.match(/^@\w+$/)) continue; // Just mentions

        // If we find a longer text that seems like actual content, use it
        if (text.length > caption.length && text.length > 15) {
          caption = text;
        }
      }

      // If still no caption, try a more specific approach
      if (!caption) {
        // Look for the post content in article descendants
        const articleSpans = element.querySelectorAll('article span');
        for (const span of articleSpans) {
          const text = span.textContent?.trim() || '';
          if (text.length > 20 &&
              !text.includes(author) &&
              !text.includes('ago') &&
              !text.includes('likes') &&
              !text.includes('Follow')) {
            caption = text;
            break;
          }
        }
      }

      console.log(`Instagram Cloud: Extracted caption: "${caption.substring(0, 100)}${caption.length > 100 ? '...' : ''}"`);


      // --- Timestamp ---
      // Extract the timestamp
      let timestamp = '';
      const timeElement = element.querySelector('time');
      if (timeElement) {
        timestamp = timeElement.dateTime || timeElement.getAttribute('datetime') || '';
      }

      // --- Interactions ---
      // Extract likes and comments with improved selectors
      let likeCount = 0;
      let commentCount = 0;

      // Try the most specific selectors first - target the exact HTML structure
      console.log(`Instagram Cloud: Looking for likes and comments using specific selectors`);

      // Look for likes using the exact structure: a[href*="liked_by"] span.html-span
      const likesSpan = element.querySelector(SELECTORS.LIKES_SPAN);
      if (likesSpan) {
        const likesText = likesSpan.textContent?.trim();
        if (likesText) {
          console.log(`Instagram Cloud: Found likes span with text: "${likesText}"`);
          likeCount = this.parseCount(likesText);
        }
      } else {
        console.log(`Instagram Cloud: No likes span found with selector: ${SELECTORS.LIKES_SPAN}`);
      }

      // Look for comments using the exact structure: a[href*="comments"] span.html-span
      const commentsSpan = element.querySelector(SELECTORS.COMMENTS_SPAN);
      if (commentsSpan) {
        const commentsText = commentsSpan.textContent?.trim();
        if (commentsText) {
          console.log(`Instagram Cloud: Found comments span with text: "${commentsText}"`);
          commentCount = this.parseCount(commentsText);
        }
      } else {
        console.log(`Instagram Cloud: No comments span found with selector: ${SELECTORS.COMMENTS_SPAN}`);
      }

      // Fallback: if still no likes/comments found, try broader search
      if (likeCount === 0) {
        console.log(`Instagram Cloud: Fallback - searching for likes link`);
        const likesLink = element.querySelector(SELECTORS.LIKES_LINK);
        if (likesLink) {
          console.log(`Instagram Cloud: Found likes link, searching for html-span inside`);
          const htmlSpan = likesLink.querySelector('span.html-span');
          if (htmlSpan) {
            console.log(`Instagram Cloud: Found html-span in likes link: "${htmlSpan.textContent}"`);
            likeCount = this.parseCount(htmlSpan.textContent);
          }
        }
      }

      if (commentCount === 0) {
        console.log(`Instagram Cloud: Fallback - searching for comments link`);
        const commentsLink = element.querySelector(SELECTORS.COMMENTS_LINK);
        if (commentsLink) {
          console.log(`Instagram Cloud: Found comments link, searching for html-span inside`);
          const htmlSpan = commentsLink.querySelector('span.html-span');
          if (htmlSpan) {
            console.log(`Instagram Cloud: Found html-span in comments link: "${htmlSpan.textContent}"`);
            commentCount = this.parseCount(htmlSpan.textContent);
          }
        }
      }

      console.log(`Instagram Cloud: Likes: ${likeCount}, Comments: ${commentCount}`);
      console.log(`Instagram Cloud: Stats object will be:`, {
        likes: likeCount,
        comments: commentCount,
        shares: 0,
        views: 0
      });

      // --- Media URLs ---
      // Extract image URLs and videos (enhanced for better carousel detection)
      const mediaItems: MediaItem[] = [];
      const videoItems: MediaItem[] = [];

      // First, check for videos with 5 extraction methods
      const videoElements = element.querySelectorAll('video');
      console.log(`Instagram Cloud: Found ${videoElements.length} video elements`);
      
      videoElements.forEach((video, index) => {
        if (video instanceof HTMLVideoElement) {
          let realVideoUrl = this.extractRealVideoUrl(video, element, index);
          
          if (realVideoUrl) {
            console.log(`Instagram Cloud: 🎥 Found REAL video ${index + 1}: ${realVideoUrl.substring(0, 50)}...`);
            videoItems.push({
              type: 'video',
              url: realVideoUrl,
              alt: `Instagram video ${index + 1}`
            });
          } else {
            console.log(`Instagram Cloud: 🎥 No real video URL found for video ${index + 1}, skipping...`);
          }
        }
      });

      // First, try to trigger loading of all carousel images
      await this.triggerCarouselImageLoading(element);

      // Strategy 1: Find all image elements (main images)
      const imgElements = element.querySelectorAll<HTMLImageElement>(SELECTORS.IMAGE);
      console.log(`Instagram Cloud: Strategy 1 - Found ${imgElements.length} potential image elements`);

      // Strategy 2: Look for carousel items
      const carouselItems = element.querySelectorAll<HTMLImageElement>(SELECTORS.CAROUSEL_ITEM);
      console.log(`Instagram Cloud: Strategy 2 - Found ${carouselItems.length} potential carousel items`);

      // Strategy 3: Look for all Instagram CDN images (most comprehensive)
      const cdnImages = element.querySelectorAll<HTMLImageElement>(SELECTORS.ALL_POST_IMAGES);
      console.log(`Instagram Cloud: Strategy 3 - Found ${cdnImages.length} CDN images`);

      // Strategy 4: Look inside carousel containers and slides
      const carouselContainers = element.querySelectorAll(SELECTORS.CAROUSEL_CONTAINER);
      const slideImages: HTMLImageElement[] = [];
      carouselContainers.forEach(container => {
        const slides = container.querySelectorAll<HTMLImageElement>('img[src]:not([alt*="profile picture"])');
        slideImages.push(...Array.from(slides));
      });
      console.log(`Instagram Cloud: Strategy 4 - Found ${slideImages.length} images in carousel containers`);

      // Strategy 5: Look for images in carousel slides/tabpanels
      const carouselSlides = element.querySelectorAll(SELECTORS.CAROUSEL_SLIDES);
      const tabpanelImages: HTMLImageElement[] = [];
      carouselSlides.forEach(slide => {
        const images = slide.querySelectorAll<HTMLImageElement>('img[src]:not([alt*="profile picture"])');
        tabpanelImages.push(...Array.from(images));
      });
      console.log(`Instagram Cloud: Strategy 5 - Found ${tabpanelImages.length} images in carousel slides`);

      // Strategy 6: Look for images in data attributes and background images
      const elementsWithBg = element.querySelectorAll('div[style*="background-image"], span[style*="background-image"]');
      const backgroundImages: HTMLImageElement[] = [];
      elementsWithBg.forEach(el => {
        const style = (el as HTMLElement).style.backgroundImage;
        if (style && style.includes('url(')) {
          const urlMatch = style.match(/url\(['"]?([^'"]+)['"]?\)/);
          if (urlMatch && urlMatch[1] && urlMatch[1].includes('instagram.f')) {
            // Create a virtual image element for background images
            const virtualImg = document.createElement('img') as HTMLImageElement;
            virtualImg.src = urlMatch[1];
            virtualImg.alt = 'Background image from carousel';
            backgroundImages.push(virtualImg);
          }
        }
      });
      console.log(`Instagram Cloud: Strategy 6 - Found ${backgroundImages.length} background images`);

      // Strategy 7: Look for srcset attributes which might contain additional image URLs
      const imgsWithSrcset = element.querySelectorAll('img[srcset]');
      const srcsetImages: HTMLImageElement[] = [];
      imgsWithSrcset.forEach(img => {
        if (img instanceof HTMLImageElement && img.srcset) {
          const srcsetUrls = img.srcset.split(',').map(src => src.trim().split(' ')[0]);
          srcsetUrls.forEach(url => {
            if (url && url.includes('instagram.f')) {
              const virtualImg = document.createElement('img') as HTMLImageElement;
              virtualImg.src = url;
              virtualImg.alt = img.alt || 'Image from srcset';
              srcsetImages.push(virtualImg);
            }
          });
        }
      });
      console.log(`Instagram Cloud: Strategy 7 - Found ${srcsetImages.length} srcset images`);

      // Strategy 8: Add captured images from carousel navigation (if available)
      const capturedCarouselImages: HTMLImageElement[] = [];
      if ((this as any).capturedImages) {
        const capturedUrls = (this as any).capturedImages as Set<string>;
        console.log(`Instagram Cloud: Processing ${capturedUrls.size} captured URLs for Strategy 8`);

        // Instead of creating virtual images, try to find the actual loaded images in DOM
        capturedUrls.forEach(url => {
          // First try to find the actual loaded image in DOM
          const existingImg = element.querySelector(`img[src="${url}"]`) as HTMLImageElement;
          if (existingImg) {
            console.log(`Instagram Cloud: Found existing loaded image for: ${url.substring(0, 80)}...`);
            capturedCarouselImages.push(existingImg);
          } else {
            // If not found in DOM, create a virtual image but mark it for special handling
            const virtualImg = document.createElement('img') as HTMLImageElement;
            virtualImg.src = url;
            virtualImg.alt = 'Captured from carousel navigation';
            virtualImg.setAttribute('data-captured-url', 'true');
            capturedCarouselImages.push(virtualImg);
            console.log(`Instagram Cloud: Created virtual image for: ${url.substring(0, 80)}...`);
          }
        });
      }
      console.log(`Instagram Cloud: Strategy 8 - Found ${capturedCarouselImages.length} captured carousel images`);

      // Combine all strategies to get all images
      const allImages = [
        ...Array.from(imgElements),
        ...Array.from(carouselItems),
        ...Array.from(cdnImages),
        ...slideImages,
        ...tabpanelImages,
        ...backgroundImages,
        ...srcsetImages,
        ...capturedCarouselImages
      ];
      console.log(`Instagram Cloud: Total ${allImages.length} images found across all strategies`);

      // Process each image and convert to base64
      const processedUrls = new Set<string>(); // Track processed URLs to avoid duplicates

      // Debug: Log image sources for troubleshooting
      if (allImages.length > 0) {
        console.log(`Instagram Cloud: Debug - Image sources found:`);
        allImages.slice(0, 10).forEach((img, index) => {
          console.log(`  ${index + 1}. ${img.src.substring(0, 80)}... (alt: "${img.alt || 'none'}")`);
        });
        if (allImages.length > 10) {
          console.log(`  ... and ${allImages.length - 10} more images`);
        }
      }

      // Process each image and convert to base64 (limit to 20 images max)
      const MAX_IMAGES = 20;
      let processedCount = 0;
      let skippedCount = 0;

      for (const img of allImages) {
        // Stop if we've reached the maximum number of images
        if (processedCount >= MAX_IMAGES) {
          console.log(`Instagram Cloud: Reached maximum limit of ${MAX_IMAGES} images`);
          break;
        }

        // Debug: Log why images are being skipped
        if (!img.src) {
          console.log(`Instagram Cloud: Skipping image ${skippedCount + 1} - no src`);
          skippedCount++;
          continue;
        }
        if (img.src.includes('data:image')) {
          console.log(`Instagram Cloud: Skipping image ${skippedCount + 1} - already base64 data`);
          skippedCount++;
          continue;
        }
        // Enhanced profile picture detection
        const isProfilePicture = img.alt && (
          img.alt.includes('profile picture') ||
          img.alt.includes('profil resmi') ||
          img.alt === 'none' ||
          img.alt.trim() === ''
        ) || img.src.includes('-19/'); // Instagram profile pictures often have '-19/' in the URL

        if (isProfilePicture) {
          console.log(`Instagram Cloud: Skipping image ${skippedCount + 1} - profile picture: ${img.src.substring(0, 50)}...`);
          skippedCount++;
          continue;
        }
        if (img.src === authorAvatar) {
          console.log(`Instagram Cloud: Skipping image ${skippedCount + 1} - matches author avatar`);
          skippedCount++;
          continue;
        }
        if (processedUrls.has(img.src)) {
          console.log(`Instagram Cloud: Skipping image ${skippedCount + 1} - duplicate URL: ${img.src.substring(0, 50)}...`);
          skippedCount++;
          continue;
        }

        // This image passes all filters
        processedUrls.add(img.src);

        try {
          let imageUrl: string;
          let isBase64 = false;

          // Check if this is a captured URL that needs special handling
          if (img.hasAttribute && img.hasAttribute('data-captured-url')) {
            // For captured URLs, we need to load and convert them to base64
            console.log(`Instagram Cloud: Loading captured image for base64 conversion: ${img.src.substring(0, 80)}...`);
            try {
              imageUrl = await this.loadImageAndConvertToBase64(img.src);
              isBase64 = true;
              console.log(`Instagram Cloud: Successfully converted captured image to base64 (${imageUrl.length} chars)`);
            } catch (error) {
              console.error(`Instagram Cloud: Failed to convert captured image to base64:`, error);
              // Fallback to original URL if conversion fails
              imageUrl = img.src;
              isBase64 = false;
            }
          } else {
            // For regular DOM images, try to convert to base64 to avoid CORS issues
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (ctx && img.naturalWidth && img.naturalHeight) {
              canvas.width = img.naturalWidth || img.width;
              canvas.height = img.naturalHeight || img.height;

              // Draw the image onto the canvas
              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

              // Convert canvas to data URL
              imageUrl = canvas.toDataURL('image/jpeg', 0.9);
              isBase64 = true;
              console.log(`Instagram Cloud: Successfully converted DOM image to base64 (${imageUrl.length} chars)`);
            } else {
              // Fallback to original URL if canvas fails or image not loaded
              console.log(`Instagram Cloud: Canvas failed or image not loaded, using original URL: ${img.src.substring(0, 50)}...`);
              imageUrl = img.src;
              isBase64 = false;
            }
          }

          mediaItems.push({
            type: 'image',
            url: imageUrl,
            originalUrl: img.src, // Keep original URL for reference
            alt: img.alt || `Instagram image ${processedCount + 1}`
          });

          processedCount++;
          console.log(`Instagram Cloud: Successfully processed image ${processedCount}/${Math.min(allImages.length, MAX_IMAGES)} (${isBase64 ? 'base64' : 'URL'}: ${imageUrl.length} chars)`);

        } catch (error) {
          // Handle canvas security errors (tainted canvas)
          if (error instanceof Error && error.message.includes('Tainted canvases may not be exported')) {
            console.log(`Instagram Cloud: Canvas tainted, using original URL for image ${processedCount + 1}: ${img.src.substring(0, 50)}...`);
          } else {
            console.error('Instagram Cloud: Error converting image to base64:', error);
          }
          // Fallback to original URL if conversion fails
          mediaItems.push({
            type: 'image',
            url: img.src,
            alt: img.alt || `Instagram image ${processedCount + 1}`
          });
          processedCount++;
        }
      }

      console.log(`Instagram Cloud: Processing Summary for post ${shortcode}:`);
      console.log(`  - Total images found: ${allImages.length}`);
      console.log(`  - Images skipped: ${skippedCount}`);
      console.log(`  - Videos found: ${videoItems.length}`);
      console.log(`  - Unique images processed: ${mediaItems.length}`);
      console.log(`  - Duplicates filtered: ${allImages.length - skippedCount - mediaItems.length}`);

      // Debug DOM structure if we found fewer media items than expected (only if no videos found)
      if (videoItems.length === 0 && mediaItems.length <= 2 && allImages.length > mediaItems.length) {
        console.log(`Instagram Cloud: Warning - Only processed ${mediaItems.length} images but found ${allImages.length} total. Running debug...`);
        this.debugDOMStructure(element);
      }

      // Combine videos and images into final media array
      const allMediaItems = [...videoItems, ...mediaItems];
      console.log(`Instagram Cloud: Extracted ${allMediaItems.length} media items (${videoItems.length} videos, ${mediaItems.length} images)`);

      // Create the post object with minimal data
      console.log(`Instagram Cloud: Creating post object with stats - likes: ${likeCount}, comments: ${commentCount}`);
      const post: Post = {
        id: shortcode,
        platform: 'Instagram',
        author,
        authorName: author,
        authorUrl,
        authorAvatar,
        textContent: caption,
        content: caption,
        permalink: postUrl,
        timestamp,
        savedAt: new Date().toISOString(),
        media: allMediaItems,
        stats: {
          likes: likeCount,
          comments: commentCount,
          shares: 0,
          views: 0
        }
      };

      return {
        success: true,
        post
      };
    } catch (error) {
      console.error('Instagram Cloud: Error extracting post data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract image URLs from a post element or post data
   * This is a simplified version since actual image processing happens on the server
   */
  async extractImageUrls(source: Post | HTMLElement): Promise<{ success: boolean; images: MediaItem[] }> {
    // This method is simplified since image processing happens on the server
    return {
      success: true,
      images: source instanceof HTMLElement ? [] : source.media || []
    };
  }

  /**
   * Method to trigger loading of all carousel images using multiple strategies
   * @param element The post element containing the carousel
   */
  private async triggerCarouselImageLoading(element: Element): Promise<void> {
    try {
      console.log('Instagram Cloud: Attempting to trigger carousel image loading...');

      // Strategy 1: Look for carousel dots/indicators to determine image count
      const carouselDots = element.querySelectorAll('div[role="tablist"] div[role="tab"], div[style*="background"] div[style*="opacity"], .carousel-indicator, [class*="dot"], [class*="indicator"]');
      console.log(`Instagram Cloud: Found ${carouselDots.length} carousel indicators`);

      // Strategy 2: Look for carousel navigation buttons
      const nextButtons = element.querySelectorAll('button[aria-label*="Next"], button[aria-label*="next"], button[aria-label*="Next photo"], button[aria-label*="Sonraki"], button[aria-label*="sonraki"]');
      const prevButtons = element.querySelectorAll('button[aria-label*="Previous"], button[aria-label*="previous"], button[aria-label*="Previous photo"], button[aria-label*="Önceki"], button[aria-label*="önceki"]');

      // Strategy 3: Look for carousel containers with transform styles
      const carouselContainers = element.querySelectorAll('div[style*="transform"], div[style*="translateX"]');
      console.log(`Instagram Cloud: Found ${carouselContainers.length} carousel containers with transforms`);

      // Strategy 4: Try to find hidden images by looking for img elements with different src patterns
      const allImgElements = element.querySelectorAll('img');
      const potentialCarouselImages = Array.from(allImgElements).filter(img => {
        return img.src.includes('instagram.f') && !img.alt?.includes('profile picture');
      });
      console.log(`Instagram Cloud: Found ${potentialCarouselImages.length} potential carousel images in DOM`);

      // Strategy 5: Look for data attributes that might contain image URLs
      const elementsWithData = element.querySelectorAll('*');
      const imageUrls = new Set<string>();

      elementsWithData.forEach(el => {
        Array.from(el.attributes).forEach(attr => {
          if (attr.name.startsWith('data-') && attr.value.includes('instagram.f') && attr.value.includes('.jpg')) {
            imageUrls.add(attr.value);
          }
        });
      });
      console.log(`Instagram Cloud: Found ${imageUrls.size} image URLs in data attributes`);

      // Strategy 6: Check for script tags containing image data
      const scriptTags = document.querySelectorAll('script[type="application/json"], script:not([src])');
      let scriptImageCount = 0;

      scriptTags.forEach(script => {
        if (script.textContent && script.textContent.includes('instagram.f') && script.textContent.includes('.jpg')) {
          const matches = script.textContent.match(/https:\/\/instagram\.f[^"'\s]+\.jpg/g);
          if (matches) {
            scriptImageCount += matches.length;
          }
        }
      });
      console.log(`Instagram Cloud: Found ${scriptImageCount} image URLs in script tags`);

      // Strategy 8: Try to extract images from Instagram's internal data structures
      try {
        const scriptTags = document.querySelectorAll('script:not([src])');
        const carouselImageUrls = new Set<string>();

        scriptTags.forEach(script => {
          if (script.textContent) {
            // Look for Instagram's internal data structures
            const instagramImageRegex = /https:\/\/instagram\.f[^"'\s]+\.(jpg|jpeg|png|webp)/gi;
            const matches = script.textContent.match(instagramImageRegex);

            if (matches) {
              matches.forEach(url => {
                // Filter out profile pictures and small images
                if (!url.includes('-19/') && !url.includes('150x150') && !url.includes('44x44')) {
                  carouselImageUrls.add(url);
                }
              });
            }
          }
        });

        console.log(`Instagram Cloud: Strategy 8 - Found ${carouselImageUrls.size} image URLs in script data`);

        // If we found more images in scripts than in DOM, log them
        if (carouselImageUrls.size > potentialCarouselImages.length) {
          console.log('Instagram Cloud: Script data contains more images than DOM:');
          Array.from(carouselImageUrls).slice(0, 10).forEach((url, index) => {
            console.log(`  Script Image ${index + 1}: ${url.substring(0, 80)}...`);
          });
        }
      } catch (error) {
        console.error('Instagram Cloud: Error extracting from script data:', error);
      }

      // If we have navigation buttons, try clicking through and capture each image
      const capturedImages = new Set<string>();

      if (nextButtons.length > 0) {
        console.log(`Instagram Cloud: Found ${nextButtons.length} next buttons, attempting to navigate carousel and capture images...`);

        // First, capture the current image
        const currentImages = element.querySelectorAll('img[src*="instagram.f"]:not([alt*="profile"]):not([src*="-19/"])');
        currentImages.forEach(img => {
          if (img instanceof HTMLImageElement && img.src) {
            capturedImages.add(img.src);
            console.log(`Instagram Cloud: Captured initial image: ${img.src.substring(0, 80)}...`);
          }
        });

        // Try clicking through carousel and capture each new image
        for (let i = 0; i < Math.max(carouselDots.length, 8); i++) {
          const nextButton = Array.from(nextButtons).find(btn => {
            const style = window.getComputedStyle(btn);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
          });

          if (nextButton) {
            console.log(`Instagram Cloud: Navigating to image ${i + 2}...`);
            (nextButton as HTMLElement).click();

            // Wait for Instagram to load new content
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Capture any new images that appeared
            const newImages = element.querySelectorAll('img[src*="instagram.f"]:not([alt*="profile"]):not([src*="-19/"])');
            let newImageFound = false;

            newImages.forEach(img => {
              if (img instanceof HTMLImageElement && img.src && !capturedImages.has(img.src)) {
                capturedImages.add(img.src);
                newImageFound = true;
                console.log(`Instagram Cloud: Captured new image ${capturedImages.size}: ${img.src.substring(0, 80)}...`);
              }
            });

            if (!newImageFound) {
              console.log(`Instagram Cloud: No new images found after click ${i + 1}, stopping navigation`);
              break;
            }
          } else {
            console.log(`Instagram Cloud: No more next buttons available after ${i} clicks`);
            break;
          }
        }

        console.log(`Instagram Cloud: Navigation complete. Captured ${capturedImages.size} unique images total.`);

        // Store captured images for use in extraction
        (this as any).capturedImages = capturedImages;

        // Return to first image
        if (prevButtons.length > 0) {
          console.log('Instagram Cloud: Returning to first image...');
          for (let i = 0; i < 10; i++) {
            const prevButton = Array.from(prevButtons).find(btn => {
              const style = window.getComputedStyle(btn);
              return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
            });

            if (prevButton) {
              (prevButton as HTMLElement).click();
              await new Promise(resolve => setTimeout(resolve, 300));
            } else {
              break;
            }
          }
        }

        // Final wait for DOM to stabilize
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Instagram Cloud: Carousel navigation complete');
      } else {
        console.log('Instagram Cloud: No carousel navigation buttons found');
      }

    } catch (error) {
      console.error('Instagram Cloud: Error triggering carousel image loading:', error);
    }
  }

  /**
   * Load an image from URL and convert it to base64
   * @param imageUrl The URL of the image to load and convert
   * @returns Promise<string> The base64 data URL
   */
  private async loadImageAndConvertToBase64(imageUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();

      // Set up CORS handling
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            reject(new Error('Failed to get canvas context'));
            return;
          }

          canvas.width = img.naturalWidth || img.width;
          canvas.height = img.naturalHeight || img.height;

          // Draw the image onto the canvas
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // Convert canvas to data URL
          const base64Url = canvas.toDataURL('image/jpeg', 0.9);
          resolve(base64Url);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error(`Failed to load image: ${imageUrl}`));
      };

      // Start loading the image
      img.src = imageUrl;

      // Set a timeout to prevent hanging
      setTimeout(() => {
        reject(new Error(`Image load timeout: ${imageUrl}`));
      }, 10000); // 10 second timeout
    });
  }

  /**
   * Debug function to log DOM structure for troubleshooting
   * @param element The post element to analyze
   */
  private debugDOMStructure(element: HTMLElement): void {
    console.log('Instagram Cloud: DOM Structure Debug');
    console.log('='.repeat(50));

    // Log all images found
    const allImgs = element.querySelectorAll('img');
    console.log(`Total images in post: ${allImgs.length}`);
    allImgs.forEach((img, index) => {
      console.log(`  Image ${index + 1}: ${img.src.substring(0, 60)}... (alt: "${img.alt || 'none'}")`);
    });

    // Log carousel-related elements
    const carouselContainers = element.querySelectorAll('div[style*="transform"]');
    console.log(`Carousel containers (transform): ${carouselContainers.length}`);

    const tabpanels = element.querySelectorAll('div[role="tabpanel"]');
    console.log(`Tabpanels: ${tabpanels.length}`);

    const buttons = element.querySelectorAll('div[role="button"]');
    console.log(`Buttons: ${buttons.length}`);

    console.log('='.repeat(50));
  }

  /**
   * Parse a count string (e.g., "1,234 likes" or "1.2K comments")
   * @param countText The count text to parse
   * @returns The parsed count as a number
   */
  private parseCount(countText: string | undefined | null): number {
    if (!countText) return 0;

    console.log(`Instagram Cloud: Parsing count text: "${countText}"`);

    // Remove non-numeric characters except for K, M, decimal points, and commas
    const cleanText = countText.replace(/[^0-9KkMm.,]/g, '');

    console.log(`Instagram Cloud: Cleaned text: "${cleanText}"`);

    // Handle K and M suffixes
    if (cleanText.match(/[Kk]$/)) {
      const number = parseFloat(cleanText.replace(/[Kk]$/, ''));
      return Math.round(number * 1000);
    } else if (cleanText.match(/[Mm]$/)) {
      const number = parseFloat(cleanText.replace(/[Mm]$/, ''));
      return Math.round(number * 1000000);
    } else {
      // Remove commas and parse as regular number
      const number = parseFloat(cleanText.replace(/,/g, ''));
      return Math.round(number) || 0;
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      // Use the existing storage service
      await savePost(post);
      return true;
    } catch (error) {
      console.error('Instagram Cloud: Error saving post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post to cloud
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post, token?: string): Promise<boolean> {
    try {
      if (!token) {
        token = await authService.getToken();
        if (!token) {
          console.error('Instagram Cloud: No authentication token available');
          return false;
        }
      }

      // Define backend URL - use the unified endpoint for queue processing
      const API_URL = 'https://api.notely.social';
      const apiUrl = `${API_URL}/api/posts`;

      console.log(`Instagram Cloud: Uploading post to ${apiUrl}`);

      // Prepare the post for upload - only send necessary data
      const postToUpload = {
        originalPostId: post.id,
        platform: post.platform,
        authorName: post.authorName || post.author,
        authorHandle: post.authorHandle,
        authorAvatar: post.authorAvatar,
        content: post.content || post.textContent,
        timestamp: post.timestamp,
        permalink: post.permalink,
        media: post.media,
        interactions: {
          likes: post.stats?.likes || 0,
          comments: post.stats?.comments || 0
        }
      };

      // Send the post to the backend
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(postToUpload)
      });

      if (response.ok) {
        console.log(`Instagram Cloud: Successfully uploaded post ${post.id} to cloud.`);
        return true;
      } else if (response.status === 409) {
        console.log(`Instagram Cloud: Post ${post.id} already exists in cloud.`);
        return true; // Consider this a success
      } else {
        const errorData = await response.json().catch(() => ({ message: `Cloud upload failed with status: ${response.status}` }));
        console.error(`Instagram Cloud: Failed to upload post ${post.id} to cloud. Status: ${response.status}`, errorData);
        return false;
      }
    } catch (error) {
      console.error('Instagram Cloud: Error uploading post to cloud:', error);
      return false;
    }
  }

  /**
   * Extract real video URL using 5 different methods
   * @param video The video element
   * @param element The post element
   * @param index Video index
   * @returns Real video URL or empty string if not found
   */
  private extractRealVideoUrl(video: HTMLVideoElement, element: Element, index: number): string {
    console.log(`Instagram Cloud: 🎥 Trying 7 extraction methods for video ${index + 1}...`);
    
    // Method 6: Check cached URLs from network interception (try first as most reliable)
    try {
      const cachedUrl = this.findCachedVideoUrl();
      console.log(`Instagram Cloud: 🔍 Cached URLs available: ${this.videoUrlCache.size}`);
      if (cachedUrl) {
        console.log(`Instagram Cloud: ✅ Method 6 (Network cache) found video URL`);
        return cachedUrl;
      }
    } catch (e) {
      console.log(`Instagram Cloud: ❌ Method 6 (Network cache) failed:`, e);
    }
    
    // Method 1: Extract from Instagram JSON-LD data
    try {
      const jsonLdScript = document.querySelector('script[type="application/ld+json"]');
      if (jsonLdScript && jsonLdScript.textContent) {
        const jsonData = JSON.parse(jsonLdScript.textContent);
        if (jsonData.video && jsonData.video.contentUrl) {
          console.log(`Instagram Cloud: ✅ Method 1 (JSON-LD) found video URL`);
          return jsonData.video.contentUrl;
        }
      }
    } catch (e) {
      console.log(`Instagram Cloud: ❌ Method 1 (JSON-LD) failed:`, e);
    }

    // Method 2: Extract from Instagram embedded data scripts
    try {
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        if (script.textContent && script.textContent.includes('video_url')) {
          const match = script.textContent.match(/"video_url":"([^"]+)"/);
          if (match && match[1]) {
            const videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
            console.log(`Instagram Cloud: ✅ Method 2 (Embedded data) found video URL`);
            return videoUrl;
          }
        }
      }
    } catch (e) {
      console.log(`Instagram Cloud: ❌ Method 2 (Embedded data) failed:`, e);
    }

    // Method 3: Extract from Instagram GraphQL response data
    try {
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        if (script.textContent && script.textContent.includes('video_versions')) {
          const match = script.textContent.match(/"video_versions":\[([^\]]+)\]/);
          if (match && match[1]) {
            const videoVersions = JSON.parse(`[${match[1]}]`);
            if (videoVersions.length > 0 && videoVersions[0].url) {
              console.log(`Instagram Cloud: ✅ Method 3 (GraphQL data) found video URL`);
              return videoVersions[0].url;
            }
          }
        }
      }
    } catch (e) {
      console.log(`Instagram Cloud: ❌ Method 3 (GraphQL data) failed:`, e);
    }

    // Method 4: Extract from meta tags
    try {
      const videoMeta = document.querySelector('meta[property="og:video"], meta[property="og:video:secure_url"]');
      if (videoMeta && videoMeta.getAttribute('content')) {
        const videoUrl = videoMeta.getAttribute('content');
        if (videoUrl && !videoUrl.startsWith('blob:')) {
          console.log(`Instagram Cloud: ✅ Method 4 (Meta tags) found video URL`);
          return videoUrl;
        }
      }
    } catch (e) {
      console.log(`Instagram Cloud: ❌ Method 4 (Meta tags) failed:`, e);
    }

    // Method 5: Network interception fallback (check for cached responses)
    try {
      // Look for any cached video URLs in the element's data attributes
      const videoContainer = video.closest('article') || video.closest('[role="main"]') || element;
      const allElements = videoContainer.querySelectorAll('*');
      
      for (const el of allElements) {
        const attrs = el.attributes;
        for (let i = 0; i < attrs.length; i++) {
          const attr = attrs[i];
          if (attr.value && attr.value.includes('.mp4') && !attr.value.startsWith('blob:')) {
            console.log(`Instagram Cloud: ✅ Method 5 (DOM attributes) found video URL`);
            return attr.value;
          }
        }
      }
    } catch (e) {
      console.log(`Instagram Cloud: ❌ Method 5 (DOM attributes) failed:`, e);
    }

    console.log(`Instagram Cloud: ❌ All 7 methods failed to extract real video URL`);
    return '';
  }

  /**
   * Process and save a post
   * @param post The post to save
   * @param options Options for saving
   * @returns SaveResult containing success status and error message if applicable
   */
  async processAndSavePost(post: Post, options: SaveOptions = {}): Promise<SaveResult> {
    try {
      console.log(`Instagram Cloud: Processing and saving post ${post.id}`);

      // Always save to local storage first
      let savedLocally = false;
      if (options.saveToLocal !== false) {
        savedLocally = await this.saveToLocal(post);
        console.log(`Instagram Cloud: Post ${post.id} saved locally: ${savedLocally}`);
      }

      // Upload to cloud if requested
      if (options.uploadToCloud) {
        const token = await authService.getToken();

        if (!token) {
          console.log('Instagram Cloud: No authentication token available. Post saved locally only.');
          return {
            success: true, // Still consider this a success since we saved locally
            postId: post.id,
            savedLocally: savedLocally,
            uploadedToCloud: false,
            error: 'Post saved locally only. Sign in to enable cloud backup.'
          };
        }

        const cloudSuccess = await this.uploadToCloud(post, token);
        return {
          success: true,
          postId: post.id,
          savedLocally: savedLocally,
          uploadedToCloud: cloudSuccess,
          error: cloudSuccess ? undefined : 'Failed to upload to cloud, but post was saved locally'
        };
      }

      return {
        success: true,
        postId: post.id,
        savedLocally: savedLocally,
        uploadedToCloud: false
      };
    } catch (error) {
      console.error('Instagram Cloud: Error processing and saving post:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}
