/**
 * Reddit Service
 *
 * Handles Reddit-specific post extraction and saving logic.
 * Fixes image extraction issues.
 */

import { Post, Platform, MediaItem } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult, ImageExtractionResult } from '../base/types';
import { savePost } from '../../storage';

// Reddit-specific selectors
const POST_CONTAINER_SELECTOR = 'div[data-testid="post-container"], div.Post';

export class RedditService extends BasePlatformService {
  constructor() {
    super('Reddit');
  }

  /**
   * Extract post data from a Reddit post element
   * @param element The post element
   * @param options Options for extraction
   * @returns ExtractionResult containing the extracted post data or an error
   */
  async extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult> {
    try {
      console.log('Reddit: Extracting post data from element');
      console.log('Reddit: Element HTML preview:', element.outerHTML.substring(0, 500));

      // --- Author Information ---
      let authorHandle = '';
      let authorImageUrl = '';
      let authorName = '';

      // Try different selectors for author information (updated for modern Reddit)
      const authorSelectors = [
        // Modern Reddit selectors (2024) - more comprehensive
        'shreddit-post [slot="authorName"] a',
        'shreddit-post [slot="authorName"]',
        'shreddit-post [slot="author"] a',
        'shreddit-post [slot="author"]',
        'faceplate-tracker[data-faceplate-tracking-context*="author"] a',
        'a[slot="authorName"]',
        'a[slot="author"]',
        'shreddit-post-author-line a',
        'div[slot="authorLine"] a',
        // Try looking inside the post for any user links
        'shreddit-post a[href*="/user/"]',
        'shreddit-post a[href*="/u/"]',
        // Look for any element with author-related attributes
        'shreddit-post [data-testid*="author"]',
        'shreddit-post [aria-label*="author"]',
        // Legacy selectors
        'a[data-testid="post_author_link"]',
        '[slot="author"]',
        'a[href^="/user/"]',
        'a[href^="/u/"]',
        // Fallback - look for any link that looks like a username
        'shreddit-post a[href*="/u/"][href*="/"]'
      ];

      // First try to get author from the shreddit-post element attribute
      const authorFromAttribute = element.getAttribute('author');

      if (authorFromAttribute) {
        authorName = authorFromAttribute;
        authorHandle = authorFromAttribute.replace(/^u\//, ''); // Remove u/ prefix if present
        console.log(`Reddit: Found author from attribute: ${authorName}`);
      } else {
        console.log('Reddit: No author attribute found, trying author selectors...');
        for (const selector of authorSelectors) {
          const authorElement = element.querySelector(selector);
          if (authorElement) {
            authorName = authorElement.textContent?.trim() || '';
            authorHandle = authorName.replace(/^u\//, ''); // Remove u/ prefix if present
            console.log(`Reddit: Found author from ${selector}: ${authorName}`);
            break;
          } else {
            console.log(`Reddit: No author found with selector: ${selector}`);
          }
        }
      }

      // Extract author avatar from icon attribute
      const authorIconFromAttribute = element.getAttribute('icon');
      if (authorIconFromAttribute) {
        authorImageUrl = authorIconFromAttribute;
        console.log(`Reddit: Found author avatar from icon attribute: ${authorImageUrl.substring(0, 50)}...`);
      } else {
        // Fallback to selector-based approach
        const avatarSelectors = [
          'faceplate-tracker[data-faceplate-tracking-context*="author"] img',
          'shreddit-post-author-line img',
          'div[slot="authorLine"] img',
          'a[href^="/user/"] img',
          'a[href^="/u/"] img'
        ];

        for (const selector of avatarSelectors) {
          const authorImg = element.querySelector(selector);
          if (authorImg instanceof HTMLImageElement && authorImg.src) {
            authorImageUrl = authorImg.src;
            console.log(`Reddit: Found author avatar from selector ${selector}: ${authorImageUrl.substring(0, 50)}...`);
            break;
          }
        }
      }

      // --- Post Content ---
      let content = '';

      // Extract title (updated selectors for modern Reddit)
      const titleSelectors = [
        // Modern Reddit selectors (2024)
        'shreddit-post [slot="title"]',
        'shreddit-post [slot="title"] h1',
        'shreddit-post [slot="title"] h3',
        'shreddit-post [slot="title"] a',
        'h1[slot="title"]',
        'div[slot="title"] h1',
        'div[slot="title"] h3',
        'div[slot="title"] a',
        'shreddit-post h1',
        'shreddit-post h3',
        'faceplate-tracker h1',
        'faceplate-tracker h3',
        // Legacy selectors
        'h1',
        'h3',
        '[data-testid="post-title"]'
      ];

      let title = '';
      console.log('Reddit: Trying title selectors...');
      for (const selector of titleSelectors) {
        const titleElement = element.querySelector(selector);
        if (titleElement && titleElement.textContent?.trim()) {
          title = titleElement.textContent.trim();
          console.log(`Reddit: Found title from ${selector}: ${title}`);
          break;
        } else {
          console.log(`Reddit: No title found with selector: ${selector}`);
        }
      }

      // Extract text content (updated selectors for modern Reddit)
      const textSelectors = [
        // Modern Reddit selectors (2024)
        'shreddit-post [slot="text-body"]',
        'shreddit-post [slot="textBody"]',
        'shreddit-post div[slot="text-body"]',
        'shreddit-post div[slot="textBody"]',
        'div[slot="text-body"]',
        'div[slot="textBody"]',
        'shreddit-post-content',
        'div[data-adclicklocation="media"]',
        'faceplate-partial[src*="post-content"]',
        // Legacy selectors
        '[data-testid="post-content"]',
        '.RichTextJSON-root',
        '.usertext-body',
        '.md'
      ];

      let text = '';
      console.log('Reddit: Trying text content selectors...');
      for (const selector of textSelectors) {
        const textElement = element.querySelector(selector);
        if (textElement && textElement.textContent?.trim()) {
          text = textElement.textContent.trim();
          console.log(`Reddit: Found text content from ${selector}, length: ${text.length}`);
          break;
        } else {
          console.log(`Reddit: No text content found with selector: ${selector}`);
        }
      }

      // If no content found, try to extract any text from the post element
      if (!title && !text) {
        console.log('Reddit: No content found with specific selectors, trying fallback extraction...');

        // Try to get all text content and filter out navigation/UI elements
        const allText = element.textContent || '';
        const lines = allText.split('\n').map(line => line.trim()).filter(line => {
          // Filter out common UI elements
          return line.length > 10 &&
                 !line.match(/^(upvote|downvote|share|save|hide|report|crosspost)$/i) &&
                 !line.match(/^\d+\s*(points?|upvotes?|comments?)$/i) &&
                 !line.match(/^(r\/|u\/)\w+$/i) &&
                 !line.includes('ago') &&
                 !line.includes('•');
        });

        if (lines.length > 0) {
          // Take the first substantial line as title, rest as content
          title = lines[0];
          if (lines.length > 1) {
            text = lines.slice(1).join('\n');
          }
          console.log(`Reddit: Fallback extraction found title: ${title.substring(0, 50)}...`);
          console.log(`Reddit: Fallback extraction found text: ${text.substring(0, 50)}...`);
        }
      }

      // Combine title and text
      content = title ? (text ? `${title}\n\n${text}` : title) : text;
      console.log(`Reddit: Final content length: ${content.length}, content preview: ${content.substring(0, 100)}...`);

      // --- Post URL and ID ---
      // Extract post URL (updated selectors for modern Reddit)
      let permalink = window.location.href;
      const linkSelectors = [
        // Modern Reddit selectors
        'a[slot="full-post-link"]',
        'shreddit-post a[href*="/comments/"]',
        'faceplate-tracker a[href*="/comments/"]',
        'div[slot="title"] a',
        'h1 a',
        'h3 a',
        // Legacy selectors
        'a[data-click-id="body"]',
        'a[data-testid="post-title"]',
        'a[href^="/r/"][href*="/comments/"]'
      ];

      for (const selector of linkSelectors) {
        const postLink = element.querySelector(selector);
        if (postLink instanceof HTMLAnchorElement && postLink.href.includes('/comments/')) {
          permalink = postLink.href;
          console.log(`Reddit: Found permalink from ${selector}: ${permalink}`);
          break;
        }
      }

      // Extract post ID from permalink
      let postId = '';
      const idMatch = permalink.match(/\/comments\/([a-z0-9]+)\//i);
      if (idMatch && idMatch[1]) {
        postId = idMatch[1];
        console.log(`Reddit: Extracted post ID from permalink: ${postId}`);
      } else {
        postId = `reddit_${Date.now()}_${Math.floor(Math.random() * 9999)}`;
        console.log(`Reddit: Generated fallback post ID: ${postId}`);
      }

      // --- Timestamp ---
      let timestamp = new Date().toISOString();
      const timeSelectors = [
        // Modern Reddit selectors
        'faceplate-timeago',
        'shreddit-post time',
        'div[slot="authorLine"] time',
        'time[data-testid="post_timestamp"]',
        // Legacy selectors
        'time',
        '[data-testid="post_timestamp"]'
      ];

      for (const selector of timeSelectors) {
        const timeElement = element.querySelector(selector);
        if (timeElement instanceof HTMLTimeElement && timeElement.dateTime) {
          timestamp = new Date(timeElement.dateTime).toISOString();
          console.log(`Reddit: Found timestamp from ${selector}: ${timestamp}`);
          break;
        } else if (timeElement && timeElement.getAttribute('datetime')) {
          timestamp = new Date(timeElement.getAttribute('datetime') || '').toISOString();
          console.log(`Reddit: Found timestamp from ${selector} datetime attribute: ${timestamp}`);
          break;
        }
      }

      // --- Stats ---
      // Extract upvotes (updated selectors for modern Reddit)
      let upvotes = 0;
      const upvoteSelectors = [
        // Modern Reddit selectors - more specific to avoid picking up view counts
        'shreddit-post[vote-count]', // Check vote-count attribute first
        'div[slot="voteButton"] faceplate-number', // Vote button specific
        'shreddit-post-voting-button[aria-label*="upvote"] + faceplate-number', // Number next to upvote button
        'div[data-testid="post-voting"] faceplate-number', // Voting section specific
        // Legacy selectors
        '[data-testid="post-voting"] div[data-testid="post-voting-count"]',
        '.score',
        '.upvotes'
      ];

      // First try to get vote count from element attributes
      const voteCountAttr = element.getAttribute('vote-count');
      if (voteCountAttr) {
        const voteCount = parseInt(voteCountAttr, 10);
        if (!isNaN(voteCount)) {
          upvotes = voteCount;
          console.log(`Reddit: Found upvotes from vote-count attribute: ${upvotes}`);
        }
      }

      // If no attribute, try selectors
      if (upvotes === 0) {
        console.log('Reddit: Trying to find upvotes with selectors...');

        for (const selector of upvoteSelectors) {
          const upvoteElement = element.querySelector(selector);
          console.log(`Reddit: Checking selector "${selector}": ${upvoteElement ? 'found element' : 'no element'}`);

          if (upvoteElement) {
            const upvoteText = upvoteElement.textContent?.trim() || upvoteElement.getAttribute('aria-label') || '';
            console.log(`Reddit: Element text: "${upvoteText}"`);

            const numberMatch = upvoteText.match(/(\d+(?:\.\d+)?)\s*([kK]?)/);
            if (numberMatch) {
              const number = parseFloat(numberMatch[1]);
              const suffix = numberMatch[2].toLowerCase();
              const calculatedUpvotes = suffix === 'k' ? number * 1000 : number;

              // Sanity check: Reddit posts rarely have more than 100k upvotes
              if (calculatedUpvotes <= 100000) {
                upvotes = calculatedUpvotes;
                console.log(`Reddit: Found upvotes from ${selector}: ${upvotes}`);
                break;
              } else {
                console.log(`Reddit: Skipping suspicious upvote count from ${selector}: ${calculatedUpvotes} (too high, likely view count)`);
              }
            }
          }
        }

        // If still no upvotes found, try a broader search for any faceplate-number
        if (upvotes === 0) {
          console.log('Reddit: No upvotes found with specific selectors, trying broader search...');
          const allNumbers = element.querySelectorAll('faceplate-number');
          console.log(`Reddit: Found ${allNumbers.length} faceplate-number elements`);

          for (let i = 0; i < allNumbers.length; i++) {
            const numberEl = allNumbers[i];
            const text = numberEl.textContent?.trim() || '';
            const numberMatch = text.match(/(\d+(?:\.\d+)?)\s*([kK]?)/);

            if (numberMatch) {
              const number = parseFloat(numberMatch[1]);
              const suffix = numberMatch[2].toLowerCase();
              const calculatedNumber = suffix === 'k' ? number * 1000 : number;

              console.log(`Reddit: faceplate-number ${i}: "${text}" = ${calculatedNumber}`);

              // Take the first reasonable number (likely upvotes, as they appear first)
              if (calculatedNumber > 0 && calculatedNumber <= 100000 && upvotes === 0) {
                upvotes = calculatedNumber;
                console.log(`Reddit: Using first reasonable number as upvotes: ${upvotes}`);
                break;
              }
            }
          }
        }
      }

      // Extract comment count (updated selectors for modern Reddit)
      let commentCount = 0;
      const commentSelectors = [
        // Modern Reddit selectors
        'shreddit-comment-action-row faceplate-number',
        'div[slot="actionRow"] faceplate-number',
        'a[aria-label*="comment"] faceplate-number',
        'button[aria-label*="comment"] faceplate-number',
        // Legacy selectors
        'a[data-testid="comments-page-link-num-comments"]',
        '.comments',
        '.comment-count'
      ];

      for (const selector of commentSelectors) {
        const commentElement = element.querySelector(selector);
        if (commentElement) {
          const commentText = commentElement.textContent?.trim() || commentElement.getAttribute('aria-label') || '';
          const numberMatch = commentText.match(/(\d+(?:\.\d+)?)\s*([kK]?)/);
          if (numberMatch) {
            const number = parseFloat(numberMatch[1]);
            const suffix = numberMatch[2].toLowerCase();
            commentCount = suffix === 'k' ? number * 1000 : number;
            console.log(`Reddit: Found comment count from ${selector}: ${commentCount}`);
            break;
          }
        }
      }

      // Create post object
      const post: Post = {
        id: postId,
        platform: 'Reddit',
        author: authorName || 'Unknown Author',
        authorName: authorName || 'Unknown Author',
        authorHandle,
        authorUrl: authorHandle ? `https://www.reddit.com/user/${authorHandle}` : '',
        authorAvatar: authorImageUrl,
        textContent: content,
        content,
        permalink,
        savedAt: new Date().toISOString(),
        timestamp,
        media: [],
        stats: {
          comments: commentCount,
          shares: 0,
          likes: upvotes,
          views: 0
        },
        categories: [],
        tags: []
      };

      console.log('Reddit: Created post object:', {
        id: post.id,
        platform: post.platform,
        author: post.author,
        authorName: post.authorName,
        content: post.content,
        contentLength: post.content?.length,
        permalink: post.permalink
      });

      // Extract images if requested
      if (options?.includeImages !== false) {
        const imageResult = await this.extractImageUrls(element, options);
        if (imageResult.success && imageResult.images && imageResult.images.length > 0) {
          post.media = imageResult.images;
          console.log(`Reddit: Extracted ${post.media.length} media items`);
        } else {
          console.log(`Reddit: No media extracted or extraction failed: ${imageResult.error || 'No error'}`);
        }
      }

      return {
        success: true,
        post
      };
    } catch (error) {
      console.error('Reddit: Error extracting post data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract image URLs from a Reddit post element or post data
   * @param source The post element or post data
   * @param options Options for extraction
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  async extractImageUrls(source: Post | HTMLElement, options?: ExtractionOptions): Promise<ImageExtractionResult> {
    try {
      const images: MediaItem[] = [];
      const uniqueMediaUrls = new Set<string>();
      const uniqueImageIds = new Set<string>(); // Track Reddit image IDs to prevent duplicates

      // Helper function to extract Reddit image ID from URL
      const getRedditImageId = (url: string): string | null => {
        // Extract image ID from URLs like i.redd.it/abc123.jpg or preview.redd.it/title-v0-abc123.jpg
        const match = url.match(/(?:i\.redd\.it|preview\.redd\.it)\/(?:.*?-v\d+-)?([a-zA-Z0-9]+)\./);
        return match ? match[1] : null;
      };

      // Helper function to check if image should be added (prevents duplicates)
      const shouldAddImage = (url: string): boolean => {
        if (uniqueMediaUrls.has(url)) return false;
        
        const imageId = getRedditImageId(url);
        if (imageId && uniqueImageIds.has(imageId)) {
          console.log(`Reddit: Skipping duplicate image with ID ${imageId}: ${url.substring(0, 50)}...`);
          return false;
        }
        
        uniqueMediaUrls.add(url);
        if (imageId) uniqueImageIds.add(imageId);
        return true;
      };

      if (source instanceof HTMLElement) {
        console.log('Reddit: Extracting images from DOM element');

        // First, try to get ALL gallery carousel images
        const galleryCarouselImages = source.querySelectorAll('gallery-carousel img.media-lightbox-img, gallery-carousel img, [data-testid="gallery"] img');
        console.log(`Reddit: Found ${galleryCarouselImages.length} gallery carousel images`);

        galleryCarouselImages.forEach((img, index) => {
          if (img instanceof HTMLImageElement && img.src) {
            const imageUrl = img.src;
            if (!uniqueMediaUrls.has(imageUrl)) {
              uniqueMediaUrls.add(imageUrl);
              images.push({
                type: 'image',
                url: imageUrl,
                alt: img.alt || `Reddit gallery image ${index + 1}`
              });
              console.log(`Reddit: Added gallery carousel image ${index + 1}: ${imageUrl.substring(0, 50)}...`);
            }
          }
        });

        // Then, try to get single post images
        const singlePostImg = source.querySelector('.media-lightbox-img.preview-img');
        if (singlePostImg instanceof HTMLImageElement && singlePostImg.src) {
          let imageUrl = singlePostImg.src;
          
          // If it's a preview URL, try to convert it to high-res i.redd.it URL
          if (imageUrl.includes('preview.redd.it')) {
            // Look for corresponding high-res image in the post's content-href attribute
            const contentHref = source.getAttribute('content-href');
            if (contentHref && contentHref.includes('i.redd.it')) {
              imageUrl = contentHref; // Use the high-res URL instead
              console.log(`Reddit: Using high-res URL from content-href: ${imageUrl.substring(0, 50)}...`);
            }
          }
          
          if (shouldAddImage(imageUrl)) {
            images.push({
              type: 'image',
              url: imageUrl,
              alt: singlePostImg.alt || 'Reddit post image'
            });
            console.log(`Reddit: Added single post image: ${imageUrl.substring(0, 50)}...`);
          }
        }

        // Check if this is an image post (for legacy detection)
        const isImagePost = source.querySelector('[data-testid="post-content"] img, .media-element img') !== null;
        console.log(`Reddit: Is image post: ${isImagePost}`);

        // --- Strategy 1: Check for images in text body ---
        const textBody = source.querySelector('[data-testid="post-content"], .RichTextJSON-root');
        if (textBody) {
          // Look for direct image links in the text
          const imageLinks = textBody.querySelectorAll('a[href*=".jpg"], a[href*=".jpeg"], a[href*=".png"], a[href*=".gif"], a[href*="i.redd.it"], a[href*="i.imgur.com"]');
          console.log(`Reddit: Found ${imageLinks.length} image links in text body`);

          imageLinks.forEach(link => {
            if (link instanceof HTMLAnchorElement && link.href) {
              // Extract the highest quality image URL from the link
              let imageUrl = link.href;

              // If it's a Reddit preview URL, try to get the highest quality version
              if (imageUrl.includes('preview.redd.it')) {
                // Extract the width parameter and try to increase it
                const widthMatch = imageUrl.match(/width=(\d+)/);
                if (widthMatch && widthMatch[1]) {
                  // Replace with a higher resolution
                  imageUrl = imageUrl.replace(`width=${widthMatch[1]}`, 'width=2048');
                }
              }

              // If it's an Imgur link, ensure it's a direct image link
              if (imageUrl.includes('imgur.com') && !imageUrl.match(/\.(jpg|jpeg|png|gif)$/i)) {
                // Convert to direct image link if possible
                imageUrl = imageUrl.replace(/imgur\.com\//, 'i.imgur.com/') + '.jpg';
              }

              if (!uniqueMediaUrls.has(imageUrl)) {
                uniqueMediaUrls.add(imageUrl);
                images.push({
                  type: 'image',
                  url: imageUrl,
                  alt: link.textContent || 'Reddit post linked image'
                });
                console.log(`Reddit: Added image URL from link: ${imageUrl.substring(0, 50)}...`);
              }
            }
          });
        }

        // --- Strategy 2: Check for high-resolution images in zoomable containers ---
        const highResImages = source.querySelectorAll('.zoomable-img-wrapper img, zoomable-img img, [data-testid="media-element"] img');
        console.log(`Reddit: Found ${highResImages.length} high-resolution images`);

        if (highResImages.length > 0) {
          highResImages.forEach(img => {
            if (img instanceof HTMLImageElement && img.src) {
              // Get the highest quality version of the image
              let imageUrl = img.src;

              // If it's a Reddit preview URL, try to get the highest quality version
              if (imageUrl.includes('preview.redd.it')) {
                // Extract the width parameter and try to increase it
                const widthMatch = imageUrl.match(/width=(\d+)/);
                if (widthMatch && widthMatch[1]) {
                  // Replace with a higher resolution
                  imageUrl = imageUrl.replace(`width=${widthMatch[1]}`, 'width=2048');
                }
              }

              if (shouldAddImage(imageUrl)) {
                images.push({
                  type: 'image',
                  url: imageUrl,
                  alt: img.alt || 'Reddit post image'
                });
                console.log(`Reddit: Added high-res image: ${imageUrl.substring(0, 50)}...`);
              }
            }
          });
        }

        // --- Strategy 2.5: Check for additional gallery formats ---
        if (images.length === 0) {
          const additionalGalleryImages = source.querySelectorAll(
            'shreddit-gallery img, ' +
            '[data-testid="gallery-carousel"] img, ' +
            '.gallery-carousel img, ' +
            '.reddit-gallery img, ' +
            '[class*="gallery"] img, ' +
            '[class*="carousel"] img'
          );
          console.log(`Reddit: Found ${additionalGalleryImages.length} additional gallery images`);

          additionalGalleryImages.forEach((img, index) => {
            if (img instanceof HTMLImageElement && img.src && (img.width > 100 || img.height > 100)) {
              let imageUrl = img.src;

              // Enhance image quality for Reddit preview URLs
              if (imageUrl.includes('preview.redd.it')) {
                const widthMatch = imageUrl.match(/width=(\d+)/);
                if (widthMatch && widthMatch[1]) {
                  imageUrl = imageUrl.replace(`width=${widthMatch[1]}`, 'width=2048');
                }
              }

              if (!uniqueMediaUrls.has(imageUrl)) {
                uniqueMediaUrls.add(imageUrl);
                images.push({
                  type: 'image',
                  url: imageUrl,
                  alt: img.alt || `Reddit gallery image ${index + 1}`
                });
                console.log(`Reddit: Added additional gallery image ${index + 1}: ${imageUrl.substring(0, 50)}...`);
              }
            }
          });
        }

        // --- Strategy 3: Check for media elements (images and videos) ---
        const mediaElements = source.querySelectorAll('.media-element img, [data-testid="post-content"] img, video, [data-testid="post-content"] video');
        console.log(`Reddit: Found ${mediaElements.length} media elements`);

        mediaElements.forEach(media => {
          if (media instanceof HTMLImageElement && media.src) {
            // Skip small thumbnails and icons
            if (media.width > 100 || media.height > 100 || media.src.includes('i.redd.it')) {
              // Get the highest quality version of the image
              let imageUrl = media.src;

              // If it's a Reddit preview URL, try to get the highest quality version
              if (imageUrl.includes('preview.redd.it')) {
                // Extract the width parameter and try to increase it
                const widthMatch = imageUrl.match(/width=(\d+)/);
                if (widthMatch && widthMatch[1]) {
                  // Replace with a higher resolution
                  imageUrl = imageUrl.replace(`width=${widthMatch[1]}`, 'width=2048');
                }
              }

              if (!uniqueMediaUrls.has(imageUrl)) {
                uniqueMediaUrls.add(imageUrl);
                images.push({
                  type: 'image',
                  url: imageUrl,
                  alt: media.alt || 'Reddit post image'
                });
                console.log(`Reddit: Added image from media element: ${imageUrl.substring(0, 50)}...`);
              }
            }
          } else if (media instanceof HTMLVideoElement) {
            // Try to get the video source
            if (media.src && !uniqueMediaUrls.has(media.src)) {
              uniqueMediaUrls.add(media.src);
              images.push({
                type: 'video',
                url: media.src,
                alt: 'Reddit post video'
              });
              console.log(`Reddit: Added video from src: ${media.src.substring(0, 50)}...`);
            }
            // If no src, try to get sources from source elements
            else {
              const sources = media.querySelectorAll('source');
              if (sources.length > 0) {
                // Get the highest quality source
                let bestSource: HTMLSourceElement | null = null;
                let bestWidth = 0;

                sources.forEach(source => {
                  // Check if it has a size attribute
                  const width = parseInt(source.getAttribute('width') || '0', 10);
                  if (width > bestWidth) {
                    bestWidth = width;
                    bestSource = source;
                  }
                });

                // If no width info, just use the first source
                if (!bestSource && sources.length > 0) {
                  bestSource = sources[0] as HTMLSourceElement;
                }

                if (bestSource && bestSource.src && !uniqueMediaUrls.has(bestSource.src)) {
                  uniqueMediaUrls.add(bestSource.src);
                  images.push({
                    type: 'video',
                    url: bestSource.src,
                    alt: 'Reddit post video'
                  });
                  console.log(`Reddit: Added video from source: ${bestSource.src.substring(0, 50)}...`);
                }
              }
              // If no sources, try to get the poster image
              else if (media.poster && !uniqueMediaUrls.has(media.poster)) {
                uniqueMediaUrls.add(media.poster);
                images.push({
                  type: 'image',
                  url: media.poster,
                  alt: 'Reddit post video thumbnail'
                });
                console.log(`Reddit: Added video poster: ${media.poster.substring(0, 50)}...`);
              }
            }
          }
        });

        // --- Strategy 4: Look for preview images in post cards ---
        if (images.length === 0) {
          const previewImages = source.querySelectorAll('[data-testid="post-container"] [data-click-id="media"] img, [data-click-id="image"] img');
          console.log(`Reddit: Found ${previewImages.length} preview images`);

          previewImages.forEach(img => {
            if (img instanceof HTMLImageElement && img.src) {
              // Skip small thumbnails and icons
              if (img.width > 100 || img.height > 100 || img.src.includes('i.redd.it')) {
                // Get the highest quality version of the image
                let imageUrl = img.src;

                // If it's a Reddit preview URL, try to get the highest quality version
                if (imageUrl.includes('preview.redd.it')) {
                  // Extract the width parameter and try to increase it
                  const widthMatch = imageUrl.match(/width=(\d+)/);
                  if (widthMatch && widthMatch[1]) {
                    // Replace with a higher resolution
                    imageUrl = imageUrl.replace(`width=${widthMatch[1]}`, 'width=2048');
                  }
                }

                if (!uniqueMediaUrls.has(imageUrl)) {
                  uniqueMediaUrls.add(imageUrl);
                  images.push({
                    type: 'image',
                    url: imageUrl,
                    alt: img.alt || 'Reddit post preview image'
                  });
                  console.log(`Reddit: Added preview image: ${imageUrl.substring(0, 50)}...`);
                }
              }
            }
          });
        }
      } else {
        console.log('Reddit: Extracting images from post data');

        // Extract images from post data
        if (source.media) {
          source.media.forEach(item => {
            if (!uniqueMediaUrls.has(item.url)) {
              uniqueMediaUrls.add(item.url);
              images.push(item);
            }
          });
          console.log(`Reddit: Extracted ${images.length} media items from post data`);
        }
      }

      return {
        success: true,
        images
      };
    } catch (error) {
      console.error('Reddit: Error extracting image URLs:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      await savePost(post);
      return true;
    } catch (error) {
      console.error('Error saving Reddit post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post): Promise<boolean> {
    try {
      const { syncToCloud } = await import('../../services/cloudSyncService');
      const result = await syncToCloud(post);
      return result.success;
    } catch (error) {
      console.error('Error uploading Reddit post to cloud:', error);
      return false;
    }
  }
}
