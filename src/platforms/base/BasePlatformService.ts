/**
 * Base Platform Service
 * 
 * Abstract base class that defines the common interface for all platform services.
 * Each platform-specific service should extend this class and implement its methods.
 */

import { Post, Platform, MediaItem } from '../../types';
import { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult, ImageExtractionResult } from './types';

export abstract class BasePlatformService {
  protected platform: Platform;

  constructor(platform: Platform) {
    this.platform = platform;
  }

  /**
   * Extract post data from a DOM element
   * @param element The DOM element containing the post
   * @param options Options for extraction
   * @returns ExtractionResult containing the extracted post data or an error
   */
  abstract extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult>;

  /**
   * Extract image URLs from a post or DOM element
   * @param source The post data or DOM element to extract images from
   * @param options Options for extraction
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  abstract extractImageUrls(source: Post | HTMLElement, options?: ExtractionOptions): Promise<ImageExtractionResult>;

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  abstract saveToLocal(post: Post): Promise<boolean>;

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  abstract uploadToCloud(post: Post): Promise<boolean>;

  /**
   * Process and save a post
   * @param post The post data to process and save
   * @param options Options for saving
   * @returns SaveResult containing the result of the save operation
   */
  async processAndSavePost(post: Post, options: SaveOptions = {}): Promise<SaveResult> {
    try {
      const { saveToLocal = true, uploadToCloud = true } = options;
      
      // Validate post data
      const validatedPost = this.validatePost(post);
      
      // Save to local storage if requested
      let savedLocally = false;
      if (saveToLocal) {
        savedLocally = await this.saveToLocal(validatedPost);
        if (!savedLocally) {
          return { 
            success: false, 
            error: 'Failed to save post to local storage',
            savedLocally: false,
            uploadedToCloud: false
          };
        }
      }
      
      // Upload to cloud if requested
      let uploadedToCloud = false;
      if (uploadToCloud) {
        uploadedToCloud = await this.uploadToCloud(validatedPost);
      }
      
      return {
        success: true,
        postId: validatedPost.id,
        savedLocally,
        uploadedToCloud
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        savedLocally: false,
        uploadedToCloud: false
      };
    }
  }

  /**
   * Validate post data before saving
   * @param post The post data to validate
   * @returns The validated post data
   */
  validatePost(post: Partial<Post>): Post {
    // Ensure all required fields are present
    const validatedPost: Post = {
      id: post.id || `${this.platform}-${Date.now()}`,
      platform: this.platform,
      author: post.author || '',
      authorName: post.authorName || post.author || '',
      authorTitle: post.authorTitle || '',
      authorUrl: post.authorUrl || '',
      authorAvatar: post.authorAvatar || post.authorImage || '',
      textContent: post.textContent || post.content || '',
      content: post.content || post.textContent || '',
      permalink: post.permalink || '',
      savedAt: new Date().toISOString(),
      media: post.media || [],
      stats: {
        likes: post.stats?.likes || 0,
        comments: post.stats?.comments || 0,
        shares: post.stats?.shares || 0,
        views: post.stats?.views || 0
      },
      categories: post.categories || [],
      tags: post.tags || []
    };
    
    return validatedPost;
  }
}
