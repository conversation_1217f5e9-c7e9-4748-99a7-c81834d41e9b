/**
 * Common types for platform modules
 */

import { Post, Platform, MediaItem } from '../../types';

/**
 * Interface for platform-specific extraction options
 */
export interface ExtractionOptions {
  includeImages?: boolean;
  includeVideos?: boolean;
  includeText?: boolean;
  maxImages?: number;
  maxVideos?: number;
}

/**
 * Interface for platform-specific save options
 */
export interface SaveOptions {
  saveToLocal?: boolean;
  uploadToCloud?: boolean;
  processWithAI?: boolean;
  useCloudService?: boolean;
}

/**
 * Interface for extraction result
 */
export interface ExtractionResult {
  success: boolean;
  post?: Post;
  error?: string;
}

/**
 * Interface for save result
 */
export interface SaveResult {
  success: boolean;
  postId?: string;
  error?: string;
  savedLocally?: boolean;
  uploadedToCloud?: boolean;
}

/**
 * Interface for image extraction result
 */
export interface ImageExtractionResult {
  success: boolean;
  images?: MediaItem[];
  error?: string;
  isDocumentCarousel?: boolean;
  message?: string;
}
