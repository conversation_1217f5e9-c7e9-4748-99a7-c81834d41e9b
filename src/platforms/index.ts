/**
 * Platform Service Factory
 *
 * This module exports the platform services and provides a factory function
 * to get the appropriate service for a given platform.
 */

import { Platform } from '../types';
import { BasePlatformService } from './base/BasePlatformService';
import { TwitterService } from './twitter/TwitterService';
import { LinkedInService } from './linkedin/LinkedInService';
import { InstagramServiceV2 } from './instagram/InstagramServiceV2';
import { InstagramCloudService } from './instagram/InstagramCloudService';
import { RedditService } from './reddit/RedditService';
import { PinterestService } from './pinterest/PinterestService';
import { WebService } from './web/WebService';

// Export all platform services
export { TwitterService } from './twitter/TwitterService';
export { LinkedInService } from './linkedin/LinkedInService';
export { InstagramServiceV2 } from './instagram/InstagramServiceV2';
export { InstagramCloudService } from './instagram/InstagramCloudService';
export { RedditService } from './reddit/RedditService';
export { PinterestService } from './pinterest/PinterestService';
export { WebService } from './web/WebService';

// Export types
export { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult, ImageExtractionResult } from './base/types';

// Platform service instances cache
const serviceInstances: Record<Platform, BasePlatformService> = {} as Record<Platform, BasePlatformService>;

/**
 * Get the appropriate platform service for a given platform
 * @param platform The platform to get the service for
 * @param options Additional options for service creation
 * @returns The platform service
 * @throws Error if the platform is not supported
 */
export function getPlatformService(
  platform: Platform,
  options: { useCloudService?: boolean } = {}
): BasePlatformService {
  // For Instagram with cloud service, use a different cache key
  const cacheKey = platform + (options.useCloudService ? '-cloud' : '');

  // Return cached instance if available
  if (serviceInstances[cacheKey]) {
    return serviceInstances[cacheKey];
  }

  // Create a new instance
  let service: BasePlatformService;

  switch (platform) {
    case 'X/Twitter':
      service = new TwitterService();
      break;
    case 'LinkedIn':
      service = new LinkedInService();
      break;
    case 'Instagram':
      // Use the Cloud service for Instagram if specified
      if (options.useCloudService) {
        service = new InstagramCloudService();
      } else {
        service = new InstagramServiceV2();
      }
      break;
    case 'Reddit':
      service = new RedditService();
      break;
    case 'pinterest':
      service = new PinterestService();
      break;
    case 'Web':
      service = new WebService();
      break;
    case 'facebook':
      // Facebook is not implemented yet, but we'll create a placeholder
      throw new Error('Facebook integration is not implemented yet');
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }

  // Cache the instance
  serviceInstances[cacheKey] = service;

  return service;
}

/**
 * Detect the current platform based on the URL
 * @returns The detected platform or null if not on a supported platform
 */
export function detectCurrentPlatform(): Platform | null {
  const url = window.location.href;

  if (url.includes('twitter.com') || url.includes('x.com')) {
    return 'X/Twitter';
  } else if (url.includes('linkedin.com')) {
    return 'LinkedIn';
  } else if (url.includes('instagram.com')) {
    return 'Instagram';
  } else if (url.includes('reddit.com')) {
    return 'Reddit';
  } else if (url.includes('pinterest.com')) {
    return 'pinterest';
  } else if (url.includes('facebook.com')) {
    return 'facebook';
  }

  return null;
}
