// Authentication handler for background script
import { env } from '../config/env';

const SERVER_BASE_URL = env.SERVER_BASE_URL;

export function handleLogin() {
  // Create the OAuth 2.0 URL with the required parameters
  const authUrl = new URL(`${SERVER_BASE_URL}/auth/google`);

  // Get the extension ID for the redirect URL
  const extensionId = chrome.runtime.id;
  
  // Open the auth window
  chrome.windows.create({
    url: authUrl.toString(),
    type: 'popup',
    width: 600,
    height: 700
  }, (window) => {
    if (chrome.runtime.lastError) {
      console.error('[Background] Error opening auth window:', chrome.runtime.lastError.message);
    }
  });
}

// Listen for messages from the auth callback page
chrome.runtime.onMessageExternal.addListener((message, sender, sendResponse) => {
  if (message.type === 'AUTH_CALLBACK' && message.token) {
    // Store the token
    chrome.storage.local.set({
      'auth_token': message.token,
      'authToken': message.token, // Also store as authToken for compatibility
      'auth_timestamp': Date.now()
    }).then(async () => {
      // Fetch user profile data
      try {
        const response = await fetch(`${SERVER_BASE_URL}/auth/me`, {
          headers: {
            'Authorization': `Bearer ${message.token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const userData = await response.json();
          await chrome.storage.local.set({ user_profile: userData });
          console.log('[Background] User profile fetched and stored');
        } else {
          console.error('[Background] Failed to fetch user profile:', response.status);
        }
      } catch (error) {
        console.error('[Background] Error fetching user profile:', error);
      }

      // Close the auth window if it's still open
      if (sender.tab?.windowId) {
        chrome.windows.remove(sender.tab.windowId);
      }

      // Notify any listeners that auth is complete
      chrome.runtime.sendMessage({
        type: 'AUTH_COMPLETED',
        success: true
      });
    });
  }
});
