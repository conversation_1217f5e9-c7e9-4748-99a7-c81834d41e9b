/**
 * Notely Social - Unified Background Service Worker
 *
 * This is the main background script that handles:
 * - Context menus for web content saving
 * - Social media post processing and AI analysis
 * - Cloud synchronization
 * - Image fetching and storage
 * - Authentication management
 */

import { savePost, deletePost } from '../storage';
import { Post, AnalyzedPost, Platform, MediaItem } from '../types';
import {
  getAllCoreSubCategories,
  biasAdjustCategories,
  finalizeTags
} from '../categorizer';
import type { CoreSubCategorySlug } from '../config/constants';

import {
  callGpt4oVision,
  getOpenAiTextEmbedding,
} from '../analysisService';
import {
  generateAIInsight,
  generateAIFastTake,
  generateAIContentIdeas,
  InSightData
} from '../services/aiService';
import { setupImageFetchingHandler } from './imageHandler';

// Configuration
const API_URL = 'https://api.notely.social';
const BACKEND_URL = API_URL; // Alias for consistency



// =============================================================================
// INITIALIZATION
// =============================================================================

/**
 * Create context menus for web content saving
 */
function createContextMenus(): void {
  console.log('[Background] Creating context menus...');

  try {
    // Remove existing context menus first
    chrome.contextMenus.removeAll(() => {
      console.log('[Background] Existing context menus removed');

      // Create context menu for selected text
      chrome.contextMenus.create({
        id: 'add-text-to-notely',
        title: 'Add to Notely',
        contexts: ['selection'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
      }, () => {
        if (chrome.runtime.lastError) {
          console.error('[Background] Error creating text menu:', chrome.runtime.lastError);
        } else {
          console.log('[Background] ✅ Text menu created successfully');
        }
      });

      // Create context menu for images
      chrome.contextMenus.create({
        id: 'add-image-to-notely',
        title: 'Add to Notely',
        contexts: ['image'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
      }, () => {
        if (chrome.runtime.lastError) {
          console.error('[Background] Error creating image menu:', chrome.runtime.lastError);
        } else {
          console.log('[Background] ✅ Image menu created successfully');
        }
      });

      // Create context menu for pages
      chrome.contextMenus.create({
        id: 'add-page-to-notely',
        title: 'Add to Notely',
        contexts: ['page'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
      }, () => {
        if (chrome.runtime.lastError) {
          console.error('[Background] Error creating page menu:', chrome.runtime.lastError);
        } else {
          console.log('[Background] ✅ Page menu created successfully');
          console.log('[Background] ✅ All context menus created!');
        }
      });
    });
  } catch (error) {
    console.error('[Background] ❌ Context menu creation failed:', error);
  }
}

// Initialize context menus immediately
createContextMenus();

// Initialize the image fetching handler
setupImageFetchingHandler();

// Initialize API keys in Chrome storage
async function initializeApiKeys(): Promise<void> {
  try {
    const result = await chrome.storage.sync.get(['openaiApiKey']);
    if (!result.openaiApiKey) {
      // Set the OpenAI API key in Chrome storage
      await chrome.storage.sync.set({
        openaiApiKey: '********************************************************************************************************************************************************************'
      });
      console.log('[Background] OpenAI API key initialized in Chrome storage');
    } else {
      console.log('[Background] OpenAI API key already exists in Chrome storage');
    }
  } catch (error) {
    console.error('[Background] Error initializing API keys:', error);
  }
}

// Initialize API keys
initializeApiKeys();

// =============================================================================
// USER CATEGORY FREQUENCY DATA MANAGEMENT
// =============================================================================

interface UserCategoryPreferenceData {
  categoryCounts: { [slug: string]: number };
  totalAnalyzedPosts: number;
}

/**
 * Get user's category preference data from storage
 */
async function getUserFrequencyData(): Promise<UserCategoryPreferenceData> {
  try {
    const data = await chrome.storage.local.get(['userCategoryCounts', 'totalAnalyzedPosts']);
    return {
      categoryCounts: data.userCategoryCounts || {},
      totalAnalyzedPosts: data.totalAnalyzedPosts || 0,
    };
  } catch (error) {
    console.error('[Background] Error getting user frequency data:', error);
    return { categoryCounts: {}, totalAnalyzedPosts: 0 };
  }
}

/**
 * Update user's category preference data after AI analysis
 */
async function updateUserFrequencyData(finalCategories: CoreSubCategorySlug[]): Promise<void> {
  if (!finalCategories || finalCategories.length === 0) return;

  try {
    const currentData = await getUserFrequencyData();
    finalCategories.forEach(slug => {
      currentData.categoryCounts[slug] = (currentData.categoryCounts[slug] || 0) + 1;
    });
    currentData.totalAnalyzedPosts += 1;

    await chrome.storage.local.set({
      userCategoryCounts: currentData.categoryCounts,
      totalAnalyzedPosts: currentData.totalAnalyzedPosts,
    });

    console.log('[Background] User frequency data updated:', currentData);
  } catch (error) {
    console.error('[Background] Error updating user frequency data:', error);
  }
}

// =============================================================================
// AI PROCESSING AND CLOUD SYNC
// =============================================================================

/**
 * Process a post with AI analysis FIRST, then save to local and sync to cloud
 * This ensures the cloud gets the complete post with AI data (NEW FLOW)
 */
async function processPostWithAIAndSync(rawPostData: Post): Promise<void> {
  const { savePost } = await import('../storage'); // Moved import to function scope
  console.log(`[Background] Starting complete processing (AI first) for post ID: ${rawPostData.id}`);

  try {
    // 1. Check if user is logged in first (to save AI tokens)
    const tokenResult = await chrome.storage.local.get('authToken');
    const token = tokenResult?.authToken;

    if (!token) {
      console.log(`[Background] User not logged in. Saving basic post without AI for ID: ${rawPostData.id}`);
      // Save basic post without AI data
      const basicPost: Post = {
        ...rawPostData,
        savedAt: new Date().toISOString(),
      };
      await savePost(basicPost);
      return;
    }

    console.log(`[Background] User logged in, proceeding with AI-first processing for post ID: ${rawPostData.id}`);

    // 2. Generate AI analysis FIRST
    const analyzedPost = await generateAnalyzedPost(rawPostData);

    // 3. Save the complete post (with AI data) to local storage
    const completePost: AnalyzedPost = {
      ...analyzedPost,
      savedAt: new Date().toISOString(),
      source: 'local',
      uploadStatus: 'pending',
    };

    console.log(`[Background] About to save complete post:`, {
      id: completePost.id,
      platform: completePost.platform,
      permalink: completePost.permalink,
      hasPermalink: !!completePost.permalink,
      allKeys: Object.keys(completePost)
    });

    // Save the complete post (with AI data) to local storage
    const localSaveResult = await savePost(completePost);
    if (localSaveResult.status !== 'success') {
      console.error(`[Background] Failed to save complete post locally for ID ${completePost.id}. Status: ${localSaveResult.status}, Message: ${localSaveResult.message}`);
      // If local save fails (error or duplicate), stop further processing for this post.
      return;
    }
    console.log(`[Background] Complete post with AI data saved locally successfully for ID: ${completePost.id}`);

    // 4. Update user frequency data
    await updateUserFrequencyData(completePost.categories || []);

    // 5. Sync to cloud (now with AI data included)
    console.log(`[Background] Syncing complete post to cloud for ID: ${completePost.id}`);
    const { syncToCloud } = await import('../services/cloudSyncService');
    const result = await syncToCloud(completePost, token);

    if (result.success) {
      console.log(`[Background] Successfully synced complete post ${completePost.id} to cloud`);

      // Update with upload status
      const finalPost: AnalyzedPost = {
        ...completePost,
        uploadStatus: 'uploaded',
        source: 'synced',
      };
      const finalSaveResult = await savePost(finalPost);
      if (finalSaveResult.status !== 'success') {
        console.error(`[Background] Failed to update post ${finalPost.id} with 'uploaded' status locally. Status: ${finalSaveResult.status}, Message: ${finalSaveResult.message}`);
      } else {
        console.log(`[Background] Post ${finalPost.id} updated with 'uploaded' status locally.`);
      }

    } else {
      console.error(`[Background] Failed to sync complete post ${completePost.id} to cloud:`, result.error);

      // Update with failed status
      const failedPost: AnalyzedPost = {
        ...completePost,
        uploadStatus: 'failed',
        source: 'local',
      };
      const failedSaveResult = await savePost(failedPost);
      if (failedSaveResult.status !== 'success') {
        console.error(`[Background] Failed to update post ${failedPost.id} with 'failed' status locally. Status: ${failedSaveResult.status}, Message: ${failedSaveResult.message}`);
      } else {
        console.log(`[Background] Post ${failedPost.id} updated with 'failed' status locally.`);
      }
    }

    // 6. Notify dashboard to refresh
    chrome.runtime.sendMessage({ action: 'REFRESH_DASHBOARD_FROM_CLOUD' }, (refreshResponse) => {
      if (chrome.runtime.lastError) {
        console.warn(`[Background] Error sending REFRESH_DASHBOARD_FROM_CLOUD:`, chrome.runtime.lastError.message);
      } else {
        console.log(`[Background] REFRESH_DASHBOARD_FROM_CLOUD message sent. Response:`, refreshResponse);
      }
    });

  } catch (error) {
    console.error(`[Background] Error in AI-first processing for post ${rawPostData.id}:`, error);

    // Fallback: save basic post without AI data
    try {
      const basicPost: Post = {
        ...rawPostData,
        savedAt: new Date().toISOString(),
      };
      const fallbackSaveResult = await savePost(basicPost);
      if (fallbackSaveResult.status === 'success') {
        console.log(`[Background] Fallback: Successfully saved basic post without AI data for ID: ${basicPost.id}`);
      } else {
        console.error(`[Background] Fallback: Failed to save basic post for ID ${basicPost.id}. Status: ${fallbackSaveResult.status}, Message: ${fallbackSaveResult.message}`);
      }
    } catch (fallbackError) {
      console.error(`[Background] Fallback save also failed for post ${rawPostData.id}:`, fallbackError);
    }
  }
}

// processAndSyncPostInBackground function removed - using unified processPostWithAIAndSync flow

// syncPostToCloudInternal function removed - using unified processPostWithAIAndSync flow

/**
 * Generate analyzed post with AI enrichment
 * Takes raw post data and enriches it with AI analysis, categories, tags, and insights
 */
async function generateAnalyzedPost(rawPostData: Post): Promise<AnalyzedPost> {
  console.log('[Background] AI Analysis Pipeline initiated for post:', rawPostData.id || 'new post');
  console.log('[Background] Raw post data:', JSON.parse(JSON.stringify(rawPostData)));

  // 1. Data Collection
  const textContent = rawPostData.textContent || rawPostData.content || '';
  console.log(`[Background] Text content length: ${textContent.length}, preview: "${textContent.substring(0,50)}"`);

  let imageUrl: string | undefined = undefined;
  if (rawPostData.media && rawPostData.media.length > 0) {
    const firstMedia = rawPostData.media[0];
    if (firstMedia.type === 'image' && firstMedia.url) {
      imageUrl = firstMedia.url;
    }
  }
  if (imageUrl === '') imageUrl = undefined;

  if (imageUrl && (!textContent || textContent.length < 20)) {
    console.log('[Background] Image present with minimal/no text. AI analysis will be critical.');
  }

  // 2. Get all core sub-category slugs
  const allCoreSlugs = getAllCoreSubCategories();

  // 3. Call GPT-4o Vision for initial analysis
  const gptResponse = await callGpt4oVision(allCoreSlugs, textContent, imageUrl);
  const { snapNote, categories: categoriesFromGpt, tags: tagsFromGpt } = gptResponse;

  // 4. Generate text embedding
  const textToEmbed = [textContent, snapNote, ...(tagsFromGpt || [])].filter(Boolean).join(' | ');
  let embeddingVector: number[] = [];

  if (textToEmbed.trim().length > 0) {
      console.log('[Background] Generating text embedding for:', textToEmbed.substring(0,100) + '...');
      const embeddingResponse = await getOpenAiTextEmbedding(textToEmbed);
      embeddingVector = embeddingResponse.vector;
      console.log('[Background] Embedding generated, dimensions:', embeddingVector.length);
  } else {
      console.log('[Background] No text content to embed after combining inputs.');
  }

  // 5. Apply user bias adjustment to categories
  console.log('[Background] Adjusting categories with user bias...');
  const userFrequency = await getUserFrequencyData();
  const categoryPercentages: { [slug: string]: number } = {};

  if (userFrequency.totalAnalyzedPosts > 0) {
    for (const slug in userFrequency.categoryCounts) {
      categoryPercentages[slug] = userFrequency.categoryCounts[slug] / userFrequency.totalAnalyzedPosts;
    }
  }

  const finalCategories = biasAdjustCategories(
    (categoriesFromGpt || []) as CoreSubCategorySlug[],
    categoryPercentages,
    userFrequency.totalAnalyzedPosts
  );
  console.log('[Background] Final categories after bias adjustment:', finalCategories);

  // 6. Finalize tags based on categories
  const finalTags = finalizeTags(tagsFromGpt || [], finalCategories);
  console.log('[Background] Final tags:', finalTags);

  // 7. Generate additional AI insights
  let inSightData: InSightData | null = null;
  let fastTakeData: string | null = null;
  let contentIdeasData: string[] = [];

  // Determine text to use for additional analysis
  let textForAdditionalAnalysis = textContent;
  if (!textContent && snapNote) {
    textForAdditionalAnalysis = snapNote;
    console.log(`[Background] Using snapNote for additional AI analysis: "${snapNote.substring(0,50)}..."`);
  }

  console.log(`[Background] Text for additional analysis - Length: ${textForAdditionalAnalysis.length}, Preview: "${textForAdditionalAnalysis.substring(0,50)}..."`);

  if (textForAdditionalAnalysis) {
    // Generate AI Insight
    try {
      console.log(`[Background] Generating AI insight for post ID: ${rawPostData.id || 'N/A'}`);
      inSightData = await generateAIInsight(textForAdditionalAnalysis);
      console.log(`[Background] AI insight generated successfully for post ID: ${rawPostData.id || 'N/A'}`, inSightData);
    } catch (error) {
      console.error(`[Background] Error generating AI insight for post ID ${rawPostData.id || 'N/A'}:`, error);
    }

    // Generate AI Fast Take
    try {
      console.log(`[Background] Generating AI fast take for post ID: ${rawPostData.id || 'N/A'}`);
      fastTakeData = await generateAIFastTake(textForAdditionalAnalysis);
      console.log(`[Background] AI fast take generated successfully for post ID: ${rawPostData.id || 'N/A'}`, fastTakeData);
    } catch (error) {
      console.error(`[Background] Error generating AI fast take for post ID ${rawPostData.id || 'N/A'}:`, error);
    }

    // Generate AI Content Ideas
    try {
      console.log(`[Background] Generating AI content ideas for post ID: ${rawPostData.id || 'N/A'}`);
      contentIdeasData = await generateAIContentIdeas(textForAdditionalAnalysis);
      console.log(`[Background] AI content ideas generated successfully for post ID: ${rawPostData.id || 'N/A'}`, contentIdeasData);
    } catch (error) {
      console.error(`[Background] Error generating AI content ideas for post ID ${rawPostData.id || 'N/A'}:`, error);
    }
  } else {
    console.log('[Background] Skipping additional AI analysis - no effective text content available.');
  }

  // 8. Assemble the analyzed post data
  const analyzedPostData: AnalyzedPost = {
    ...rawPostData,
    originalPostId: rawPostData.id,
    id: rawPostData.id || `local_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,
    platform: rawPostData.platform,
    permalink: rawPostData.permalink,
    snapNote: snapNote || null,
    categories: finalCategories,
    tags: finalTags,
    embeddingVector: embeddingVector.length > 0 ? embeddingVector : undefined,
    analyzedAt: new Date().toISOString(),
    createdAt: rawPostData.createdAt || new Date().toISOString(),
    // savedAt will be set by the calling function
    inSight: inSightData,
    fastTake: fastTakeData,
    contentIdeas: contentIdeasData.length > 0 ? contentIdeasData : null,
  };

  // Clean up empty media array
  if (analyzedPostData.media && analyzedPostData.media.length === 0) {
      delete analyzedPostData.media;
  }

  console.log('[Background] AI Analysis Pipeline complete for post:', analyzedPostData.id);
  console.log('[Background] Analyzed post data keys:', Object.keys(analyzedPostData));
  console.log('[Background] Analyzed post permalink:', analyzedPostData.permalink);
  return analyzedPostData;
}

// =============================================================================
// MESSAGE LISTENERS
// =============================================================================

/**
 * Main message listener for handling requests from content scripts and popup
 */
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {

  // Handle iframe image fetching requests from LinkedIn content script
  if (message.type === 'FETCH_IFRAME_IMAGES' && message.url && message.platform === 'linkedin') {
    console.log(`[Background] Fetching iframe images from: ${message.url.substring(0, 100)}...`);

    (async () => {
      try {
        // Fetch the iframe content
        const response = await fetch(message.url, {
          credentials: 'omit',
          mode: 'cors'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        console.log(`[Background] Fetched HTML content, length: ${html.length}`);

        // Parse HTML and extract image URLs
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const images: string[] = [];
        const imgElements = doc.querySelectorAll('img');

        imgElements.forEach((img) => {
          // Check both src and data-delayed-url attributes
          const src = img.src || img.getAttribute('data-delayed-url') || img.getAttribute('data-src');
          if (src && src.includes('media.licdn.com')) {
            // Convert relative URLs to absolute
            const absoluteUrl = src.startsWith('http') ? src : new URL(src, message.url).href;
            if (!images.includes(absoluteUrl)) {
              images.push(absoluteUrl);
            }
          }
        });

        console.log(`[Background] Extracted ${images.length} images from iframe`);
        images.forEach((url, index) => {
          console.log(`[Background] Image ${index + 1}: ${url.substring(0, 80)}...`);
        });

        sendResponse({
          success: true,
          images: images
        });

      } catch (error) {
        console.error(`[Background] Error fetching iframe images:`, error);
        sendResponse({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    })();

    return true; // Indicates async response
  }

  // Handle post save requests from content scripts
  if (message.action === 'SAVE_POST_REQUEST' && message.data) {
    const rawPostData = message.data as Post;
    console.log(`[Background] Processing SAVE_POST_REQUEST for post ID: ${rawPostData.id}`);
    console.log(`[Background] Raw post data received:`, {
      id: rawPostData.id,
      platform: rawPostData.platform,
      permalink: rawPostData.permalink,
      author: rawPostData.author,
      content: rawPostData.content?.substring(0, 50),
      hasPermalink: !!rawPostData.permalink,
      allKeys: Object.keys(rawPostData)
    });

    (async () => {
      try {
        // Respond immediately to content script
        sendResponse({
          status: 'success',
          message: 'Post save initiated. AI analysis and cloud sync in progress.'
        });

        // Process AI analysis FIRST, then save and sync
        processPostWithAIAndSync(rawPostData).catch(error => {
          console.error(`[Background] Complete processing error for post ${rawPostData.id}:`, error);
        });

      } catch (error) {
        console.error(`[Background] Error during initial save for post ID ${rawPostData.id}:`, error);
        sendResponse({
          status: 'error',
          message: `Failed to save post: ${error instanceof Error ? error.message : String(error)}`
        });
      }
    })();

    return true; // Indicates async response
  }
  // Handle manual cloud sync requests from dashboard
  else if (message.action === 'SAVE_POST_CLOUD' && message.data && message.token) {
    console.log(`[Background] Processing SAVE_POST_CLOUD for post ID: ${message.data.id}`);

    (async () => {
      try {
        const postData = message.data;
        const token = message.token;

        console.log(`[Background] Starting manual cloud sync for post ID: ${postData.id}`);

        // Use the universal cloud sync service
        const { syncToCloud } = await import('../services/cloudSyncService');
        const result = await syncToCloud(postData, token);

        if (result.success) {
          console.log(`[Background] Successfully synced post ${postData.id} to cloud`);
          sendResponse({ status: 'success', message: 'Post synced to cloud successfully' });
        } else {
          console.error(`[Background] Failed to sync post ${postData.id} to cloud:`, result.error);
          sendResponse({ status: 'error', message: result.error || 'Cloud sync failed' });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[Background] Error syncing post to cloud:`, errorMessage);
        sendResponse({ status: 'error', message: errorMessage });
      }
    })();

    return true; // Indicates async response
  }

  // Handle authentication token requests
  else if (message.type === 'GET_AUTH_TOKEN' || message.action === 'GET_AUTH_TOKEN') {
    console.log('[Background] Processing GET_AUTH_TOKEN request');
    (async () => {
      try {
        const result = await chrome.storage.local.get('authToken');
        console.log('[Background] Auth token retrieved:', result.authToken ? 'present' : 'absent');
        sendResponse({ token: result.authToken || null });
      } catch (e) {
        console.error('[Background] Error getting auth token:', e);
        sendResponse({ token: null, error: e instanceof Error ? e.message : String(e) });
      }
    })();
    return true;
  }

  // Handle image fetching requests
  // Note: Primary image fetching is handled by setupImageFetchingHandler()
  // This is a fallback handler for any missed requests
  else if (message.action === 'FETCH_IMAGE') {
    console.log(`[Background] Received FETCH_IMAGE request for URL: ${message.url}`);
    // The imageHandler should have already processed this, but we return true
    // to indicate we're handling it asynchronously as a fallback
    return true;
  }
  // Handle dashboard opening requests
  else if (message.action === 'openDashboard') {
    chrome.tabs.create({ url: 'dashboard.html' });
    sendResponse({ status: 'success', message: 'Dashboard opened.' });
    return false;
  }

  // Handle cloud post deletion requests
  else if (message.action === 'DELETE_POST_FROM_CLOUD' && message.postId && message.token) {
    const postId = message.postId as string;
    const token = message.token as string;
    console.log(`[Background] Processing DELETE_POST_FROM_CLOUD for postId: ${postId}`);

    (async () => {
      if (!token) {
        console.error(`[Background] Missing token for cloud deletion of post ${postId}`);
        sendResponse({ status: 'error', message: 'Authentication token not provided for cloud deletion.' });
        return;
      }

      try {
        const apiUrl = `${BACKEND_URL}/api/posts/${postId}`;
        console.log(`[Background] Attempting to delete post ${postId} from cloud: ${apiUrl}`);

        const response = await fetch(apiUrl, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
        });

        if (response.ok) {
          console.log(`[Background] Successfully deleted post ${postId} from cloud`);

          // Clean up local storage
          try {
            await deletePost(postId);
            console.log(`[Background] Successfully cleaned up post ${postId} from local storage`);
          } catch (localDeleteError) {
            console.warn(`[Background] Error cleaning up post ${postId} from local storage:`, localDeleteError);
          }

          sendResponse({
            status: 'success',
            message: 'Post deleted successfully from cloud and local storage.'
          });
        } else {
          const errorData = await response.json().catch(() => ({
            message: `Cloud deletion failed with status: ${response.status}`
          }));
          console.error(`[Background] Failed to delete post ${postId} from cloud:`, errorData);
          sendResponse({
            status: 'error',
            message: `Failed to delete post from cloud: ${errorData.message || response.statusText}`,
            details: errorData
          });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[Background] Exception during cloud deletion for post ${postId}:`, errorMessage);
        sendResponse({ status: 'error', message: `Error deleting post: ${errorMessage}` });
      }
    })();
    return true;
  }

  // Handle local post deletion requests
  else if (message.action === 'DELETE_POST_LOCALLY' && message.postId) {
    const postId = message.postId as string;
    console.log(`[Background] Processing DELETE_POST_LOCALLY for postId: ${postId}`);

    (async () => {
      try {
        await deletePost(postId);
        console.log(`[Background] Successfully deleted post ${postId} from local storage`);
        sendResponse({ status: 'success', message: 'Post deleted successfully from local storage.' });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[Background] Error deleting post ${postId} locally:`, errorMessage);
        sendResponse({ status: 'error', message: `Error deleting post locally: ${errorMessage}` });
      }
    })();
    return true;
  }

  // Fallback for unhandled messages
  return false;
});

// =============================================================================
// PORT CONNECTION LISTENERS
// =============================================================================

/**
 * Handle port connections from content scripts (primarily Instagram)
 */
chrome.runtime.onConnect.addListener((port) => {

  port.onMessage.addListener((message) => {

    // Handle Instagram save requests via port connection
    if (port.name === 'instagram-save' && message.action === 'SAVE_POST_REQUEST' && message.data) {
      const rawPostData = message.data;
      console.log(`[Background] Processing Instagram save request for post ID: ${rawPostData.id}`);

      (async () => {
        try {
          // Verify that the post has valid media
          if (!rawPostData.media || rawPostData.media.length === 0) {
            console.error(`[Background] Instagram post ${rawPostData.id} has no media`);
            port.postMessage({
              status: 'error',
              message: 'Post has no images or videos. Please try again.',
              errorType: 'NO_MEDIA'
            });
            return;
          }

          // Verify that media URLs are valid
          const validMediaItems = rawPostData.media.filter((item: MediaItem) =>
            item && item.url && typeof item.url === 'string' && item.url.trim() !== ''
          );

          if (validMediaItems.length === 0) {
            console.error(`[Background] Instagram post ${rawPostData.id} has no valid media URLs`);
            port.postMessage({
              status: 'error',
              message: 'Post has no valid image URLs. Please try again.',
              errorType: 'INVALID_MEDIA_URLS'
            });
            return;
          }

          console.log(`[Background] Instagram post ${rawPostData.id} has ${validMediaItems.length} valid media items`);

          // Use the full post data structure ensuring ALL required fields for dashboard are present
          const initialPostToSave = {
            id: rawPostData.id,
            platform: 'Instagram' as Platform,
            author: rawPostData.author,
            authorName: rawPostData.authorName,
            authorUrl: rawPostData.authorUrl,
            authorAvatar: rawPostData.authorAvatar,
            postUrl: rawPostData.permalink || `https://www.instagram.com/p/${rawPostData.id}/`,
            permalink: rawPostData.permalink || `https://www.instagram.com/p/${rawPostData.id}/`,
            textContent: rawPostData.textContent || rawPostData.content || '',
            content: rawPostData.content || rawPostData.textContent || '',
            media: validMediaItems, // Use only valid media items
            createdAt: rawPostData.createdAt || new Date().toISOString(),
            savedAt: new Date().toISOString(),
            stats: rawPostData.stats || {},
            categories: rawPostData.categories || [],
            tags: rawPostData.tags || [],
          };

          // Try to fetch at least one image to verify it's accessible
          let imageVerified = false;
          let verificationError = null;

          try {
            if (validMediaItems.length > 0) {
              const firstMediaItem = validMediaItems[0];
              console.log(`[Background] Verifying first media item URL: ${firstMediaItem.url.substring(0, 50)}...`);

              // Check if it's a base64 data URL (from Instagram content script)
              if (firstMediaItem.url.startsWith('data:image/')) {
                console.log(`[Background] Base64 data URL detected for Instagram post ${rawPostData.id} - skipping HTTP verification`);
                imageVerified = true;
              } else {
                // Attempt to fetch the image for regular URLs
                const response = await fetch(firstMediaItem.url, {
                  method: 'HEAD', // Just check headers, don't download the full image
                  cache: 'no-cache',
                  headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://www.instagram.com/',
                    'Origin': 'https://www.instagram.com'
                  }
                });

                if (response.ok) {
                  console.log(`[Background] Successfully verified media URL for Instagram post ${rawPostData.id}`);
                  imageVerified = true;
                } else {
                  console.warn(`[Background] Media URL verification failed with status: ${response.status}`);
                  verificationError = `Image verification failed with status: ${response.status}`;
                }
              }
            }
          } catch (verifyError) {
            console.warn(`[Background] Error verifying media URL: ${verifyError instanceof Error ? verifyError.message : String(verifyError)}`);
            verificationError = verifyError instanceof Error ? verifyError.message : String(verifyError);
            // Continue with save even if verification fails - we'll try again during actual save
          }

          // Save post to local storage
          await savePost(initialPostToSave);
          console.log(`[Background] Initial save successful for Instagram post: ${initialPostToSave.id}`);

          // Send appropriate response based on verification
          if (imageVerified) {
            port.postMessage({
              status: 'success',
              message: 'Post saved successfully with verified images'
            });
          } else {
            port.postMessage({
              status: 'success',
              message: 'Post saved, but image verification failed. Images may not display correctly.',
              warning: verificationError
            });
          }

          // Process post in background for AI analysis using unified flow
          processPostWithAIAndSync(rawPostData).catch(error => {
            console.error(`[Background] Error in processPostWithAIAndSync for Instagram post ${rawPostData.id}:`, error);
          });

        } catch (error) {
          console.error(`[Background] Error saving Instagram post ID ${rawPostData.id}:`, error);
          port.postMessage({
            status: 'error',
            message: `Failed to save post: ${error instanceof Error ? error.message : String(error)}`,
            errorType: 'SAVE_ERROR'
          });
        }
      })();
    } else {
      port.postMessage({ status: 'error', message: 'Unrecognized message format or action' });
    }
  });

  port.onDisconnect.addListener(() => {
    if (chrome.runtime.lastError) {
      console.error(`[Background] Port ${port.name} disconnected with error:`, chrome.runtime.lastError);
    }
  });
});

// =============================================================================
// CONTEXT MENU HANDLERS
// =============================================================================

// Import locale utilities
import { SUPPORTED_LOCALES, isSupportedLocale, getBrowserLocale } from '../utils/translation';

/**
 * Get current locale from storage
 */
async function getCurrentLocale(): Promise<string> {
  return new Promise<string>((resolve) => {
    chrome.storage.sync.get({ locale: 'en' }, (result: { locale: string }) => {
      const storedLocale = result.locale;
      if (storedLocale && isSupportedLocale(storedLocale)) {
        resolve(storedLocale);
      } else {
        resolve('en'); // Default to English
      }
    });
  });
}

/**
 * Handle context menu clicks
 */
chrome.contextMenus.onClicked.addListener((info, tab) => {
  console.log('[Background] Context menu clicked:', info.menuItemId, 'on tab:', tab?.id);

  if (!tab?.id) {
    console.error('[Background] No tab ID available for context menu action');
    return;
  }

  // Handle different context menu actions
  switch (info.menuItemId) {
    case 'add-text-to-notely':
      handleAddTextToNotely(info, tab);
      break;
    case 'add-image-to-notely':
      handleAddImageToNotely(info, tab);
      break;
    case 'add-page-to-notely':
      handleAddPageToNotely(info, tab);
      break;
  }
});

// =============================================================================
// EXTENSION LIFECYCLE EVENTS
// =============================================================================

chrome.runtime.onInstalled.addListener(() => {
  getCurrentLocale().then(() => {
    chrome.storage.sync.get({ locale: 'en' }, (result: { locale: string }) => {
      if (!result.locale) {
        const browserLocale = getBrowserLocale();
        chrome.storage.sync.set({ locale: browserLocale }, () => {
          console.log(`[Background] Locale set to ${browserLocale}`);
        });
      }
    });
  });

  // Create context menus for web content saving
  createContextMenus();
});

chrome.runtime.onStartup.addListener(() => {
  console.log('[Background] Extension startup - creating context menus');
  createContextMenus();
});

/**
 * Handle adding selected text to Notely
 */
async function handleAddTextToNotely(info: chrome.contextMenus.OnClickData, tab: chrome.tabs.Tab) {
  try {

    // Inject web content script if needed
    await injectWebContentScript(tab.id!);

    // Send message to content script to extract and save selected text
    chrome.tabs.sendMessage(tab.id!, {
      action: 'EXTRACT_AND_SAVE_SELECTION',
      data: {
        selectionText: info.selectionText,
        pageUrl: info.pageUrl,
        frameUrl: info.frameUrl
      }
    });

  } catch (error) {
    console.error('[Background] Error handling add text to Notely:', error);
  }
}

/**
 * Handle adding image to Notely
 */
async function handleAddImageToNotely(info: chrome.contextMenus.OnClickData, tab: chrome.tabs.Tab) {
  try {
    console.log('[Background] Adding image to Notely');

    // Inject web content script if needed
    await injectWebContentScript(tab.id!);

    // Send message to content script to extract and save image
    chrome.tabs.sendMessage(tab.id!, {
      action: 'EXTRACT_AND_SAVE_IMAGE',
      data: {
        srcUrl: info.srcUrl,
        pageUrl: info.pageUrl,
        frameUrl: info.frameUrl
      }
    });

  } catch (error) {
    console.error('[Background] Error handling add image to Notely:', error);
  }
}

/**
 * Handle adding page to Notely
 */
async function handleAddPageToNotely(info: chrome.contextMenus.OnClickData, tab: chrome.tabs.Tab) {
  try {
    console.log('[Background] Adding page to Notely');

    // Inject web content script if needed
    await injectWebContentScript(tab.id!);

    // Send message to content script to extract and save page
    chrome.tabs.sendMessage(tab.id!, {
      action: 'EXTRACT_AND_SAVE_PAGE',
      data: {
        pageUrl: info.pageUrl,
        frameUrl: info.frameUrl
      }
    });

  } catch (error) {
    console.error('[Background] Error handling add page to Notely:', error);
  }
}

/**
 * Inject web content script if not already present
 */
async function injectWebContentScript(tabId: number): Promise<void> {
  try {
    // Try to ping the content script first
    const response = await chrome.tabs.sendMessage(tabId, { action: 'PING' });
    if (response?.status === 'pong') {
      console.log('[Background] Web content script already active');
      return;
    }
  } catch (error) {
    // Content script not present, need to inject
    console.log('[Background] Injecting web content script');
  }

  try {
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['assets/content/web.js']
    });
    console.log('[Background] Web content script injected successfully');
  } catch (error) {
    console.error('[Background] Failed to inject web content script:', error);
    throw error;
  }
}

/**
 * Handle extension icon clicks
 */
chrome.action.onClicked.addListener(() => {
  console.log("[Background] Extension icon clicked - opening dashboard");
  chrome.tabs.create({ url: chrome.runtime.getURL("dashboard.html") });
});
