import React, { useState, useEffect } from 'react';
import '../index.css'; // Import Tailwind CSS
import { getSavedPosts, updatePostDetails, getAllCategories, getAllTags, saveAllTags, saveAllCategories } from '../storage'; // Add saveAllTags and saveAllCategories
import { Post } from '../types';
import MultiItemInput from '../components/MultiItemInput'; // <-- Import the new component

// Placeholder for loading state
const LoadingSpinner: React.FC = () => (
  <div className="flex justify-center items-center h-screen">
    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Placeholder for error message
const ErrorMessage: React.FC<{ message: string }> = ({ message }) => (
  <div className="p-4 text-center text-red-600 bg-red-100 border border-red-300 rounded-md">
    <p>Error: {message}</p>
    <div className="mt-2 text-sm text-gray-600">
      Debug Info:
      <pre className="mt-1 bg-gray-50 p-2 rounded">
        URL: {window.location.href}
        {'\n'}Search: {window.location.search}
      </pre>
    </div>
  </div>
);

function PostDetail() {
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [postId, setPostId] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [hasChanges, setHasChanges] = useState(false);

  const [allCategories, setAllCategories] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);

  // 1. Get Post ID from URL query parameter
  useEffect(() => {
    console.log("[PostDetail] Component mounted");
    console.log("[PostDetail] Current URL:", window.location.href);
    console.log("[PostDetail] Search params:", window.location.search);
    
    const params = new URLSearchParams(window.location.search);
    const id = params.get('id');
    console.log("[PostDetail] Extracted ID:", id);
    
    if (id) {
      setPostId(id);
      setDebugInfo(prev => prev + `\nFound ID in URL: ${id}`);
    } else {
      const error = 'No Post ID provided in the URL.';
      setError(error);
      setDebugInfo(prev => prev + '\nNo ID found in URL');
      setLoading(false);
    }
  }, []);

  // 2. Fetch post data and master lists once Post ID is available
  useEffect(() => {
    if (!postId) {
      console.log("[PostDetail] No postId available yet");
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      setDebugInfo(prev => prev + `\nFetching data for post ID: ${postId}`);
      
      try {
        console.log("[PostDetail] Fetching saved posts");
        const allPosts = await getSavedPosts();
        console.log("[PostDetail] All posts:", allPosts);
        
        const foundPost = allPosts.find(p => p.id === postId);
        console.log("[PostDetail] Found post:", foundPost);

        if (foundPost) {
          setPost(foundPost);
          setDebugInfo(prev => prev + `\nFound post: ${JSON.stringify(foundPost)}`);
        } else {
          const error = `Post with ID ${postId} not found.`;
          console.error("[PostDetail]", error);
          setError(error);
          setDebugInfo(prev => prev + `\nPost not found in storage`);
        }

        // Fetch master lists for category/tag suggestions
        const categories = await getAllCategories();
        const tags = await getAllTags();
        console.log("[PostDetail] Categories:", categories);
        console.log("[PostDetail] Tags:", tags);
        
        setAllCategories(categories);
        setAllTags(tags);
        setDebugInfo(prev => prev + `\nLoaded ${categories.length} categories and ${tags.length} tags`);

      } catch (err: any) {
        const errorMsg = err.message || 'Failed to load post details.';
        console.error("[PostDetail] Error:", err);
        setError(errorMsg);
        setDebugInfo(prev => prev + `\nError: ${errorMsg}`);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [postId]);

  // --- Event Handlers --- 
  const handleCategoryChange = async (newCategories: string[]) => {
    if (!post) return;
    const validCategories = newCategories.slice(0, 3);
    console.log("[PostDetail] Categories updated:", validCategories);
    
    // Update local state
    setPost(prev => ({
      ...prev!,
      categories: validCategories
    }));
    setHasChanges(true);

    // Update master list
    try {
      const existingCategories = await getAllCategories();
      const updatedCategories = [...new Set([...existingCategories, ...validCategories])];
      await saveAllCategories(updatedCategories);
      setAllCategories(updatedCategories);
    } catch (err) {
      console.error("[PostDetail] Error updating master categories:", err);
    }
  };

  const handleTagChange = async (newTags: string[]) => {
    if (!post) return;
    const validTags = newTags.slice(0, 5);
    console.log("[PostDetail] Tags updated:", validTags);
    
    // Update local state
    setPost(prev => ({
      ...prev!,
      tags: validTags
    }));
    setHasChanges(true);

    // Update master list
    try {
      const existingTags = await getAllTags();
      const updatedTags = [...new Set([...existingTags, ...validTags])];
      await saveAllTags(updatedTags);
      setAllTags(updatedTags);
    } catch (err) {
      console.error("[PostDetail] Error updating master tags:", err);
    }
  };
  
  const handleSaveChanges = async () => {
    if (!post || !postId || !hasChanges) return;
    
    setLoading(true);
    try {
      console.log("[PostDetail] Saving changes:", {
        categories: post.categories,
        tags: post.tags
      });

      await updatePostDetails(postId, {
        categories: post.categories || [],
        tags: post.tags || []
      });

      setHasChanges(false);
      console.log("[PostDetail] Changes saved successfully");
      
      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow';
      successMessage.textContent = 'Changes saved successfully!';
      document.body.appendChild(successMessage);
      setTimeout(() => successMessage.remove(), 3000);

    } catch (err) {
      console.error("[PostDetail] Error saving changes:", err);
      setError("Failed to save changes.");
    } finally {
      setLoading(false);
    }
  };

  // --- Render Logic ---

  if (loading) {
    return (
      <div>
        <LoadingSpinner />
        <div className="text-center mt-4 text-gray-600">
          <p>Loading post details...</p>
          <pre className="mt-2 text-sm bg-gray-50 p-2 rounded">{debugInfo}</pre>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <ErrorMessage message={error} />
        <pre className="mt-4 text-sm bg-gray-50 p-2 rounded mx-4">{debugInfo}</pre>
      </div>
    );
  }

  if (!post) {
    return (
      <div>
        <ErrorMessage message="Post data could not be loaded." />
        <pre className="mt-4 text-sm bg-gray-50 p-2 rounded mx-4">{debugInfo}</pre>
      </div>
    );
  }

  // --- Main Render --- 
  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <h1 className="text-2xl font-bold mb-4">Edit Post Details</h1>
      
      {/* Display basic post info (optional, could show a mini-card) */} 
      <div className="bg-white p-4 rounded-lg shadow mb-6 border border-gray-200">
          <p className="text-sm text-gray-500">Post ID: {post.id}</p>
          <p className="text-sm text-gray-500">Platform: {post.platform}</p>
          {post.content && <p className="mt-2 text-gray-700 italic">"{post.content.substring(0, 100)}..."</p>}
          {/* Add more identifying info if needed */} 
      </div>

      {/* Category Editor */} 
      <div className="mb-6">
        <MultiItemInput
          label="Categories (Max 3)"
          items={post.categories || []}
          allItems={allCategories}
          maxItems={3}
          placeholder="Add a category..."
          onChange={handleCategoryChange}
        />
      </div>

      {/* Tag Editor */} 
      <div className="mb-6">
        <MultiItemInput
          label="Tags (Max 5)"
          items={post.tags || []}
          allItems={allTags}
          maxItems={5}
          placeholder="Add a tag..."
          onChange={handleTagChange}
        />
      </div>

      {/* Save Button */} 
       <div className="mt-8 flex justify-end">
            <button 
                onClick={handleSaveChanges}
                disabled={loading || !hasChanges}
                className={`px-6 py-2 rounded-md text-white font-medium transition-colors 
                    ${loading ? 'bg-gray-400 cursor-not-allowed' : 
                        !hasChanges ? 'bg-gray-300 cursor-not-allowed' :
                        'bg-blue-600 hover:bg-blue-700'}`}
            >
                {loading ? 'Saving...' : 'Save Changes'}
            </button>
        </div>

    </div>
  );
}

export default PostDetail; 