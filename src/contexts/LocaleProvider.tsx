import React, { useEffect, useState } from 'react';
import { LocaleContext } from './LocaleContext';
import { getCurrentLocaleAsync, SupportedLocale, isSupportedLocale, getBrowserLocale } from '../utils/translation';

export const LocaleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [locale, setLocaleState] = useState<SupportedLocale>('en');

  useEffect(() => {
    // Initialize locale from storage or browser
    getCurrentLocaleAsync().then((storedLocale) => {
      setLocaleState(storedLocale);
    }).catch(() => {
      // Fallback to browser locale if storage fails
      setLocaleState(getBrowserLocale());
    });
  }, []);

  const setLocale = (newLocale: string) => {
    const validLocale = isSupportedLocale(newLocale) ? newLocale : 'en';
    setLocaleState(validLocale);

    // Save to Chrome storage
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.sync.set({ locale: validLocale });
    }
  };

  return (
    <LocaleContext.Provider value={{ locale, setLocale }}>
      {children}
    </LocaleContext.Provider>
  );
};
