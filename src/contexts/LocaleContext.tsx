import React, { createContext, useEffect, useState } from 'react';
import { LocaleContextType } from './LocaleContext.types';

export const LocaleContext = createContext<LocaleContextType | undefined>(undefined);

// Helper to check if we're in a Chrome extension environment
const isChromeExtension = (): boolean => {
  return typeof chrome !== 'undefined' && !!chrome.storage && !!chrome.storage.sync;
};

export const LocaleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [locale, setLocaleState] = useState('en');

  // Load saved locale from storage on mount
  useEffect(() => {
    if (isChromeExtension()) {
      chrome.storage.sync.get({ locale: 'en' }, (result: { locale: string }) => {
        setLocaleState(result.locale);
      });
    } else {
      // For web context, use localStorage instead
      const savedLocale = localStorage.getItem('locale') || 'en';
      setLocaleState(savedLocale);
    }
  }, []);

  const setLocale = (newLocale: string) => {
    setLocaleState(newLocale);
    if (isChromeExtension()) {
      chrome.storage.sync.set({ locale: newLocale });
    } else {
      // For web context, use localStorage
      localStorage.setItem('locale', newLocale);
    }
  };

  return (
    <LocaleContext.Provider value={{ locale, setLocale }}>
      {children}
    </LocaleContext.Provider>
  );
};
