<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImageSwiper Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        // Mock ImageSwiper component for testing
        const ImageSwiper = ({ images, className, aspectRatio, onImageClick, showControls = true, showDots = true }) => {
            const [currentIndex, setCurrentIndex] = React.useState(0);
            
            if (!images || images.length === 0) return null;
            
            if (images.length === 1) {
                return (
                    <div className={`relative overflow-hidden rounded-xl ${className}`}>
                        <img
                            src={images[0]}
                            alt="Single image"
                            className="w-full h-full object-cover"
                            onClick={() => onImageClick?.(0)}
                        />
                    </div>
                );
            }
            
            return (
                <div className={`relative overflow-hidden rounded-xl ${className}`}>
                    <div className="relative w-full aspect-video">
                        <img
                            src={images[currentIndex]}
                            alt={`Image ${currentIndex + 1}`}
                            className="w-full h-full object-cover"
                            onClick={() => onImageClick?.(currentIndex)}
                        />
                    </div>
                    
                    {showControls && images.length > 1 && (
                        <>
                            <button
                                className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70"
                                onClick={() => setCurrentIndex(prev => prev > 0 ? prev - 1 : images.length - 1)}
                            >
                                ←
                            </button>
                            <button
                                className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70"
                                onClick={() => setCurrentIndex(prev => prev < images.length - 1 ? prev + 1 : 0)}
                            >
                                →
                            </button>
                        </>
                    )}
                    
                    {showDots && images.length > 1 && (
                        <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1">
                            {images.map((_, index) => (
                                <button
                                    key={index}
                                    className={`h-2 w-2 rounded-full transition-all ${
                                        index === currentIndex ? 'bg-white' : 'bg-white/50 hover:bg-white/70'
                                    }`}
                                    onClick={() => setCurrentIndex(index)}
                                />
                            ))}
                        </div>
                    )}
                    
                    {images.length > 1 && (
                        <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded-md">
                            {currentIndex + 1} / {images.length}
                        </div>
                    )}
                </div>
            );
        };
        
        const TestApp = () => {
            const singleImage = ['https://picsum.photos/600/400?random=1'];
            const multipleImages = [
                'https://picsum.photos/600/400?random=1',
                'https://picsum.photos/600/400?random=2',
                'https://picsum.photos/600/400?random=3',
                'https://picsum.photos/600/400?random=4'
            ];
            
            return (
                <div className="p-8 bg-gray-50 min-h-screen">
                    <h1 className="text-3xl font-bold text-center mb-8">ImageSwiper Test</h1>
                    
                    <div className="test-container">
                        <h2 className="text-xl font-semibold mb-4">Single Image Test</h2>
                        <ImageSwiper
                            images={singleImage}
                            className="shadow-lg"
                            onImageClick={(index) => alert(`Clicked image ${index + 1}`)}
                        />
                    </div>
                    
                    <div className="test-container">
                        <h2 className="text-xl font-semibold mb-4">Multiple Images Test (4 images)</h2>
                        <ImageSwiper
                            images={multipleImages}
                            className="shadow-lg"
                            onImageClick={(index) => alert(`Clicked image ${index + 1}`)}
                        />
                    </div>
                    
                    <div className="test-container">
                        <h2 className="text-xl font-semibold mb-4">Square Aspect Ratio Test</h2>
                        <ImageSwiper
                            images={multipleImages}
                            className="shadow-lg"
                            aspectRatio="square"
                            onImageClick={(index) => alert(`Clicked image ${index + 1}`)}
                        />
                    </div>
                </div>
            );
        };
        
        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>
