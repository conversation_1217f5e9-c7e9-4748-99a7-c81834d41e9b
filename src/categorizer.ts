import {
  CORE_CATEGORIES,
  CoreSubCategorySlug,
  MAX_CATEGORIES,
  MAX_TAGS,
  MIN_POSTS_FOR_BIAS,
  BIAS_THRESHOLD_PERCENTAGE,
  CATEGORY_SIMILARITY_THRESHOLD
} from './config/constants';

/**
 * Flattens the CORE_CATEGORIES into a single array of all sub-category slugs.
 */
export function getAllCoreSubCategories(): CoreSubCategorySlug[] {
  return Object.values(CORE_CATEGORIES).flat() as CoreSubCategorySlug[];
}

interface UserCategoryFrequency {
  // Example: { "world": 0.7, "technology": 0.4 } (fraction of posts with this category)
  // Keys are CoreSubCategorySlug or general strings that are then matched against slugs.
  [categorySlug: string]: number; 
}

/**
 * Adjusts a list of proposed categories based on user frequency bias.
 * Ensures the final list does not exceed MAX_CATEGORIES.
 * Rules:
 * - User's highly preferred categories (meeting BIAS_THRESHOLD_PERCENTAGE after MIN_POSTS_FOR_BIAS) are added first.
 * - Then, <PERSON><PERSON>'s validated suggestions are added if space permits, up to MAX_CATEGORIES.
 */
export function biasAdjustCategories(
  gptSuggestedCategories: CoreSubCategorySlug[], 
  userCategoryFrequency: UserCategoryFrequency,
  totalUserPosts: number
): CoreSubCategorySlug[] {
  const finalCategories = new Set<CoreSubCategorySlug>();

  if (totalUserPosts >= MIN_POSTS_FOR_BIAS) {
    const allCoreSlugs = getAllCoreSubCategories(); // Consider passing this in if called frequently
    // Sort preferred categories by frequency to add the most preferred ones first if many exceed threshold
    const preferredUserSlugs = allCoreSlugs
      .filter(slug => userCategoryFrequency[slug] && userCategoryFrequency[slug] >= BIAS_THRESHOLD_PERCENTAGE)
      .sort((a, b) => (userCategoryFrequency[b] || 0) - (userCategoryFrequency[a] || 0));

    for (const slug of preferredUserSlugs) {
      if (finalCategories.size < MAX_CATEGORIES) {
        finalCategories.add(slug);
      }
    }
  }
  
  // Add GPT's suggestions if there's still space
  for (const gptCategory of gptSuggestedCategories) {
    if (finalCategories.size < MAX_CATEGORIES && !finalCategories.has(gptCategory)) {
      finalCategories.add(gptCategory);
    }
  }

  const result = Array.from(finalCategories);
  return result;
}

/**
 * Validates and normalizes a category string against CORE_CATEGORIES sub-categories.
 * Uses Levenshtein distance for fuzzy matching if an exact match isn't found.
 * Rules:
 * - Input is normalized (lower-case, trimmed).
 * - If an exact match to a CORE_CATEGORIES sub-category slug is found, it's returned.
 * - Otherwise, if a fuzzy match (similarity ≥ CATEGORY_SIMILARITY_THRESHOLD) to a slug is found, that slug is returned.
 * - If no good match, null is returned (indicating it's not a core category).
 */
export function validateAndMapToCoreCategory(
  proposedCategory: string,
  allCoreSlugs: CoreSubCategorySlug[] // Pass in to avoid re-calculating frequently
): CoreSubCategorySlug | null {
  if (!proposedCategory || typeof proposedCategory !== 'string') {
    return null;
  }
  const normalizedProposed = proposedCategory.toLowerCase().trim();
  if (!normalizedProposed) {
    return null;
  }

  // Check for exact match first
  if ((allCoreSlugs as string[]).includes(normalizedProposed)) {
    return normalizedProposed as CoreSubCategorySlug;
  }

  // Fuzzy matching if no exact match
  let bestMatch: CoreSubCategorySlug | null = null;
  let highestSimilarity = 0;

  for (const slug of allCoreSlugs) {
    const distance = levenshteinDistance(normalizedProposed, slug);
    const maxLength = Math.max(normalizedProposed.length, slug.length);
    if (maxLength === 0) continue; // Avoid division by zero for empty strings
    const similarity = 1 - (distance / maxLength);

    if (similarity > highestSimilarity) {
      highestSimilarity = similarity;
      bestMatch = slug;
    }
  }

  if (bestMatch && highestSimilarity >= CATEGORY_SIMILARITY_THRESHOLD) {
    return bestMatch;
  }

  return null;
}

/**
 * Cleans and validates tags received from GPT-4o.
 * Ensures tags are unique and don't overlap with chosen categories.
 */
export function finalizeTags(gptTags: string[], chosenCategories: CoreSubCategorySlug[]): string[] {
    if (!Array.isArray(gptTags)) return [];
    
    const lowerCaseCategories = chosenCategories.map(c => c.toLowerCase());
    
    const uniqueValidTags = gptTags
        .map(tag => typeof tag === 'string' ? tag.toLowerCase().trim() : '')
        .filter(tag => tag.length > 0 && !lowerCaseCategories.includes(tag))
        // Further check: ensure tag is not a substring of any category and vice-versa (simple check)
        .filter(tag => {
            return !lowerCaseCategories.some(cat => cat.includes(tag) || tag.includes(cat));
        });

    return [...new Set(uniqueValidTags)].slice(0, MAX_TAGS); // Use imported MAX_TAGS
}

// Levenshtein Distance function
function levenshteinDistance(a: string, b: string): number {
  const an = a ? a.length : 0;
  const bn = b ? b.length : 0;
  if (an === 0) return bn;
  if (bn === 0) return an;
  const matrix = Array(an + 1);
  for (let i = 0; i <= an; i++) {
    matrix[i] = Array(bn + 1);
    matrix[i][0] = i;
  }
  for (let j = 0; j <= bn; j++) matrix[0][j] = j;
  for (let i = 1; i <= an; i++) {
    for (let j = 1; j <= bn; j++) {
      const cost = a[i - 1] === b[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }
  return matrix[an][bn];
}

// TODO: Add unit tests for getAllCoreSubCategories
// TODO: Add unit tests for validateAndMapToCoreCategory
// TODO: Add unit tests for biasAdjustCategories
// TODO: Add unit tests for finalizeTags
// TODO: Add unit tests for levenshteinDistance (if kept public or complex enough)