/* Notely Dark Mode-First Theme */

/* Use system fonts for better performance and compatibility */
/* Removed external Google Fonts imports to fix MIME type issues in Chrome extensions */

/* Root variables for theme switching */
:root {
  /* Dark theme (default) - Updated to match notely.social */
  --notely-bg: #0B0B0B; /* User's preferred background */
  --notely-surface: rgba(26, 26, 26, 0.8);
  --notely-card: rgba(37, 37, 37, 0.6);
  --notely-border: rgba(51, 51, 51, 0.3);
  --notely-text-primary: #ffffff;
  --notely-text-secondary: #b3b3b3;
  --notely-text-muted: #666666;

  /* Brand colors matching user preferences */
  --notely-pink: #FF43C8; /* User's brand pink */
  --notely-purple: #7A5CFF; /* User's brand purple */

  /* Accent colors */
  --notely-coral: #ff6b6b;
  --notely-mint: #51cf66;
  --notely-sky: #74c0fc;
  --notely-lavender: #b197fc;
  --notely-peach: #ffa8a8;
  --notely-sage: #8ce99a;

  /* Primary accent colors for buttons and highlights */
  --notely-accent: var(--notely-purple);
  --notely-accent-secondary: var(--notely-pink);
  --notely-accent-light: #a5d4fd;

  /* Gradient colors for notely.social design */
  --notely-gradient-primary: linear-gradient(135deg, var(--notely-purple), var(--notely-pink));
  --notely-gradient-secondary: linear-gradient(135deg, #8b5cf6, #6366f1);
  --notely-gradient-bg: linear-gradient(135deg, #0B0B0B 0%, #1a1a1a 50%, #0B0B0B 100%);
  --notely-gradient-surface: linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(37, 37, 37, 0.7) 100%);

  /* Glassmorphism effects */
  --notely-glass-bg: rgba(255, 255, 255, 0.05);
  --notely-glass-border: rgba(255, 255, 255, 0.1);
  --notely-glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Spacing */
  --notely-space-xs: 8px;
  --notely-space-sm: 12px;
  --notely-space-md: 16px;
  --notely-space-lg: 24px;
  --notely-space-xl: 32px;
  --notely-space-2xl: 48px;

  /* Compact spacing for smaller cards */
  --notely-space-compact-xs: 6px;
  --notely-space-compact-sm: 10px;
  --notely-space-compact-md: 14px;
  --notely-space-compact-lg: 20px;

  /* Border radius */
  --notely-radius-sm: 8px;
  --notely-radius-md: 12px;
  --notely-radius-lg: 16px;
  --notely-radius-xl: 20px;
  --notely-radius-2xl: 24px;

  /* Shadows */
  --notely-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --notely-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --notely-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --notely-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

/* Light theme override */
.light-theme {
  --notely-bg: #fafbfc;
  --notely-surface: rgba(255, 255, 255, 0.9);
  --notely-card: rgba(255, 255, 255, 0.8);
  --notely-border: rgba(225, 229, 233, 0.6);
  --notely-text-primary: #1a202c;
  --notely-text-secondary: #4a5568;
  --notely-text-muted: #718096;

  /* Light theme gradients */
  --notely-gradient-bg: linear-gradient(135deg, #fafbfc 0%, #ffffff 50%, #f8fafc 100%);
  --notely-gradient-surface: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.8) 100%);

  /* Light theme glassmorphism */
  --notely-glass-bg: rgba(255, 255, 255, 0.7);
  --notely-glass-border: rgba(0, 0, 0, 0.1);
  --notely-glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  /* Lighter shadows for light theme */
  --notely-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --notely-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --notely-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --notely-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo visibility based on theme */
.notely-logo-light {
  display: none;
}

.notely-logo-dark {
  display: block;
}

.light-theme .notely-logo-light {
  display: block;
}

.light-theme .notely-logo-dark {
  display: none;
}

/* Search input styling */
.notely-search-input {
  background-color: var(--notely-surface);
  border-color: var(--notely-border);
  color: var(--notely-text-primary);
  box-shadow: var(--notely-shadow-sm);
}

.notely-search-input:focus {
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
}

.notely-search-input::placeholder {
  color: var(--notely-text-muted);
}

.light-theme .notely-search-input {
  background-color: #ffffff;
  border-color: #d1d5db;
  color: var(--notely-text-primary);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.light-theme .notely-search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.light-theme .notely-search-input::placeholder {
  color: #9ca3af;
}

/* Chat input styling with proper contrast for both themes */
.notely-chat-input {
  background-color: #1a1a1a;
  border: 1px solid #333333;
  color: #ffffff;
}

.notely-chat-input::placeholder {
  color: #666666;
}

.light-theme .notely-chat-input {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #1f2937;
}

.light-theme .notely-chat-input::placeholder {
  color: #6b7280;
}

/* Base styles with gradient background */
body {
  background: var(--notely-gradient-bg);
  color: var(--notely-text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  transition: background 0.4s cubic-bezier(0.4, 0, 0.2, 1), color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  position: relative;
}

/* Ambient background effects for notely.social style */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(123, 92, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 67, 200, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Ensure all elements inherit the theme */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Typography classes with Space Grotesk for headings */
.notely-heading {
  font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 600;
  color: var(--notely-text-primary);
}

.notely-body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--notely-text-secondary);
}

.notely-quote {
  font-family: 'Georgia', serif;
  font-style: italic;
  color: var(--notely-text-secondary);
}

/* Gradient text effects matching notely.social */
.notely-gradient-text {
  background: var(--notely-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient 6s ease infinite;
}

.notely-gradient-text-secondary {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Glow effects for special elements */
.notely-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.notely-glow-hover:hover {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Card styles with glassmorphism */
.notely-card {
  background: var(--notely-glass-bg);
  backdrop-filter: blur(12px);
  border: 1px solid var(--notely-glass-border);
  border-radius: var(--notely-radius-lg);
  box-shadow: var(--notely-glass-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  transform: translateY(-4px);
}

/* Enhanced glassmorphism card variant */
.notely-card-glass {
  background: var(--notely-glass-bg);
  backdrop-filter: blur(16px);
  border: 1px solid var(--notely-glass-border);
  border-radius: var(--notely-radius-xl);
  box-shadow: var(--notely-glass-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-card-glass:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.5);
  transform: translateY(-6px) scale(1.02);
}

/* Surface styles */
.notely-surface {
  background-color: var(--notely-surface);
  border-radius: var(--notely-radius-md);
}

/* Button styles */
.notely-btn {
  border-radius: var(--notely-radius-md);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.notely-btn-primary {
  background-color: var(--notely-sky);
  color: white;
  padding: var(--notely-space-sm) var(--notely-space-lg);
}

.notely-btn-primary:hover {
  background-color: #5ba7f7;
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

/* Pill button styles for platform filters */
.notely-pill {
  border-radius: 9999px;
  padding: var(--notely-space-xs) var(--notely-space-md);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid var(--notely-border);
  background-color: var(--notely-surface);
  color: var(--notely-text-secondary);
}

.notely-pill:hover {
  background-color: var(--notely-card);
  transform: translateY(-1px);
}

.notely-pill.active {
  color: white;
  border-color: transparent;
}

/* Platform-specific pill colors */
.notely-pill.active.twitter {
  background-color: #000000;
}

.notely-pill.active.linkedin {
  background-color: #0077b5;
}

.notely-pill.active.reddit {
  background-color: #FF4500;
}

.notely-pill.active.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.notely-pill.active.pinterest {
  background-color: #E60023;
}

.notely-pill.active.web {
  background: linear-gradient(45deg, var(--notely-mint), var(--notely-sky));
}

.notely-pill.active.all {
  background: linear-gradient(45deg, var(--notely-lavender), var(--notely-sky));
}

.notely-pill.active.mindstream {
  background: linear-gradient(45deg, #8b5cf6, #3b82f6, #6366f1);
}

/* Smooth transitions for category changes */
.notely-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Progress bar styles */
.notely-progress {
  background-color: var(--notely-surface);
  border-radius: var(--notely-radius-sm);
  overflow: hidden;
  height: 4px;
}

.notely-progress-bar {
  height: 100%;
  border-radius: var(--notely-radius-sm);
  transition: width 0.3s ease;
}

/* Quick action button styles */
.notely-quick-action {
  background-color: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-sm);
  padding: var(--notely-space-xs);
  color: var(--notely-text-muted);
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateY(4px);
}

.notely-card:hover .notely-quick-action {
  opacity: 1;
  transform: translateY(0);
}

.notely-quick-action:hover {
  background-color: var(--notely-card);
  color: var(--notely-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-sm);
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--notely-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--notely-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--notely-text-muted);
}

/* Loading animation */
.notely-loading {
  animation: notely-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes notely-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced breathing room utility classes */
.notely-breathing-sm {
  padding: var(--notely-space-sm);
}

.notely-breathing-md {
  padding: var(--notely-space-md);
}

.notely-breathing-lg {
  padding: var(--notely-space-lg);
}

.notely-breathing-xl {
  padding: var(--notely-space-xl);
}

.notely-breathing-2xl {
  padding: var(--notely-space-2xl);
}

/* Compact breathing classes for smaller cards */
.notely-breathing-compact-sm {
  padding: var(--notely-space-compact-sm);
}

.notely-breathing-compact-md {
  padding: var(--notely-space-compact-md);
}

.notely-breathing-compact-lg {
  padding: var(--notely-space-compact-lg);
}

/* Post card specific breathing room */
.notely-post-content {
  padding: var(--notely-space-xl);
  margin: var(--notely-space-md) 0;
}

.notely-post-header {
  padding: var(--notely-space-lg) var(--notely-space-xl) var(--notely-space-md) var(--notely-space-xl);
}

.notely-post-footer {
  padding: var(--notely-space-md) var(--notely-space-xl) var(--notely-space-xl) var(--notely-space-xl);
}

/* Enhanced filter transition effects */
.notely-filter-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.notely-filter-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.notely-filter-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.notely-filter-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.notely-filter-exit-active {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

/* Modal and popup animations */
.notely-modal-overlay {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-modal-content {
  transform: scale(0.95) translateY(20px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-modal-content.open {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* Stagger animation for post cards */
.notely-stagger-item {
  animation: notelyFadeInUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(15px);
}

.notely-stagger-item:nth-child(1) { animation-delay: 0.1s; }
.notely-stagger-item:nth-child(2) { animation-delay: 0.2s; }
.notely-stagger-item:nth-child(3) { animation-delay: 0.3s; }
.notely-stagger-item:nth-child(4) { animation-delay: 0.4s; }
.notely-stagger-item:nth-child(5) { animation-delay: 0.5s; }
.notely-stagger-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes notelyFadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Micro-interactions for category and tag buttons */
.notely-category-button {
  animation: notelySlideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.notely-tag-button {
  animation: notelySlideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(20px);
}

/* Stagger delays for category buttons */
.notely-category-button:nth-child(1) { animation-delay: 0.05s; }
.notely-category-button:nth-child(2) { animation-delay: 0.1s; }
.notely-category-button:nth-child(3) { animation-delay: 0.15s; }
.notely-category-button:nth-child(4) { animation-delay: 0.2s; }
.notely-category-button:nth-child(5) { animation-delay: 0.25s; }
.notely-category-button:nth-child(6) { animation-delay: 0.3s; }
.notely-category-button:nth-child(7) { animation-delay: 0.35s; }
.notely-category-button:nth-child(8) { animation-delay: 0.4s; }

/* Stagger delays for tag buttons */
.notely-tag-button:nth-child(1) { animation-delay: 0.1s; }
.notely-tag-button:nth-child(2) { animation-delay: 0.15s; }
.notely-tag-button:nth-child(3) { animation-delay: 0.2s; }
.notely-tag-button:nth-child(4) { animation-delay: 0.25s; }
.notely-tag-button:nth-child(5) { animation-delay: 0.3s; }
.notely-tag-button:nth-child(6) { animation-delay: 0.35s; }
.notely-tag-button:nth-child(7) { animation-delay: 0.4s; }
.notely-tag-button:nth-child(8) { animation-delay: 0.45s; }

@keyframes notelySlideInFromLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes notelySlideInFromRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for active selections */
@keyframes notelyPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.notely-selection-pulse {
  animation: notelyPulse 0.6s ease-in-out;
}

/* Enhanced gradient button styles matching notely.social */
.notely-btn-primary {
  background: var(--notely-gradient-primary);
  color: white;
  border: none;
  border-radius: var(--notely-radius-xl);
  padding: var(--notely-space-md) var(--notely-space-lg);
  font-weight: 600;
  font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(123, 92, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.notely-btn-primary::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 67, 200, 0.3), rgba(123, 92, 255, 0.3));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notely-btn-primary:hover::before {
  opacity: 1;
}

.notely-btn-primary:hover {
  box-shadow: 0 12px 35px rgba(123, 92, 255, 0.4);
  transform: translateY(-3px) scale(1.05);
}

/* Glassmorphism button variant */
.notely-btn-glass {
  background: var(--notely-glass-bg);
  backdrop-filter: blur(12px);
  color: white;
  border: 1px solid var(--notely-glass-border);
  border-radius: var(--notely-radius-xl);
  padding: var(--notely-space-md) var(--notely-space-lg);
  font-weight: 500;
  font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--notely-glass-shadow);
}

.notely-btn-glass:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.notely-btn-secondary {
  background: var(--notely-surface);
  color: var(--notely-text-primary);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-lg);
  padding: var(--notely-space-md) var(--notely-space-lg);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Elegant Reading Mode Styles */
.notely-elegant-reading-card {
  /* Enhanced card styling for text-only posts */
  background: linear-gradient(135deg, var(--notely-card) 0%, var(--notely-surface) 100%);
  border: 2px solid var(--notely-border);
  box-shadow: var(--notely-shadow-lg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-elegant-reading-card:hover {
  box-shadow: var(--notely-shadow-xl);
  transform: translateY(-4px);
  border-color: var(--notely-sky);
}

.notely-elegant-content {
  /* Georgia serif font for elegant reading */
  font-family: 'Georgia', 'Times New Roman', serif !important;
  font-size: 1.125rem; /* 18px */
  line-height: 1.75; /* 28px */
  letter-spacing: 0.025em;
  text-align: left;
  max-width: none;
  margin: 0 auto;
  position: relative;
}

/* Light theme adjustments for elegant reading */
.light-theme .notely-elegant-reading-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-color: #e2e8f0;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.light-theme .notely-elegant-reading-card:hover {
  box-shadow: 0 20px 35px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.light-theme .notely-elegant-content {
  color: #1e293b;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: rgba(226, 232, 240, 0.5);
}

.notely-btn-secondary:hover {
  background: var(--notely-card);
  border-color: var(--notely-accent);
  transform: translateY(-1px);
}

.notely-btn-accent {
  background: linear-gradient(135deg, var(--notely-accent-light), var(--notely-accent));
  color: white;
  border: none;
  border-radius: var(--notely-radius-lg);
  padding: var(--notely-space-sm) var(--notely-space-md);
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-btn-accent:hover {
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

.notely-btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: var(--notely-radius-lg);
  padding: var(--notely-space-md) var(--notely-space-lg);
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: var(--notely-shadow-md);
}

/* Quick action button styles */
.notely-quick-action {
  padding: var(--notely-space-sm);
  border-radius: var(--notely-radius-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  border: none;
}

.notely-quick-action:hover {
  background: var(--notely-surface);
  transform: scale(1.1);
}

/* Pill button styles */
.notely-pill {
  border-radius: var(--notely-radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.notely-pill.active {
  font-weight: 600;
  box-shadow: var(--notely-shadow-md);
}

/* Ask My Bookmarks Search Styles */
.notely-search-container {
  position: relative;
}

.notely-search-input {
  background: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notely-search-input:focus-within {
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
  transform: translateY(-1px);
}

.notely-search-input:hover:not(:focus-within) {
  border-color: var(--notely-mint);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notely-search-suggestions {
  background: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-lg);
  box-shadow: var(--notely-shadow-xl);
  backdrop-filter: blur(8px);
  animation: notelySlideDown 0.2s ease-out;
}

.notely-search-suggestion-item {
  transition: all 0.15s ease;
}

.notely-search-suggestion-item:hover {
  background: var(--notely-card);
  transform: translateX(4px);
}

.notely-search-loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.notely-search-loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: notelyPulse 1.4s ease-in-out infinite both;
}

.notely-search-loading-dot:nth-child(1) { animation-delay: -0.32s; }
.notely-search-loading-dot:nth-child(2) { animation-delay: -0.16s; }
.notely-search-loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes notelySlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes notelyPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Search Results Summary Styles */
.notely-search-results {
  animation: notelyFadeInUp 0.4s ease-out;
}

.notely-search-result-card {
  background: var(--notely-surface);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-lg);
  transition: all 0.2s ease;
}

.notely-search-result-card:hover {
  border-color: var(--notely-sky);
  box-shadow: var(--notely-shadow-md);
  transform: translateY(-2px);
}

.notely-search-highlight {
  background: linear-gradient(120deg, var(--notely-mint) 0%, var(--notely-sky) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

/* Pastel highlight borders for search */
.notely-search-border-mint {
  border-color: var(--notely-mint);
  box-shadow: 0 0 0 1px var(--notely-mint), 0 0 20px rgba(81, 207, 102, 0.1);
}

.notely-search-border-coral {
  border-color: var(--notely-coral);
  box-shadow: 0 0 0 1px var(--notely-coral), 0 0 20px rgba(255, 107, 107, 0.1);
}

/* Search input styling */
.notely-search-input {
  background-color: var(--notely-surface);
  border-color: var(--notely-border);
  color: var(--notely-text-primary);
}

.notely-search-input::placeholder {
  color: var(--notely-text-muted);
}

.notely-search-input:focus {
  background-color: var(--notely-card);
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 2px rgba(116, 192, 252, 0.2);
}

.notely-search-input:hover:not(:disabled) {
  background-color: var(--notely-card);
  border-color: var(--notely-text-muted);
}

.notely-search-border-sky {
  border-color: var(--notely-sky);
  box-shadow: 0 0 0 1px var(--notely-sky), 0 0 20px rgba(116, 192, 252, 0.1);
}

/* Keyboard shortcut styling */
.notely-kbd {
  background: var(--notely-card);
  border: 1px solid var(--notely-border);
  border-radius: var(--notely-radius-sm);
  padding: 2px 6px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 11px;
  font-weight: 500;
  color: var(--notely-text-muted);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Right panel styling - fixes for white lines */
/* Target specific elements in the right panels */
.MindstreamWidgets .notely-card {
  border: none !important;
  box-shadow: var(--notely-shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.MindstreamWidgets .notely-card:hover {
  box-shadow: var(--notely-shadow-lg);
  transform: translateY(-2px);
}

/* Fix for any dividers or separators in right panels */
.MindstreamWidgets hr,
.MindstreamWidgets .divider {
  border: none !important;
  height: 1px;
  background: rgba(255, 255, 255, 0.1) !important;
  margin: var(--notely-space-md) 0;
}

.light-theme .MindstreamWidgets hr,
.light-theme .MindstreamWidgets .divider {
  background: rgba(0, 0, 0, 0.1) !important;
}

/* Fix for any borders within right panels */
.MindstreamWidgets .notely-card * {
  border-color: transparent !important;
}

/* Fix for any table borders or grid lines in right panels */
.MindstreamWidgets table,
.MindstreamWidgets td,
.MindstreamWidgets th,
.MindstreamWidgets tr {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.light-theme .MindstreamWidgets table,
.light-theme .MindstreamWidgets td,
.light-theme .MindstreamWidgets th,
.light-theme .MindstreamWidgets tr {
  border-color: rgba(0, 0, 0, 0.1) !important;
}

/* Fix for list item separators in right panels */
.MindstreamWidgets .notely-card li + li {
  border-top: none !important;
  margin-top: var(--notely-space-xs);
}

/* Fix for any input fields within panels */
.notely-card input,
.notely-card textarea,
.notely-card select {
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.light-theme .notely-card input,
.light-theme .notely-card textarea,
.light-theme .notely-card select {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Dark Mode Specific Optimizations - Fix for white borders */

/* Platform selector buttons in dark mode */
.bg-notely-surface {
  background-color: var(--notely-surface) !important;
}

/* Sidebar background with improved readability */
.notely-sidebar-bg {
  background-color: rgba(255, 255, 255, 0.75) !important;
  backdrop-filter: blur(10px) !important;
}

.dark .notely-sidebar-bg {
  background-color: rgba(26, 26, 26, 0.90) !important;
  backdrop-filter: blur(10px) !important;
}

.notely-sidebar-border {
  border-color: rgba(229, 231, 235, 0.4) !important;
}

.dark .notely-sidebar-border {
  border-color: rgba(64, 64, 64, 0.4) !important;
}



/* Ensure border opacity is properly applied in dark mode */
.border-notely-border\/10 {
  border-color: rgba(51, 51, 51, 0.3) !important;
}

/* Improved border visibility for light mode */
.light-theme .border-notely-border\/10 {
  border-color: rgba(229, 231, 235, 0.4) !important;
}

/* Enhanced dark mode border visibility */
.dark .border-notely-border\/10 {
  border-color: rgba(64, 64, 64, 0.4) !important;
}

/* Fix for modal borders in dark mode */
.notely-modal-content {
  border-color: rgba(51, 51, 51, 0.3) !important;
}

/* Fix for button borders in dark mode */
.dark button.border-notely-border\/10 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

.dark button.border-notely-border-dark\/20 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

/* Fix for chat interface borders */
.dark .notely-chat-container .border-notely-border\/10,
.dark .notely-chat-container .border-notely-border-dark\/20 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

.dark .notely-chat-container {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

/* Enhance shadow visibility in dark mode */
.dark .shadow-notely-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.6), 0 2px 4px -1px rgba(0, 0, 0, 0.4) !important;
}

.dark .shadow-notely-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.7), 0 4px 6px -2px rgba(0, 0, 0, 0.5) !important;
}

.dark .shadow-notely-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.dark .shadow-notely-xs {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.4) !important;
}

/* Fix for toggle buttons in dark mode */
.bg-notely-lavender {
  background-color: var(--notely-lavender) !important;
}

/* Fix for input fields in dark mode */
.dark input.border-notely-border\/10,
.dark textarea.border-notely-border\/10,
.dark select.border-notely-border\/10 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

.dark input.border-notely-border-dark\/20,
.dark textarea.border-notely-border-dark\/20,
.dark select.border-notely-border-dark\/20 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

/* Enhanced dark mode styling for platform selectors */
.dark-mode-platform-btn {
  border-color: rgba(120, 120, 120, 0.25) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.dark-mode-platform-btn:hover {
  border-color: rgba(150, 150, 150, 0.4) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

/* Chat interface enhancements for both light and dark modes */
.notely-chat-container {
  background-color: var(--notely-card) !important;
  border-color: var(--notely-border) !important;
}

.dark .notely-chat-container {
  background-color: var(--notely-card) !important;
  border-color: rgba(51, 51, 51, 0.2) !important;
}

.dark .notely-chat-message {
  border-color: rgba(80, 80, 80, 0.2) !important;
}

/* Chat input styling with proper contrast for both themes */
.notely-chat-input {
  background-color: var(--notely-surface) !important;
  border-color: var(--notely-border) !important;
  color: var(--notely-text-primary) !important;
}

.notely-chat-input::placeholder {
  color: var(--notely-text-muted) !important;
}

.dark .notely-chat-input {
  border-color: rgba(80, 80, 80, 0.3) !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2) !important;
  background-color: var(--notely-surface) !important;
  color: var(--notely-text-primary) !important;
}

.dark .notely-chat-input::placeholder {
  color: var(--notely-text-muted) !important;
}

/* Dark mode modal enhancements */
.dark .notely-modal-overlay {
  background: rgba(0, 0, 0, 0.75) !important;
  backdrop-filter: blur(4px) !important;
}

.dark .notely-modal-overlay .border-notely-border\/10 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

/* Dark mode toggle button enhancement */
.dark button[role="switch"] {
  border-color: rgba(80, 80, 80, 0.2) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;

}

/* Dark mode category selector enhancements */
.notely-category-button {
  border-color: rgba(80, 80, 80, 0.3) !important;
}

/* ===== REFINED DARK MODE VISUAL IMPROVEMENTS ===== */

/* Keep platform selector button borders as they are - they're fine */

/* Remove white borders from thread indicators */
.dark .border-white {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Fix ContentCategorySelector white backgrounds and borders in dark mode */
.dark .bg-white {
  background-color: var(--notely-surface) !important;
}

.dark .border-gray-200 {
  border-color: rgba(51, 51, 51, 0.15) !important;
}

.dark .border-gray-300 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

.dark .text-gray-600 {
  color: var(--notely-text-secondary) !important;
}

.dark .hover\:bg-gray-50:hover {
  background-color: var(--notely-card) !important;
}

.dark .hover\:border-gray-300:hover {
  border-color: rgba(51, 51, 51, 0.25) !important;
}

.dark .hover\:text-gray-900:hover {
  color: var(--notely-text-primary) !important;
}

/* Fix Sidebar white backgrounds and borders in dark mode */
.dark .bg-white\/70 {
  background-color: rgba(26, 26, 26, 0.95) !important;
}

.dark .border-gray-200\/50 {
  border-color: rgba(51, 51, 51, 0.1) !important;
}

.dark .border-gray-200 {
  border-color: rgba(51, 51, 51, 0.15) !important;
}

/* Fix InspirationFeedWidget white backgrounds and borders */
.dark .bg-white {
  background-color: var(--notely-card) !important;
}

.dark .border-gray-200 {
  border-color: rgba(51, 51, 51, 0.15) !important;
}

.dark .bg-blue-50 {
  background-color: rgba(116, 192, 252, 0.1) !important;
}

/* Fix ThemeToggle white circle background */
.dark .bg-white {
  background-color: var(--notely-surface) !important;
}

/* Fix any remaining white/gray borders in components */
.dark .border-gray-100 {
  border-color: rgba(51, 51, 51, 0.1) !important;
}

.dark .border-gray-400 {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

.dark .border-gray-500 {
  border-color: rgba(51, 51, 51, 0.25) !important;
}

.dark .border-gray-600 {
  border-color: rgba(51, 51, 51, 0.3) !important;
}

.dark .border-gray-700 {
  border-color: rgba(51, 51, 51, 0.35) !important;
}

/* Fix any white text that should be muted in dark mode */
.dark .text-gray-400 {
  color: var(--notely-text-muted) !important;
}

.dark .text-gray-500 {
  color: var(--notely-text-muted) !important;
}

.dark .text-gray-700 {
  color: var(--notely-text-secondary) !important;
}

.dark .text-gray-800 {
  color: var(--notely-text-primary) !important;
}

/* Fix any remaining gray backgrounds that should be dark */
.dark .bg-gray-100 {
  background-color: var(--notely-surface) !important;
}

.dark .bg-gray-200 {
  background-color: var(--notely-card) !important;
}

.dark .bg-gray-800 {
  background-color: var(--notely-card) !important;
}

.dark .bg-gray-900 {
  background-color: var(--notely-surface) !important;
}

/* Ensure all hover states respect dark mode */
.dark .hover\:bg-gray-100:hover {
  background-color: var(--notely-card) !important;
}

.dark .hover\:bg-gray-200:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Additional comprehensive fixes for any missed elements */
.dark .bg-gray-50 {
  background-color: var(--notely-surface) !important;
}

.dark .bg-gray-300 {
  background-color: var(--notely-card) !important;
}

.dark .bg-gray-400 {
  background-color: var(--notely-border) !important;
}

.dark .bg-gray-500 {
  background-color: var(--notely-border) !important;
}

.dark .bg-gray-600 {
  background-color: var(--notely-card) !important;
}

/* Fix any remaining text colors */
.dark .text-gray-300 {
  color: var(--notely-text-secondary) !important;
}

.dark .text-gray-900 {
  color: var(--notely-text-primary) !important;
}

/* Fix any remaining border colors */
.dark .border-gray-50 {
  border-color: rgba(51, 51, 51, 0.05) !important;
}

.dark .border-gray-800 {
  border-color: rgba(51, 51, 51, 0.4) !important;
}

.dark .border-gray-900 {
  border-color: rgba(51, 51, 51, 0.5) !important;
}

/* Fix any remaining hover border states */
.dark .hover\:border-gray-100:hover {
  border-color: rgba(51, 51, 51, 0.1) !important;
}

.dark .hover\:border-gray-200:hover {
  border-color: rgba(51, 51, 51, 0.15) !important;
}

.dark .hover\:border-gray-400:hover {
  border-color: rgba(51, 51, 51, 0.2) !important;
}

.dark .hover\:border-gray-500:hover {
  border-color: rgba(51, 51, 51, 0.25) !important;
}

.dark .hover\:border-gray-600:hover {
  border-color: rgba(51, 51, 51, 0.3) !important;
}

/* Fix any remaining hover text states */
.dark .hover\:text-gray-700:hover {
  color: var(--notely-text-secondary) !important;
}

.dark .hover\:text-gray-800:hover {
  color: var(--notely-text-primary) !important;
}

.dark .hover\:text-gray-600:hover {
  color: var(--notely-text-secondary) !important;
}

/* Ensure spinner borders are subtle in dark mode */
.dark .border-white\/30 {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.dark .border-t-white {
  border-top-color: rgba(255, 255, 255, 0.3) !important;
}

/* Fix any focus ring colors that might be too bright */
.dark .focus\:ring-gray-500:focus {
  --tw-ring-color: rgba(51, 51, 51, 0.3) !important;
}

.dark .focus\:ring-gray-300:focus {
  --tw-ring-color: rgba(51, 51, 51, 0.2) !important;
}

/* Dark mode specific styles for SimpleCategorySelector */
.SimpleCategorySelector button {
  border-color: rgba(80, 80, 80, 0.3) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.SimpleCategorySelector button:hover {
  border-color: rgba(100, 100, 100, 0.4) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

/* Admin Dashboard Utilities */
.bg-notely-sky\/20 {
  background-color: rgba(116, 192, 252, 0.2);
}

.text-notely-sky {
  color: var(--notely-sky);
}

.bg-notely-mint\/20 {
  background-color: rgba(81, 207, 102, 0.2);
}

.text-notely-mint {
  color: var(--notely-mint);
}

.bg-notely-lavender\/20 {
  background-color: rgba(177, 151, 252, 0.2);
}

.text-notely-lavender {
  color: var(--notely-lavender);
}

.bg-notely-coral\/20 {
  background-color: rgba(255, 107, 107, 0.2);
}

.text-notely-coral {
  color: var(--notely-coral);
}

.bg-notely-text-muted\/20 {
  background-color: rgba(102, 102, 102, 0.2);
}

.text-notely-text-muted {
  color: var(--notely-text-muted);
}

.bg-notely-surface\/50 {
  background-color: rgba(26, 26, 26, 0.5);
}

.light-theme .bg-notely-surface\/50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.hover\:text-notely-sky\/80:hover {
  color: rgba(116, 192, 252, 0.8);
}

.hover\:text-notely-lavender\/80:hover {
  color: rgba(177, 151, 252, 0.8);
}

.shadow-notely-lg {
  box-shadow: var(--notely-shadow-lg);
}

.focus\:ring-notely-accent:focus {
  --tw-ring-color: var(--notely-accent);
}
