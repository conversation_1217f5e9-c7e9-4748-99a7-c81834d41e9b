/* 
 * UI Improvements - Enhanced Design System
 * Addressing layout consistency, typography scaling, and interactive feedback
 */

/* ===== CONSISTENT SPACING TOKENS ===== */
:root {
  /* Spacing scale for consistent layouts */
  --space-xs: 8px;
  --space-sm: 12px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  
  /* Typography scale for hierarchy */
  --text-h1: 20px;
  --text-h2: 16px;
  --text-body: 14px;
  --text-caption: 12px;
  --text-micro: 10px;
  
  /* Enhanced border radius for modern feel */
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-full: 9999px;
  
  /* Shadow system for depth */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
}

/* ===== LIGHT MODE SPECIFIC IMPROVEMENTS ===== */
.light-theme {
  /* Enhanced borders for better contrast in light mode */
  --enhanced-border: #e5e7eb;
  --enhanced-border-strong: #d1d5db;
  --enhanced-card-bg: #fcfcfd;
  --enhanced-surface-bg: #f9fafb;
  
  /* Better shadows for light mode */
  --light-shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --light-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --light-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --light-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --light-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
}

/* ===== ENHANCED CARD SYSTEM ===== */
.ui-card-enhanced {
  background: var(--notely-card);
  border: 1px solid var(--notely-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.light-theme .ui-card-enhanced {
  background: var(--enhanced-card-bg);
  border: 1px solid var(--enhanced-border);
  box-shadow: var(--light-shadow-sm);
}

.ui-card-enhanced:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--notely-sky);
  transform: translateY(-2px);
}

.light-theme .ui-card-enhanced:hover {
  box-shadow: var(--light-shadow-lg);
  border-color: #3b82f6;
  transform: translateY(-2px);
}

/* Enhanced light mode card backgrounds */
.light-theme .notely-card {
  background: var(--enhanced-card-bg) !important;
  border-color: var(--enhanced-border) !important;
  box-shadow: var(--light-shadow-sm) !important;
}

.light-theme .notely-card:hover {
  box-shadow: var(--light-shadow-lg) !important;
  border-color: var(--enhanced-border-strong) !important;
}

.light-theme .bg-notely-surface {
  background: var(--enhanced-surface-bg) !important;
}

/* Category Stats Box - Visual Enhancement */
.ui-category-stats {
  background: var(--enhanced-surface-bg);
  border: 1px solid var(--enhanced-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--light-shadow-sm);
  transition: all 0.2s ease;
}

.light-theme .ui-category-stats {
  background: var(--enhanced-surface-bg);
  border: 1px solid var(--enhanced-border);
}

.ui-category-stats:hover {
  box-shadow: var(--light-shadow-md);
  border-color: var(--enhanced-border-strong);
}

.ui-category-stats-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-md);
}

.ui-category-stats-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-sm);
  box-shadow: var(--light-shadow-xs);
}

.ui-category-stats-title {
  font-size: var(--text-h2);
  font-weight: 600;
  color: #1f2937;
}

.ui-category-stats-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.ui-category-stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xs) 0;
}

.ui-category-stat-label {
  font-size: var(--text-body);
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.ui-category-stat-value {
  font-size: var(--text-body);
  font-weight: 600;
  color: #1f2937;
}

.ui-category-link {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--text-caption);
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  transition: all 0.2s ease;
  margin-top: var(--space-sm);
}

.ui-category-link:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: var(--light-shadow-sm);
}

/* AI Assistant Card Enhancement */
.ui-ai-card {
  background: linear-gradient(135deg, var(--enhanced-card-bg) 0%, #f3f4f6 100%);
  border: 1px solid var(--enhanced-border);
  border-left: 4px solid #8b5cf6;
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--light-shadow-sm);
  position: relative;
  overflow: hidden;
}

.ui-ai-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.02) 0%, rgba(59, 130, 246, 0.02) 100%);
  pointer-events: none;
}

.ui-ai-card-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-md);
  position: relative;
  z-index: 1;
}

.ui-ai-card-icon {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-sm);
  box-shadow: var(--light-shadow-xs);
}

.ui-ai-card-title {
  font-size: var(--text-h2);
  font-weight: 600;
  color: #1f2937;
}

.ui-ai-card-content {
  font-size: var(--text-body);
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: var(--space-lg);
  position: relative;
  z-index: 1;
}

.ui-ai-card-cta {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--text-body);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  position: relative;
  z-index: 1;
}

.ui-ai-card-cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--light-shadow-md);
}

/* Tag Chips Enhancement */
.ui-tag-chip {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-caption);
  font-weight: 500;
  border: 1px solid var(--enhanced-border);
  background: var(--enhanced-card-bg);
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.ui-tag-chip:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: var(--light-shadow-xs);
}

.ui-tag-chip.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  box-shadow: var(--light-shadow-sm);
}

/* Button System Enhancement */
.ui-btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.ui-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  padding: var(--space-sm) var(--space-lg);
  box-shadow: var(--light-shadow-sm);
  border: 1px solid transparent;
}

.ui-btn-primary:hover {
  box-shadow: var(--light-shadow-md);
  transform: translateY(-1px);
}

.ui-btn-secondary {
  background: var(--enhanced-card-bg);
  color: #1f2937;
  border: 1px solid var(--enhanced-border);
  padding: var(--space-sm) var(--space-lg);
}

.ui-btn-secondary:hover {
  background: var(--enhanced-surface-bg);
  border-color: var(--enhanced-border-strong);
  transform: translateY(-1px);
  box-shadow: var(--light-shadow-xs);
}

.ui-btn-ghost {
  background: transparent;
  color: #6b7280;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
}

.ui-btn-ghost:hover {
  background: var(--enhanced-surface-bg);
  color: #1f2937;
  transform: scale(1.05);
}

/* Platform Tab Enhancement */
.ui-platform-tab {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  font-size: var(--text-body);
  font-weight: 500;
  border: 1px solid var(--enhanced-border);
  background: var(--enhanced-card-bg);
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.ui-platform-tab:hover {
  background: var(--enhanced-surface-bg);
  border-color: var(--enhanced-border-strong);
  color: #1f2937;
  transform: translateY(-1px);
  box-shadow: var(--light-shadow-xs);
}

.ui-platform-tab.active {
  background: #1f2937;
  color: white;
  border-color: #1f2937;
  box-shadow: var(--light-shadow-sm);
}

/* Fix X/Twitter tab to match others */
.light-theme .ui-platform-tab.x-twitter {
  background: var(--enhanced-card-bg);
  color: #6b7280;
  border-color: var(--enhanced-border);
}

.light-theme .ui-platform-tab.x-twitter.active {
  background: #1f2937;
  color: white;
  border-color: #1f2937;
}

.light-theme .ui-platform-tab.x-twitter:hover:not(.active) {
  background: var(--enhanced-surface-bg);
  color: #1f2937;
  border-color: var(--enhanced-border-strong);
}

/* Enhanced Card Heights for Visual Balance */
.ui-equal-height-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  align-items: start;
}

.ui-equal-height-card {
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.ui-equal-height-card .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* ===== ICON ALIGNMENT SYSTEM ===== */
.ui-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
}

.ui-metadata-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 20px;
  gap: var(--space-sm);
}

.ui-stat-icons {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.ui-action-icons {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-left: auto;
}

/* ===== TYPOGRAPHY HIERARCHY ===== */
.ui-heading-1 {
  font-size: var(--text-h1);
  font-weight: 700;
  line-height: 1.2;
  color: var(--notely-text-primary);
}

.ui-heading-2 {
  font-size: var(--text-h2);
  font-weight: 600;
  line-height: 1.3;
  color: var(--notely-text-primary);
}

.ui-body-text {
  font-size: var(--text-body);
  font-weight: 400;
  line-height: 1.5;
  color: var(--notely-text-secondary);
}

.ui-caption-text {
  font-size: var(--text-caption);
  font-weight: 500;
  line-height: 1.4;
  color: var(--notely-text-muted);
}

.ui-micro-text {
  font-size: var(--text-micro);
  font-weight: 500;
  line-height: 1.3;
  color: var(--notely-text-muted);
}

/* Light mode typography adjustments */
.light-theme .ui-heading-1,
.light-theme .ui-heading-2 {
  color: #1f2937;
}

.light-theme .ui-body-text {
  color: #4b5563;
}

.light-theme .ui-caption-text,
.light-theme .ui-micro-text {
  color: #6b7280;
}

/* Line clamp utilities for consistent text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Tag overflow utilities */
.ui-tag-overflow {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  align-items: center;
}

.ui-tag-overflow .tag:nth-child(n+5) {
  display: none;
}

.ui-tag-overflow .overflow-indicator {
  background: var(--enhanced-surface-bg);
  color: #6b7280;
  border: 1px solid var(--enhanced-border);
  border-radius: var(--radius-full);
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--text-micro);
  font-weight: 500;
  cursor: pointer;
}

.ui-tag-overflow .overflow-indicator:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Enhanced focus states */
.ui-focus-visible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Screen reader utilities */
.ui-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@media (prefers-color-scheme: dark) {
  .ui-card-enhanced {
    background: var(--notely-card);
    border-color: var(--notely-border-dark);
  }
  
  .ui-tag {
    background: var(--notely-surface);
    border-color: var(--notely-border-dark);
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.ui-gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.ui-hardware-accelerated {
  transform: translate3d(0, 0, 0);
} 