/* Force vertical centering for carousel images */
.embla__container {
  height: 100% !important;
}

.embla__slide {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure ImageSwiper container fills height */
.image-swiper-container {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Force carousel content to center */
[data-carousel-content] {
  height: 100% !important;
}

[data-carousel-item] {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Specific targeting for our carousel component */
.carousel-image-container {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.carousel-image-container img {
  max-height: 100% !important;
  max-width: 100% !important;
  object-fit: contain !important;
} 