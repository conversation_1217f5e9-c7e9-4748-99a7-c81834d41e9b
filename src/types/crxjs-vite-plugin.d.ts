declare module '@crxjs/vite-plugin' {
  import { PluginOption, ConfigEnv } from 'vite';

  // This is a simplified representation of the manifest structure.
  // For full type safety, you might consider using types from '@types/chrome' (chrome.runtime.ManifestV3)
  // if it's available in your project, or define a more detailed interface here.
  interface ActionProperty {
    default_icon?: Record<string, string> | string;
    default_title?: string;
    default_popup?: string;
  }

  interface BackgroundProperty {
    service_worker: string;
    type?: 'module';
  }

  interface ContentScriptProperty {
    matches: string[];
    js?: string[];
    css?: string[];
    run_at?: 'document_start' | 'document_end' | 'document_idle';
    all_frames?: boolean;
    match_about_blank?: boolean;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any; // Allow other content script properties
  }

  interface WebAccessibleResourceProperty {
    resources: string[];
    matches?: string[];
    extension_ids?: string[];
    use_dynamic_url?: boolean;
  }

  interface ContentSecurityPolicyProperty {
    extension_pages?: string;
    sandbox?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any; // Allow other CSP properties
  }

  type ManifestInput = {
    manifest_version: 3;
    name: string;
    description?: string;
    version: string;
    version_name?: string;
    icons?: Record<string, string>;
    action?: ActionProperty;
    background?: BackgroundProperty;
    content_scripts?: ContentScriptProperty[];
    web_accessible_resources?: WebAccessibleResourceProperty[];
    host_permissions?: string[];
    permissions?: string[];
    content_security_policy?: ContentSecurityPolicyProperty;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any; // Allow other manifest properties for flexibility
  };

  export function defineManifest(
    manifest: ManifestInput | ((env: ConfigEnv) => ManifestInput) | ((env: ConfigEnv) => Promise<ManifestInput>)
  ): PluginOption;
}
