export interface WidgetConfig {
  id: string;
  type: string;
  title: string;
  icon: string;
  description: string;
  enabled: boolean;
  span: 1 | 2;
  category: 'ai' | 'content' | 'analytics' | 'social';
  settings?: {
    [key: string]: any;
  };
}

export interface ContentBasedWidgetSettings {
  // Fashion Gallery / Art Gallery / Web Design Gallery
  visualGallery?: {
    mode: 'auto' | 'fashion' | 'art' | 'webdesign' | 'photography';
    title: string;
    filterByHashtags?: string[];
    minImageQuality?: 'low' | 'medium' | 'high';
  };

  // AI Insights
  aiInsights?: {
    showTrendingTopics: boolean;
    showSmartInsights: boolean;
    confidenceThreshold: number; // 0.1 to 1.0
    maxInsights: number;
  };

  // Content Performance
  contentPerformance?: {
    timeRange: '7d' | '30d' | '90d' | 'all';
    metrics: ('likes' | 'comments' | 'shares' | 'saves')[];
    showBestPerformer: boolean;
  };

  // Saved Bookmarks
  savedBookmarks?: {
    autoCategorizeDomains: boolean;
    customCategories: { name: string; emoji: string; domains: string[] }[];
    maxBookmarks: number;
  };
}

export interface WidgetPreset {
  id: string;
  name: string;
  description: string;
  icon: string;
  widgets: WidgetConfig[];
}

// Default widget presets
export const WIDGET_PRESETS: WidgetPreset[] = [
  {
    id: 'content-creator',
    name: 'Content Creator',
    description: 'Perfect for influencers and content creators',
    icon: '🎨',
    widgets: [
      {
        id: 'ai-insights',
        type: 'ai-insights',
        title: '🧠 AI Insights',
        icon: '🧠',
        description: 'AI-powered content trends and insights',
        enabled: true,
        span: 2,
        category: 'ai',
        settings: {
          showTrendingTopics: true,
          showSmartInsights: true,
          confidenceThreshold: 0.6,
          maxInsights: 3
        }
      },
      {
        id: 'visual-gallery',
        type: 'fashion-gallery',
        title: '📸 Visual Gallery',
        icon: '📸',
        description: 'Auto-categorized visual content gallery',
        enabled: true,
        span: 1,
        category: 'content',
        settings: {
          mode: 'auto',
          title: 'Visual Gallery',
          minImageQuality: 'medium'
        }
      },
      {
        id: 'content-performance',
        type: 'content-performance',
        title: '📊 Performance',
        icon: '📊',
        description: 'Content engagement analytics',
        enabled: true,
        span: 1,
        category: 'analytics',
        settings: {
          timeRange: '30d',
          metrics: ['likes', 'comments', 'shares'],
          showBestPerformer: true
        }
      }
    ]
  },
  {
    id: 'researcher',
    name: 'Researcher',
    description: 'Ideal for researchers and knowledge workers',
    icon: '🔬',
    widgets: [
      {
        id: 'saved-bookmarks',
        type: 'bookmark-grid',
        title: '🔖 Research Library',
        icon: '🔖',
        description: 'Organized bookmark collection',
        enabled: true,
        span: 2,
        category: 'content',
        settings: {
          autoCategorizeDomains: true,
          maxBookmarks: 8,
          customCategories: []
        }
      }
    ]
  },
  {
    id: 'business',
    name: 'Business Pro',
    description: 'For business professionals and marketers',
    icon: '💼',
    widgets: [
      {
        id: 'ai-insights',
        type: 'ai-insights',
        title: '🧠 Market Insights',
        icon: '🧠',
        description: 'Business and market trend analysis',
        enabled: true,
        span: 2,
        category: 'ai'
      },
      {
        id: 'content-performance',
        type: 'content-performance',
        title: '📈 ROI Analytics',
        icon: '📈',
        description: 'Content performance metrics',
        enabled: true,
        span: 1,
        category: 'analytics'
      },
      {
        id: 'saved-bookmarks',
        type: 'bookmark-grid',
        title: '📚 Business Resources',
        icon: '📚',
        description: 'Curated business content',
        enabled: true,
        span: 1,
        category: 'content'
      }
    ]
  }
];

export const DEFAULT_WIDGET_CONFIGS: WidgetConfig[] = [
  {
    id: 'ai-insights',
    type: 'ai-insights',
    title: '🧠 AI Insights',
    icon: '🧠',
    description: 'AI-powered content analysis and trending topics',
    enabled: true,
    span: 2,
    category: 'ai'
  },
  {
    id: 'top-post',
    type: 'top-post',
    title: '🏆 Top Post',
    icon: '🏆',
    description: 'Your highest performing content',
    enabled: true,
    span: 2,
    category: 'analytics'
  },
  {
    id: 'visual-gallery',
    type: 'fashion-gallery',
    title: '📸 Visual Gallery',
    icon: '📸',
    description: 'AI-categorized visual content (Fashion/Art/Design)',
    enabled: true,
    span: 1,
    category: 'content'
  },
  {
    id: 'saved-bookmarks',
    type: 'bookmark-grid',
    title: '🔖 Saved Bookmarks',
    icon: '🔖',
    description: 'Organized link collection with smart categorization',
    enabled: true,
    span: 2,
    category: 'content'
  },
  {
    id: 'content-performance',
    type: 'content-performance',
    title: '📊 Performance',
    icon: '📊',
    description: 'Engagement metrics and analytics',
    enabled: true,
    span: 1,
    category: 'analytics'
  }
];
