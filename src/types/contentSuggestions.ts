import { Post, Platform } from './index';

export interface ContentSuggestion {
  id: string;
  type: 'thread' | 'caption' | 'newsletter' | 'quote' | 'poll' | 'story';
  title: string;
  description: string;
  originalPost: Post;
  suggestedContent?: string;
  confidence: number; // 0-1
  platform?: Platform;
  estimatedEngagement?: 'low' | 'medium' | 'high';
  createdAt: Date;
}

export interface ContentAction {
  id: string;
  label: string;
  icon: string;
  description: string;
  platforms: Platform[];
  minContentLength?: number;
  maxContentLength?: number;
}

export interface GeneratedContent {
  id: string;
  suggestionId: string;
  content: string;
  platform: Platform;
  status: 'draft' | 'scheduled' | 'published';
  createdAt: Date;
  scheduledFor?: Date;
  metadata?: {
    hashtags?: string[];
    mentions?: string[];
    mediaUrls?: string[];
  };
}

export interface ContentSuggestionAnalysis {
  suggestions: ContentSuggestion[];
  totalAnalyzed: number;
  processingTime: number;
  lastAnalyzedAt: Date;
}

// Predefined content actions
export const CONTENT_ACTIONS: ContentAction[] = [
  {
    id: 'thread',
    label: 'Turn into Thread',
    icon: '🧵',
    description: 'Break this content into a Twitter/X thread',
    platforms: ['X/Twitter'],
    minContentLength: 100,
    maxContentLength: 2000
  },
  {
    id: 'caption',
    label: 'Summarize as Caption',
    icon: '📝',
    description: 'Create a concise social media caption',
    platforms: ['Instagram', 'LinkedIn', 'X/Twitter'],
    minContentLength: 50
  },
  {
    id: 'newsletter',
    label: 'Newsletter Intro',
    icon: '📧',
    description: 'Use as newsletter introduction',
    platforms: ['LinkedIn', 'Web'],
    minContentLength: 100
  },
  {
    id: 'quote',
    label: 'Extract Quote',
    icon: '💬',
    description: 'Pull out key quotes for sharing',
    platforms: ['X/Twitter', 'LinkedIn', 'Instagram'],
    minContentLength: 20
  },
  {
    id: 'poll',
    label: 'Create Poll',
    icon: '📊',
    description: 'Turn insights into engaging polls',
    platforms: ['X/Twitter', 'LinkedIn', 'Instagram'],
    minContentLength: 30
  },
  {
    id: 'story',
    label: 'Story Highlight',
    icon: '⭐',
    description: 'Create story content from key points',
    platforms: ['Instagram', 'LinkedIn'],
    minContentLength: 50
  }
];

// Content conversion tracking interface
export interface ContentConversionStats {
  totalConversions: number;
  weeklyConversions: number;
  conversionsByType: Record<string, number>;
  lastConversionDate?: Date;
}

// Smart recap data interface
export interface SmartRecapData {
  totalSaves: number;
  weeklyGrowth: number;
  topPlatform: {
    name: string;
    count: number;
    percentage: number;
  } | null;
  topCategory: {
    name: string;
    count: number;
  } | null;
  timeSaved: string;
  contentConversions: ContentConversionStats;
  aiInsight: string | null;
  suggestedActions: string[];
}
