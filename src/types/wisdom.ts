export interface WisdomQuote {
  id: string;
  text: string;
  author?: string;
  source?: string;
  source_link?: string; // URL link to original post if extracted from saved content
  categories: string[];
  tags: string[];
  createdAt: string;
  shownAt?: string;
  isFavorite?: boolean;
  relatedPostIds?: string[]; // IDs of posts that influenced this quote
  extractedFrom?: 'post' | 'ai-generated'; // Track if wisdom came from user's posts or AI
  batchId?: string; // ID of the 5-day batch this wisdom belongs to
  dayIndex?: number; // Position in the 5-day cycle (0-4)
  // Add any additional metadata fields as needed
}

export interface UserWisdomPreferences {
  lastShownDate?: string; // ISO date string of when the last quote was shown
  preferredCategories?: string[];
  preferredAuthors?: string[];
  // Add any user preferences for quote selection
}

export interface WisdomInsight {
  // This can be used to store insights about user's saved posts
  // that help in quote selection
  topCategories: {
    category: string;
    count: number;
  }[];
  commonTags: string[];
  lastAnalyzedAt: string;
}

// Example of how to structure the storage
// This would be stored in chrome.storage.local
// 5-day wisdom batch interface
export interface WisdomBatch {
  id: string;
  quotes: WisdomQuote[]; // Array of 5 quotes
  createdAt: string;
  startDate: string; // ISO date string when this batch starts
  currentDayIndex: number; // Current day in the cycle (0-4)
  isActive: boolean;
}

export interface WisdomStorage {
  quotes: WisdomQuote[];
  preferences: UserWisdomPreferences;
  insights: WisdomInsight;
  currentBatch?: WisdomBatch; // Current active 5-day batch
  batchHistory: WisdomBatch[]; // Previous batches for reference
}
