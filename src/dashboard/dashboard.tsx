import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import '../styles/platformIcons.css';
import ReactDOM from 'react-dom/client';
import '../index.css'; // Import Tailwind CSS
import '../styles/notely-theme.css'; // Import Notely theme
import {
  getSavedPosts,
  getAllCategories,
  updatePostDetails // <-- Import (replaces updatePostCategory)
} from '../storage';
import { Post, Platform, IUserFrontend } from '../types';
import LoginModal from '../components/LoginModal'; // Import the modal
import DailyWisdom from '../components/DailyWisdom';
import StorageUsage from '../components/StorageUsage';
import MindstreamSidebar from '../components/MindstreamSidebar';
import MindstreamWidgets from '../components/MindstreamWidgets';
import ThreadCard from '../components/ThreadCard';
import { PremiumBadge } from '../components/PremiumBadge';

import SimpleCategorySelector from '../components/SimpleCategorySelector';

import CategorySummary from '../components/CategorySummary';
import CategoryChatWithPosts from '../components/CategoryChatWithPosts';
import ThemeToggle from '../components/ThemeToggle';
import { LocaleProvider } from '../contexts/LocaleProvider';
import { useTranslation } from '../hooks/useTranslation';
import { downloadPostCardAsImage, copyPostCardAsImage } from '../utils/postCardCapture';
import { toast } from '../utils/toast';
import { initializeTheme } from '../utils/themeUtils';
import { formatForDisplay } from '../utils/formatUtils';
import { migrationService } from '../services/migrationService';
import { syncUnifiedDataFromCloud } from '../services/cloudSyncService';
import { performSemanticSearch, debounce } from '../services/semanticSearchService';
// import domtoimage from 'dom-to-image-more'; // Remove import
// import html2canvas from 'html2canvas'; // Remove import

// --- NEW IMPORTS ---
import PostViewerFullScreen, { PostWithAIData } from '../components/PostViewerFullScreen';
import ProxyImage from '../components/ProxyImage'; // Import ProxyImage component for handling CORS issues
import { ImageSwiper } from '../components/ui/image-swiper'; // Import ImageSwiper component for multi-image posts
import { getImage } from '../utils/imageUtils'; // Import the unified image retrieval function
import TagsCategoriesWidget from '../components/TagsCategoriesWidget';
import { useLocale, LocaleProvider } from '../contexts/LocaleContext';
import { useAuth } from '../web/context/WebAuthContext';
// Thread utilities are no longer needed since we display posts individually
// --- END NEW IMPORTS ---

// Define API_URL if not already present or ensure it's correctly defined
const API_URL = 'https://api.notely.social';

// --- Placeholder Components ---

// --- NEW: Confirmation Modal Component ---
interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText,
}) => {
  const { t } = useTranslation();
  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
      onClick={handleOverlayClick}
    >
      <div className="notely-card bg-notely-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg p-6 w-full max-w-md mx-auto" onClick={(e) => e.stopPropagation()}>
        <h3 className="text-lg font-semibold mb-2 text-notely-text-primary">{title}</h3>
        <p className="text-sm text-notely-text-muted mb-6">{message}</p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="px-4 py-2 rounded-md text-sm font-medium text-notely-text-muted bg-notely-surface hover:bg-notely-border focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-notely-border transition-colors"
          >
            {cancelText || 'Cancel'}
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onConfirm();
            }}
            className="px-4 py-2 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
          >
            {confirmText || t('post.delete')}
          </button>
        </div>
      </div>
    </div>
  );
};
// --- END: Confirmation Modal Component ---

interface ThreadCardProps {
  threadPosts: Post[];
  firstPost: Post;
  onDelete: (postId: string) => void;
  onOpenDetails: () => void;
  t: (key: string) => string;
}

interface PostCardProps {
  post: Post;
  onDelete: (postId: string) => void;
  onOpenDetails: (postId: string) => void; // Prop to handle opening the detail view
  isForCapture?: boolean; // Add prop to indicate capture mode
  t: (key: string) => string; // Translation function
}

// Helper to format timestamp (can be expanded)
const formatTimestamp = (isoString: string | undefined | null): string => {
  // 1. Check if input string is provided
  if (!isoString) {
    return "Unknown date";
  }

  // 2. Try parsing the date
  const date = new Date(isoString);

  // 3. Check if the parsed date is valid
  if (isNaN(date.getTime())) {
    console.warn("Invalid date string received by formatTimestamp:", isoString);
    return "Invalid date"; // Return a fallback string for invalid dates
  }

  // 4. Calculate the difference in days (ensure finite result)
  const diffInMs = date.getTime() - Date.now();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // 5. Format only if diffInDays is a finite number
  if (!isFinite(diffInDays)) {
      console.warn("Could not calculate finite date difference for:", isoString);
      return "Some time ago"; // Fallback for non-finite diff
  }

  // 6. Proceed with formatting
  try {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(diffInDays, 'day');
  } catch (error) {
      console.error("Error formatting relative time:", error, "Input:", isoString, "Calculated days:", diffInDays);
      return "Error formatting date"; // Fallback on formatting error
  }
};

// Format number for display (e.g., 1500 -> 1.5K)
const formatNumber = (num: number | undefined): string => {
  if (num === undefined) return '0';
  if (num === 0) return '0';
  if (num < 1000) return num.toString();
  return (num / 1000).toFixed(1) + 'K';
};

// Helper function to get engagement metrics from either stats or interactions
const getEngagementMetrics = (post: Post) => {
  const stats = post.stats;
  const interactions = (post as any).interactions;

  return {
    comments: stats?.comments ?? interactions?.replies ?? 0,
    shares: stats?.shares ?? interactions?.reposts ?? 0,
    likes: stats?.likes ?? interactions?.likes ?? 0,
    views: stats?.views ?? 0
  };
};

// Image Modal Component for full-screen image viewing
interface ImageModalProps {
  imageUrl?: string;
  images?: string[];
  initialIndex?: number;
  alt?: string;
  onClose: () => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ imageUrl, images, initialIndex = 0, alt, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Determine if we're showing multiple images or single image
  const isMultiImage = images && images.length > 1;
  const displayImages = images && images.length > 0 ? images : (imageUrl ? [imageUrl] : []);
  const [currentImageIndex, setCurrentImageIndex] = useState(initialIndex);

  // Check if URL is a base64 data URL
  const isBase64Image = (url: string | null | undefined): boolean => {
    return !!url && url.startsWith('data:image/');
  };

  // Close modal when clicking outside the image
  const handleClickOutside = (e: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (isMultiImage) {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        setCurrentImageIndex(prev => prev > 0 ? prev - 1 : displayImages.length - 1);
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        setCurrentImageIndex(prev => prev < displayImages.length - 1 ? prev + 1 : 0);
      }
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    // Prevent scrolling when modal is open
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };
  }, []);

  if (displayImages.length === 0) {
    return null;
  }

  const currentImageUrl = displayImages[currentImageIndex];

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="relative max-w-full max-h-full" ref={modalRef}>
        {isMultiImage ? (
          // Use ImageSwiper for multiple images
          <div className="max-w-full max-h-[90vh]">
            <ImageSwiper
              images={displayImages}
              className="max-w-full max-h-full"
              aspectRatio="auto"
              showControls={true}
              showDots={true}
              imageClassName="max-w-full max-h-[90vh] object-contain"
            />
          </div>
        ) : (
          // Single image display
          <>
            {isBase64Image(currentImageUrl) ? (
              // Directly render base64 image in modal
              <img
                src={currentImageUrl}
                alt={alt || 'Full size image'}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
                onLoad={() => console.log(`[ImageModal] Base64 image loaded successfully`)}
                onError={(e) => {
                  console.error(`[ImageModal] Base64 image failed to load`);
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : isInstagramUrl(currentImageUrl) ? (
              // Use ProxyImage for Instagram URLs that aren't base64
              <ProxyImage
                src={currentImageUrl}
                alt={alt || 'Full size image'}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
                fallbackSrc={currentImageUrl}
                postId={currentPost?.id || 'unknown'}
                onError={() => console.log(`[ImageModal] ProxyImage error for: ${currentImageUrl.substring(0, 50)}...`)}
                onLoad={() => console.log(`[ImageModal] ProxyImage loaded for: ${currentImageUrl.substring(0, 50)}...`)}
              />
            ) : (
              // Regular image for other platforms
              <img
                src={currentImageUrl}
                alt={alt || 'Full size image'}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
              />
            )}
          </>
        )}

        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors z-10"
          aria-label="Close image modal"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

const ThreadCard: React.FC<ThreadCardProps> = ({ threadPosts, firstPost, onDelete, onOpenDetails, t }) => {
  const currentPlatform: Platform = firstPost.platform;
  const [imageDataUrls, setImageDataUrls] = useState<Record<string, string>>({});

  // Use the first post's data for display
  const displayPost = firstPost;
  
  // Calculate thread stats from all posts
  const totalLikes = threadPosts.reduce((sum, post) => sum + (post.interactions?.likes || 0), 0);
  const totalComments = threadPosts.reduce((sum, post) => sum + (post.interactions?.replies || 0), 0);
  const totalShares = threadPosts.reduce((sum, post) => sum + (post.interactions?.reposts || 0), 0);

  // Get preview text from first post
  const previewText = displayPost.content || displayPost.title || '';
  const truncatedText = previewText.length > 150 ? previewText.substring(0, 150) + '...' : previewText;

  // Get main image from first post
  const mainImageUrl = displayPost.savedImage || (displayPost.media && displayPost.media.length > 0 ? displayPost.media[0].url : null);
  const mainImageSrc = mainImageUrl; // Simplified for thread cards

  const handleCardClick = (e: React.MouseEvent) => {
    // Add brief scale animation to the clicked card
    const target = e.currentTarget as HTMLElement;
    target.style.transform = 'scale(1.02)';
    target.style.transition = 'transform 0.15s ease-out';

    setTimeout(() => {
      target.style.transform = 'scale(1)';
      setTimeout(() => {
        target.style.transform = '';
        target.style.transition = '';
      }, 150);
    }, 100);

    onOpenDetails();
  };

  return (
    <article
      className={`post-card notely-card relative bg-notely-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg hover:-translate-y-2 group break-inside-avoid mb-4 overflow-hidden cursor-pointer notely-filter-transition
      ${currentPlatform === 'X/Twitter' ? 'border-t-[3px] border-t-black' : ''}
      ${currentPlatform === 'LinkedIn' ? 'border-t-[3px] border-t-blue-600' : ''}
      ${currentPlatform === 'Reddit' ? 'border-t-[3px] border-t-orange-500' : ''}
      ${currentPlatform === 'Instagram' ? 'border-t-[3px] border-t-purple-500' : ''}
      ${currentPlatform === 'pinterest' ? 'border-t-[3px] border-t-red-600' : ''}
      ${currentPlatform === 'Web' ? 'border-t-[3px] border-t-green-500' : ''}`}
      onClick={handleCardClick}
      data-post-id={displayPost.id}
    >
      {/* Thread indicator - prominent display */}
      <div className="absolute top-3 right-3 z-10">
        <div className="bg-red-500 text-white text-sm px-3 py-2 rounded-full font-bold flex items-center gap-2 border-2 border-red-600/50 shadow-lg">
          🧵 THREAD
          <span className="bg-white/20 px-2 py-0.5 rounded-full text-xs">
            {threadPosts.length}
          </span>
        </div>
      </div>

      {/* Header with author info */}
      <div className="flex items-center space-x-3 p-4 pb-3">
        <div className="flex-shrink-0">
          <PlatformLogo platform={currentPlatform} className="w-6 h-6" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-notely-text-primary truncate">
              {displayPost.authorName || displayPost.author}
            </span>
            {displayPost.authorHandle && (
              <span className="text-xs text-notely-text-muted truncate">
                @{displayPost.authorHandle}
              </span>
            )}
          </div>
          <div className="text-xs text-notely-text-muted">
            {formatTimestamp(displayPost.timestamp || displayPost.savedAt)}
          </div>
        </div>
      </div>

      {/* Content preview */}
      {truncatedText && (
        <div className="px-4 pb-3">
          <p className="text-sm text-notely-text-secondary leading-relaxed line-clamp-3">
            {truncatedText}
          </p>
        </div>
      )}

      {/* Media preview if available */}
      {mainImageSrc && (
        <div className="px-4 pb-3">
          <div className="relative rounded-lg overflow-hidden bg-gray-100">
            <img
              src={mainImageSrc}
              alt={displayPost.media?.[0]?.alt || 'Thread preview'}
              className="w-full h-32 object-cover"
              loading="lazy"
              onError={(e) => (e.currentTarget.style.display = 'none')}
            />
          </div>
        </div>
      )}

      {/* Thread engagement metrics */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between text-xs text-notely-text-muted">
          <div className="flex items-center space-x-4">
            {totalLikes > 0 && (
              <div className="flex items-center space-x-1">
                <HeartIcon className="w-3 h-3" />
                <span>{formatNumber(totalLikes)}</span>
              </div>
            )}
            {totalComments > 0 && (
              <div className="flex items-center space-x-1">
                <CommentIcon className="w-3 h-3" />
                <span>{formatNumber(totalComments)}</span>
              </div>
            )}
            {totalShares > 0 && (
              <div className="flex items-center space-x-1">
                <ShareIcon className="w-3 h-3" />
                <span>{formatNumber(totalShares)}</span>
              </div>
            )}
          </div>
          <div className="text-xs text-notely-text-tertiary">
            {threadPosts.length} posts
          </div>
        </div>
      </div>

      {/* Tags preview if available */}
      {displayPost.tags && displayPost.tags.length > 0 && (
        <div className="px-4 pb-4">
          <div className="flex flex-wrap gap-1">
            {displayPost.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-notely-surface text-notely-text-secondary text-xs rounded-full"
              >
                #{tag}
              </span>
            ))}
            {displayPost.tags.length > 3 && (
              <span className="px-2 py-1 bg-notely-surface text-notely-text-tertiary text-xs rounded-full">
                +{displayPost.tags.length - 3}
              </span>
            )}
          </div>
        </div>
      )}
    </article>
  );
};

const PostCard: React.FC<PostCardProps> = ({ post, onDelete, onOpenDetails, isForCapture, t }) => {
  const currentPlatform: Platform = post.platform; // Explicitly type the platform
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const [imageDataUrls, setImageDataUrls] = useState<Record<string, string>>({});

  // Debug: Log thread status for each post
  if (post.isThread) {
    console.log(`[PostCard] Rendering thread post ${post.id}:`, { isThread: post.isThread, threadId: post.threadId });
  }

  // Debug: Always log for specific post IDs
  if (post.id === '1930040580010320352' || post.id === '1930040457083687269' || post.id === '1930040146034078061') {
    console.log(`[PostCard] DEBUG specific post ${post.id}:`, {
      isThread: post.isThread,
      threadId: post.threadId,
      threadPosition: post.threadPosition,
      threadLength: post.threadLength
    });
  }
  // Removed confirmation modal state as it's handled at the Dashboard level

  // Debug logging for Reddit posts
  if (post.platform === 'Reddit') {
    console.log(`[PostCard] Reddit post data for ${post.id}:`, {
      id: post.id,
      platform: post.platform,
      author: post.author,
      authorName: post.authorName,
      content: post.content,
      contentLength: post.content?.length,
      textContent: post.textContent,
      permalink: post.permalink,
      fullPost: post
    });
    console.log(`[PostCard] Reddit content preview: "${post.content?.substring(0, 100)}..."`);
  }

  // Visual truncation is now handled purely by CSS line-clamp
  // We only need expansion state for very long posts (more than ~500 chars)
  const EXPANSION_THRESHOLD = 500; // Much higher threshold for expansion button
  const postContentLength = post.content?.length || 0;
  const needsExpansionButton = postContentLength > EXPANSION_THRESHOLD;
  // End of truncation definitions

  // --- Helper functions for image handling ---
  // Check if URL needs special handling for Instagram/Facebook images
  const needsFetching = (url: string | null | undefined): boolean => {
    // Skip fetching if it's a base64 image or if we don't have a URL
    if (!url || url.startsWith('data:image/')) {
      return false;
    }

    // For Instagram avatar URLs, we want to use them directly without fetching
    // Only fetch Instagram/Facebook post image URLs
    if (post.platform === 'Instagram' && url === post.authorAvatar) {
      return false;
    }

    // Only fetch Instagram/Facebook URLs for other cases
    return url.includes('fbcdn.net') || url.includes('instagram');
  };

  // Check if URL is specifically from Instagram
  const isInstagramUrl = (url: string | null | undefined): boolean => {
    return !!url && (url.includes('instagram') || url.includes('fbcdn.net'));
  };

  // Check if URL is a base64 data URL
  const isBase64Image = (url: string | null | undefined): boolean => {
    return !!url && url.startsWith('data:image/');
  };
  // --- End helper functions ---

  // Handle Toggle Expansion
  const toggleExpansion = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    setIsExpanded(!isExpanded);
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await onDelete(post.id);
    } catch (error) {
      console.error('Failed to delete post:', error);
    }
    return false; // Explicitly return false to be absolutely sure
  };

  // useEffect to load images using our unified approach
  useEffect(() => {
    // Skip all fetching if we already have base64 images
    const hasBase64Avatar = post.authorAvatar && post.authorAvatar.startsWith('data:image/');
    const hasBase64MainImage = post.savedImage && post.savedImage.startsWith('data:image/');
    const hasBase64MediaImage = post.media && post.media.length > 0 &&
                               post.media[0].url && post.media[0].url.startsWith('data:image/');

    // If we already have base64 images for both avatar and main image, skip fetching
    if (hasBase64Avatar && (hasBase64MainImage || hasBase64MediaImage)) {
      console.log('[PostCard] Already have base64 images for both avatar and main image, skipping fetch');
      return;
    }

    const loadImages = async () => {
      try {
        // For Instagram posts, we need special handling
        if (post.platform === 'Instagram') {
          console.log('[PostCard] Loading images for Instagram post');

          const loadInstagramImages = async () => {
            try {
              const db = await new Promise<IDBDatabase>((resolve, reject) => {
                const request = indexedDB.open('social-saver-images', 2);
                request.onerror = (event) => reject('Error opening IndexedDB');
                request.onsuccess = (event) => resolve((event.target as IDBOpenDBRequest).result);
              });
  
              if (!db.objectStoreNames.contains('images')) {
                console.warn('[PostCard] No images object store found.');
                db.close();
                return;
              }
  
              const transaction = db.transaction(['images'], 'readonly');
              const store = transaction.objectStore('images');
              const index = store.index('postId'); // Assuming 'postId' index exists
              const getAllRequest = index.getAll(post.id);
  
              getAllRequest.onsuccess = async () => {
                const images = getAllRequest.result;
                if (images && images.length > 0) {
                  const processedImages = await Promise.all(images.map(async (img: any) => {
                    if (img.blob) {
                      return new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onloadend = () => resolve({ ...img, dataUrl: reader.result as string });
                        reader.onerror = () => resolve({ ...img, dataUrl: null });
                        reader.readAsDataURL(img.blob);
                      });
                    }
                    return { ...img, dataUrl: null };
                  }));
  
                  const validImages = processedImages.filter(img => img.dataUrl);
                  
                  const mainImage = validImages.find(img => !img.url.includes('profile') && !img.id.includes('profile'));
                  if (mainImage) {
                    setImageDataUrls(prev => ({ ...prev, [post.media?.[0]?.url || 'main']: mainImage.dataUrl }));
                  }
  
                  const avatarImage = validImages.find(img => img.url.includes('profile') || img.id.includes('profile'));
                  if (avatarImage && post.authorAvatar) {
                    setImageDataUrls(prev => ({ ...prev, [post.authorAvatar || 'avatar']: avatarImage.dataUrl }));
                  }
                }
                db.close();
              };
  
              getAllRequest.onerror = (event) => {
                console.error('[PostCard] Error fetching images from IndexedDB:', event);
                db.close();
              };
            } catch (error) {
              console.error('[PostCard] Error loading Instagram images from IndexedDB:', error);
            }
          };

          loadInstagramImages();
          return; // Return after starting async operation
        }

        // Regular loading process for non-Instagram posts or if Instagram special handling failed

        // Load avatar image if needed - only if it's not already a direct URL
        if (!hasBase64Avatar && needsFetching(post.authorAvatar)) {
          const avatarUrl = post.authorAvatar;
          if (avatarUrl && !imageDataUrls[avatarUrl]) {
            console.log(`[PostCard] Loading avatar image from IndexedDB: ${avatarUrl.substring(0, 50)}...`);
            const avatarData = await getImage(post.id, avatarUrl);
            if (avatarData) {
              console.log('[PostCard] Successfully loaded avatar image');
              setImageDataUrls(prev => ({ ...prev, [avatarUrl]: avatarData }));
            }
          }
        }

        // Load main image if needed
        const mainImageUrl = post.savedImage || (post.media && post.media.length > 0 ? post.media[0].url : null);
        if (!hasBase64MainImage && !hasBase64MediaImage && needsFetching(mainImageUrl)) {
          if (mainImageUrl && !imageDataUrls[mainImageUrl]) {
            console.log(`[PostCard] Loading main image from IndexedDB: ${mainImageUrl.substring(0, 50)}...`);
            const mainImageData = await getImage(post.id, mainImageUrl);
            if (mainImageData) {
              console.log('[PostCard] Successfully loaded main image');
              setImageDataUrls(prev => ({ ...prev, [mainImageUrl]: mainImageData }));
            }
          }
        }
      } catch (error) {
        console.error('[PostCard] Error loading images:', error);
      }
    };

    loadImages();
  }, [post.id, post.authorAvatar, post.savedImage, post.media, post.platform, imageDataUrls]);

  // --- Determine image sources conditionally ---
  // Select the correct avatar URL based on what's available
  const sourceAvatarUrl = post.authorAvatar || post.authorImage; // Prefer authorAvatar, fallback to authorImage

  const needsFetchingAvatar = needsFetching(sourceAvatarUrl);
  const avatarSrc = needsFetchingAvatar
    ? imageDataUrls[sourceAvatarUrl!] // Use data URL if fetched
    : sourceAvatarUrl; // Use original URL directly otherwise

  // --- Prepare media data for single or multi-media display ---
  const hasMultipleMedia = post.media && post.media.length > 1;
  const hasVideo = post.media && post.media.some(item => item.type === 'video');

  // For multi-media posts, prepare all media URLs and types
  const allMediaItems = hasMultipleMedia
    ? post.media!.map((mediaItem, index) => {
        const needsFetchingMedia = mediaItem.type === 'image' ? needsFetching(mediaItem.url) : false;
        const finalUrl = needsFetchingMedia
          ? imageDataUrls[mediaItem.url] || mediaItem.url // Use data URL if available, fallback to original
          : mediaItem.url; // Use original URL directly

        // Debug logging for multi-media posts
        console.log(`[PostCard] Multi-media ${index + 1}:`, {
          type: mediaItem.type,
          originalUrl: mediaItem.url?.substring(0, 50) + '...',
          needsFetching: needsFetchingMedia,
          hasDataUrl: !!imageDataUrls[mediaItem.url],
          finalUrl: finalUrl?.substring(0, 50) + '...'
        });

        return {
          ...mediaItem,
          url: finalUrl
        };
      }).filter(item => item.url && item.url.trim() !== '') // Remove any null/undefined/empty URLs
    : [];

  // For legacy compatibility, extract image URLs for existing ImageSwiper
  const allImageUrls = allMediaItems
    .filter(item => item.type === 'image')
    .map(item => item.url);

  // For single image posts, use existing logic
  const mainImageUrl = post.savedImage || (post.media && post.media.length > 0 ? post.media[0].url : null);
  const needsFetchingMainImage = needsFetching(mainImageUrl);
  const mainImageSrc = needsFetchingMainImage
    ? imageDataUrls[mainImageUrl!] // Use data URL if fetched
    : mainImageUrl; // Use original URL directly otherwise

  const mainImageAlt = (post.media && post.media.length > 0 ? post.media[0].alt : '') || post.altText || '';

  // Determine if we should show the swiper or single media
  const shouldUseSwiper = hasMultipleMedia && allMediaItems.length > 1;
  const finalImageUrls = shouldUseSwiper ? allImageUrls : (mainImageSrc ? [mainImageSrc] : []);
  
  // Get the first video if available
  const firstVideo = post.media && post.media.find(item => item.type === 'video');
  const shouldShowVideo = hasVideo && !shouldUseSwiper; // Show video only if single video post

  // Determine if placeholders should be shown
  // const showAvatarPlaceholder = !avatarSrc; // Unused, removing
  // const showMainImagePlaceholder = !!mainImageUrl && !mainImageSrc; // Unused, removing

  // --- Handle Card Click ---
  const handleCardClick = (e: React.MouseEvent) => {
    if (!isForCapture) { // Only allow opening details if not in capture mode
      // Add brief scale animation to the clicked card
      const target = e.currentTarget as HTMLElement;
      target.style.transform = 'scale(1.02)';
      target.style.transition = 'transform 0.15s ease-out';

      setTimeout(() => {
        target.style.transform = 'scale(1)';
        setTimeout(() => {
          target.style.transform = '';
          target.style.transition = '';
        }, 150);
      }, 100);

      // Open post details (handleOpenPostDetails already handles thread logic)
      onOpenDetails(post.id);
    }
  };

  return (
    <article
      className={`post-card notely-card relative bg-notely-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg hover:-translate-y-2 group break-inside-avoid mb-4 overflow-hidden cursor-pointer notely-filter-transition
      ${currentPlatform === 'X/Twitter' ? 'border-t-[3px] border-t-black' : ''}
      ${currentPlatform === 'LinkedIn' ? 'border-t-[3px] border-t-blue-600' : ''}
      ${currentPlatform === 'Reddit' ? 'border-t-[3px] border-t-orange-500' : ''}
      ${currentPlatform === 'Instagram' ? 'border-t-[3px] border-t-purple-500' : ''}
      ${currentPlatform === 'pinterest' ? 'border-t-[3px] border-t-red-600' : ''}
      ${currentPlatform === 'Web' ? 'border-t-[3px] border-t-green-500' : ''}`}
      onClick={handleCardClick}
      data-post-id={post.id}
    >
      {/* Thread indicator - show if post is part of a thread */}
      {!isForCapture && post.isThread && (
        <div className="absolute top-2 right-2 z-10">
          <div className="bg-red-500 text-white text-sm px-3 py-2 rounded-full font-bold flex items-center gap-1 border-2 border-red-600/50 shadow-lg">
            🧵 THREAD
          </div>
        </div>
      )}

      <div className={`${currentPlatform === 'pinterest' ? 'notely-breathing-compact-sm' : 'notely-breathing-compact-md'} ${currentPlatform !== 'pinterest' ? 'px-2 sm:px-4' : ''}`}>
        {/* Header: Avatar + Name + Handle + Time - Enhanced hierarchy */}
        {currentPlatform !== 'pinterest' && (
          <div className="flex items-start mb-2 sm:mb-3">
            {/* Avatar section with better spacing */}
            <div className="flex-shrink-0">
              {avatarSrc ? (
                isBase64Image(avatarSrc) ? (
                  // Directly render base64 avatar image
                  <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden shadow-sm border border-gray-100">
                    <img
                      src={avatarSrc}
                      alt={`${post.authorName || 'Author'} avatar`}
                      className="w-full h-full object-cover"
                      onLoad={() => console.log(`[PostCard] Base64 avatar loaded successfully`)}
                      onError={(e) => {
                        console.error(`[PostCard] Base64 avatar failed to load`);
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                ) : isInstagramUrl(sourceAvatarUrl) ? (
                  // For Instagram, use the avatar URL directly without proxying
                  <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden shadow-sm border border-gray-100">
                    <img
                      src={sourceAvatarUrl}
                      alt={`${post.authorName || 'Author'} avatar`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error(`[PostCard] Instagram avatar failed to load directly: ${sourceAvatarUrl?.substring(0, 50)}...`);
                        // Instead of hiding, show a fallback avatar with initials
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.style.display = 'flex';
                          parent.style.alignItems = 'center';
                          parent.style.justifyContent = 'center';
                          parent.style.backgroundColor = '#e0e0e0';
                          parent.style.color = '#666';
                          parent.style.fontWeight = 'bold';
                          parent.style.fontSize = '16px';

                          // Get initials from author name
                          const name = post.authorName || post.authorHandle || 'U';
                          const initials = name.split(' ')
                            .map(part => part.charAt(0))
                            .join('')
                            .substring(0, 2)
                            .toUpperCase();

                          parent.textContent = initials;
                        }
                      }}
                      onLoad={() => console.log(`[PostCard] Instagram avatar loaded directly: ${sourceAvatarUrl?.substring(0, 50)}...`)}
                    />
                  </div>
                ) : (
                  // Render the actual avatar image for non-Instagram sources
                  <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gray-200 bg-cover bg-center shadow-sm border border-gray-100`}
                       style={{ backgroundImage: `url(${avatarSrc})` }}>
                  </div>
                )
              ) : (
                // Render default grey circle placeholder for other platforms (Pinterest avatar section is hidden entirely)
                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gray-200">
                </div>
              )}
            </div>

            {/* Author info with improved hierarchy */}
            <div className={`flex-grow min-w-0 ml-2 sm:ml-2.5`}>
              <div className="flex flex-col space-y-1">
                {/* Author name - larger and more prominent */}
                <div className="flex items-center space-x-2">
                  <span className={`font-semibold text-sm sm:text-base text-notely-text-primary notely-heading ${!isForCapture ? 'truncate' : ''}`}>
                    {post.platform === 'Web' && post.permalink ? new URL(post.permalink).hostname.replace('www.', '') : (post.authorName || post.authorHandle || 'Unknown Author')}
                  </span>
                </div>

                {/* Handle and timestamp - smaller and secondary */}
                <div className="flex items-center space-x-2 text-xs sm:text-sm">
                    {post.platform !== 'Web' && post.authorHandle && (
                      <span className={`text-notely-text-muted notely-body ${!isForCapture ? 'truncate' : ''}`}>
                        @{post.authorHandle}
                      </span>
                    )}
                    {(post.timestamp || post.savedAt) && (
                      <>
                        {post.platform !== 'Web' && post.authorHandle && <span className="text-notely-text-tertiary">·</span>}
                        <span className="text-notely-text-muted hover:text-notely-text-secondary notely-body transition-colors">
                          {formatTimestamp(post.timestamp || post.savedAt)}
                        </span>
                      </>
                    )}

                    {/* Domain/source for web content - even smaller */}
                    {post.platform === 'Web' && post.permalink && false && (
                      <>
                        <span className="text-notely-text-tertiary">·</span>
                        <span className="text-xs text-notely-text-muted">
                          {new URL(post.permalink).hostname.replace('www.', '')}
                        </span>
                      </>
                    )}
                  </div>
              </div>
            </div>
          </div>
        )}

        {/* Media - Show image(s) and video(s) with improved spacing */}
        {currentPlatform !== 'pinterest' && (finalImageUrls.length > 0 || shouldShowVideo) && (
          <div className={`mt-2 mb-2 sm:mt-3 sm:mb-3`}>
            {shouldShowVideo && firstVideo ? (
              // Single video display
              <div
                className="relative rounded-xl overflow-hidden bg-gray-100 shadow-sm hover:shadow-md transition-shadow duration-200 flex items-center justify-center"
                style={{
                  aspectRatio: currentPlatform === 'Instagram' ? '1 / 1' :
                               currentPlatform === 'LinkedIn' ? '16 / 9' :
                               '16 / 9',
                  minHeight: currentPlatform === 'Instagram' ? 'min(200px, 40vw)' : 'min(150px, 30vw)'
                }}
              >
                <video
                  src={firstVideo.url}
                  className="w-full h-full object-cover"
                  controls
                  preload="metadata"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click when interacting with video controls
                  }}
                  onLoadedMetadata={() => console.log(`[PostCard] 🎥 Video loaded: ${firstVideo.url?.substring(0, 50)}...`)}
                  onError={(e) => {
                    console.error(`[PostCard] 🎥 Video failed to load: ${firstVideo.url?.substring(0, 50)}...`);
                    e.currentTarget.style.display = 'none';
                  }}
                >
                  <div className="flex items-center justify-center h-full bg-gray-200 text-gray-500">
                    Video not supported
                  </div>
                </video>
                {/* Video overlay indicator */}
                <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                <span>Video</span>
                </div>
              </div>
            ) : shouldUseSwiper ? (
              // Multi-image carousel using ImageSwiper
              <ImageSwiper
                images={finalImageUrls}
                className="rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200"
                imageClassName={currentPlatform === 'Reddit' ? 'max-h-[400px]' : undefined}
                aspectRatio={currentPlatform === 'Instagram' ? 'square' : 'video'}
                onImageClick={(index) => {
                  // Open PostViewerFullScreen instead of ImageModal
                  console.log(`[PostCard] ImageSwiper clicked - index: ${index}, opening PostViewerFullScreen`);
                  onOpenDetails(post.id);
                }}
              />
            ) : (
              // Single image display (existing logic)
              <div
                className="relative rounded-xl overflow-hidden bg-gray-100 shadow-sm hover:shadow-md transition-shadow duration-200 flex items-center justify-center"
                style={{
                  aspectRatio: currentPlatform === 'Instagram' ? '1 / 1' :
                               currentPlatform === 'LinkedIn' ? '16 / 9' :
                               '16 / 9',
                  minHeight: currentPlatform === 'Instagram' ? 'min(200px, 40vw)' : 'min(150px, 30vw)'
                }}
              >
                {isBase64Image(mainImageSrc) ? (
                  // Directly render base64 image data - centered with flexbox
                  <img
                    src={mainImageSrc}
                    alt={mainImageAlt}
                    className={`w-full h-full ${isForCapture ? 'object-contain' : 'object-cover'}`}
                    style={{ objectPosition: 'center center', objectFit: isForCapture ? 'contain' : 'cover' }}
                    loading="lazy"
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onLoad={() => console.log(`[PostCard] Base64 image loaded successfully`)}
                    onError={(e) => {
                      console.error(`[PostCard] Base64 image failed to load`);
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                ) : isInstagramUrl(mainImageUrl) ? (
                  // Use ProxyImage for Instagram URLs that aren't base64 - centered with flexbox
                  <ProxyImage
                    src={mainImageSrc || ''}
                    alt={mainImageAlt}
                    className={`w-full h-full ${isForCapture ? 'object-contain' : 'object-cover'}`}
                    style={{ objectPosition: 'center center', objectFit: isForCapture ? 'contain' : 'cover' }}
                    fallbackSrc={mainImageUrl || ''}
                    postId={post.id}
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onError={() => console.log(`[PostCard] ProxyImage error for: ${mainImageUrl?.substring(0, 50)}...`)}
                    onLoad={() => console.log(`[PostCard] ProxyImage loaded for: ${mainImageUrl?.substring(0, 50)}...`)}
                  />
                ) : (
                  // Regular image for other platforms - centered with flexbox
                  <img
                    src={mainImageSrc}
                    alt={mainImageAlt}
                    className={`w-full h-full ${isForCapture ? 'object-contain' : 'object-cover'}`}
                    style={{ objectPosition: 'center center', objectFit: isForCapture ? 'contain' : 'cover' }}
                    loading="lazy"
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onError={(e) => (e.currentTarget.style.display = 'none')}
                  />
                )}
              </div>
            )}
          </div>
        )}

        {/* Content for non-Pinterest (shown after images now) */}
        {currentPlatform !== 'pinterest' && post.content && (
          <div className="px-4 py-3 font-normal text-[14px] text-notely-text-primary leading-relaxed">
            {/* Proper JavaScript truncation instead of broken CSS line-clamp */}
            {isExpanded ? post.content : 
              post.content.length > 300 ? 
                post.content.substring(0, 300).trim() + '...' : 
                post.content
            }
            {post.content.length > 300 && (
              <button onClick={(e) => {
                e.stopPropagation();
                onOpenDetails(post.id);
              }} className="text-notely-accent font-medium hover:underline ml-1 notely-filter-transition">
                {t('dashboard.showMore')}
              </button>
            )}
          </div>
        )}

        {/* Pinterest-specific render approach */}
        {currentPlatform === 'pinterest' && finalImageUrls.length > 0 && (
          <div className="w-full rounded-xl overflow-hidden">
            {shouldUseSwiper ? (
              // Multi-image carousel for Pinterest using ImageSwiper
              <div className="relative w-full">
                <ImageSwiper
                  images={finalImageUrls}
                  className="rounded-xl"
                  aspectRatio="auto"
                  onImageClick={(index) => {
                    // Open PostViewerFullScreen instead of ImageModal
                    console.log(`[PostCard] Pinterest ImageSwiper clicked - index: ${index}, opening PostViewerFullScreen`);
                    onOpenDetails(post.id);
                  }}
                />
                {post.content && post.content.trim() && (
                  <div className="absolute bottom-2 left-2 right-2 bg-white/80 backdrop-blur-md p-2 rounded-lg text-xs line-clamp-2">
                    {post.content}
                  </div>
                )}
              </div>
            ) : (
              // Single image for Pinterest (existing logic)
              <div className="relative w-full">
                {isBase64Image(mainImageSrc) ? (
                  // Directly render base64 image data for Pinterest - centered
                  <img
                    src={mainImageSrc}
                    alt={mainImageAlt}
                    className="w-full object-cover object-center rounded-xl"
                    loading="lazy"
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onLoad={() => console.log(`[PostCard] Pinterest base64 image loaded successfully`)}
                    onError={(e) => {
                      console.error(`[PostCard] Pinterest base64 image failed to load`);
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                ) : isInstagramUrl(mainImageUrl) ? (
                  // Use ProxyImage for Instagram URLs that aren't base64 - centered
                  <ProxyImage
                    src={mainImageSrc || ''}
                    alt={mainImageAlt}
                    className="w-full object-cover object-center rounded-xl"
                    fallbackSrc={mainImageUrl || ''}
                    postId={post.id}
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onError={() => console.log(`[PostCard] Pinterest ProxyImage error for: ${mainImageUrl?.substring(0, 50)}...`)}
                    onLoad={() => console.log(`[PostCard] Pinterest ProxyImage loaded for: ${mainImageUrl?.substring(0, 50)}...`)}
                  />
                ) : (
                  // Regular image for other platforms - centered
                  <img
                    src={mainImageSrc}
                    alt={mainImageAlt}
                    className="w-full object-cover object-center rounded-xl"
                    loading="lazy"
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onError={(e) => (e.currentTarget.style.display = 'none')}
                  />
                )}
                {post.content && post.content.trim() && (
                  <div className="absolute bottom-2 left-2 right-2 bg-white/80 backdrop-blur-md p-2 rounded-lg text-xs line-clamp-2">
                    {post.content}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Engagement Metrics - Enhanced with consistent alignment */}
        {/* Hide engagement metrics for Pinterest as they don't provide meaningful data */}
        {currentPlatform !== 'pinterest' && (() => {
          const metrics = getEngagementMetrics(post);
          const hasMetrics = metrics.comments > 0 || metrics.shares > 0 || metrics.likes > 0 || (post.views && post.views > 0);

          return hasMetrics ? (
            <div className="mt-3 pt-3 pb-2 border-t border-[#2F2F2F]">
              <div className="flex items-center justify-between min-h-[24px]">
                {/* Left side - Statistics with consistent spacing */}
                <div className="flex items-center space-x-4">
                  {/* Comments */}
                  {metrics.comments > 0 ? (
                    <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-sky transition-all duration-200 cursor-pointer">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <CommentIcon className="w-4 h-4 group-hover:scale-110 transition-transform" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(metrics.comments)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <CommentIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">0</span>
                    </div>
                  )}

                  {/* Shares */}
                  {metrics.shares > 0 ? (
                    <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-mint transition-all duration-200 cursor-pointer">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <ShareIcon className="w-4 h-4 group-hover:scale-110 transition-transform" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(metrics.shares)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <ShareIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">0</span>
                    </div>
                  )}

                  {/* Likes */}
                  {metrics.likes > 0 ? (
                    <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-coral transition-all duration-200 cursor-pointer">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <HeartIcon className="w-4 h-4 group-hover:scale-110 transition-transform" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(metrics.likes)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <HeartIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">0</span>
                    </div>
                  )}

                  {/* Views - only show if available */}
                  {post.views && post.views > 0 && (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <EyeIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(post.views)}</span>
                    </div>
                  )}
                </div>

                {/* Right side - Platform indicator with hover glow */}
                <div className="flex items-center">
                  <div className="flex items-center space-x-2 px-2 py-1 rounded-full bg-notely-surface/50 border border-notely-border/30 hover:border-notely-border hover:bg-notely-surface transition-all duration-200">
                    <PlatformLogo platform={currentPlatform} className="w-3.5 h-3.5 opacity-70" />
                    <span className="text-xs font-medium text-notely-text-muted">{currentPlatform}</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Always show placeholder row for consistent spacing */
            <div className="mt-3 pt-3 pb-2 border-t border-[#2F2F2F]">
              <div className="flex items-center justify-between min-h-[24px]">
                <div className="flex items-center space-x-4 opacity-40">
                  <div className="flex items-center space-x-1.5 text-notely-text-muted">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <CommentIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium">0</span>
                  </div>
                  <div className="flex items-center space-x-1.5 text-notely-text-muted">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <ShareIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium">0</span>
                  </div>
                  <div className="flex items-center space-x-1.5 text-notely-text-muted">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <HeartIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium">0</span>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="flex items-center space-x-2 px-2 py-1 rounded-full bg-notely-surface/50 border border-notely-border/30">
                    <PlatformLogo platform={currentPlatform} className="w-3.5 h-3.5 opacity-70" />
                    <span className="text-xs font-medium text-notely-text-muted">{currentPlatform}</span>
                  </div>
                </div>
              </div>
            </div>
          );
        })()}

        {/* Quick Action Buttons - Enhanced hover states */}
        {!isForCapture && (
          <div className="notely-post-footer">
            <div className="px-4 py-3 border-t border-[#2F2F2F] bg-notely-surface/30">
              <div className="flex items-center justify-between">
                {/* Left side - Primary actions */}
                <div className="flex items-center space-x-2">
                  {/* Delete Button */}
                  <button
                    onClick={handleDelete}
                    className="group p-2 rounded-lg bg-notely-surface/50 hover:bg-red-500/10 border border-notely-border/30 hover:border-red-500/30 text-notely-text-muted hover:text-red-500 transition-all duration-200 hover:scale-105"
                    title="Delete Post"
                  >
                    <div className="w-4 h-4 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </div>
                  </button>

                  {/* Original Link */}
                  {post.permalink && (
                    <a
                      href={post.permalink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group p-2 rounded-lg bg-notely-surface/50 hover:bg-notely-sky/10 border border-notely-border/30 hover:border-notely-sky/30 text-notely-text-muted hover:text-notely-sky transition-all duration-200 hover:scale-105"
                      onClick={(e) => e.stopPropagation()}
                      title="View original post"
                    >
                      <div className="w-4 h-4 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </a>
                  )}
                </div>

                {/* Right side - Secondary actions */}
                <div className="flex items-center space-x-2">
                  {/* Download as Image Button */}
                  <button
                    onClick={async (e) => {
                      e.stopPropagation();
                      try {
                        const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                        if (postCardElement) {
                          toast.info('Generating image...', { duration: 1000 });
                          await downloadPostCardAsImage(postCardElement, post.id, post.platform, post);
                          toast.success('Post card downloaded successfully!');
                        }
                      } catch (error) {
                        console.error('Failed to download post card as image:', error);
                        toast.error('Failed to download post card. Please try again.');
                      }
                    }}
                    className="group p-2 rounded-lg bg-notely-surface/50 hover:bg-orange-500/10 border border-notely-border/30 hover:border-orange-500/30 text-notely-text-muted hover:text-orange-500 transition-all duration-200 hover:scale-105"
                    title="Download post card as image"
                  >
                    <div className="w-4 h-4 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                    </div>
                  </button>

                  {/* Copy as Image Button */}
                  <button
                    onClick={async (e) => {
                      e.stopPropagation();
                      try {
                        const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                        if (postCardElement) {
                          toast.info('Generating image...', { duration: 1000 });
                          await copyPostCardAsImage(postCardElement, post.platform, post);
                          toast.success('Post card copied to clipboard!');
                        }
                      } catch (error) {
                        console.error('Failed to copy post card as image:', error);
                        toast.error('Failed to copy post card. Please try again.');
                      }
                    }}
                    className="group p-2 rounded-lg bg-notely-surface/50 hover:bg-notely-mint/10 border border-notely-border/30 hover:border-notely-mint/30 text-notely-text-muted hover:text-notely-mint transition-all duration-200 hover:scale-105"
                    title="Copy post card as image to clipboard"
                  >
                    <div className="w-4 h-4 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

      </div>

      {/* Image Modal */}
      {(selectedImage || selectedImages.length > 0) && (
        <ImageModal
          imageUrl={selectedImage}
          images={selectedImages}
          initialIndex={selectedImageIndex}
          alt={mainImageAlt}
          onClose={() => {
            setSelectedImage(null);
            setSelectedImages([]);
            setSelectedImageIndex(0);
          }}
        />
      )}

      {/* No confirmation modal at the individual card level */}
    </article>
  );
};





// --- NEW: Platform Logo Component ---
const PlatformLogo: React.FC<{ platform: Platform, className?: string }> = ({ platform, className = "w-4 h-4" }) => { // Default to w-4 h-4
  // PlatformLogo ONLY passes down the className for size/layout.
  // Color (fill/stroke) is handled by individual logo components below.
  switch (platform) {
    case 'X/Twitter': return <div title="X/Twitter"><XLogo className={className} /></div>;
    case 'LinkedIn': return <div title="LinkedIn"><LinkedInLogo className={className} /></div>;
    case 'Reddit': return <div title="Reddit"><RedditLogo className={className} /></div>;
    case 'Instagram': return <div title="Instagram"><InstagramLogo className={className} /></div>;
    case 'pinterest': return <div title="Pinterest"><PinterestLogo className={className} /></div>;
    case 'Web': return <div title="Web"><WebLogo className={className} /></div>;
    // Add cases for other platforms if needed
    default: return null;
  }
};

// --- Individual Logo SVG Components - Accept className Prop AND set their own color ---

// X (Twitter) Logo SVG
const XLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg viewBox="0 0 24 24" className={`fill-current ${className}`} xmlns="http://www.w3.org/2000/svg">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231 5.45-6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

// LinkedIn Logo SVG - Using Bootstrap Icons definition
const LinkedInLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-current ${className}`} viewBox="0 0 16 16">
    <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248S2.4 3.226 2.4 3.934c0 .694.521 1.248 1.327 1.248zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016l.016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225z"/>
  </svg>
);

// Reddit Logo SVG - Using correct Bootstrap Icons definition
const RedditLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-current ${className}`} viewBox="0 0 16 16">
    <path d="M6.167 8a.83.83 0 0 0-.83.83c0 .459.372.84.83.831a.831.831 0 0 0 0-1.661m1.843 3.647c.315 0 1.403-.038 1.976-.611a.23.23 0 0 0 0-.306.213.213 0 0 0-.306 0c-.353.363-1.126.487-1.67.487-.545 0-1.308-.124-1.671-.487a.213.213 0 0 0-.306 0 .213.213 0 0 0 0 .306c.564.563 1.652.61 1.977.61zm.992-2.807c0 .458.373.83.831.83s.83-.381.83-.83a.831.831 0 0 0-1.66 0z"/>
    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0m-3.828-1.165c-.315 0-.602.124-.812.325-.801-.573-1.9-.945-3.121-.993l.534-2.501 1.738.372a.83.83 0 1 0 .83-.869.83.83 0 0 0-.744.468l-1.938-.41a.2.2 0 0 0-.153.028.2.2 0 0 0-.086.134l-.592 2.788c-1.24.038-2.358.41-3.17.992-.21-.2-.496-.324-.81-.324a1.163 1.163 0 0 0-.478 2.224q-.03.17-.029.353c0 1.795 2.091 3.256 4.669 3.256s4.668-1.451 4.668-3.256c0-.114-.01-.238-.029-.353.401-.181.688-.592.688-1.069 0-.65-.525-1.165-1.165-1.165"/>
  </svg>
);

const InstagramLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} /* Removed stroke/fill attributes here */ viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
  </svg>
);

// Pinterest Logo SVG - Using the new SVG provided by user
const PinterestLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={`fill-current ${className}`} viewBox="0 0 579.148 579.148" xmlns="http://www.w3.org/2000/svg" xmlSpace="preserve">
      <path d="M434.924,38.847C390.561,12.954,342.107,0.01,289.574,0.01c-52.54,0-100.992,12.944-145.356,38.837
        C99.854,64.741,64.725,99.87,38.837,144.228C12.944,188.597,0,237.049,0,289.584c0,58.568,15.955,111.732,47.883,159.486
        c31.922,47.768,73.771,83.08,125.558,105.949c-1.01-26.896,0.625-49.137,4.902-66.732l37.326-157.607
        c-6.285-12.314-9.425-27.645-9.425-45.999c0-21.365,5.404-39.217,16.212-53.538c10.802-14.333,24.003-21.5,39.59-21.5
        c12.564,0,22.246,4.143,29.034,12.448c6.787,8.292,10.184,18.727,10.184,31.292c0,7.797-1.451,17.289-4.334,28.47
        c-2.895,11.187-6.665,24.13-11.31,38.837c-4.651,14.701-7.98,26.451-9.994,35.252c-3.525,15.33-0.63,28.463,8.672,39.4
        c9.295,10.936,21.616,16.4,36.952,16.4c26.898,0,48.955-14.951,66.176-44.865c17.217-29.914,25.826-66.236,25.826-108.973
        c0-32.925-10.617-59.701-31.859-80.312c-21.242-20.606-50.846-30.918-88.795-30.918c-42.486,0-76.862,13.642-103.123,40.906
        c-26.267,27.277-39.401,59.896-39.401,97.84c0,22.625,6.414,41.609,19.229,56.941c4.272,5.029,5.655,10.428,4.149,16.205
        c-0.508,1.512-1.511,5.281-3.017,11.309c-1.505,6.029-2.515,9.934-3.017,11.689c-2.014,8.049-6.787,10.564-14.333,7.541
        c-19.357-8.043-34.064-21.99-44.113-41.85c-10.055-19.854-15.08-42.852-15.08-68.996c0-16.842,2.699-33.685,8.103-50.527
        c5.404-16.842,13.819-33.115,25.264-48.832c11.432-15.698,25.135-29.596,41.102-41.659c15.961-12.069,35.38-21.738,58.256-29.04
        c22.871-7.283,47.51-10.93,73.904-10.93c35.693,0,67.744,7.919,96.146,23.751c28.402,15.839,50.086,36.329,65.043,61.463
        c14.951,25.135,22.436,52.026,22.436,80.692c0,37.705-6.541,71.641-19.607,101.807c-13.072,30.166-31.549,53.855-55.43,71.072
        c-23.887,17.215-51.035,25.826-81.445,25.826c-15.336,0-29.664-3.58-42.986-10.748c-13.33-7.166-22.503-15.648-27.528-25.453
        c-11.31,44.486-18.097,71.018-20.361,79.555c-4.78,17.852-14.584,38.457-29.413,61.836c26.897,8.043,54.296,12.062,82.198,12.062
        c52.534,0,100.987-12.943,145.35-38.83c44.363-25.895,79.492-61.023,105.387-105.393c25.887-44.365,38.838-92.811,38.838-145.352
        c0-52.54-12.951-100.985-38.838-145.355C514.422,99.87,479.287,64.741,434.924,38.847z"/>
  </svg>
);

// Web Logo SVG - Globe icon for web content
const WebLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="2" y1="12" x2="22" y2="12"></line>
    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
  </svg>
);

// Engagement Icons - SVG icons for post metrics
const CommentIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
);

const ShareIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M17 1l4 4-4 4"></path>
    <path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
    <path d="M7 23l-4-4 4-4"></path>
    <path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
  </svg>
);

const HeartIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
  </svg>
);

const EyeIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
    <circle cx="12" cy="12" r="3"></circle>
  </svg>
);

type ViewMode = Platform | 'All' | 'Mindstream';

const PlatformSelector: React.FC<{
  onSelect: (platform: ViewMode) => void,
  selectedPlatform: ViewMode,
  onCategoryViewToggle?: (platform: ViewMode) => void,
  enabledPlatforms?: Record<string, boolean>
}> = ({ onSelect, selectedPlatform, onCategoryViewToggle, enabledPlatforms = {} }) => {
  // Ensure these values exactly match the Platform type in types.ts and background.ts
  const allPlatforms: ViewMode[] = ['Mindstream', 'All', 'X/Twitter', 'LinkedIn', 'Reddit', 'Instagram', 'pinterest', 'Web'];
  
  // Filter platforms based on enabled integrations
  const platforms = allPlatforms.filter(platform => {
    // Always show Mindstream and All tabs
    if (platform === 'Mindstream' || platform === 'All') return true;
    
    // For other platforms, check if they're enabled in settings
    // If enabledPlatforms is empty (not loaded yet), show all platforms
    return Object.keys(enabledPlatforms).length === 0 || enabledPlatforms[platform] === true;
  });

  // Function to get platform-specific styles with enhanced visual hierarchy
  const getPlatformStyles = (platform: string, isSelected: boolean) => {
    const baseStyles = 'rounded-lg flex items-center justify-center px-4 py-2.5 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 relative transform';

    if (!isSelected) {
      return `${baseStyles} bg-notely-surface text-notely-text-secondary border border-gray-500/20 hover:bg-notely-dark-hover hover:text-notely-text-primary hover:shadow-notely-md hover:scale-105 focus:ring-notely-border/30 dark-mode-platform-btn`;
    }

    // Selected state styles with enhanced visual feedback and rounded design
    switch (platform) {
      case 'X/Twitter':
        return `${baseStyles} bg-black text-white border-none shadow-notely-md font-semibold hover:bg-gray-800 hover:shadow-notely-lg hover:scale-105 focus:ring-gray-500/30`;
      case 'LinkedIn':
        return `${baseStyles} bg-[#0077b5] text-white border-none shadow-notely-md font-semibold hover:bg-[#005885] hover:shadow-notely-lg hover:scale-105 focus:ring-blue-400/30`;
      case 'Reddit':
        return `${baseStyles} bg-[#FF4500] text-white border-none shadow-notely-md font-semibold hover:bg-[#e03d00] hover:shadow-notely-lg hover:scale-105 focus:ring-orange-400/30`;
      case 'Instagram':
        return `${baseStyles} text-white border-none shadow-notely-md font-semibold bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 hover:shadow-notely-lg hover:scale-105 focus:ring-purple-400/30`;
      case 'pinterest':
        return `${baseStyles} bg-[#E60023] text-white border-none shadow-notely-md font-semibold hover:bg-[#c7001e] hover:shadow-notely-lg hover:scale-105 focus:ring-red-400/30`;
      case 'Web':
        return `${baseStyles} text-white border-none shadow-notely-md font-semibold bg-green-500 hover:bg-green-600 hover:shadow-notely-lg hover:scale-105 focus:ring-green-400/30`;
      case 'Mindstream':
        return `${baseStyles} text-white border border-purple-400/30 dark:border-purple-500/50 shadow-notely-md font-semibold bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 hover:shadow-notely-lg hover:scale-105 focus:ring-purple-400/30`;
      case 'All':
      default:
        return `${baseStyles} text-white border-none shadow-notely-md font-semibold bg-notely-sky hover:bg-blue-600 hover:shadow-notely-lg hover:scale-105 focus:ring-blue-400/30`;
    }
  };

  return (
    <div className="flex flex-wrap gap-2 py-1">
      {platforms.map((platform) => {
        const isSelected = selectedPlatform === platform;

        // Handle logo element for different platform types
        let logoElement = null;
        if (platform === 'Mindstream') {
          logoElement = <span className="text-lg">🧠</span>;
        } else if (platform !== 'All') {
          logoElement = <PlatformLogo platform={platform as Platform} className={`w-4 h-4 ${isSelected ? 'text-white' : 'text-current'}`} />;
        }

        return (
          <button
            key={platform}
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              e.nativeEvent?.preventDefault();
              e.nativeEvent?.stopPropagation();
              e.nativeEvent?.stopImmediatePropagation();
              console.log('[PlatformSelector] Platform clicked:', platform);
              console.log('[PlatformSelector] Event prevented and stopped');
              onSelect(platform);
              // Trigger category view for specific platforms
              if (platform !== 'All' && platform !== 'Mindstream' && onCategoryViewToggle) {
                onCategoryViewToggle(platform);
              }
              return false;
            }}
            className={getPlatformStyles(platform, isSelected)}
          >
            {logoElement && <span className={`mr-1.5 ${platform === 'Mindstream' ? 'text-lg' : ''}`}>{logoElement}</span>}
            {platform === 'pinterest' ? 'Pinterest' : platform === 'All' ? 'All' : platform === 'Mindstream' ? 'Mindstream' : platform}
          </button>
        );
      })}
    </div>
  );
};

// Add cog icon component
const SettingsIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="3"></circle>
    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
  </svg>
);

// --- Main Dashboard Component ---

// Constants for storage keys
const PLATFORM_INTEGRATIONS_KEY = 'platformIntegrations';

function DashboardContent() {
  const { t } = useTranslation();
  const [posts, setPosts] = useState<Post[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<Post[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<ViewMode>('Mindstream');
  const [availableCategoriesForFilter, setAvailableCategoriesForFilter] = useState<string[]>([]);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<Post[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [enabledPlatforms, setEnabledPlatforms] = useState<Record<string, boolean>>({});
  const [platformsLoaded, setPlatformsLoaded] = useState<boolean>(false);

    const loadPlatformIntegrations = async () => {
      try {
        const result = await chrome.storage.local.get([PLATFORM_INTEGRATIONS_KEY]);
        const savedIntegrations = result[PLATFORM_INTEGRATIONS_KEY] || {};
        setEnabledPlatforms(savedIntegrations);
        setPlatformsLoaded(true);
      } catch (error) {
        console.error('Error loading platform integrations:', error);
        // Set default values if there's an error
        setEnabledPlatforms({
          'X/Twitter': true,
          'LinkedIn': true,
          'Reddit': true,
          'Instagram': true,
          'pinterest': true,
          'Web': true
        });
        setPlatformsLoaded(true);
      }
    };

  const loadAndMergePosts = async (token: string | null, userId: string | null) => {
    // The actual implementation of merging local and cloud posts would go here.
    const localPosts = await getSavedPosts();
    setPosts(localPosts);
  };

  const loadWisdomBatch = async () => {
    // This function will contain the logic to load the daily wisdom
  };

  // Load platform integrations settings from storage
  useEffect(() => {
    loadPlatformIntegrations();

    // Listen for storage changes to update platform integrations in real-time
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes[PLATFORM_INTEGRATIONS_KEY]) {
        const newIntegrations = changes[PLATFORM_INTEGRATIONS_KEY].newValue || {};
        setEnabledPlatforms(newIntegrations);
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    // Cleanup listener on unmount
    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        setSearchError(null);
        setIsSearching(false);
        return;
      }

      if (!isLoggedIn) {
        setSearchError('Please log in to search your posts');
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      // Auto-switch to "All" tab only when searching from Mindstream tab
      if (selectedPlatform === 'Mindstream') {
        // Save current scroll position so we can restore after the DOM updates
        prevScrollYRef.current = window.scrollY;
        setSelectedPlatform('All');
      }

      setIsSearching(true);
      setSearchError(null);

      try {
        const results = await performSemanticSearch(query);

        if (results && results.results) {
          // Store search type for UI indicators
          (window as any).lastSearchType = results.searchType;
          // Merge search results with local posts to get complete data
          const searchPosts = results.results.map(result => {
            const searchPost = result.post as any; // Type assertion for search result

            // Try multiple matching strategies since local and cloud IDs are different
            let localPost = posts.find(p => p.id === searchPost.id); // Direct ID match

            if (!localPost) {
              // Try matching by permalink (most reliable)
              localPost = posts.find(p => p.permalink === searchPost.permalink);
              // Found by permalink
            }

            if (!localPost) {
              // Try matching by content + platform + author (fallback)
              localPost = posts.find(p =>
                p.platform === searchPost.platform &&
                p.authorName === searchPost.authorName &&
                p.content === searchPost.content
              );
              // Found by content match
            }

            if (localPost) {

              // Merge local post data with search result, prioritizing search result fields
              const mergedPost = {
                ...localPost,
                ...searchPost,
                // Ensure we keep important local fields that might be missing from search
                authorAvatar: searchPost.authorAvatar || localPost.authorAvatar,
                authorImage: searchPost.authorImage || localPost.authorImage,
                savedImage: searchPost.savedImage || localPost.savedImage,
                timestamp: searchPost.timestamp || localPost.timestamp,
                authorHandle: searchPost.authorHandle || localPost.authorHandle,
              } as Post;

              return mergedPost;
            } else {
              // If no local post found, use search result as-is (might be missing some fields)
              return searchPost as Post;
            }
          });

          setSearchResults(searchPosts);
          setSearchError(null);
        } else {
          setSearchResults([]);
          setSearchError('No results found');
        }
      } catch (error) {
        setSearchResults([]);
        setSearchError(error instanceof Error ? error.message : 'Search failed');
      } finally {
        setIsSearching(false);
      }
    }, 500),
    [isLoggedIn, selectedPlatform, posts]
  );

  // Handle search query changes
  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);



  // Simple category and tag filtering
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [categoryPostCounts, setCategoryPostCounts] = useState<Record<string, number>>({});
  // Preserve previous window scroll position between category switches
  const prevScrollYRef = useRef<number>(0);



  // Extract categories from posts filtered by selected platform and count posts per category
  useEffect(() => {
    const categories = new Set<string>();
    const categoryCounts: Record<string, number> = {};

    // Filter posts by selected platform first
    let postsToProcess = posts;
    if (selectedPlatform !== 'All' && selectedPlatform !== 'Mindstream') {
      postsToProcess = posts.filter(post => {
        const postPlatformLower = post.platform?.toLowerCase();
        const selectedPlatformLower = selectedPlatform.toLowerCase();
        return postPlatformLower === selectedPlatformLower;
      });
    }

    // Process posts to extract categories and count them
    postsToProcess.forEach(post => {
      if (post.categories && Array.isArray(post.categories)) {
        // Track which categories we've counted for this post
        const countedCategories = new Set<string>();
        
        post.categories.forEach(category => {
          if (category && category.trim() !== '') {
            // Add to unique categories set
            categories.add(category);
            
            // Only count each category once per post to avoid inflated counts
            if (!countedCategories.has(category)) {
              categoryCounts[category] = (categoryCounts[category] || 0) + 1;
              countedCategories.add(category);
            }
          }
        });
      }
    });

    // Sort categories by post count (descending), then alphabetically as a tiebreaker
    const sortedCategories = Array.from(categories).sort((a, b) => {
      const countA = categoryCounts[a] || 0;
      const countB = categoryCounts[b] || 0;
      
      // First sort by count (descending)
      if (countA !== countB) {
        return countB - countA;
      }
      
      // If counts are equal, sort alphabetically
      return a.localeCompare(b);
    });
    
    setAvailableCategories(sortedCategories);
    setCategoryPostCounts(categoryCounts);
    console.log(`[Dashboard] Extracted categories for platform ${selectedPlatform}:`, sortedCategories);
    console.log('[Dashboard] Category post counts:', categoryCounts);

    // Clear selected category if it's no longer available for the current platform
    if (selectedCategory && !categories.has(selectedCategory)) {
      setSelectedCategory(null);
    }
  }, [posts, selectedPlatform]);



  const [loggedInUser, setLoggedInUser] = useState<IUserFrontend | null>(null);
  const [isUserDataLoading, setIsUserDataLoading] = useState<boolean>(true); // Start true

  // --- NEW STATE FOR POST VIEWER ---
  const [selectedPostForViewer, setSelectedPostForViewer] = useState<PostWithAIData | null>(null);

  // Thread navigation state
  const [currentThreadPosts, setCurrentThreadPosts] = useState<PostWithAIData[] | null>(null);
  const [currentThreadIndex, setCurrentThreadIndex] = useState(0);
  // --- END NEW STATE ---

  // --- DELETE CONFIRMATION DIALOG STATE ---
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState<boolean>(false);
  const [postToDelete, setPostToDelete] = useState<string | null>(null);
  // --- END DELETE CONFIRMATION DIALOG STATE ---

  // const contentRef = useRef<HTMLDivElement>(null); // Removed unused ref

  // Function to merge and deduplicate posts from cloud and local storage
  const mergeAndDeduplicatePosts = (cloudPosts: Post[], localPosts: Post[]): Post[] => {
    console.log('[Dashboard] mergeAndDeduplicatePosts: Starting merge process');
    console.log('[Dashboard] mergeAndDeduplicatePosts: Cloud posts by platform:', cloudPosts.reduce((acc, p) => {
      acc[p.platform] = (acc[p.platform] || 0) + 1;
      return acc;
    }, {} as Record<string, number>));
    console.log('[Dashboard] mergeAndDeduplicatePosts: Local posts by platform:', localPosts.reduce((acc, p) => {
      acc[p.platform] = (acc[p.platform] || 0) + 1;
      return acc;
    }, {} as Record<string, number>));

    // Debug: Check for thread posts in input
    const localThreadPosts = localPosts.filter(p => p.isThread);
    const cloudThreadPosts = cloudPosts.filter(p => p.isThread);
    console.log('[Dashboard] mergeAndDeduplicatePosts: Local thread posts:', localThreadPosts.length);
    console.log('[Dashboard] mergeAndDeduplicatePosts: Cloud thread posts:', cloudThreadPosts.length);

    // Create a map to track posts by their unique identifiers
    const postMap = new Map<string, Post>();

    // Add cloud posts first (they take priority)
    cloudPosts.forEach(post => {
      if (post) {
        // Use originalPostId as id if id is missing (for cloud posts)
        const postId = post.id || post.originalPostId || post._id;
        if (postId) {
          const normalizedPost = { ...post, id: postId };
          const key = getPostKey(normalizedPost);
          console.log(`[Dashboard] mergeAndDeduplicatePosts: Adding cloud post ${postId} (${post.platform}) with key: ${key}`);
          postMap.set(key, { ...normalizedPost, source: 'cloud' });
        }
      }
    });

    // Add local posts, but only if they don't already exist from cloud
    localPosts.forEach(post => {
      if (post && post.id) {
        const key = getPostKey(post);

        // Debug: Log thread posts specifically
        if (post.isThread) {
          console.log(`[Dashboard] mergeAndDeduplicatePosts: Processing local thread post ${post.id}:`, {
            isThread: post.isThread,
            threadId: post.threadId,
            threadPosition: post.threadPosition,
            threadLength: post.threadLength,
            key: key
          });
        }

        if (!postMap.has(key)) {
          // Check if this is a local post that might have been uploaded to cloud
          const cloudMatch = cloudPosts.find(cp =>
            cp.originalPostId === post.id ||
            cp.permalink === post.permalink ||
            (cp.authorName === post.authorName && cp.timestamp === post.timestamp)
          );

          if (!cloudMatch) {
            console.log(`[Dashboard] mergeAndDeduplicatePosts: Adding local post ${post.id} (${post.platform}) with key: ${key}`);
            if (post.isThread) {
              console.log(`[Dashboard] mergeAndDeduplicatePosts: Adding thread post to map:`, {
                isThread: post.isThread,
                threadId: post.threadId
              });
            }
            postMap.set(key, { ...post, source: 'local' });
          } else {
            console.log(`[Dashboard] mergeAndDeduplicatePosts: Found cloud match for local post ${post.id}, checking if cloud post exists in map`);
            // Mark the cloud post as synced since we found a local match
            const cloudKey = getPostKey(cloudMatch);
            if (postMap.has(cloudKey)) {
              console.log(`[Dashboard] mergeAndDeduplicatePosts: Cloud post ${cloudMatch.id} exists in map, marking as synced`);
              const existingCloudPost = postMap.get(cloudKey)!;

              // IMPORTANT: Preserve local fields that might be newer than cloud
              const mergedPost = { ...existingCloudPost, source: 'synced' };

              // Preserve thread metadata from local post if cloud post doesn't have it
              if (post.isThread && !existingCloudPost.isThread) {
                console.log(`[Dashboard] mergeAndDeduplicatePosts: Preserving thread metadata from local post ${post.id}`);
                mergedPost.isThread = post.isThread;
                mergedPost.threadId = post.threadId;
                mergedPost.threadPosition = post.threadPosition;
                mergedPost.threadLength = post.threadLength;
              }

              // Preserve notes from local post if it's newer or cloud doesn't have it
              if (post.notes && (!existingCloudPost.notes || post.notes !== existingCloudPost.notes)) {
                console.log(`[Dashboard] mergeAndDeduplicatePosts: Preserving notes from local post ${post.id}`);
                mergedPost.notes = post.notes;
              }

              postMap.set(cloudKey, mergedPost);
            } else {
              console.log(`[Dashboard] mergeAndDeduplicatePosts: Cloud post ${cloudMatch.id} NOT in map, adding local post instead`);
              if (post.isThread) {
                console.log(`[Dashboard] mergeAndDeduplicatePosts: Adding thread post (cloud not in map):`, {
                  isThread: post.isThread,
                  threadId: post.threadId
                });
              }
              postMap.set(key, { ...post, source: 'local' });
            }
          }
        } else {
          console.log(`[Dashboard] mergeAndDeduplicatePosts: Post ${post.id} already exists in map`);
          // Check if we need to preserve local data (thread metadata or notes)
          const existingPost = postMap.get(key)!;
          let needsUpdate = false;
          const updatedPost = { ...existingPost };

          if (post.isThread) {
            console.log(`[Dashboard] mergeAndDeduplicatePosts: THREAD POST already exists - checking if we need to preserve thread metadata`);
            if (!existingPost.isThread) {
              console.log(`[Dashboard] mergeAndDeduplicatePosts: Existing post lacks thread metadata, preserving from local post`);
              updatedPost.isThread = post.isThread;
              updatedPost.threadId = post.threadId;
              updatedPost.threadPosition = post.threadPosition;
              updatedPost.threadLength = post.threadLength;
              updatedPost.source = 'synced' as const;
              needsUpdate = true;
            } else {
              console.log(`[Dashboard] mergeAndDeduplicatePosts: Existing post already has thread metadata`);
            }
          }

          // Check if we need to preserve notes from local post
          if (post.notes && (!existingPost.notes || post.notes !== existingPost.notes)) {
            console.log(`[Dashboard] mergeAndDeduplicatePosts: Preserving notes from local post ${post.id} (already exists case)`);
            updatedPost.notes = post.notes;
            updatedPost.source = 'synced' as const;
            needsUpdate = true;
          }

          if (needsUpdate) {
            postMap.set(key, updatedPost);
          }
        }
      }
    });

    const mergedPosts = Array.from(postMap.values());
    console.log('[Dashboard] mergeAndDeduplicatePosts: Final merged posts by platform:', mergedPosts.reduce((acc, p) => {
      acc[p.platform] = (acc[p.platform] || 0) + 1;
      return acc;
    }, {} as Record<string, number>));
    console.log('[Dashboard] mergeAndDeduplicatePosts: Merged', mergedPosts.length, 'unique posts');

    // Debug: Check for thread posts in final result
    const finalThreadPosts = mergedPosts.filter(p => p.isThread);
    console.log('[Dashboard] mergeAndDeduplicatePosts: Final thread posts:', finalThreadPosts.length);
    if (finalThreadPosts.length > 0) {
      console.log('[Dashboard] mergeAndDeduplicatePosts: Sample final thread post:', finalThreadPosts[0]);
    }

    return mergedPosts;
  };

  // Helper function to generate a unique key for a post
  const getPostKey = (post: Post): string => {
    // Use multiple identifiers to create a unique key
    const id = post.id || post._id?.toString() || '';
    const permalink = post.permalink || '';
    const platform = post.platform || '';

    // Create a composite key
    return `${platform}:${id}:${permalink}`;
  };

  // Define initiateLocalToCloudSync early to avoid reference errors
  const initiateLocalToCloudSync = async () => {
    console.log('[Dashboard] initiateLocalToCloudSync: Checking for local posts to sync...');
    const localPostsToSync = await getSavedPosts(); // From '../storage'

    if (localPostsToSync && localPostsToSync.length > 0) {
      console.log(`[Dashboard] initiateLocalToCloudSync: Found ${localPostsToSync.length} local posts. Attempting to sync...`);
      // For now, let's try to sync them one by one without complex queueing first
      // We'll rely on the backend to handle duplicates (409 conflict)
      // A more robust solution would involve checking if a post *needs* syncing (e.g., no cloud ID yet)

      const tokenResult = await new Promise<{ authToken?: string }>(resolve =>
        chrome.storage.local.get(['authToken'], result => resolve(result as { authToken?: string }))
      );
      const currentToken = tokenResult?.authToken;

      if (!currentToken) {
        console.warn('[Dashboard] initiateLocalToCloudSync: No auth token found. Cannot sync local posts.');
        return;
      }

      for (const post of localPostsToSync) {
        // We need to ensure the post object sent to SAVE_POST_CLOUD is what the background script expects.
        // The raw post from getSavedPosts() might be slightly different from what content.ts sends.
        // For now, assume it's compatible or background script's generateAnalyzedPost handles it.
        console.log(`[Dashboard] initiateLocalToCloudSync: Syncing post ${post.id} to cloud.`);
        chrome.runtime.sendMessage(
          {
            action: 'SAVE_POST_CLOUD',
            data: post, // Send the local post data
            token: currentToken
          },
          (response) => {
            if (chrome.runtime.lastError) {
              console.error(`[Dashboard] initiateLocalToCloudSync: Error syncing post ${post.id}:`, chrome.runtime.lastError.message);
              // Handle error, maybe retry later or notify user
            } else if (response?.status === 'success' || response?.status === 'duplicate') {
              console.log(`[Dashboard] initiateLocalToCloudSync: Post ${post.id} synced/already exists. Status: ${response.status}`);
              // Optionally, update local post with cloud ID or mark as synced
            } else {
              console.warn(`[Dashboard] initiateLocalToCloudSync: Failed to sync post ${post.id}. Response:`, response);
              // Handle failure
            }
          }
        );
        // Add a small delay to avoid overwhelming the background script/backend
        await new Promise(resolve => setTimeout(resolve, 500)); // 0.5 second delay between sync attempts
      }
      console.log('[Dashboard] initiateLocalToCloudSync: Finished sync attempt for all local posts.');
      refreshPosts(); // Refresh dashboard to show newly synced posts from cloud
    } else {
      console.log('[Dashboard] initiateLocalToCloudSync: No local posts found to sync.');
    }
  };

  // --- MODIFIED: loadInitialData ---
  const [isLoadingData, setIsLoadingData] = useState(false);
  const loadInitialData = async () => {
    // Prevent multiple concurrent calls
    if (isLoadingData) {
      console.log('[ADMIN DEBUG] loadInitialData already in progress, skipping...');
      return;
    }
    
    console.log('[ADMIN DEBUG] loadInitialData started');
    setIsLoadingData(true);
    setIsUserDataLoading(true);
    
    // Add a timeout to prevent infinite hanging
    const timeoutId = setTimeout(() => {
      console.error('[ADMIN DEBUG] loadInitialData TIMEOUT - forcing completion');
      setIsUserDataLoading(false);
      setIsLoggedIn(false);
      setLoggedInUser(null);
      setIsLoadingData(false);
    }, 10000); // 10 second timeout
    
    try {
      console.log('[ADMIN DEBUG] Getting token from storage...');
      const tokenData = await new Promise<{ token?: string; authToken?: string }>(
        (resolve) =>
          chrome.storage.local.get(['token', 'authToken'], (result) =>
            resolve(result as { token?: string; authToken?: string })
          )
      );
      const currentToken = tokenData.authToken || tokenData.token;
      console.log('[ADMIN DEBUG] Token retrieved:', currentToken ? 'Yes' : 'No');

      let fetchedPosts: Post[] = [];
      let fetchedCategories: string[] = [];

      if (currentToken) {
        console.log('[ADMIN DEBUG] Has token, setting logged in and fetching user data...');
        setIsLoggedIn(true);
        // Fetch user data
        try {
          console.log('[ADMIN DEBUG] Calling /auth/me API...');
          // Add timeout to prevent hanging
          const controller = new AbortController();
          const fetchTimeoutId = setTimeout(() => {
            console.log('[ADMIN DEBUG] /auth/me API call timed out, aborting...');
            controller.abort();
          }, 5000); // 5 second timeout
          
          const userResponse = await fetch(`${API_URL}/auth/me`, {
            headers: { 'Authorization': `Bearer ${currentToken}` },
            signal: controller.signal
          });
          clearTimeout(fetchTimeoutId);
          console.log('[ADMIN DEBUG] /auth/me response status:', userResponse.status);
          if (userResponse.ok) {
            const userData: IUserFrontend = await userResponse.json();
            setLoggedInUser(userData);
            // Save user profile to chrome storage for authService
            await chrome.storage.local.set({ user_profile: userData });
            console.log('[ADMIN DEBUG] User profile loaded:', { email: userData.email, role: userData.role });
          } else {
            console.error('[Dashboard] Failed to fetch user data:', userResponse.status);
            // Clear authentication state directly instead of calling handleLogout to avoid infinite loop
            setIsLoggedIn(false);
            setLoggedInUser(null);
            await chrome.storage.local.remove(['token', 'authToken', 'user_profile']);
            console.log('[Dashboard] Auth state cleared due to failed user fetch');
          }
        } catch (error) {
          if (error.name === 'AbortError') {
            console.error('[ADMIN DEBUG] /auth/me API call was aborted due to timeout');
          } else {
          console.error('[Dashboard] Error fetching user data:', error);
          }
          // Clear authentication state directly instead of calling handleLogout to avoid infinite loop
          setIsLoggedIn(false);
          setLoggedInUser(null);
          await chrome.storage.local.remove(['token', 'authToken', 'user_profile']);
          console.log('[Dashboard] Auth state cleared due to error');
        }

        // Fetch posts from both cloud and local storage for logged-in users
        try {
            console.log('[Dashboard] Logged in. Fetching posts from cloud and local storage...');

            // Fetch cloud posts
            let cloudPosts: Post[] = [];
            try {
                const postsResponse = await fetch(`${API_URL}/api/posts`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` },
                });
                if (postsResponse.ok) {
                    cloudPosts = await postsResponse.json();
                    console.log('[Dashboard] loadInitialData: Successfully parsed', cloudPosts.length, 'posts from cloud');
                } else {
                    console.error('[Dashboard] Failed to fetch posts from cloud:', postsResponse.status);
                }
            } catch (cloudError) {
                console.error('[Dashboard] Error fetching posts from cloud:', cloudError);
            }

            // Fetch local posts
            let localPosts: Post[] = [];
            try {
                localPosts = await getSavedPosts();
                console.log('[Dashboard] loadInitialData: Successfully fetched', localPosts.length, 'posts from local storage');
            } catch (localError) {
                console.error('[Dashboard] Error fetching posts from local storage:', localError);
            }

            // Merge and deduplicate posts
            const mergedPosts = mergeAndDeduplicatePosts(cloudPosts, localPosts);
            console.log('[Dashboard] loadInitialData: Merged posts - Cloud:', cloudPosts.length, 'Local:', localPosts.length, 'Final:', mergedPosts.length);

            // Validate merged posts
            console.log('[Dashboard] loadInitialData: Starting validation of merged posts');
            const validPosts = mergedPosts.filter(p => {
                if (!p) {
                    console.warn('[Dashboard] loadInitialData: Filtering out null/undefined post object.');
                    return false;
                }

                const hasPlatform = p.platform && typeof p.platform === 'string';
                const hasPermalink = p.permalink && typeof p.permalink === 'string';

                let idToUse = p.id;
                if (p._id && (typeof p._id === 'string' || typeof p._id === 'object')) {
                    if (!(typeof idToUse === 'string' && idToUse.trim() !== '')) {
                        idToUse = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
                    }
                }
                const hasValidStringId = typeof idToUse === 'string' && idToUse.trim() !== '';

                // Special logging for Pinterest posts
                if (typeof p.platform === 'string' && p.platform.toLowerCase() === 'pinterest') {
                    console.log(`[Dashboard] loadInitialData: Validating Pinterest post ${p.id}:`, {
                        hasValidStringId,
                        hasPlatform,
                        hasPermalink,
                        platform: p.platform,
                        permalink: p.permalink,
                        id: p.id,
                        fullPost: p
                    });
                }

                if (!hasValidStringId) {
                    console.warn(`[Dashboard] loadInitialData: Filtering out post due to missing or invalid ID. Post data (raw):`, JSON.parse(JSON.stringify(p)));
                    return false;
                }
                if (!hasPlatform) {
                    console.warn(`[Dashboard] loadInitialData: Filtering out post (ID: ${idToUse}) due to missing or invalid platform. Post data:`, JSON.parse(JSON.stringify(p)));
                    return false;
                }
                if (!hasPermalink) {
                    console.warn(`[Dashboard] loadInitialData: Filtering out post (ID: ${idToUse}) due to missing or invalid permalink. Post data:`, JSON.parse(JSON.stringify(p)));
                    return false;
                }
                return true; // All checks passed
            });

            fetchedPosts = validPosts.map(p => {
                let finalId = p.id;
                if (!(typeof finalId === 'string' && finalId.trim() !== '') && p._id) {
                    finalId = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
                }
                return {
                    ...p,
                    id: finalId,
                    _id: p._id,
                    source: p.source || (p._id ? 'cloud' : 'local') // Add source metadata
                };
            });

            if (fetchedPosts.length < mergedPosts.length && mergedPosts.length > 0) {
                console.warn('[Dashboard] loadInitialData: Filtered out', mergedPosts.length - fetchedPosts.length, 'of', mergedPosts.length, 'posts due to missing/invalid ID, platform, or permalink.');
            }
            console.log('[Dashboard] loadInitialData: Final validated posts:', fetchedPosts.length);
        } catch (error) {
            console.error('[Dashboard] Error fetching and merging posts:', error);
        }
        // TODO: Fetch categories & tags from cloud if they are user-specific
        // For now, using local as they might be global or locally managed for now.
        console.log('[ADMIN DEBUG] Fetching categories...');
        fetchedCategories = await getAllCategories();
        console.log('[ADMIN DEBUG] Categories fetched:', fetchedCategories.length);

      } else {
        console.log('[ADMIN DEBUG] No token, handling not logged in state...');
        setIsLoggedIn(false);
        setLoggedInUser(null);
        console.log('[Dashboard] Not logged in. Fetching posts locally...');
        fetchedPosts = await getSavedPosts();
        fetchedCategories = await getAllCategories();
        console.log('[Dashboard] Fetched posts locally:', fetchedPosts.length);
      }

      console.log('[ADMIN DEBUG] About to set posts and categories...');
      console.log('[Dashboard] Setting posts:', fetchedPosts.map(p => ({ id: p.id, platform: p.platform, source: p.source })));
      console.log('[Dashboard] Posts with thread data:', fetchedPosts.filter(p => p.isThread));
      console.log('[Dashboard] Sample thread post:', fetchedPosts.find(p => p.isThread));
      setPosts(fetchedPosts);
      // setFilteredPosts(fetchedPosts); // This is handled by the useEffect for filtering
      setAvailableCategoriesForFilter(fetchedCategories);
      console.log('[ADMIN DEBUG] Posts and categories set successfully');

    } catch (error) {
      console.error("[Dashboard] Error loading initial data:", error);
      // Ensure some default state if everything fails
      setPosts([]);
      setAvailableCategoriesForFilter([]);
      // Clear auth state if there's a critical error
      setIsLoggedIn(false);
      setLoggedInUser(null);
    } finally {
      clearTimeout(timeoutId); // Clear the timeout since we completed normally
      console.log('[ADMIN DEBUG] loadInitialData finished, setting isUserDataLoading to false');
      setIsUserDataLoading(false);
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    // Initialize theme
    initializeTheme();

    // Add global navigation prevention for debugging
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      console.log('[Dashboard] history.pushState called with:', args);
      return originalPushState.apply(this, args);
    };

    history.replaceState = function(...args) {
      console.log('[Dashboard] history.replaceState called with:', args);
      return originalReplaceState.apply(this, args);
    };

    // Listen for popstate events
    const handlePopState = (e: PopStateEvent) => {
      console.log('[Dashboard] popstate event:', e);
    };

    window.addEventListener('popstate', handlePopState);

    // Initialize unified storage migration and cloud sync
    migrationService.runMigrations().then(() => {
      console.log('[Dashboard] Storage migration completed, loading initial data...');
      loadInitialData();

      // Initialize unified data sync after migration
      setTimeout(() => {
        syncUnifiedDataFromCloud().catch(error => {
          console.log('[Dashboard] Initial unified data sync failed (this is normal if offline):', error);
        });
      }, 2000);
    }).catch(error => {
      console.error('[Dashboard] Storage migration failed, loading initial data anyway:', error);
      loadInitialData();
    });

    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }, areaName: string) => {
      if (areaName === 'local' || areaName === 'sync') { // Listen to both local and sync
        let needsRefresh = false;
        let authChanged = false;

        if (changes.token) { // Token change means auth state might have changed
          authChanged = true;
        }
        // Assuming 'savedPosts' might be a key used by older local saves,
        // 'localSavedPosts' for explicit local saves, and cloud sync would also trigger a refresh.
        // The key 'posts' itself in storage is not standard here, rather 'savedPosts'.
        if (changes.savedPosts || changes.localSavedPosts || changes.allPosts) { // Monitor 'allPosts' if backend syncs to this key
          needsRefresh = true;
        }
        if (changes.categories) {
          // Refresh categories if they change
          getAllCategories().then(setAvailableCategoriesForFilter);
        }

        if (authChanged) {
          console.log('[Dashboard] Auth token changed, re-loading initial data...');
          setLoggedInUser(null); // Reset user to trigger fetch if new token
          loadInitialData(); // Re-run full load to check auth and fetch user/posts
        } else if (needsRefresh) {
          console.log('[Dashboard] Relevant storage changed, refreshing posts data...');
          refreshPosts(); // Just refresh post data if auth didn't change
        }
      }
    };
    chrome.storage.onChanged.addListener(handleStorageChange);

    const handleAuthMessage = (event: MessageEvent) => {
      if (event.data?.type === 'AUTH_SUCCESS' && event.data?.token) {
        chrome.storage.local.set({ token: event.data.token, authToken: event.data.token, userInfo: {} }, () => {
            // The storage listener (authChanged) will now trigger loadInitialData
            console.log('[Dashboard] AUTH_SUCCESS message received, token and authToken stored.');
            // The storage listener (authChanged) will trigger loadInitialData by itself.
            // We can call initiateLocalToCloudSync here directly after auth success.
            initiateLocalToCloudSync();
        });
        setIsLoginModalOpen(false);
      } else if (event.data?.type === 'AUTH_FAILURE') {
         console.error('[Dashboard] Received auth failure via postMessage:', event.data.error);
      }
    };
    window.addEventListener('message', handleAuthMessage);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
      window.removeEventListener('message', handleAuthMessage);
      window.removeEventListener('popstate', handlePopState);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, []); // Main useEffect runs once on mount

  // --- MODIFIED: refreshPosts with useCallback ---
  const refreshPosts = useCallback(async () => {
    console.log('[Dashboard] refreshPosts: Initiating post refresh.');
    try {
      const tokenResult = await new Promise<{ token?: string }>(resolve =>
        chrome.storage.local.get(['token'], result => resolve(result as { token?: string }))
      );
      const currentToken = tokenResult?.token;
      let refreshedPosts: Post[] = [];

      if (currentToken) {
        console.log(`[Dashboard] refreshPosts: User is logged in (token found). Fetching from cloud and local storage. Token: ${currentToken.substring(0, 20)}...`);

        // Fetch cloud posts
        let cloudPosts: Post[] = [];
        try {
          const fetchUrl = `${API_URL}/api/posts`;
          console.log(`[Dashboard] refreshPosts: Fetch URL: ${fetchUrl}`);
          const response = await fetch(fetchUrl, {
            headers: { 'Authorization': `Bearer ${currentToken}` },
          });
          console.log(`[Dashboard] refreshPosts: Cloud fetch response status: ${response.status}, statusText: ${response.statusText}`);
          if (response.ok) {
            cloudPosts = await response.json();
            console.log(`[Dashboard] refreshPosts: Successfully parsed ${cloudPosts.length} posts from cloud response`);
          } else {
            const errorText = await response.text().catch(() => "Failed to get error text from response");
            console.error(`[Dashboard] refreshPosts: Failed to refresh posts from cloud. Status: ${response.status}. Response text:`, errorText);
          }
        } catch (cloudError) {
          console.error(`[Dashboard] refreshPosts: Error fetching cloud posts:`, cloudError);
        }

        // Fetch local posts
        let localPosts: Post[] = [];
        try {
          localPosts = await getSavedPosts();
          console.log(`[Dashboard] refreshPosts: Fetched ${localPosts.length} posts from local storage`);
        } catch (localError) {
          console.error(`[Dashboard] refreshPosts: Error fetching local posts:`, localError);
        }

        // Merge and deduplicate posts
        const mergedPosts = mergeAndDeduplicatePosts(cloudPosts, localPosts);
        console.log(`[Dashboard] refreshPosts: Merged posts - Cloud: ${cloudPosts.length}, Local: ${localPosts.length}, Final: ${mergedPosts.length}`);

        // Validate and normalize merged posts
        const validIdPosts = mergedPosts.filter(p => {
          if (!p) {
            console.warn('[Dashboard] refreshPosts: Filtering out null/undefined post object.');
            return false;
          }

          const hasPlatform = p.platform && typeof p.platform === 'string';
          const hasPermalink = p.permalink && typeof p.permalink === 'string';

          let idToUse = p.id;
          if (p._id && (typeof p._id === 'string' || typeof p._id === 'object')) {
              // If p.id is not a valid string, try to use p._id (or its string representation)
              if (!(typeof idToUse === 'string' && idToUse.trim() !== '')) {
                  idToUse = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
              }
          }
          const hasValidStringId = typeof idToUse === 'string' && idToUse.trim() !== '';

          if (!hasValidStringId) {
            console.warn(`[Dashboard] refreshPosts: Filtering out post due to missing or invalid ID. Post data (raw):`, JSON.parse(JSON.stringify(p)));
            return false;
          }
          if (!hasPlatform) {
            console.warn(`[Dashboard] refreshPosts: Filtering out post (ID: ${idToUse}) due to missing or invalid platform. Post data:`, JSON.parse(JSON.stringify(p)));
            return false;
          }
          if (!hasPermalink) {
            console.warn(`[Dashboard] refreshPosts: Filtering out post (ID: ${idToUse}) due to missing or invalid permalink. Post data:`, JSON.parse(JSON.stringify(p)));
            return false;
          }
          return true; // All checks passed
        });

        // Map MongoDB format to frontend Post format if needed
        const normalizedPosts = validIdPosts.map(p => {
          let finalId = p.id;
          // If p.id is not a valid string, but p._id is, derive ID from p._id
          if (!(typeof finalId === 'string' && finalId.trim() !== '') && p._id) {
              finalId = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
          }
          // Ensure the final object has a valid 'id' field for the frontend.
          return {
            ...p,
            id: finalId,
            _id: p._id, // keep original _id for reference if needed
            source: p.source || (p._id ? 'cloud' : 'local') // Add source metadata
          };
        });

        if (normalizedPosts.length < mergedPosts.length && mergedPosts.length > 0) { // Only log if there were posts to begin with
              console.warn(`[Dashboard] refreshPosts: Filtered out ${mergedPosts.length - normalizedPosts.length} of ${mergedPosts.length} posts due to missing/invalid ID, platform, or permalink.`);
        }
        refreshedPosts = normalizedPosts;
        console.log(`[Dashboard] refreshPosts: After ID validation and normalization, ${refreshedPosts.length} posts remain.`);
      } else {
        console.log("[Dashboard] refreshPosts: User not logged in (no token). Fetching locally...");
        refreshedPosts = await getSavedPosts();
        console.log(`[Dashboard] refreshPosts: Fetched ${refreshedPosts.length} posts locally.`);
      }
      console.log(`[Dashboard] refreshPosts: Calling setPosts with ${refreshedPosts.length} posts.`);
      console.log('[Dashboard] refreshPosts: Posts by platform:', refreshedPosts.reduce((acc, p) => {
        acc[p.platform] = (acc[p.platform] || 0) + 1;
        return acc;
      }, {} as Record<string, number>));
      setPosts(refreshedPosts);
    } catch (error) {
      console.error("[Dashboard] refreshPosts: General error during refresh:", error);
      setPosts([]); // Clear posts on error or show a message
    }
  }, [setPosts]); // Dependencies: setPosts. BACKEND_URL and getSavedPosts are stable.

  // Effect for listening to messages from background script
  useEffect(() => {
    const messageListener = (message: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
      if (message.action === 'REFRESH_DASHBOARD_FROM_CLOUD') {
        refreshPosts(); // Calling the memoized version of refreshPosts
        sendResponse({ status: 'ok', message: 'Dashboard refresh triggered by REFRESH_DASHBOARD_FROM_CLOUD.' });
        return true; // Indicate async response potential

      } else {
        console.log('[Dashboard] Message listener: Received unhandled message action:', message.action);
      }
    };
    console.log('[Dashboard] Adding message listeners for dashboard refresh.');
    chrome.runtime.onMessage.addListener(messageListener);
    return () => {
      console.log('[Dashboard] Removing message listeners.');
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, [refreshPosts]); // Dependency array now only contains the memoized refreshPosts

  const handleUpdatePostDetails = async (postId: string, details: { categories?: string[], tags?: string[], notes?: string }) => {
    try {
      await updatePostDetails(postId, details);
      // Optimistically update local state
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId ? {
              ...p,
              ...(details.categories !== undefined && { categories: details.categories }),
              ...(details.tags !== undefined && { tags: details.tags }),
              ...(details.notes !== undefined && { notes: details.notes }),
           } : p
        )
      );
      // If the post viewer is open and this is the selected post, update its data too
      if (selectedPostForViewer && selectedPostForViewer.id === postId) {
        setSelectedPostForViewer(prevSelectedPost => {
          if (!prevSelectedPost) return null;
          // Update with flattened structure
          return {
            ...prevSelectedPost,
            ...(details.categories !== undefined && { categories: details.categories }),
            ...(details.tags !== undefined && { tags: details.tags }),
            ...(details.notes !== undefined && { notes: details.notes }),
          };
        });
      }
      console.log(`[Dashboard] Updated details for post ${postId}:`, details);
    } catch (error) {
      console.error(`[Dashboard] Failed to update details for post ${postId}:`, error);
      // Consider reverting optimistic update or showing error message
    }
  };

  // Helper function to convert Post to PostWithAIData format
  const convertPostToViewerFormat = (postToView: Post): PostWithAIData => {
    // Add detailed logging to debug the conversion
    console.log('[convertPostToViewerFormat] Original post data:', {
      id: postToView.id,
      timestamp: postToView.timestamp,
      createdAt: postToView.createdAt,
      savedAt: postToView.savedAt,
      mediaType: (postToView as any).mediaType,
      savedImage: postToView.savedImage,
      media: postToView.media,
      snapNote: (postToView as any).snapNote,
      fastTake: (postToView as any).fastTake
    });

    let determinedMediaType: 'image' | 'video' | 'text';

    // Check if postToView itself has a valid mediaType property first
    if (postToView.hasOwnProperty('mediaType')) {
      const existingMediaType = (postToView as any).mediaType as string;
      if (existingMediaType === 'image' || existingMediaType === 'video' || existingMediaType === 'text') {
        determinedMediaType = existingMediaType;
      } else if (postToView.savedImage || (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('image'))) {
        determinedMediaType = 'image';
      } else if (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('video')) {
        determinedMediaType = 'video';
      } else {
        determinedMediaType = 'text';
      }
    } else {
      if (postToView.savedImage || (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('image'))) {
        determinedMediaType = 'image';
      } else if (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('video')) {
        determinedMediaType = 'video';
      } else {
        determinedMediaType = 'text';
      }
    }

    console.log('[convertPostToViewerFormat] Determined media type:', determinedMediaType);

    // Safely access potential existing AI data
    const snapNote = (postToView as any).snapNote;
    const inSight = (postToView as any).inSight;
    const fastTake = (postToView as any).fastTake;
    const postTags = (postToView as any).tags;
    const postCategories = (postToView as any).categories;

    // Better timestamp handling - check multiple possible fields
    const getTimestamp = (): string => {
      const possibleTimestamps = [
        postToView.timestamp,
        postToView.createdAt,
        postToView.savedAt
      ].filter(Boolean);

      console.log('[convertPostToViewerFormat] Available timestamps:', possibleTimestamps);

      for (const ts of possibleTimestamps) {
        if (ts) {
          // Validate the timestamp
          try {
            const date = new Date(ts);
            if (!isNaN(date.getTime())) {
              console.log('[convertPostToViewerFormat] Using timestamp:', ts);
              return ts;
            }
          } catch (e) {
            console.warn('Invalid timestamp:', ts);
          }
        }
      }

      // Fallback to current time if no valid timestamp found
      console.warn('No valid timestamp found for post:', postToView.id);
      return new Date().toISOString();
    };

    const finalTimestamp = getTimestamp();
    
    const result = {
      id: postToView.id,
      platform: postToView.platform,
      mediaType: determinedMediaType,
      mediaUrl: postToView.savedImage || (postToView.media && postToView.media.length > 0 ? postToView.media[0].url : undefined),
      text: postToView.content,
      author: postToView.authorName || postToView.authorHandle || 'Unknown Author',
      timestamp: finalTimestamp,
      stats: (postToView as any).interactions ? {
        likes: (postToView as any).interactions.likes,
        comments: (postToView as any).interactions.replies,
        shares: (postToView as any).interactions.reposts,
      } : undefined,
      // Better snapNote handling - don't show "pending" if it's actually available
      snapNote: snapNote && typeof snapNote === 'string' ? snapNote : null,
      notes: (postToView as any).notes || '',
      // Include the full media array for carousel support
      media: postToView.media || [],
      inSight: {
        sentiment: (
          inSight?.sentiment &&
          ['positive', 'neutral', 'negative'].includes(inSight.sentiment)
        ) ? inSight.sentiment as 'positive' | 'neutral' | 'negative' : 'neutral',
        emoji: typeof inSight?.emoji === 'string' ? inSight.emoji : '🤔',
        contextTags: Array.isArray(inSight?.contextTags) ? inSight.contextTags.filter((tag: any) => typeof tag === 'string') : [],
      },
      // Better fastTake handling - don't show "pending" if it's actually available
      fastTake: fastTake && typeof fastTake === 'string' ? fastTake : null,
      tags: postTags || [],
      categories: postCategories || [],
      // Thread-specific fields
      isThread: postToView.isThread,
      threadId: postToView.threadId,
      threadPosition: postToView.threadPosition,
      threadLength: postToView.threadLength,
    };

    console.log('[convertPostToViewerFormat] Final converted data:', {
      id: result.id,
      timestamp: result.timestamp,
      mediaType: result.mediaType,
      mediaUrl: result.mediaUrl,
      snapNote: result.snapNote,
      fastTake: result.fastTake
    });

    return result;
  };

  // --- MODIFIED: handleOpenPostDetails to use PostViewerFullScreen ---
  const handleOpenPostDetails = (postId: string) => {
    // First try to find in processedPosts (which includes search results), then fall back to posts
    const postToView = processedPosts.find(p => p.id === postId) || posts.find(p => p.id === postId);
    if (postToView) {
      console.log(`[Dashboard] Opening PostViewerFullScreen for post: ${postId}`);

      // Check if this is a thread post
      if (postToView.isThread && postToView.threadId) {
        console.log(`[Dashboard] Post ${postId} is part of thread ${postToView.threadId}`);

        // Find all posts in the same thread
        const threadPosts = processedPosts.filter(p => p.threadId === postToView.threadId);
        console.log(`[Dashboard] Found ${threadPosts.length} posts in thread ${postToView.threadId}`);

        // Sort thread posts by position
        const sortedThreadPosts = threadPosts.sort((a, b) =>
          (a.threadPosition || 0) - (b.threadPosition || 0)
        );

        // Find the index of the current post in the thread
        const currentIndex = sortedThreadPosts.findIndex(p => p.id === postId);
        console.log(`[Dashboard] Current post is at index ${currentIndex} in thread`);

        // Convert thread posts to PostWithAIData format
        const threadPostsForViewer = sortedThreadPosts.map(post => convertPostToViewerFormat(post));

        // Set thread navigation state
        setCurrentThreadPosts(threadPostsForViewer);
        setCurrentThreadIndex(currentIndex >= 0 ? currentIndex : 0);
        setSelectedPostForViewer(threadPostsForViewer[currentIndex >= 0 ? currentIndex : 0]);

        return;
      }

      // For non-thread posts, use the converter function
      const postForViewer = convertPostToViewerFormat(postToView);

      // Clear thread state for individual posts
      setCurrentThreadPosts(null);
      setCurrentThreadIndex(0);
      setSelectedPostForViewer(postForViewer);
    } else {
      console.warn(`[Dashboard] Post with ID ${postId} not found for viewer.`);
    }
  };
  // --- END MODIFIED handleOpenPostDetails ---

  // --- NEW HANDLERS FOR POST VIEWER ---
  const handleClosePostViewer = () => {
    setSelectedPostForViewer(null);
    setCurrentThreadPosts(null);
    setCurrentThreadIndex(0);
  };

  // Thread navigation handlers
  const handleThreadNavigate = (index: number) => {
    setCurrentThreadIndex(index);
    if (currentThreadPosts && currentThreadPosts[index]) {
      setSelectedPostForViewer(currentThreadPosts[index]);
    }
  };

  const handleAddCategoryToPost = (postId: string, category: string) => {
    console.log(`[Dashboard] Add category '${category}' to post ${postId}`);
    // Find the post and update its categories
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedCategories = Array.from(new Set([...(postToUpdate.categories || []), category]));
      handleUpdatePostDetails(postId, { categories: updatedCategories });
    }
  };

  const handleAddTagToPost = (postId: string, tag: string) => {
    console.log(`[Dashboard] Add tag '${tag}' to post ${postId}`);
    // Find the post and update its tags
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedTags = Array.from(new Set([...(postToUpdate.tags || []), tag]));
      handleUpdatePostDetails(postId, { tags: updatedTags });
    }
  };

  const handleRemoveTagFromPost = (postId: string, tag: string) => {
    console.log(`[Dashboard] Remove tag '${tag}' from post ${postId}`);
    // Find the post and update its tags
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedTags = (postToUpdate.tags || []).filter(t => t !== tag);
      handleUpdatePostDetails(postId, { tags: updatedTags });
    }
  };

  const handleRemoveCategoryFromPost = (postId: string, category: string) => {
    console.log(`[Dashboard] Remove category '${category}' from post ${postId}`);
    // Find the post and update its categories
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedCategories = (postToUpdate.categories || []).filter(c => c !== category);
      handleUpdatePostDetails(postId, { categories: updatedCategories });
    }
  };

  // New handlers for bulk updates
  const handleUpdateCategories = (postId: string, categories: string[]) => {
    console.log(`[Dashboard] Update categories for post ${postId}:`, categories);
    handleUpdatePostDetails(postId, { categories });
  };

  const handleUpdateTags = (postId: string, tags: string[]) => {
    console.log(`[Dashboard] Update tags for post ${postId}:`, tags);
    handleUpdatePostDetails(postId, { tags });
  };

  const handleUpdateNotes = (postId: string, notes: string) => {
    console.log(`[Dashboard] Update notes for post ${postId}:`, notes);
    handleUpdatePostDetails(postId, { notes });
  };

  // --- END NEW HANDLERS ---



  // Show delete confirmation dialog
  const showDeleteConfirmation = (postId: string) => {
    setPostToDelete(postId);
    setIsDeleteConfirmOpen(true);
  };

  const handleDeletePost = (postId: string) => {
    showDeleteConfirmation(postId);
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteConfirmOpen(false);
    setPostToDelete(null);
  };

  // Confirm delete
  const confirmDelete = async () => {
    const postId = postToDelete;
    if (!postId) return;

    setIsDeleteConfirmOpen(false);
    setPostToDelete(null);
    try {
      console.log(`[Dashboard] Attempting to delete post ${postId}`);

      // Use direct storage API to delete the post instead of messaging
      try {
        // First, try to delete directly using the storage API
        await deletePostDirectly(postId);
        console.log(`[Dashboard] handleDeletePost: Successfully deleted post ${postId} directly`);

        // After successful local deletion, try to delete from cloud if user is logged in
        const tokenResult = await new Promise<{ token?: string, authToken?: string }>(resolve =>
          chrome.storage.local.get(['token', 'authToken'], result => resolve(result as { token?: string, authToken?: string }))
        );
        const currentToken = tokenResult?.authToken || tokenResult?.token;

        if (currentToken) {
          console.log(`[Dashboard] handleDeletePost: User is logged in, attempting cloud deletion for ${postId}`);

          // Find the post to get the correct MongoDB _id for cloud deletion
          const postToDelete = posts.find(p => p.id === postId);
          const cloudPostId = postToDelete?._id || postToDelete?.id || postId;

          console.log(`[Dashboard] handleDeletePost: Using cloud post ID ${cloudPostId} for deletion (original: ${postId})`);

          // Use a Promise with timeout for cloud deletion
          try {
            const cloudDeletePromise = new Promise<void>((resolve, _reject) => {
              chrome.runtime.sendMessage(
                {
                  action: 'DELETE_POST_FROM_CLOUD',
                  postId: cloudPostId,
                  token: currentToken
                },
                (response) => {
                  if (chrome.runtime.lastError) {
                    console.warn('[Dashboard] Cloud deletion error:', chrome.runtime.lastError.message);
                    resolve(); // Resolve anyway since it's just cloud cleanup
                  } else if (response?.status === 'success') {
                    console.log('[Dashboard] Cloud deletion successful');
                    resolve();
                  } else {
                    console.warn('[Dashboard] Cloud deletion failed:', response);
                    resolve(); // Resolve anyway since it's just cloud cleanup
                  }
                }
              );
            });

            // Set a timeout for cloud deletion (3 seconds)
            const timeoutPromise = new Promise<void>((resolve) => {
              setTimeout(() => {
                console.log('[Dashboard] Cloud deletion timed out, but continuing');
                resolve();
              }, 3000);
            });

            // Wait for either cloud deletion to complete or timeout
            await Promise.race([cloudDeletePromise, timeoutPromise]);
          } catch (cloudError) {
            console.warn('[Dashboard] Error in cloud deletion:', cloudError);
            // Continue anyway since local deletion was successful
          }
        }

        // Refresh posts after deletion
        refreshPosts();
      } catch (directDeleteError) {
        console.error('[Dashboard] Error in direct deletion:', directDeleteError);

        // Fallback to background script if direct deletion fails
        try {
          await new Promise<void>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
              console.warn('[Dashboard] Delete message timed out, continuing anyway');
              resolve(); // Resolve anyway to prevent hanging
            }, 3000);

            chrome.runtime.sendMessage(
              {
                action: 'DELETE_POST_LOCALLY',
                postId: postId
              },
              (response) => {
                clearTimeout(timeoutId);
                if (chrome.runtime.lastError) {
                  console.error('[Dashboard] Fallback delete error:', chrome.runtime.lastError.message);
                  reject(new Error(chrome.runtime.lastError.message));
                } else if (response?.status === 'success') {
                  console.log('[Dashboard] Fallback delete successful');
                  resolve();
                } else {
                  console.error('[Dashboard] Fallback delete failed:', response);
                  reject(new Error(response?.message || 'Unknown error'));
                }
              }
            );
          });

          // If we get here, the fallback was successful
          refreshPosts();
        } catch (fallbackError) {
          console.error('[Dashboard] Fallback delete failed:', fallbackError);
          alert(`Failed to delete post: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
          refreshPosts();
        }
      }
    } catch (error) {
      console.error('[Dashboard] handleDeletePost: General error:', error);
      alert(`An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
      refreshPosts(); // Refresh to ensure UI consistency even on error
    }
  };

  // Helper function to delete a post directly using the storage API
  const deletePostDirectly = async (postId: string): Promise<void> => {
    // Get posts from sync storage
    const syncResult = await new Promise<{savedPosts?: any[]}>(resolve => {
      chrome.storage.sync.get('savedPosts', result => resolve(result as {savedPosts?: any[]}));
    });

    // Get posts from local storage
    const localResult = await new Promise<{localSavedPosts?: any[]}>(resolve => {
      chrome.storage.local.get('localSavedPosts', result => resolve(result as {localSavedPosts?: any[]}));
    });

    let syncPosts = syncResult.savedPosts || [];
    let localPosts = localResult.localSavedPosts || [];

    // Filter out the post to delete
    const newSyncPosts = syncPosts.filter(post => post.id !== postId);
    const newLocalPosts = localPosts.filter(post => post.id !== postId);

    // Check if any posts were removed
    const syncRemoved = newSyncPosts.length < syncPosts.length;
    const localRemoved = newLocalPosts.length < localPosts.length;

    // Save the updated posts back to storage
    if (syncRemoved) {
      await new Promise<void>(resolve => {
        chrome.storage.sync.set({savedPosts: newSyncPosts}, () => resolve());
      });
    }

    if (localRemoved) {
      await new Promise<void>(resolve => {
        chrome.storage.local.set({localSavedPosts: newLocalPosts}, () => resolve());
      });
    }

    if (!syncRemoved && !localRemoved) {
      console.warn(`[Dashboard] Post ${postId} not found in storage`);
    } else {
      console.log(`[Dashboard] Post ${postId} deleted directly from storage`);
    }
  };

  useEffect(() => {
    let sortedPosts = [...posts];

    // Sort posts by timestamp or savedAt date
    sortedPosts.sort((a, b) => {
      const dateA = new Date(a.timestamp || a.savedAt || 0).getTime(); // Added fallback for safety
      const dateB = new Date(b.timestamp || b.savedAt || 0).getTime(); // Added fallback for safety
      // Handle potential NaN dates
      if (isNaN(dateA) && isNaN(dateB)) return 0;
      if (isNaN(dateA)) return 1; // Put posts without valid dates last
      if (isNaN(dateB)) return -1;
      return dateB - dateA; // Descending order
    });





    if (selectedPlatform !== 'All' && selectedPlatform !== 'Mindstream') {
      console.log(`[Dashboard Filter] Filtering by platform: ${selectedPlatform}`);
      console.log(`[Dashboard Filter] Posts before filtering:`, sortedPosts.map(p => ({ id: p.id, platform: p.platform })));
      sortedPosts = sortedPosts.filter(post => {
        const postPlatformLower = post.platform?.toLowerCase();
        const selectedPlatformLower = selectedPlatform.toLowerCase();
        const matches = postPlatformLower === selectedPlatformLower;
        console.log(`[Dashboard Filter DEBUG] Post ${post.id}: '${postPlatformLower}' === '${selectedPlatformLower}' = ${matches}`);
        return matches;
      });
      console.log(`[Dashboard Filter] Posts after filtering:`, sortedPosts.map(p => ({ id: p.id, platform: p.platform })));
    }

    // Filter by selected category
    if (selectedCategory) {
      sortedPosts = sortedPosts.filter(post =>
        post.categories && Array.isArray(post.categories) && post.categories.includes(selectedCategory)
      );
    }



    setFilteredPosts(sortedPosts);
  }, [posts, selectedPlatform, selectedCategory]);

  const processedPosts = useMemo(() => {
    // If we have search results and a search query, use search results
    if (searchQuery.trim() && searchResults.length > 0) {
      return searchResults;
    }
    // Otherwise use filtered posts
    return filteredPosts;
  }, [filteredPosts, searchQuery, searchResults]);

  // Group posts by threads and display threads as single cards
  const postsForDisplay = useMemo(() => {
    console.log('[Dashboard] Preparing posts for display. Total posts:', processedPosts.length);

    const threadGroups = new Map<string, Post[]>();
    const nonThreadPosts: Post[] = [];

    // Group posts by threadId
    processedPosts.forEach(post => {
      if (post.isThread && post.threadId) {
        if (!threadGroups.has(post.threadId)) {
          threadGroups.set(post.threadId, []);
        }
        threadGroups.get(post.threadId)!.push(post);
      } else {
        nonThreadPosts.push(post);
      }
    });

    // Sort thread posts by position
    threadGroups.forEach(posts => {
      posts.sort((a, b) => (a.threadPosition || 0) - (b.threadPosition || 0));
    });

    const result: Array<{ type: 'post' | 'thread', data: Post | Post[] }> = [];

    // Add non-thread posts
    nonThreadPosts.forEach(post => {
      result.push({ type: 'post', data: post });
    });

    // Add thread groups
    threadGroups.forEach(posts => {
      result.push({ type: 'thread', data: posts });
    });

    console.log('[Dashboard] Posts for display:', result.length, '(including', threadGroups.size, 'threads)');
    return result;
  }, [processedPosts]);

  // Restore scroll position after all filtering and processing effects complete
  // This runs after postsForDisplay has been updated (the final rendered data)
  useEffect(() => {
    const restore = () => {
      if (typeof prevScrollYRef.current === 'number') {
        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
        const targetScroll = Math.min(prevScrollYRef.current, Math.max(0, maxScroll));
        window.scrollTo({ top: targetScroll, behavior: 'auto' });
        console.log(`[Dashboard] Scroll restored to ${targetScroll} (saved: ${prevScrollYRef.current})`);
      }
    };

    // Wait for DOM to settle after all filtering effects complete
    requestAnimationFrame(() => {
      restore();
      // Additional attempts to handle any remaining layout shifts
      setTimeout(restore, 50);
      setTimeout(restore, 150);
      setTimeout(restore, 300); // Extra attempt for complex layouts
    });
  }, [postsForDisplay]); // Watch the final display data that actually renders

  const handleOpenLoginModal = () => {
    sessionStorage.setItem('acceptGoogleAuth', 'true');
    localStorage.setItem('acceptingAuthMessages', 'true');
    setIsLoginModalOpen(true);
  };

  const handleCloseLoginModal = () => {
    if (!isLoggedIn) {
      sessionStorage.removeItem('acceptGoogleAuth');
      localStorage.removeItem('acceptingAuthMessages');
    }
    setIsLoginModalOpen(false);
  };

  const handleLogout = async () => {
    sessionStorage.removeItem('acceptGoogleAuth');
    localStorage.removeItem('acceptingAuthMessages');
    await new Promise<void>(resolve => 
      chrome.storage.local.remove(['token', 'authToken', 'userInfo', 'user_profile'], () => resolve())
    );
    setIsLoggedIn(false);
    setLoggedInUser(null);
    console.log("[Dashboard] User logged out. Reloading data (should fetch local).");
    await loadInitialData(); // Reload data, which will now fetch local posts
  };

  const handleLoginSuccess = (token: string, _user: any) => { // User param no longer needed from modal
    console.log('Login successful! JWT received in dashboard (from postMessage or direct call).');
    chrome.storage.local.set({ token: token, authToken: token, userInfo: {} /* Clear old userInfo, /auth/me is source of truth */ }, () => {
      // The storage listener (authChanged) will trigger loadInitialData.
      // Call sync after setting token.
      initiateLocalToCloudSync();
    });
    setIsLoginModalOpen(false);
  };

  const handleUpgradeToPremium = async () => {
    if (!loggedInUser) {
      console.error("User not logged in, cannot upgrade.");
      return;
    }

    try {
      // Import subscription service and config dynamically to avoid circular dependencies
      const { createCheckoutSession } = await import('../services/subscriptionService');
      const { getPriceId } = await import('../config/stripe');

      // Use monthly price ID from configuration
      const monthlyPriceId = getPriceId('monthly');

      const checkoutUrl = await createCheckoutSession(monthlyPriceId);
      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      } else {
        console.error('Failed to create checkout session');
        // TODO: Show error to user
      }
    } catch (error) {
      console.error('Error during upgrade process:', error);
      // TODO: Show error to user
    }
  };

  // initiateLocalToCloudSync is now defined earlier in the file

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-notely-dark-bg via-black to-zinc-900 overflow-hidden notely-filter-transition relative">
      {/* Ambient background effects matching notely.social */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 right-1/4 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl animate-float" />
      </div>

      <header className="sticky top-0 z-50 bg-black/20 backdrop-blur-xl border-b border-purple-500/20 shadow-2xl notely-filter-transition">
        {/* Top header with logo and user info */}
        <div className="notely-breathing-lg flex justify-between items-center">
          <div className="flex items-center group">
            <div className="relative">
              <img
                src="/notely.svg"
                alt="Notely"
                className="h-12 w-auto notely-filter-transition hover:scale-110 cursor-pointer notely-logo-light drop-shadow-lg"
                title="Notely – Your Smart Post Saver"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  prevScrollYRef.current = window.scrollY;
                  console.log('[Dashboard] Logo clicked - resetting to All');
                  setSelectedPlatform('All');
                  setSelectedCategory(null);
                  setSearchQuery('');
                }}
              />
              <img
                src="/notely-dark.svg"
                alt="Notely"
                className="h-12 w-auto notely-filter-transition hover:scale-110 cursor-pointer notely-logo-dark drop-shadow-lg"
                title="Notely – Your Smart Post Saver"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  prevScrollYRef.current = window.scrollY;
                  console.log('[Dashboard] Dark logo clicked - resetting to All');
                  setSelectedPlatform('All');
                  setSelectedCategory(null);
                  setSearchQuery('');
                }}
              />
              {/* Subtle glow effect on hover */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
            <div className="ml-3">
              <h1 className="text-xl font-bold notely-gradient-text">Notely</h1>
              <p className="text-xs text-gray-400">Your social second brain</p>
            </div>
          </div>

          {/* Search Input - Center with glassmorphism */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                {isSearching ? (
                  <svg className="h-4 w-4 text-purple-400 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg className="h-4 w-4 text-gray-400 group-focus-within:text-purple-400 transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
              </div>
              <input
                type="text"
                placeholder={isLoggedIn ? t('dashboard.searchPlaceholder') : t('dashboard.searchPlaceholderLoggedOut')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                disabled={!isLoggedIn}
                className={`w-full h-12 pl-11 pr-4 bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl text-sm text-white placeholder-gray-400 focus:outline-none focus:bg-white/10 focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 ease-out ${
                  !isLoggedIn ? 'opacity-50 cursor-not-allowed' : ''
                } ${
                  searchQuery.trim() && searchResults.length > 0 ? 'ring-2 ring-purple-500/30 border-purple-500/30' : ''
                }`}
              />
              {searchError && (
                <div className="absolute top-full left-0 right-0 mt-2 p-2 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-xs">
                  {searchError}
                </div>
              )}
            </div>
            {/* Debug Button (only in development) */}
            {process.env.NODE_ENV === 'development' && (
              <button
                onClick={debugSearchData}
                className="ml-2 px-3 py-1 text-xs bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 rounded-md hover:bg-yellow-500/30 transition-colors"
                title="Debug search data"
              >
                Debug
              </button>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle className="hidden sm:flex" />

            {isUserDataLoading && <p className="text-sm text-notely-text-muted leading-relaxed">{t('dashboard.loading')}</p>}
            {!isUserDataLoading && isLoggedIn && loggedInUser ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-semibold text-notely-text-primary">
                    {loggedInUser.name}
                  </p>
                  <a href="settings.html" title={t('settings.title')}>
                    <SettingsIcon className="w-4 h-4 text-notely-text-tertiary hover:text-notely-accent notely-filter-transition" />
                  </a>
                  {loggedInUser.plan === 'premium' ? (
                    <PremiumBadge
                      plan={loggedInUser.plan}
                      subscriptionStatus={loggedInUser.subscriptionStatus}
                      size="sm"
                    />
                  ) : (
                    <button
                      onClick={handleUpgradeToPremium}
                      className="group relative inline-flex items-center px-4 py-2 text-sm font-semibold text-white bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500/50 transition-all duration-300 ease-out shadow-lg hover:shadow-xl hover:shadow-purple-500/25"
                      style={{ minWidth: '120px' }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300" />
                      <span className="relative">{t('premium.upgrade')}</span>
                    </button>
                  )}
                  <button
                    onClick={handleLogout}
                    className="group relative bg-white/5 backdrop-blur-md border border-red-500/30 text-red-400 hover:text-white hover:bg-red-500/20 hover:border-red-500/50 font-medium text-sm px-4 py-2 rounded-xl transition-all duration-300 ease-out hover:scale-105"
                  >
                    {t('auth.logout')}
                  </button>
                </div>
              </div>
            ) : !isUserDataLoading && (
              <div className="flex items-center space-x-4 notely-stagger-item">
                <a href="settings.html" className="mr-2" title={t('settings.title')}>
                  <SettingsIcon className="w-4 h-4 text-notely-text-tertiary hover:text-notely-accent notely-filter-transition" />
                </a>
                <button
                  onClick={handleOpenLoginModal}
                  className="group relative bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-xl font-semibold text-sm transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-purple-500/25"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300" />
                  <span className="relative">{t('auth.login')}</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Two-Layer Header Structure */}
        {/* Top Layer: Main Navigation with glassmorphism */}
        <div className="notely-breathing-lg bg-white/5 backdrop-blur-md border-b border-white/10">
          <div className="flex flex-wrap gap-2 mb-4 justify-center md:justify-start">
            <PlatformSelector
              onSelect={(platform) => {
                // Save current scroll position so we can restore after the DOM updates
                prevScrollYRef.current = window.scrollY;
                console.log('[Dashboard] Platform selected:', platform);
                setSelectedPlatform(platform);
              }}
              selectedPlatform={selectedPlatform}
              enabledPlatforms={enabledPlatforms}
            />
            <div className="flex items-center gap-2 text-sm text-notely-text-secondary">
              {selectedCategory && (
                <span className="px-2 py-1 bg-notely-surface rounded-full text-xs">
                  📁 {formatForDisplay(selectedCategory)}
                </span>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="flex-1 overflow-y-auto">
        {/* Category and Tag Filters - Positioned just above posts */}
        {selectedPlatform !== 'Mindstream' && (
          <div className="bg-notely-surface notely-filter-transition">
            {/* Category Selector - Show when we have categories */}
            {availableCategories.length > 0 && (
              <div className="notely-breathing-lg">
                <SimpleCategorySelector
                  categories={availableCategories}
                  selectedCategory={selectedCategory}
                  onCategorySelect={(category) => {
                    // Save current scroll position so we can restore after the DOM updates
                    prevScrollYRef.current = window.scrollY;
                    console.log('[Dashboard] Category selected:', category, 'Saved scroll position:', prevScrollYRef.current);
                    setSelectedCategory(category);
                  }}
                  categoryCounts={categoryPostCounts}
                />
              </div>
            )}

          </div>
        )}



        <div className="notely-breathing-xl max-w-full bg-notely-bg">
          {/* Main container with persistent left sidebar */}
          <div className="w-full flex flex-col lg:flex-row gap-4">
            {/* Left Sidebar with Daily Wisdom and Storage - Always visible */}
            <div className="w-full lg:w-1/5 space-y-6">
              {/* Daily Wisdom Card */}
              <DailyWisdom
                className="w-full"
                onQuoteClick={(quote) => {
                  // You can enhance this to show more details in a modal if needed
                }}
                onOpenPost={(post) => {
                  setSelectedPostForViewer(post as PostWithAIData);
                }}
              />

              {/* Storage Usage Card */}
              {isLoggedIn && (
                <StorageUsage
                  className="w-full"
                  onRefresh={() => {
                    refreshPosts();
                  }}
                />
              )}

              {/* Conditionally render Tags & Categories for Mindstream view */}
              {selectedPlatform === 'Mindstream' && (
                <div className="notely-card bg-notely-card rounded-notely-xl shadow-notely-md hover:shadow-notely-lg notely-filter-transition px-6 py-4 overflow-hidden">
                  <TagsCategoriesWidget posts={posts} />
                </div>
              )}
            </div>

            {/* Main Content Area */}
            <div className="w-full lg:w-4/5">
              {selectedPlatform === 'Mindstream' ? (
                // Mindstream View - Widgets take full width
                <div className="w-full">
                  <MindstreamWidgets
                    posts={processedPosts}
                    onOpenPost={(post) => setSelectedPostForViewer(post as PostWithAIData)}
                  />
                </div>
              ) : postsForDisplay.length > 0 ? (
                // Regular view with posts and threads
                <div className="space-y-6">
                  {/* Post cards grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {postsForDisplay.map((item, index) => {
                      if (item.type === 'thread') {
                        // Thread group - show only the first post with thread indicator
                        const threadPosts = item.data as Post[];
                        const firstPost = threadPosts[0];
                        if (!firstPost) return null;
                        
                        return (
                          <ThreadCard
                            key={`thread-${firstPost.threadId}-${index}`}
                            threadPosts={threadPosts}
                            firstPost={firstPost}
                            onDelete={handleDeletePost}
                            onOpenDetails={() => handleOpenPostDetails(firstPost.id)}
                            t={t}
                          />
                        );
                      } else {
                        // Regular post
                        const post = item.data as Post;
                        return (
                          <PostCard
                            key={post.id}
                            post={post}
                            onDelete={handleDeletePost}
                            onOpenDetails={() => handleOpenPostDetails(post.id)}
                            t={t}
                          />
                        );
                      }
                    })}
                  </div>
                </div>
              ) : (
                // No posts view
                <div className="text-center py-12">
                  <p className="text-lg text-notely-text-muted mb-4">{t('dashboard.noPosts')}</p>
                  {/* Only show install extension message in web environment */}
                  {!chrome?.runtime?.id && (
                    <p className="text-sm text-notely-text-tertiary">{t('dashboard.installExtension')}</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={handleCloseLoginModal}
        onLoginSuccess={handleLoginSuccess}
        preventAutoGoogleLogin={true}
      />

      {selectedPostForViewer && (
        <PostViewerFullScreen
          post={selectedPostForViewer}
          onClose={handleClosePostViewer}
          onAddCategory={handleAddCategoryToPost}
          onAddTag={handleAddTagToPost}
          onRemoveTag={handleRemoveTagFromPost}
          onRemoveCategory={handleRemoveCategoryFromPost}
          onUpdateNotes={handleUpdateNotes}
          onUpdateCategories={handleUpdateCategories}
          onUpdateTags={handleUpdateTags}
          threadPosts={currentThreadPosts || undefined}
          currentThreadIndex={currentThreadIndex}
          onThreadNavigate={handleThreadNavigate}
        />
      )}

      {/* Delete Confirmation Modal with blurred background */}
      {isDeleteConfirmOpen && (
        <div className="notely-modal-overlay fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="notely-modal-content open notely-card bg-notely-card rounded-notely-lg shadow-notely-md hover:shadow-notely-lg px-6 py-4 w-full max-w-md mx-auto border border-[#2F2F2F]/10 transition-all duration-200">
            <h3 className="text-xl font-semibold leading-tight mb-3 text-notely-text-primary">{t('dashboard.deletePostTitle')}</h3>
            <p className="text-sm text-notely-text-muted leading-relaxed mb-6">{t('dashboard.deleteConfirmation')}</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="notely-btn-secondary px-4 py-2 text-sm font-medium notely-filter-transition hover:scale-105 border-none shadow-notely-sm hover:shadow-notely-md"
              >
                {t('dashboard.cancel')}
              </button>
              <button
                onClick={confirmDelete}
                className="notely-btn-danger px-4 py-2 text-sm font-medium notely-filter-transition hover:scale-105 border-none shadow-notely-sm hover:shadow-notely-md"
              >
                {t('dashboard.deletePostTitle')}
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="p-4 border-t border-gray-700">
        <p className="text-sm text-gray-500">
          © 2025 Notely. All rights reserved. 
          <button onClick={() => {
            chrome.tabs.create({ url: 'settings.html' });
          }} className="ml-4 text-blue-400 hover:text-blue-300">Settings</button>
        </p>
      </div>
    </div>
  );
}  // End of DashboardContent

// Main Dashboard wrapper with LocaleProvider
function Dashboard() {
  return (
    <LocaleProvider>
      <DashboardContent />
    </LocaleProvider>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Dashboard />
  </React.StrictMode>,
);
