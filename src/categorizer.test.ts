import { describe, it, expect } from 'vitest';
import {
  CORE_CATEGORIES,
  getAllCoreSubCategories,
  biasAdjustCategories,
  validateAndMapToCoreCategory,
  finalizeTags,
  // levenshteinDistance is tested implicitly via validateAndMapToCoreCategory
} from './categorizer';
import type { CoreSubCategory } from './categorizer';

describe('categorizer utility functions (v2)', () => {

  describe('getAllCoreSubCategories', () => {
    const subCategories = getAllCoreSubCategories();

    it('should return a non-empty array', () => {
      expect(subCategories).toBeInstanceOf(Array);
      expect(subCategories.length).toBeGreaterThan(0);
    });

    it('should include known sub-categories', () => {
      expect(subCategories).toContain('world');
      expect(subCategories).toContain('technology');
      expect(subCategories).toContain('memes');
    });

    it('should only contain strings', () => {
      expect(subCategories.every(sc => typeof sc === 'string')).toBe(true);
    });
  });

  describe('biasAdjustCategories', () => {
    const mockUserFrequency = {
      'news': 0.7, // Strong bias for 'news' (assuming 'news' is a slug)
      'world': 0.7, // Strong bias for 'world' (sub-category of news)
      'tech_innovation': 0.3,
      'ai_data': 0.65, // Strong bias for 'ai_data'
    };

    it('should not apply bias if totalUserPosts < MIN_POSTS_FOR_BIAS', () => {
      const proposed = ['technology', 'gaming_esports'];
      const result = biasAdjustCategories(proposed, mockUserFrequency, 20); // 20 posts < 30
      expect(result).toEqual(proposed);
    });

    it('should add a strongly biased category if not present and space allows', () => {
      const proposed = ['technology']; // Max 3, space for 2 more
      // 'world' and 'ai_data' are biased and not in proposed.
      const result = biasAdjustCategories(proposed, mockUserFrequency, 35);
      expect(result).toContain('technology');
      expect(result).toContain('world');
      expect(result).toContain('ai_data');
      expect(result.length).toBe(3);
    });

    it('should keep proposed categories if they are already strongly biased', () => {
      const proposed = ['world', 'ai_data'];
      const result = biasAdjustCategories(proposed, mockUserFrequency, 35);
      expect(result).toContain('world');
      expect(result).toContain('ai_data');
      expect(result.length).toBe(2); // No other non-biased category to fill up to 3 from proposed
    });

    it('should respect MAX_CATEGORIES (3) when adding biased categories', () => {
      const manyBiasedFreq = {
        'world': 0.7, 'politics': 0.7, 'business': 0.7, 'science': 0.7
      };
      const proposed = ['technology'];
      const result = biasAdjustCategories(proposed, manyBiasedFreq, 35);
      expect(result.length).toBe(3);
      // Contains 'technology', and two from the biased list
      expect(result).toContain('technology');
      // The specific two biased ones might depend on iteration order if not handled, 
      // but for this test, we just check length and inclusion of original.
    });

    it('should handle empty proposed categories and fill with biased ones', () => {
      const result = biasAdjustCategories([], mockUserFrequency, 35);
      expect(result).toContain('world');
      expect(result).toContain('ai_data');
      expect(result.length).toBe(2); // Only two meet the >0.6 threshold in mockUserFrequency
    });

    it('should not add a biased category if proposed is full and biased one is not already there', () => {
      const proposed = ['technology', 'gaming_esports', 'entertainment'];
      const freq = { 'world': 0.7, ...mockUserFrequency };
      const result = biasAdjustCategories(proposed, freq, 35);
      expect(result).toEqual(proposed); // 'world' is biased but no space to add without complex replacement
      expect(result).not.toContain('world');
    });
    
    it('should return unique categories even if proposed and bias overlap', () => {
      const proposed = ['world', 'ai_data']; // These are already biased
      const result = biasAdjustCategories(proposed, mockUserFrequency, 35);
      expect(result).toEqual(['world', 'ai_data']);
      expect(result.length).toBe(2);
    });

  });

  describe('validateAndMapToCoreCategory', () => {
    const allCoreSlugs = getAllCoreSubCategories();

    it('should find an exact match', () => {
      expect(validateAndMapToCoreCategory('world', allCoreSlugs)).toBe('world');
    });

    it('should handle case-insensitivity and trimming for exact match', () => {
      expect(validateAndMapToCoreCategory('  WoRlD  ', allCoreSlugs)).toBe('world');
    });

    it('should return null for empty or whitespace-only string', () => {
      expect(validateAndMapToCoreCategory('', allCoreSlugs)).toBeNull();
      expect(validateAndMapToCoreCategory('   ', allCoreSlugs)).toBeNull();
    });

    it('should return null for non-string input', () => {
      expect(validateAndMapToCoreCategory(null as any, allCoreSlugs)).toBeNull();
      expect(validateAndMapToCoreCategory(undefined as any, allCoreSlugs)).toBeNull();
      expect(validateAndMapToCoreCategory(123 as any, allCoreSlugs)).toBeNull();
    });

    // For fuzzy matching, we need a known CORE_CATEGORY sub-slug and a variation.
    // Example: 'technologi' vs 'technology' (distance 1, len 10, sim 0.9)
    // Example: 'technlogy' vs 'technology' (distance 1, len 10, sim 0.9)
    it('should find a fuzzy match above threshold (e.g., technologi -> technology)', () => {
      expect(validateAndMapToCoreCategory('technologi', allCoreSlugs)).toBe('technology');
      expect(validateAndMapToCoreCategory('poltics', allCoreSlugs)).toBe('politics'); // d=1, len=8, sim=0.875
    });

    it('should return null for a fuzzy match below threshold (e.g., techy -> technology)', () => {
      // 'techy' vs 'technology' (distances are higher, similarity lower)
      // d("techy", "technology") is likely 6. similarity = 1 - (6/10) = 0.4
      expect(validateAndMapToCoreCategory('techy', allCoreSlugs)).toBeNull();
    });

    it('should return null for a completely different string', () => {
      expect(validateAndMapToCoreCategory('myrandomcategory', allCoreSlugs)).toBeNull();
    });
    
    it('should correctly map plural to singular if fuzzy matches (e.g. technologies to technology)', () => {
        // 'technologies' vs 'technology' - d=2, len=12, sim = 1-(2/12) = 1 - 0.166 = 0.833
        expect(validateAndMapToCoreCategory('technologies', allCoreSlugs)).toBe('technology');
    });
  });

  describe('finalizeTags', () => {
    it('should return unique, lowercase, trimmed tags', () => {
      const gptTags = [' TagA ', 'tagb', 'TagA', ' TagC '];
      const categories = ['categoryx'];
      expect(finalizeTags(gptTags, categories)).toEqual(['taga', 'tagb', 'tagc']);
    });

    it('should enforce MAX_TAGS (6)', () => {
      const gptTags = ['t1', 't2', 't3', 't4', 't5', 't6', 't7'];
      expect(finalizeTags(gptTags, [])).toHaveLength(6);
    });

    it('should remove tags identical to chosen categories (case-insensitive)', () => {
      const gptTags = ['news', 'Tech', 'TagA'];
      const categories = ['News', 'tech'];
      expect(finalizeTags(gptTags, categories)).toEqual(['taga']);
    });

    it('should remove tags that are substrings of categories or vice-versa', () => {
      const gptTags = ['live music', 'music festival', 'concert', 'guitar solo'];
      const categories = ['music']; // 'music' is a sub-category of culture_lifestyle
      // 'live music' includes 'music', 'music festival' includes 'music'
      // 'concert' and 'guitar solo' should remain
      expect(finalizeTags(gptTags, categories)).toEqual(['concert', 'guitar solo']);
    });
    
    it('should handle category being substring of tag', () => {
      const gptTags = ['worldnews', 'politicsweekly', 'localnews'];
      const categories = ['news', 'politics'];
      // 'worldnews' contains 'news', 'politicsweekly' contains 'politics', 'localnews' contains 'news'
      expect(finalizeTags(gptTags, categories)).toEqual([]);
    });

    it('should return empty array for non-array gptTags input', () => {
      expect(finalizeTags(null as any, [])).toEqual([]);
      expect(finalizeTags(undefined as any, [])).toEqual([]);
      expect(finalizeTags('not-an-array' as any, [])).toEqual([]);
    });

    it('should handle empty gptTags array', () => {
      expect(finalizeTags([], ['news'])).toEqual([]);
    });

    it('should handle empty chosenCategories array', () => {
      const gptTags = ['TagA', 'TagB'];
      expect(finalizeTags(gptTags, [])).toEqual(['taga', 'tagb']);
    });
    
    it('should filter out empty strings after trimming', () => {
        const gptTags = ['  ', 'tagA', '  '];
        expect(finalizeTags(gptTags, [])).toEqual(['taga']);
    });
  });

}); 