import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { FiArrowLeft, FiCheckCircle, FiTwitter, FiGlobe } from 'react-icons/fi';
import { FaLinkedinIn, FaReddit<PERSON>lien, <PERSON>a<PERSON><PERSON><PERSON>ram, FaPinterest } from 'react-icons/fa';
import '../index.css';
import { getSavedPosts } from '../storage';

import { useTranslation } from '../hooks/useTranslation';
import { useLocale } from '../contexts/useLocale';
import Toggle from '../components/Toggle';
import ThemeToggle from '../components/ThemeToggle';
import { initializeTheme } from '../utils/themeUtils';
import SubscriptionSettings from '../components/SubscriptionSettings';
import ProfileSettings from '../components/ProfileSettings';

// Utility function to format file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// --- Storage Keys ---
const CLOUD_STORAGE_KEY = 'saveMediaToCloud';
const PLATFORM_INTEGRATIONS_KEY = 'platformIntegrations';
// -------------------------------------

const languageOptions = [
  { code: 'en', name: 'English' },
  { code: 'tr', name: 'Türkçe' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: 'Deutsch' },
  { code: 'es', name: 'Español' },
];

interface PlatformStats {
  posts: number;
  mediaSize: number;
  lastUpdated?: string;
}

type StatsRecord = Record<string, PlatformStats>;

type PlatformKeys = 'X/Twitter' | 'LinkedIn' | 'Reddit' | 'Instagram' | 'pinterest' | 'Web';

// Default platform integrations - all enabled by default
const defaultPlatformIntegrations: Record<PlatformKeys, boolean> = {
  'X/Twitter': true,
  'LinkedIn': true,
  'Reddit': true,
  'Instagram': true,
  'pinterest': true,
  'Web': true
};

// Platform display names and icons - Updated for dark mode visibility
const platformInfo: Record<PlatformKeys, { name: string; icon: React.ComponentType; color: string }> = {
  'X/Twitter': { name: 'X / Twitter', icon: FiTwitter, color: 'text-gray-300' },
  'LinkedIn': { name: 'LinkedIn', icon: FaLinkedinIn, color: 'text-blue-400' },
  'Reddit': { name: 'Reddit', icon: FaRedditAlien, color: 'text-orange-400' },
  'Instagram': { name: 'Instagram', icon: FaInstagram, color: 'text-purple-400' },
  'pinterest': { name: 'Pinterest', icon: FaPinterest, color: 'text-red-400' },
  'Web': { name: 'Web Articles', icon: FiGlobe, color: 'text-green-400' }
};

// Map actual platform names from posts to our settings keys
const normalizePlatformKey = (platform: string): PlatformKeys | null => {
  const platformLower = platform.toLowerCase();
  
  // Direct matches first
  if (platform === 'X/Twitter') return 'X/Twitter';
  if (platform === 'LinkedIn') return 'LinkedIn';
  if (platform === 'Reddit') return 'Reddit';
  if (platform === 'Instagram') return 'Instagram';
  if (platform === 'Web Articles' || platform === 'Web') return 'Web';
  
  // Case-insensitive matches
  if (platformLower === 'pinterest') return 'pinterest';
  if (platformLower === 'twitter' || platformLower === 'x') return 'X/Twitter';
  if (platformLower === 'linkedin') return 'LinkedIn';
  if (platformLower === 'reddit') return 'Reddit';
  if (platformLower === 'instagram') return 'Instagram';
  if (platformLower === 'web' || platformLower === 'web articles') return 'Web';
  
  return null;
};

// This new component contains the static classes Tailwind needs to see.
const PlatformIcon: React.FC<{ platform: PlatformKeys }> = ({ platform }) => {
  const iconStyle = { color: 'var(--notely-text-primary)' };
  
  switch (platform) {
    case 'X/Twitter':
      return <FiTwitter className="w-5 h-5" style={iconStyle} />;
    case 'LinkedIn':
      return <FaLinkedinIn className="w-5 h-5" style={iconStyle} />;
    case 'Reddit':
      return <FaRedditAlien className="w-5 h-5" style={iconStyle} />;
    case 'Instagram':
      return <FaInstagram className="w-5 h-5" style={iconStyle} />;
    case 'pinterest':
      return <FaPinterest className="w-5 h-5" style={iconStyle} />;
    case 'Web':
      return <FiGlobe className="w-5 h-5" style={iconStyle} />;
    default:
      return null;
  }
};

const platformDisplayNames: Record<PlatformKeys, string> = {
  'X/Twitter': 'X / Twitter',
  'LinkedIn': 'LinkedIn',
  'Reddit': 'Reddit',
  'Instagram': 'Instagram',
  'pinterest': 'Pinterest',
  'Web': 'Web Articles'
};

export function Settings() {
  // State management
  const [stats, setStats] = useState<StatsRecord>({});
  const [loading, setLoading] = useState(true);

  const [saveMediaToCloud, setSaveMediaToCloud] = useState<boolean>(true);
  const [platformIntegrations, setPlatformIntegrations] = useState<Record<PlatformKeys, boolean>>(defaultPlatformIntegrations);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveStatus, setSaveStatus] = useState<{show: boolean; message: string; isError: boolean}>({
    show: false, 
    message: '', 
    isError: false
  });
  
  // Hooks
  const { t } = useTranslation();
  const { locale, setLocale } = useLocale();
  


  // Initialize theme on component mount
  useEffect(() => {
    initializeTheme();
  }, []);

  // Show save status and auto-hide after 3 seconds
  useEffect(() => {
    if (saveStatus.show) {
      const timer = setTimeout(() => {
        setSaveStatus(prev => ({...prev, show: false}));
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [saveStatus.show]);
  
  // Visual indicator for saving state
  useEffect(() => {
    if (isSaving) {
      document.body.style.cursor = 'wait';
    } else {
      document.body.style.cursor = 'default';
    }
    return () => {
      document.body.style.cursor = 'default';
    };
  }, [isSaving]);
  
  // Save settings to storage
  const saveSettings = useCallback(async () => {
    try {
      setIsSaving(true);

      // Save to local storage
      await chrome.storage.local.set({
        [CLOUD_STORAGE_KEY]: saveMediaToCloud,
        [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations
      });

      // Save locale to sync storage to ensure it persists across devices
      await chrome.storage.sync.set({ locale });

      setSaveStatus({
        show: true,
        message: t('common.settingsSaved') || 'Settings saved successfully',
        isError: false
      });
    } catch (e) {
      console.error('[Settings] Error saving settings:', e);
      setSaveStatus({
        show: true,
        message: t('error.generic') || 'Failed to save settings',
        isError: true
      });
    } finally {
      setIsSaving(false);
    }
  }, [saveMediaToCloud, platformIntegrations, locale, t]);

  // Load settings from storage
  const loadSettings = useCallback(async () => {
    setLoading(true);
    try {
      console.log('[Settings] Loading settings from storage...');

      // Load all local storage settings at once for efficiency
      const localStorageSettings = await new Promise<{[key: string]: unknown}>((resolve) => {
        chrome.storage.local.get([
          CLOUD_STORAGE_KEY,
          PLATFORM_INTEGRATIONS_KEY
        ], (result) => {
          console.log('[Settings] Loaded local storage settings:', result);
          resolve(result);
        });
      });

      // Apply cloud storage setting
      if (localStorageSettings[CLOUD_STORAGE_KEY] !== undefined) {
        const cloudStorageValue = Boolean(localStorageSettings[CLOUD_STORAGE_KEY]);
        console.log('[Settings] Setting cloud storage to:', cloudStorageValue);
        setSaveMediaToCloud(cloudStorageValue);
      }

      // Apply platform integrations settings
      if (localStorageSettings[PLATFORM_INTEGRATIONS_KEY] &&
          typeof localStorageSettings[PLATFORM_INTEGRATIONS_KEY] === 'object' &&
          localStorageSettings[PLATFORM_INTEGRATIONS_KEY] !== null) {
        // Type assertion after validation
        const savedIntegrations = localStorageSettings[PLATFORM_INTEGRATIONS_KEY] as Record<PlatformKeys, boolean>;
        const finalIntegrations = {
          ...defaultPlatformIntegrations,
          ...savedIntegrations
        };
        console.log('[Settings] Setting platform integrations to:', finalIntegrations);
        setPlatformIntegrations(finalIntegrations);
      } else {
        console.log('[Settings] No saved platform integrations found, using defaults');
        setPlatformIntegrations(defaultPlatformIntegrations);
      }

      // Load locale from sync storage to ensure it's consistent across devices
      await new Promise<void>((resolve) => {
        chrome.storage.sync.get({ locale: 'en' }, (result) => {
          console.log('[Settings] Loaded locale from sync storage:', result.locale);
          if (result.locale && typeof result.locale === 'string') {
            setLocale(result.locale);
          }
          resolve();
        });
      });
      
      // Load stats - Enhanced to calculate actual media sizes
      const posts = await getSavedPosts();
      console.log('[Settings] Total posts loaded:', posts.length);
      console.log('[Settings] Sample posts:', posts.slice(0, 3).map(p => ({ id: p.id, platform: p.platform })));
      
      const platformStats: StatsRecord = {};
      
      // Group posts by platform with proper key normalization
      posts.forEach(post => {
        const rawPlatform = post.platform || 'Unknown';
        const normalizedPlatform = normalizePlatformKey(rawPlatform);
        
        console.log(`[Settings] Processing post ${post.id}: platform "${rawPlatform}" → "${normalizedPlatform}"`);
        
        // Skip unknown platforms
        if (!normalizedPlatform) {
          console.warn(`[Settings] Unknown platform "${rawPlatform}" found in post ${post.id}, skipping stats`);
          return;
        }
        
        if (!platformStats[normalizedPlatform]) {
          platformStats[normalizedPlatform] = {
            posts: 0,
            mediaSize: 0,
            lastUpdated: undefined
          };
        }
        
        platformStats[normalizedPlatform].posts += 1;
        
        // Calculate media size - improved calculation
        if (post.media && Array.isArray(post.media)) {
          const mediaCount = post.media.length;
          console.log(`[Settings] Post ${post.id} has ${mediaCount} media items`);
          
          platformStats[normalizedPlatform].mediaSize += post.media.reduce((sum, item) => {
            // If size is available, use it
            if (item.size) {
              console.log(`[Settings] Using actual size for media: ${item.size} bytes`);
              return sum + item.size;
            }
            
            // Otherwise estimate based on media type and URL
            let estimatedSize = 0;
            if (item.url) {
              // Estimate based on media type
              if (item.type === 'video') {
                estimatedSize = 5000000; // ~5MB for videos
              } else if (item.url.includes('data:image/')) {
                // For base64 images, calculate from data URL length
                estimatedSize = Math.round(item.url.length * 0.75); // Base64 is ~33% larger than binary
              } else {
                // Regular images - estimate based on platform
                if (normalizedPlatform === 'Instagram') {
                  estimatedSize = 800000; // ~800KB for Instagram images
                } else if (normalizedPlatform === 'X/Twitter') {
                  estimatedSize = 400000; // ~400KB for Twitter images
                } else if (normalizedPlatform === 'pinterest') {
                  estimatedSize = 600000; // ~600KB for Pinterest images (typically high quality)
                } else if (normalizedPlatform === 'Reddit') {
                  estimatedSize = 450000; // ~450KB for Reddit images
                } else {
                  estimatedSize = 500000; // ~500KB default for Web articles
                }
              }
              console.log(`[Settings] Estimated size for ${normalizedPlatform} media: ${estimatedSize} bytes`);
            }
            
            return sum + estimatedSize;
          }, 0);
        }
        
        // Update last updated timestamp
        const timestamp = post.timestamp || post.savedAt;
        if (timestamp) {
          if (!platformStats[normalizedPlatform].lastUpdated || new Date(timestamp) > new Date(platformStats[normalizedPlatform].lastUpdated)) {
            platformStats[normalizedPlatform].lastUpdated = timestamp;
          }
        }
      });
      
      console.log('[Settings] Final platform stats calculated:', platformStats);
      setStats(platformStats);
    } catch (err) {
      console.error('Error loading settings:', err);
    } finally {
      setLoading(false);
    }
  }, [setLocale]);

  // Load settings on mount
  useEffect(() => {
    console.log('[Settings] Component mounted, loading settings...');
    loadSettings();
  }, [loadSettings]);

  // Debug: Log state changes
  useEffect(() => {
    console.log('[Settings] State updated:', {
      locale,
      saveMediaToCloud,
      platformIntegrations,
      loading,
      isSaving
    });
  }, [locale, saveMediaToCloud, platformIntegrations, loading, isSaving]);



  // Handle clear data
  const handleClearData = async () => {
    if (window.confirm(t('settings.clearDataConfirm'))) {
      try {
        await chrome.storage.local.clear();
        console.log('All data cleared successfully!');
        loadSettings();
      } catch (error) {
        console.error('Clear data error:', error);
        alert('Failed to clear data. Please try again.');
      }
    }
  };

  // Handle language change
  const handleLanguageChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLocale = e.target.value;
    console.log('[Settings] Language change requested:', newLocale);

    try {
      // Update locale in context first
      setLocale(newLocale);

      // Clear translation cache to ensure new language is applied immediately
      if (typeof window !== 'undefined' && (window as any).clearTranslationCache) {
        (window as any).clearTranslationCache();
      }

      // Save locale directly to storage
      await chrome.storage.sync.set({ locale: newLocale });

      // Also save other settings to local storage
      await chrome.storage.local.set({
        [CLOUD_STORAGE_KEY]: saveMediaToCloud,
        [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations
      });

      setSaveStatus({
        show: true,
        message: 'Language changed successfully. Refreshing page...',
        isError: false
      });

      // Force a page refresh to ensure all components use the new language
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('[Settings] Error changing language:', error);
      setSaveStatus({
        show: true,
        message: 'Failed to change language. Please try again.',
        isError: true
      });
    }
  };

  // Handle back button click
  const handleBack = () => {
    window.location.href = '/dashboard.html';
  };

  // Save status notification component
  const SaveStatus = () => {
    if (!saveStatus.show) return null;
    
    return (
      <div 
        className="fixed bottom-4 right-4 p-3 rounded-md shadow-lg flex items-center"
        style={{
          backgroundColor: saveStatus.isError ? '#ef4444' : '#10b981',
          color: 'white'
        }}
      >
        <FiCheckCircle className="mr-2" />
        <span>{saveStatus.message}</span>
      </div>
    );
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--notely-bg)', color: 'var(--notely-text-primary)' }}>
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex items-center mb-8">
          <button 
            onClick={handleBack}
            className="mr-4 p-2 rounded-full hover:bg-notely-surface transition-colors"
            style={{ color: 'var(--notely-text-primary)' }}
            aria-label="Back"
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-2xl font-bold font-georgia" style={{ color: 'var(--notely-text-primary)' }}>{t('settings.title')}</h1>
          <div className="ml-auto">
            <ThemeToggle />
          </div>
        </div>
        
        {/* Main content with two-column layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Profile Settings */}
            <ProfileSettings />

            {/* Subscription Settings */}
            <SubscriptionSettings />

            {/* Language Selector */}
            <div className="p-6 notely-card rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold mb-4" style={{ color: 'var(--notely-text-primary)' }}>{t('settings.language')}</h2>
              <select
                value={locale}
                onChange={handleLanguageChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                style={{ 
                  backgroundColor: 'var(--notely-surface)', 
                  border: '1px solid var(--notely-border)', 
                  color: 'var(--notely-text-primary)' 
                }}
                aria-label={t('settings.language')}
              >
                {languageOptions.map((option) => (
                  <option key={option.code} value={option.code}>
                    {option.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Cloud Storage Toggle */}
            <div className="p-6 notely-card rounded-lg shadow-sm">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-semibold mb-2" style={{ color: 'var(--notely-text-primary)' }}>{t('settings.cloudStorage')}</h2>
                  <p className="text-sm" style={{ color: 'var(--notely-text-secondary)' }}>
                    {t('settings.cloudStorageDescription')}
                  </p>
                </div>
                <div className="flex items-center">
                  <Toggle
                    enabled={saveMediaToCloud}
                    onChange={async (enabled) => {
                      console.log('[Settings] Cloud storage toggle:', enabled);
                      setSaveMediaToCloud(enabled);

                      // Save to storage immediately
                      try {
                        await chrome.storage.local.set({
                          [CLOUD_STORAGE_KEY]: enabled,
                          [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations
                        });

                        console.log('[Settings] Cloud storage setting saved successfully');

                        setSaveStatus({
                          show: true,
                          message: t('common.settingsSaved') || 'Settings saved successfully',
                          isError: false
                        });
                      } catch (error) {
                        console.error('[Settings] Error saving cloud storage setting:', error);
                        setSaveStatus({
                          show: true,
                          message: 'Failed to save cloud storage setting. Please try again.',
                          isError: true
                        });
                        // Revert state on error
                        setSaveMediaToCloud(!enabled);
                      }
                    }}
                  />
                </div>
              </div>
            </div>



            {/* Danger Zone - Moved to left column */}
            <div className="p-6 notely-card rounded-lg shadow-sm" style={{ borderColor: '#ef4444' }}>
              <h2 className="text-lg font-semibold mb-4" style={{ color: '#ef4444' }}>{t('settings.dangerZone')}</h2>
              <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--notely-surface)', border: '1px solid #ef4444' }}>
                <h3 className="font-medium mb-2" style={{ color: '#ef4444' }}>{t('settings.clearAllData')}</h3>
                <p className="text-sm mb-4" style={{ color: 'var(--notely-text-secondary)' }}>
                  {t('settings.clearAllDataWarning')}
                </p>
                <button
                  onClick={handleClearData}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm transition-colors"
                >
                  {t('settings.clearAllDataButton')}
                </button>
              </div>
            </div>
          </div>
          
          {/* Right Column */}
          <div className="space-y-6">
            {/* Platform Integrations Section */}
            <div className="p-6 notely-card rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold mb-3" style={{ color: 'var(--notely-text-primary)' }}>{t('settings.platformIntegrations')}</h2>
              <p className="text-sm mb-4" style={{ color: 'var(--notely-text-secondary)' }}>{t('settings.platformIntegrationsDescription')}</p>
              
              {/* Platform Toggle Switches */}
              <div className="space-y-4 mt-6">
                {(Object.keys(defaultPlatformIntegrations) as PlatformKeys[]).map((platform) => (
                  <div key={platform} className="flex justify-between items-center py-3 last:border-0" style={{ borderBottom: '1px solid var(--notely-border)' }}>
                    <div className="flex items-center">
                      <div className="mr-3">
                        <PlatformIcon platform={platform} />
                      </div>
                      <span className="text-sm font-medium" style={{ color: 'var(--notely-text-primary)' }}>{platformDisplayNames[platform]}</span>
                    </div>
                    <Toggle
                      enabled={platformIntegrations[platform]}
                      onChange={async (enabled) => {
                        console.log(`[Settings] Platform toggle ${platform}:`, enabled);

                        // Update state immediately
                        const newIntegrations = {
                          ...platformIntegrations,
                          [platform]: enabled
                        };

                        console.log('[Settings] Updated platform integrations:', newIntegrations);
                        setPlatformIntegrations(newIntegrations);

                        // Save to storage immediately with the new state
                        try {
                          await chrome.storage.local.set({
                            [CLOUD_STORAGE_KEY]: saveMediaToCloud,
                            [PLATFORM_INTEGRATIONS_KEY]: newIntegrations
                          });

                          console.log(`[Settings] Platform ${platform} ${enabled ? 'enabled' : 'disabled'} successfully`);

                          setSaveStatus({
                            show: true,
                            message: t('common.settingsSaved') || 'Settings saved successfully',
                            isError: false
                          });
                        } catch (error) {
                          console.error(`[Settings] Error saving platform ${platform} setting:`, error);
                          setSaveStatus({
                            show: true,
                            message: `Failed to save ${platform} setting. Please try again.`,
                            isError: true
                          });

                          // Revert the state change on error
                          setPlatformIntegrations(platformIntegrations);
                        }
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Storage Stats */}
            {!loading && Object.keys(stats).length > 0 && (
              <div className="p-6 notely-card rounded-lg shadow-sm">
                <h2 className="text-lg font-semibold mb-4" style={{ color: 'var(--notely-text-primary)' }}>{t('settings.storageStats')}</h2>
                <div className="space-y-4">
                  {Object.entries(stats).map(([platform, stat]) => (
                    <div 
                      key={platform} 
                      className="rounded-md p-4 transition-colors" 
                      style={{ 
                        border: '1px solid var(--notely-border)',
                        '&:hover': { backgroundColor: 'var(--notely-surface)' }
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--notely-surface)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <div className="flex justify-between">
                        <span className="font-medium capitalize" style={{ color: 'var(--notely-text-primary)' }}>{platform}</span>
                        <span className="text-sm font-medium" style={{ color: 'var(--notely-text-secondary)' }}>
                          {stat.posts} {t('settings.posts')}
                        </span>
                      </div>
                      <div className="mt-2">
                        <span className="text-sm" style={{ color: 'var(--notely-text-secondary)' }}>
                          {t('settings.mediaSize')}: {formatFileSize(stat.mediaSize || 0)}
                        </span>
                        {stat.lastUpdated && (
                          <span className="block text-xs mt-1" style={{ color: 'var(--notely-text-muted)' }}>
                            {t('settings.lastUpdated')}: {new Date(stat.lastUpdated).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
        <SaveStatus />
      </div>
    </div>
  );
}

// SaveStatus component is defined at the top level of the file
// to avoid any potential issues with hooks
