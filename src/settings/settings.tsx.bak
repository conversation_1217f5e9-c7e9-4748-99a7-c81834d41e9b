import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { createRoot } from 'react-dom/client';
import { LocaleProvider } from '../contexts/LocaleContext';
import { <PERSON>ArrowLeft, FiCheckCircle, <PERSON>Twitter, FiGlobe } from 'react-icons/fi';
import { FaLinkedinIn, FaRedditAlien, FaInstagram, FaPinterest } from 'react-icons/fa';
import { Platform } from '../types';
import '../index.css';
import { getSavedPosts } from '../storage';
import { BackupService } from '../utils/backup';
import { useTranslation } from '../hooks/useTranslation';
import { useLocale } from '../contexts/useLocale';
import { Toggle } from '../components/Toggle';

// --- Storage Keys ---
const CLOUD_STORAGE_KEY = 'saveMediaToCloud';
const MINDSTREAM_TIPS_KEY = 'showMindstreamTips';
const PLATFORM_INTEGRATIONS_KEY = 'platformIntegrations';
// -------------------------------------

const languageOptions = [
  { code: 'en', name: 'English' },
  { code: 'tr', name: 'Türkçe' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: 'Deutsch' },
  { code: 'es', name: 'Español' },
];

interface StorageStats {
  posts: number;
  mediaSize: number;
  lastUpdated?: string;
}

type PlatformKeys = 'X/Twitter' | 'LinkedIn' | 'Reddit' | 'Instagram' | 'pinterest' | 'Web';

// Default platform integrations - all enabled by default
const defaultPlatformIntegrations: Record<PlatformKeys, boolean> = {
  'X/Twitter': true,
  'LinkedIn': true,
  'Reddit': true,
  'Instagram': true,
  'pinterest': true,
  'Web': true
};

// Platform display names and icons
const platformInfo: Record<PlatformKeys, { name: string; icon: React.ComponentType; color: string }> = {
  'X/Twitter': { name: 'X / Twitter', icon: FiTwitter, color: 'text-black' },
  'LinkedIn': { name: 'LinkedIn', icon: FaLinkedinIn, color: 'text-blue-600' },
  'Reddit': { name: 'Reddit', icon: FaRedditAlien, color: 'text-orange-500' },
  'Instagram': { name: 'Instagram', icon: FaInstagram, color: 'text-purple-500' },
  'pinterest': { name: 'Pinterest', icon: FaPinterest, color: 'text-red-600' },
  'Web': { name: 'Web Articles', icon: FiGlobe, color: 'text-green-500' }
};

export function Settings() {
  // State management
  const [stats, setStats] = useState<Record<string, StorageStats>>({});
  const [loading, setLoading] = useState(true);
  const [backupStatus, setBackupStatus] = useState<string>('');
  const [saveMediaToCloud, setSaveMediaToCloud] = useState<boolean>(true);
  const [showMindstreamTips, setShowMindstreamTips] = useState<boolean>(true);
  const [platformIntegrations, setPlatformIntegrations] = useState<Record<PlatformKeys, boolean>>(defaultPlatformIntegrations);
  const [saveStatus, setSaveStatus] = useState<{show: boolean; message: string; isError: boolean}>({
    show: false, 
    message: '', 
    isError: false
  });
  
  // Hooks
  const { t } = useTranslation();
  const { locale, setLocale } = useLocale();
  
  // Services
  const backup = useMemo(() => new BackupService(), []);

  // Show save status and auto-hide after 3 seconds
  useEffect(() => {
    if (saveStatus.show) {
      const timer = setTimeout(() => {
        setSaveStatus(prev => ({...prev, show: false}));
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [saveStatus.show]);
  
  // Save settings to storage
  const saveSettings = useCallback(async () => {
    try {
      await chrome.storage.local.set({
        [CLOUD_STORAGE_KEY]: saveMediaToCloud,
        [MINDSTREAM_TIPS_KEY]: showMindstreamTips,
        [PLATFORM_INTEGRATIONS_KEY]: platformIntegrations,
        locale
      });
      setSaveStatus({
        show: true,
        message: t('common.settingsSaved'),
        isError: false
      });
    } catch (e) {
      console.error('Error saving settings:', e);
      setSaveStatus({
        show: true,
        message: 'Failed to save settings',
        isError: true
      });
    }
  }, [saveMediaToCloud, showMindstreamTips, locale, t]);

  // Load settings from storage
  const loadSettings = useCallback(async () => {
    setLoading(true);
    try {
      // Load cloud storage setting
      const cloudStorageResult = await new Promise<{[key: string]: any}>((resolve) => {
        chrome.storage.local.get([CLOUD_STORAGE_KEY], (result) => {
          resolve(result);
        });
      });
      
      setSaveMediaToCloud(cloudStorageResult[CLOUD_STORAGE_KEY] !== false); // Default to true
      
      // Load mindstream tips setting
      const mindstreamTipsResult = await new Promise<{[key: string]: any}>((resolve) => {
        chrome.storage.local.get([MINDSTREAM_TIPS_KEY], (result) => {
          resolve(result);
        });
      });
      
      setShowMindstreamTips(mindstreamTipsResult[MINDSTREAM_TIPS_KEY] !== false); // Default to true
      
      // Load platform integrations settings
      const platformIntegrationsResult = await new Promise<{[key: string]: any}>((resolve) => {
        chrome.storage.local.get([PLATFORM_INTEGRATIONS_KEY], (result) => {
          resolve(result);
        });
      });
      
      // If platform integrations exist in storage, use them; otherwise, use defaults
      if (platformIntegrationsResult[PLATFORM_INTEGRATIONS_KEY]) {
        setPlatformIntegrations(platformIntegrationsResult[PLATFORM_INTEGRATIONS_KEY]);
      } else {
        // Initialize with defaults if not found in storage
        setPlatformIntegrations(defaultPlatformIntegrations);
      }
      
      // Load stats
      const posts = await getSavedPosts();
      const stats: StorageStats = {};
      
      if (posts && posts.length > 0) {
        stats.posts = posts.length;
        stats.mediaSize = posts.reduce((sum: number, post: any) => {
          if (post.mediaItems) {
            return sum + post.mediaItems.reduce((mediaSum: number, item: any) => mediaSum + (item.size || 0), 0);
          }
          return sum;
        }, 0);
      }
      
      setStats(stats);
    } catch (e) {
      console.error('Error loading settings:', e);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
    // We're intentionally omitting loadStats and setLocale from the dependency array
    // because they're stable and we only want to run this effect once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Load statistics
  const loadStats = useCallback(async () => {
    try {
      const posts = await getSavedPosts();
      const statsByPlatform: StorageStats = {};

      posts.forEach(post => {
        if (!statsByPlatform[post.platform]) {
          statsByPlatform[post.platform] = {
            posts: 0,
            mediaSize: 0,
            lastUpdated: post.savedAt
          };
        }

        statsByPlatform[post.platform].posts += 1;
        const mediaSize = post.mediaItems?.reduce((sum, item) => sum + (item.size || 0), 0) || 0;
        statsByPlatform[post.platform].mediaSize = (statsByPlatform[post.platform].mediaSize || 0) + mediaSize;

        const postDate = new Date(post.savedAt);
        const lastUpdated = new Date(statsByPlatform[post.platform].lastUpdated || 0);
        if (postDate > lastUpdated) {
          statsByPlatform[post.platform].lastUpdated = post.savedAt;
        }
      });

      setStats(statsByPlatform);
    } catch (error) {
      console.error('Failed to load stats:', error);
      setError('Failed to load statistics');
    }
  }, []);

  // Handle language change
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLocale(e.target.value);
  };

  // Handle back button click
  const handleBack = () => {
    window.history.back();
  };

  // Handle backup
  const handleBackup = useCallback(async () => {
    try {
      const data = await backup.createBackup();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `social-saver-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      setBackupStatus('Backup created successfully!');
    } catch (error) {
      console.error('Backup failed:', error);
      setBackupStatus('Backup failed. Please try again.');
    }
  }, [backup]);

  // Handle restore
  const handleRestore = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        await backup.restoreBackup(data);
        setBackupStatus('Backup restored successfully!');
        await loadStats();
      } catch (error) {
        console.error('Restore failed:', error);
        setBackupStatus('Failed to restore backup. The file might be corrupted.');
      }
    };
    reader.readAsText(file);
  }, [backup, loadStats]);

  // Handle clear data
  const handleClearData = useCallback(async () => {
    if (window.confirm(t('settings.clearAllDataWarning'))) {
      try {
        // Clear all storage data
        await chrome.storage.local.clear();
        await chrome.storage.sync.clear();

        // Clear IndexedDB
        const deleteDB = indexedDB.deleteDatabase('social-saver-images');
        deleteDB.onsuccess = () => console.log('IndexedDB cleared');
        deleteDB.onerror = (e) => console.error('Failed to clear IndexedDB:', e);

        setBackupStatus('All data has been cleared.');
        await loadStats();
      } catch (error) {
        console.error('Failed to clear data:', error);
        setBackupStatus('Failed to clear data. Please try again.');
      }
    }
  }, [loadStats, t]);

  // Save status notification
  const SaveStatus = () => {
    if (!saveStatus.show) return null;
    
    return (
      <div 
        className={`fixed bottom-4 right-4 p-4 rounded-md shadow-lg ${
          saveStatus.isError ? 'bg-red-100 border-l-4 border-red-500 text-red-700' : 'bg-green-100 border-l-4 border-green-500 text-green-700'
        }`}
        role="alert"
      >
        <div className="flex items-center">
          <FiCheckCircle className="h-5 w-5 mr-2" />
          <p className="font-medium">{saveStatus.message}</p>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('common.loading')}...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center mb-6">
          <button
            onClick={handleBack}
            className="flex items-center text-blue-600 hover:text-blue-800 font-medium mr-4"
          >
            <FiArrowLeft className="mr-1" />
            {t('common.back')}
          </button>
          <h1 className="text-2xl font-bold text-gray-800">{t('settings.title')}</h1>
        </div>
        
        {/* Language Selection */}
        <div className="mb-6 border-b border-gray-200 pb-6">
          <div className="flex justify-between items-center">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('settings.language')}
              </label>
              <p className="text-sm text-gray-500">
                {languageOptions.find(lang => lang.code === locale)?.name}
              </p>
            </div>
            <select
              value={locale}
              onChange={handleLanguageChange}
              className="mt-1 block w-1/2 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md border"
            >
              {languageOptions.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Cloud Storage Toggle */}
        <div className="mb-6 border-b border-gray-200 pb-6">
          <div className="flex justify-between items-center">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('settings.cloudStorage')}
              </label>
              <p className="text-sm text-gray-500">
                {t('settings.cloudStorageDescription')}
              </p>
            </div>
            <div className="flex items-center">
              <div className="relative inline-block w-10 mr-2 align-middle select-none">
                <input
                  type="checkbox"
                  id="cloud-storage"
                  checked={saveMediaToCloud}
                  onChange={(e) => {
                    setSaveMediaToCloud(e.target.checked);
                    setTimeout(() => saveSettings(), 100);
                  }}
                  className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                />
                <label
                  htmlFor="cloud-storage"
                  className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${
                    saveMediaToCloud ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                ></label>
              </div>
              <span className="text-sm text-gray-700">
                {saveMediaToCloud ? t('common.on') : t('common.off')}
              </span>
            </div>
          </div>
        </div>
        
        {/* Mindstream Tips Toggle */}
        <div className="mb-6 border-b border-gray-200 pb-6">
          <div className="flex justify-between items-center">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mindstream Tips
              </label>
              <p className="text-sm text-gray-500">
                Show helpful tips in the Mindstream tab
              </p>
            </div>
            <div className="flex items-center">
              <Toggle
                enabled={showMindstreamTips}
                onChange={(enabled) => {
                  setShowMindstreamTips(enabled);
                  setTimeout(() => saveSettings(), 100);
                }}
              />
            </div>
          </div>
        </div>
        
        {/* Platform Integrations Section */}
        <div className="mb-6 border-b border-gray-200 pb-6">
          <h2 className="text-lg font-semibold text-gray-700 mb-3">{t('settings.platformIntegrations') || 'Platform Integrations'}</h2>
          <p className="text-sm text-gray-500 mb-4">{t('settings.platformIntegrationsDescription') || 'Enable or disable integrations with social media platforms. When disabled, no content will be injected on those platforms.'}</p>
          
          {/* Platform Toggle Switches */}
          <div className="space-y-4">
            {Object.entries(defaultPlatformIntegrations).map(([platform, _]) => (
              <div key={platform} className="flex justify-between items-center py-2">
                <div className="flex items-center">
                  <div className={`mr-3 ${platformInfo[platform as Platform].color}`}>
                    {React.createElement(platformInfo[platform as Platform].icon, { className: 'w-5 h-5' })}
                  </div>
                  <span className="text-sm font-medium">{platformInfo[platform as Platform].name}</span>
                </div>
                <Toggle
                  enabled={platformIntegrations[platform as Platform]}
                  onChange={(enabled) => {
                    setPlatformIntegrations(prev => ({
                      ...prev,
                      [platform]: enabled
                    }));
                    setTimeout(() => saveSettings(), 100);
                  }}
                />
              </div>
            ))}
          </div>
        </div>                  className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                />
                <label
                  htmlFor="mindstream-tips"
                  className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${
                    showMindstreamTips ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                ></label>
              </div>
              <span className="text-sm text-gray-700">
                {saveMediaToCloud ? t('common.on') : t('common.off')}
              </span>
            </div>
          </div>
        </div>

        {/* Storage Statistics */}
        {Object.keys(stats).length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-700 mb-3">{t('settings.storageStats')}</h2>
            <div className="space-y-3">
              {Object.entries(stats).map(([platform, stat]) => (
                <div key={platform} className="border rounded-md p-4">
                  <div className="flex justify-between">
                    <span className="font-medium capitalize">{platform}</span>
                    <span className="text-sm text-gray-500">
                      {stat.posts} {t('settings.posts')}
                    </span>
                  </div>
                  <div className="mt-1">
                    <span className="text-sm text-gray-500">
                      {t('settings.mediaSize')}: {formatFileSize(stat.mediaSize || 0)}
                    </span>
                    {stat.lastUpdated && (
                      <span className="block text-xs text-gray-400 mt-1">
                        {t('settings.lastUpdated')}: {new Date(stat.lastUpdated).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Backup & Restore */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-700 mb-3">{t('settings.backupRestore')}</h2>
          <div className="space-y-4">
            <div>
              <button
                onClick={handleBackup}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {t('settings.createBackup')}
              </button>
              <p className="mt-1 text-sm text-gray-500">{t('settings.backupDescription')}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('settings.restoreBackup')}
              </label>
              <input
                type="file"
                accept=".json"
                onChange={handleRestore}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p className="mt-1 text-sm text-gray-500">{t('settings.restoreDescription')}</p>
            </div>
            
            {backupStatus && (
              <p className={`text-sm ${backupStatus.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
                {backupStatus}
              </p>
            )}
          </div>
        </div>

        {/* Clear Data */}
        <div className="border-t border-red-200 pt-6">
          <h2 className="text-lg font-semibold text-red-700 mb-3">{t('settings.dangerZone')}</h2>
          <div className="bg-red-50 p-4 rounded-md">
            <h3 className="font-medium text-red-800">{t('settings.clearAllData')}</h3>
            <p className="text-sm text-red-600 mt-1 mb-3">
              {t('settings.clearAllDataWarning')}
            </p>
            <button
              onClick={handleClearData}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm"
            >
              {t('settings.clearAllDataButton')}
            </button>
          </div>
        </div>
      </div>
      <SaveStatus />
    </div>
  );
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

const container = document.getElementById('root');
if (container) {
  const root = createRoot(container);
  root.render(
    <React.StrictMode>
      <LocaleProvider>
        <Settings />
      </LocaleProvider>
    </React.StrictMode>
  );
}
