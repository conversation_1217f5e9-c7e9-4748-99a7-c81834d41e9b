document.addEventListener('DOMContentLoaded', () => {
  const loginButton = document.getElementById('login-button');
  const statusElement = document.getElementById('status');
  
  console.log('[Popup] Initialized');
  
  // Check initial auth status
  checkAuthStatus();
  
  loginButton.addEventListener('click', async () => {
    try {
      console.log('[Popup] Login button clicked');
      statusElement.textContent = 'Logging in...';
      loginButton.disabled = true;
      
      const response = await chrome.runtime.sendMessage({ action: 'login' });
      console.log('[Popup] Login response:', response);
      
      if (response && response.success) {
        statusElement.textContent = 'Logged in successfully!';
        loginButton.style.display = 'none';
      } else {
        const errorMsg = response && response.error ? response.error : 'Unknown error';
        console.error('[Popup] Login failed:', errorMsg);
        statusElement.textContent = `Login failed: ${errorMsg}`;
        loginButton.disabled = false;
      }
    } catch (error) {
      console.error('[Popup] Login error:', error);
      statusElement.textContent = `Error: ${error.message}`;
      loginButton.disabled = false;
    }
  });
});

async function checkAuthStatus() {
  const loginButton = document.getElementById('login-button');
  const statusElement = document.getElementById('status');
  
  try {
    console.log('[Popup] Checking auth status');
    const status = await chrome.runtime.sendMessage({ action: 'check_auth_status' });
    console.log('[Popup] Auth status:', status);
    
    if (status && status.isAuthenticated) {
      statusElement.textContent = 'Already logged in!';
      loginButton.style.display = 'none';
    } else {
      statusElement.textContent = 'Not logged in';
      loginButton.style.display = 'block';
      loginButton.disabled = false;
    }
  } catch (error) {
    console.error('[Popup] Error checking status:', error);
    statusElement.textContent = `Error checking status: ${error.message}`;
    loginButton.style.display = 'block';
    loginButton.disabled = false;
  }
} 