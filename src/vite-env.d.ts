/// <reference types="vite/client" />
/// <reference types="@types/chrome" />

// Add type definitions for React components
declare module '*.tsx' {
  import type { DefineComponent } from 'react';
  const component: DefineComponent<Record<string, unknown>, Record<string, unknown>, unknown>;
  export default component;
}

// Add type definitions for CSS modules
declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

// Add type definitions for Chrome extension APIs
declare namespace chrome {
  namespace storage {
    interface StorageArea {
      get(keys: string | string[] | Record<string, unknown>, callback: (items: Record<string, unknown>) => void): void;
      set(items: Record<string, unknown>, callback?: () => void): void;
      remove(keys: string | string[], callback?: () => void): void;
      clear(callback?: () => void): void;
    }

    const local: StorageArea;
    const sync: StorageArea;
    const managed: StorageArea;
  }
}
