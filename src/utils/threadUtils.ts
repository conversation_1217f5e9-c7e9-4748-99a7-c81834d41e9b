import { Post } from '../types';

export interface ThreadGroup {
  id: string;
  posts: Post[];
  isThread: true;
  firstPost: Post;
  threadLength: number;
  platform: string;
  author: string;
  savedAt: string;
}

export interface PostOrThread {
  type: 'post' | 'thread';
  data: Post | ThreadGroup;
}

/**
 * Group posts by threads and return a mixed array of individual posts and thread groups
 */
export function groupPostsByThreads(posts: Post[]): PostOrThread[] {
  console.log('[ThreadUtils] groupPostsByThreads called with', posts.length, 'posts');

  const result: PostOrThread[] = [];
  const processedPostIds = new Set<string>();

  // Create a map of threadId to posts
  const threadMap = new Map<string, Post[]>();
  const individualPosts: Post[] = [];

  posts.forEach(post => {
    if (post.isThread && post.threadId) {
      console.log('[ThreadUtils] Found thread post:', post.id, 'threadId:', post.threadId, 'position:', post.threadPosition);
      // This is part of a thread
      if (!threadMap.has(post.threadId)) {
        threadMap.set(post.threadId, []);
      }
      threadMap.get(post.threadId)!.push(post);
    } else {
      // This is an individual post
      individualPosts.push(post);
    }
  });
  
  // Process threads
  console.log('[ThreadUtils] Processing threads. Found', threadMap.size, 'unique thread IDs');
  threadMap.forEach((threadPosts, threadId) => {
    console.log('[ThreadUtils] Thread', threadId, 'has', threadPosts.length, 'posts');

    if (threadPosts.length > 1) {
      // Sort posts by thread position
      const sortedPosts = threadPosts.sort((a, b) =>
        (a.threadPosition || 0) - (b.threadPosition || 0)
      );

      const firstPost = sortedPosts[0];
      const threadGroup: ThreadGroup = {
        id: threadId,
        posts: sortedPosts,
        isThread: true,
        firstPost,
        threadLength: sortedPosts.length,
        platform: firstPost.platform,
        author: firstPost.author,
        savedAt: firstPost.savedAt
      };

      console.log('[ThreadUtils] Creating thread group:', threadGroup);

      result.push({
        type: 'thread',
        data: threadGroup
      });

      // Mark all posts in this thread as processed
      sortedPosts.forEach(post => {
        if (post.id) {
          processedPostIds.add(post.id);
        }
      });
    } else {
      // Single post marked as thread - treat as individual post
      console.log('[ThreadUtils] Single post in thread, treating as individual post');
      individualPosts.push(...threadPosts);
    }
  });
  
  // Add individual posts that weren't part of threads
  individualPosts.forEach(post => {
    if (!processedPostIds.has(post.id || '')) {
      result.push({
        type: 'post',
        data: post
      });
    }
  });
  
  // Sort by saved date (most recent first)
  result.sort((a, b) => {
    const aDate = a.type === 'thread' ? a.data.savedAt : a.data.savedAt;
    const bDate = b.type === 'thread' ? b.data.savedAt : b.data.savedAt;
    return new Date(bDate).getTime() - new Date(aDate).getTime();
  });
  
  return result;
}

/**
 * Get all posts from a mixed array of posts and threads
 */
export function getAllPostsFromMixed(items: PostOrThread[]): Post[] {
  const allPosts: Post[] = [];
  
  items.forEach(item => {
    if (item.type === 'post') {
      allPosts.push(item.data as Post);
    } else {
      allPosts.push(...(item.data as ThreadGroup).posts);
    }
  });
  
  return allPosts;
}

/**
 * Filter posts and threads by platform
 */
export function filterPostsAndThreadsByPlatform(
  items: PostOrThread[], 
  platform: string
): PostOrThread[] {
  if (platform === 'All') {
    return items;
  }
  
  return items.filter(item => {
    if (item.type === 'post') {
      return (item.data as Post).platform === platform;
    } else {
      return (item.data as ThreadGroup).platform === platform;
    }
  });
}

/**
 * Filter posts and threads by category
 */
export function filterPostsAndThreadsByCategory(
  items: PostOrThread[], 
  category: string
): PostOrThread[] {
  if (!category || category === 'All') {
    return items;
  }
  
  return items.filter(item => {
    if (item.type === 'post') {
      const post = item.data as Post;
      return post.categories?.includes(category);
    } else {
      const thread = item.data as ThreadGroup;
      // Check if any post in the thread has this category
      return thread.posts.some(post => post.categories?.includes(category));
    }
  });
}

/**
 * Filter posts and threads by tag
 */
export function filterPostsAndThreadsByTag(
  items: PostOrThread[], 
  tag: string
): PostOrThread[] {
  if (!tag || tag === 'All') {
    return items;
  }
  
  return items.filter(item => {
    if (item.type === 'post') {
      const post = item.data as Post;
      return post.tags?.includes(tag);
    } else {
      const thread = item.data as ThreadGroup;
      // Check if any post in the thread has this tag
      return thread.posts.some(post => post.tags?.includes(tag));
    }
  });
}

/**
 * Search posts and threads by content
 */
export function searchPostsAndThreads(
  items: PostOrThread[], 
  searchTerm: string
): PostOrThread[] {
  if (!searchTerm.trim()) {
    return items;
  }
  
  const lowerSearchTerm = searchTerm.toLowerCase();
  
  return items.filter(item => {
    if (item.type === 'post') {
      const post = item.data as Post;
      return (
        post.content?.toLowerCase().includes(lowerSearchTerm) ||
        post.author?.toLowerCase().includes(lowerSearchTerm) ||
        post.title?.toLowerCase().includes(lowerSearchTerm)
      );
    } else {
      const thread = item.data as ThreadGroup;
      // Check if any post in the thread matches the search
      return thread.posts.some(post => 
        post.content?.toLowerCase().includes(lowerSearchTerm) ||
        post.author?.toLowerCase().includes(lowerSearchTerm) ||
        post.title?.toLowerCase().includes(lowerSearchTerm)
      );
    }
  });
}

/**
 * Get thread statistics
 */
export function getThreadStatistics(posts: Post[]): {
  totalThreads: number;
  totalThreadPosts: number;
  averageThreadLength: number;
  longestThread: number;
} {
  const threadMap = new Map<string, Post[]>();
  
  posts.forEach(post => {
    if (post.isThread && post.threadId) {
      if (!threadMap.has(post.threadId)) {
        threadMap.set(post.threadId, []);
      }
      threadMap.get(post.threadId)!.push(post);
    }
  });
  
  const threadLengths = Array.from(threadMap.values())
    .filter(threadPosts => threadPosts.length > 1)
    .map(threadPosts => threadPosts.length);
  
  const totalThreads = threadLengths.length;
  const totalThreadPosts = threadLengths.reduce((sum, length) => sum + length, 0);
  const averageThreadLength = totalThreads > 0 ? totalThreadPosts / totalThreads : 0;
  const longestThread = threadLengths.length > 0 ? Math.max(...threadLengths) : 0;
  
  return {
    totalThreads,
    totalThreadPosts,
    averageThreadLength: Math.round(averageThreadLength * 10) / 10,
    longestThread
  };
}
