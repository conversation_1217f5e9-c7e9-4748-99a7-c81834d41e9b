// Theme utility functions for Notely

export type Theme = 'light' | 'dark';

/**
 * Initialize theme on page load
 */
export const initializeTheme = (): void => {
  // Load theme preference from storage
  chrome.storage.local.get(['theme'], (result) => {
    const savedTheme = result.theme || 'dark'; // Default to dark mode
    applyTheme(savedTheme);
  });
};

/**
 * Apply theme to the document
 */
export const applyTheme = (theme: Theme): void => {
  const root = document.documentElement;

  if (theme === 'dark') {
    root.classList.remove('light-theme');
  } else {
    root.classList.add('light-theme');
  }

  // Store theme preference
  chrome.storage.local.set({ theme });
};

/**
 * Toggle between light and dark themes
 */
export const toggleTheme = (): Promise<Theme> => {
  return new Promise((resolve) => {
    chrome.storage.local.get(['theme'], (result) => {
      const currentTheme = result.theme || 'dark';
      const newTheme: Theme = currentTheme === 'dark' ? 'light' : 'dark';
      
      applyTheme(newTheme);
      resolve(newTheme);
    });
  });
};

/**
 * Get current theme
 */
export const getCurrentTheme = (): Promise<Theme> => {
  return new Promise((resolve) => {
    chrome.storage.local.get(['theme'], (result) => {
      const theme = result.theme || 'dark';
      resolve(theme);
    });
  });
};

/**
 * Listen for theme changes
 */
export const onThemeChange = (callback: (theme: Theme) => void): void => {
  chrome.storage.onChanged.addListener((changes, areaName) => {
    if (areaName === 'local' && changes.theme) {
      callback(changes.theme.newValue);
    }
  });
};

// Initialize theme when this module is imported
if (typeof document !== 'undefined') {
  // Only initialize if we're in a browser environment
  document.addEventListener('DOMContentLoaded', initializeTheme);
  
  // If DOM is already loaded, initialize immediately
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTheme);
  } else {
    initializeTheme();
  }
}
