export const sanitizeCategoryName = (name: string): string => {
  return name.trim();
};

export const generateUniqueName = (
  name: string,
  existingNames: string[],
  maxAttempts = 100
): string => {
  const normalizedExisting = new Set(
    existingNames.map(n => n.toLowerCase())
  );
  
  let baseName = sanitizeCategoryName(name);
  let attempt = 1;
  let candidateName = baseName;
  
  while (
    attempt < maxAttempts && 
    normalizedExisting.has(candidateName.toLowerCase())
  ) {
    candidateName = `${baseName} (${attempt})`;
    attempt++;
  }
  
  return candidateName;
}; 