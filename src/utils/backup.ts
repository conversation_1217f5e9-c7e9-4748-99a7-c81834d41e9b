import { getSavedPosts, savePost, clearAllData } from '../storage';
import { Post } from '../types';

interface BackupPlatform {
  posts: Post[];
  mediaCount: number;
}

export interface BackupData {
  version: number;
  date: string;
  platforms: Record<string, BackupPlatform>;
}

export class BackupService {
  async createBackup(): Promise<BackupData> {
    const backup: BackupData = {
      version: 1,
      date: new Date().toISOString(),
      platforms: {}
    };

    // Get all posts and group by platform
    const allPosts = await getSavedPosts();
    const platformGroups: { [platform: string]: Post[] } = {};

    // Group posts by platform
    allPosts.forEach(post => {
      if (!platformGroups[post.platform]) {
        platformGroups[post.platform] = [];
      }
      platformGroups[post.platform].push(post);
    });

    // Create backup data for each platform
    for (const [platform, posts] of Object.entries(platformGroups)) {
      const mediaItems = posts.reduce((count, post) => count + (post.media?.length || 0), 0);

      backup.platforms[platform] = {
        posts,
        mediaCount: mediaItems
      };
    }

    return backup;
  }

  async exportBackup(): Promise<void> {
    const backup = await this.createBackup();
    const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `social-saver-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  async restoreBackup(backupData: BackupData): Promise<{ success: boolean; imported: number }> {
    try {
      if (!backupData.version || !backupData.platforms) {
        throw new Error('Invalid backup file format');
      }

      let importedCount = 0;

      for (const [platform, data] of Object.entries(backupData.platforms)) {
        for (const post of data.posts) {
          const result = await savePost(post);
          if (result.status === 'success') {
            importedCount++;
          }
        }
      }

      return { success: true, imported: importedCount };
    } catch (error) {
      console.error('Restore failed:', error);
      return { success: false, imported: 0 };
    }
  }
}