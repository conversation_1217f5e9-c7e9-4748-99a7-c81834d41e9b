/**
 * Utility functions for capturing post cards as images
 */

import html2canvas from 'html2canvas';

/**
 * Captures a DOM element as an image using html2canvas
 * @param element The DOM element to capture
 * @param options Optional configuration for html2canvas
 * @returns Promise<string> containing the data URL of the captured image
 */
export async function captureElementAsImage(
  element: HTMLElement,
  options: any = {}
): Promise<string> {
  console.log('[postCardCapture] Starting capture with html2canvas...');

  const defaultOptions = {
    allowTaint: true,
    useCORS: true,
    logging: false,
    scale: 2, // Higher quality
    backgroundColor: '#ffffff', // Ensure white background
    width: element.offsetWidth,
    height: element.offsetHeight,
    // Ensure fonts are loaded
    onclone: (clonedDoc: Document) => {
      // Wait for fonts to load in the cloned document
      return new Promise<void>((resolve) => {
        if (clonedDoc.fonts && clonedDoc.fonts.ready) {
          clonedDoc.fonts.ready.then(() => resolve());
        } else {
          // Fallback timeout
          setTimeout(() => resolve(), 100);
        }
      });
    },
    ...options
  };

  try {
    console.log('[postCardCapture] Element to capture:', element);
    console.log('[postCardCapture] Element dimensions:', {
      width: element.offsetWidth,
      height: element.offsetHeight,
      scrollWidth: element.scrollWidth,
      scrollHeight: element.scrollHeight
    });

    const canvas = await html2canvas(element, defaultOptions);
    console.log('[postCardCapture] Canvas created successfully:', {
      width: canvas.width,
      height: canvas.height
    });

    const dataUrl = canvas.toDataURL('image/png', 1.0);
    console.log('[postCardCapture] Data URL generated, length:', dataUrl.length);

    return dataUrl;
  } catch (error) {
    console.error('[postCardCapture] Error capturing element as image:', error);
    console.error('[postCardCapture] Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      element: element
    });
    throw new Error(`Failed to capture element: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Downloads an image data URL as a PNG file
 * @param dataUrl The data URL of the image
 * @param filename The filename for the download (without extension)
 */
export function downloadImageAsFile(dataUrl: string, filename: string): void {
  try {
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = `${filename}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading image:', error);
    throw new Error('Failed to download image');
  }
}

/**
 * Copies an image data URL to the clipboard
 * @param dataUrl The data URL of the image
 */
export async function copyImageToClipboard(dataUrl: string): Promise<void> {
  try {
    // Convert data URL to blob
    const response = await fetch(dataUrl);
    const blob = await response.blob();

    // Check if clipboard API is available
    if (navigator.clipboard && window.ClipboardItem) {
      const clipboardItem = new ClipboardItem({ 'image/png': blob });
      await navigator.clipboard.write([clipboardItem]);
    } else {
      throw new Error('Clipboard API not available');
    }
  } catch (error) {
    console.error('Error copying image to clipboard:', error);
    throw new Error('Failed to copy image to clipboard');
  }
}

/**
 * Creates an authentic platform-styled post element for capture
 * @param postCardElement The original post card DOM element
 * @param platform The platform name for styling
 * @param post The post data
 * @returns Object with the styled element and cleanup function
 */
function createAuthenticPostElement(postCardElement: HTMLElement, platform: string, post: any): { element: HTMLElement; cleanup: () => void } {
  // Create a new container for the authentic post
  const authenticPost = document.createElement('div');
  authenticPost.style.position = 'fixed';
  authenticPost.style.top = '-9999px';
  authenticPost.style.left = '-9999px';
  authenticPost.style.zIndex = '-1';

  // Get engagement metrics
  const stats = post.stats || {};
  const interactions = post.interactions || {};
  const metrics = {
    comments: stats.comments ?? interactions.replies ?? 0,
    shares: stats.shares ?? interactions.reposts ?? 0,
    likes: stats.likes ?? interactions.likes ?? 0,
    views: stats.views ?? 0
  };

  switch (platform) {
    case 'X/Twitter':
      authenticPost.innerHTML = createTwitterPost(post, metrics);
      break;
    case 'LinkedIn':
      authenticPost.innerHTML = createLinkedInPost(post, metrics);
      break;
    case 'Instagram':
      authenticPost.innerHTML = createInstagramPost(post, metrics);
      break;
    case 'Reddit':
      authenticPost.innerHTML = createRedditPost(post, metrics);
      break;
    case 'pinterest':
      authenticPost.innerHTML = createPinterestPost(post, metrics);
      break;
    default:
      // Fallback to original styling
      authenticPost.innerHTML = postCardElement.innerHTML;
  }

  // Add to DOM temporarily
  document.body.appendChild(authenticPost);

  return {
    element: authenticPost,
    cleanup: () => {
      if (authenticPost.parentNode) {
        document.body.removeChild(authenticPost);
      }
    }
  };
}

/**
 * Creates an authentic Twitter/X post HTML
 */
function createTwitterPost(post: any, metrics: any): string {
  const authorAvatar = post.authorAvatar || post.authorImage || '';
  const authorName = post.authorName || post.author || 'User';

  // Generate author handle if not available
  let authorHandle = post.authorHandle;
  if (!authorHandle || !authorHandle.startsWith('@')) {
    // Try to extract from author or create a reasonable handle
    const baseHandle = post.author || authorName || 'user';
    authorHandle = '@' + baseHandle.toLowerCase().replace(/[^a-z0-9_]/g, '').substring(0, 15);
  }

  const content = post.content || post.textContent || '';
  const timestamp = formatTwitterTime(post.timestamp || post.savedAt);
  const mainImage = post.savedImage || (post.media && post.media[0]?.url) || '';

  return `
    <div style="
      max-width: 598px;
      background: #ffffff;
      border: 1px solid #e1e8ed;
      border-radius: 16px;
      padding: 12px 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      font-size: 15px;
      line-height: 20px;
    ">
      <!-- Header -->
      <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
        <div style="
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #1d9bf0;
          margin-right: 12px;
          overflow: hidden;
          flex-shrink: 0;
        ">
          ${authorAvatar ? `<img src="${authorAvatar}" style="width: 100%; height: 100%; object-fit: cover;" />` :
            `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px;">${authorName.charAt(0).toUpperCase()}</div>`}
        </div>
        <div style="flex: 1; min-width: 0;">
          <div style="display: flex; align-items: center; gap: 4px;">
            <span style="font-weight: 700; color: #0f1419;">${authorName}</span>
            <span style="color: #536471;">${authorHandle}</span>
            <span style="color: #536471;">·</span>
            <span style="color: #536471;">${timestamp}</span>
          </div>
        </div>
      </div>

      <!-- Content -->
      ${content ? `<div style="color: #0f1419; margin-bottom: 12px; white-space: pre-wrap;">${content}</div>` : ''}

      <!-- Image -->
      ${mainImage ? `
        <div style="margin-bottom: 12px; border-radius: 16px; overflow: hidden; border: 1px solid #e1e8ed;">
          <img src="${mainImage}" style="width: 100%; height: auto; display: block;" />
        </div>
      ` : ''}

      <!-- Engagement -->
      <div style="display: flex; align-items: center; gap: 20px; color: #536471; font-size: 13px;">
        ${metrics.comments > 0 ? `
          <div style="display: flex; align-items: center; gap: 4px;">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
              <path d="M1.751 10c0-4.42 3.584-8.003 8.005-8.003h4.366c4.42 0 8.005 3.584 8.005 8.003 0 4.42-3.584 8.006-8.005 8.006H9.965l-6.077 2.798c-.508.234-1.102-.145-1.102-.702l.965-5.953C2.3 13.024 1.751 11.566 1.751 10z"/>
            </svg>
            <span>${formatNumber(metrics.comments)}</span>
          </div>
        ` : ''}
        ${metrics.shares > 0 ? `
          <div style="display: flex; align-items: center; gap: 4px;">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
              <path d="M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.791-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.791 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"/>
            </svg>
            <span>${formatNumber(metrics.shares)}</span>
          </div>
        ` : ''}
        ${metrics.likes > 0 ? `
          <div style="display: flex; align-items: center; gap: 4px;">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91zm4.187 7.69c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"/>
            </svg>
            <span>${formatNumber(metrics.likes)}</span>
          </div>
        ` : ''}
        ${metrics.views > 0 ? `
          <div style="display: flex; align-items: center; gap: 4px;">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.75 21V3h2v18h-2zM18 21V8.5h2V21h-2zM4 21l.004-10H6v10H4zm9.248 0v-7h2v7h-2z"/>
            </svg>
            <span>${formatNumber(metrics.views)}</span>
          </div>
        ` : ''}
      </div>
    </div>
  `;
}

/**
 * Helper function to format Twitter timestamp
 */
function formatTwitterTime(timestamp: string): string {
  if (!timestamp) return '1h';

  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s`;
    const diffInMinutes = Math.round(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    const diffInHours = Math.round(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h`;
    const diffInDays = Math.round(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  } catch (e) {
    return '1h';
  }
}

/**
 * Helper function to format numbers like Twitter
 */
function formatNumber(num: number): string {
  if (num < 1000) return num.toString();
  if (num < 1000000) return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
}

/**
 * Helper function to format LinkedIn timestamp
 */
function formatLinkedInTime(timestamp: string): string {
  if (!timestamp) return '1h';

  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s`;
    const diffInMinutes = Math.round(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    const diffInHours = Math.round(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h`;
    const diffInDays = Math.round(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d`;
    if (diffInDays < 30) return `${Math.round(diffInDays / 7)}w`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  } catch (e) {
    return '1h';
  }
}

/**
 * Helper function to format Instagram timestamp
 */
function formatInstagramTime(timestamp: string): string {
  if (!timestamp) return '1 hour ago';

  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    const diffInMinutes = Math.round(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
    const diffInHours = Math.round(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
    const diffInDays = Math.round(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
    if (diffInDays < 30) return `${Math.round(diffInDays / 7)} week${Math.round(diffInDays / 7) === 1 ? '' : 's'} ago`;
    return date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
  } catch (e) {
    return '1 hour ago';
  }
}

/**
 * Helper function to format Reddit timestamp
 */
function formatRedditTime(timestamp: string): string {
  if (!timestamp) return '1h ago';

  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s ago`;
    const diffInMinutes = Math.round(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.round(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.round(diffInHours / 24);
    if (diffInDays < 30) return `${diffInDays}d ago`;
    const diffInMonths = Math.round(diffInDays / 30);
    if (diffInMonths < 12) return `${diffInMonths}mo ago`;
    return `${Math.round(diffInMonths / 12)}y ago`;
  } catch (e) {
    return '1h ago';
  }
}

/**
 * Creates an authentic LinkedIn post HTML
 */
function createLinkedInPost(post: any, metrics: any): string {
  const authorAvatar = post.authorAvatar || post.authorImage || '';
  const authorName = post.authorName || post.author || 'User';
  const authorTitle = post.authorTitle || ''; // Use extracted professional title or empty string
  const content = post.content || post.textContent || '';
  const timestamp = formatLinkedInTime(post.timestamp || post.savedAt);
  const mainImage = post.savedImage || (post.media && post.media[0]?.url) || '';

  // Format timestamp to show relative time like LinkedIn
  const formatRelativeTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor(diffMs / (1000 * 60));

      if (diffDays > 7) {
        return `${Math.floor(diffDays / 7)}w`;
      } else if (diffDays > 0) {
        return `${diffDays}d`;
      } else if (diffHours > 0) {
        return `${diffHours}h`;
      } else if (diffMinutes > 0) {
        return `${diffMinutes}m`;
      } else {
        return 'now';
      }
    } catch {
      return '1h';
    }
  };

  const relativeTime = formatRelativeTime(timestamp);

  return `
    <div style="
      max-width: 552px;
      background: #ffffff;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      font-size: 14px;
      line-height: 20px;
      box-shadow: 0 0 0 1px rgba(0,0,0,0.15), 0 2px 3px rgba(0,0,0,0.2);
    ">
      <!-- Header -->
      <div style="padding: 12px 16px;">
        <div style="display: flex; align-items: flex-start;">
          <div style="
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #0a66c2;
            margin-right: 8px;
            overflow: hidden;
            flex-shrink: 0;
          ">
            ${authorAvatar ? `<img src="${authorAvatar}" style="width: 100%; height: 100%; object-fit: cover;" />` :
              `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 20px;">${authorName.charAt(0).toUpperCase()}</div>`}
          </div>
          <div style="flex: 1; min-width: 0;">
            <div style="display: flex; align-items: center; margin-bottom: 0;">
              <div style="font-weight: 600; color: #000000; font-size: 14px; line-height: 20px;">${authorName}</div>
              <div style="width: 16px; height: 16px; margin-left: 4px; background: #0a66c2; border-radius: 2px; display: flex; align-items: center; justify-content: center;">
                <div style="width: 10px; height: 8px; background: white; border-radius: 1px;"></div>
              </div>
            </div>
            ${authorTitle ? `<div style="color: #666666; font-size: 12px; line-height: 16px; margin-bottom: 0; margin-top: 0;">${authorTitle}</div>` : ''}
            <div style="color: #666666; font-size: 12px; line-height: 16px; display: flex; align-items: center; margin-top: 2px;">
              <span>${relativeTime}</span>
              <span style="margin: 0 4px;">•</span>
              <span style="font-size: 14px;">🌍</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Content -->
      ${content ? `<div style="padding: 0 16px 16px 16px; color: #000000; white-space: pre-wrap;">${content}</div>` : ''}

      <!-- Image -->
      ${mainImage ? `
        <div style="margin-bottom: 0;">
          <img src="${mainImage}" style="width: 100%; height: auto; display: block;" />
        </div>
      ` : ''}
              <path d="M21 3L0 10l7.66 4.26L16 8l-6.26 8.34L14 24l7-21z"/>
            </svg>
            <span>Send</span>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Creates an authentic Instagram post HTML
 */
function createInstagramPost(post: any, metrics: any): string {
  const authorAvatar = post.authorAvatar || post.authorImage || '';
  const authorName = post.authorName || post.author || 'user';
  const content = post.content || post.textContent || '';
  const timestamp = formatInstagramTime(post.timestamp || post.savedAt);
  const mainImage = post.savedImage || (post.media && post.media[0]?.url) || '';

  return `
    <div style="
      max-width: 470px;
      background: #ffffff;
      border: 1px solid #dbdbdb;
      border-radius: 8px;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      font-size: 14px;
      line-height: 18px;
    ">
      <!-- Header -->
      <div style="padding: 14px 16px; display: flex; align-items: center; justify-content: space-between;">
        <div style="display: flex; align-items: center;">
          <div style="
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            margin-right: 12px;
            overflow: hidden;
            flex-shrink: 0;
            padding: 2px;
          ">
            <div style="
              width: 100%;
              height: 100%;
              border-radius: 50%;
              background: white;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
            ">
              ${authorAvatar ? `<img src="${authorAvatar}" style="width: 26px; height: 26px; border-radius: 50%; object-fit: cover;" />` :
                `<div style="width: 26px; height: 26px; border-radius: 50%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999; font-weight: bold; font-size: 12px;">${authorName.charAt(0).toUpperCase()}</div>`}
            </div>
          </div>
          <div>
            <div style="font-weight: 600; color: #262626; font-size: 14px;">${authorName}</div>
          </div>
        </div>
        <div style="color: #262626;">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <circle cx="12" cy="12" r="1.5"/>
            <circle cx="6" cy="12" r="1.5"/>
            <circle cx="18" cy="12" r="1.5"/>
          </svg>
        </div>
      </div>

      <!-- Image -->
      ${mainImage ? `
        <div style="position: relative; width: 100%; padding-bottom: 100%; overflow: hidden;">
          <img src="${mainImage}" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" />
        </div>
      ` : ''}

      <!-- Action Bar -->
      <div style="padding: 6px 16px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div style="display: flex; gap: 16px;">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#262626" stroke-width="2">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </svg>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#262626" stroke-width="2">
              <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
            </svg>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#262626" stroke-width="2">
              <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
              <polyline points="16,6 12,2 8,6"/>
              <line x1="12" y1="2" x2="12" y2="15"/>
            </svg>
          </div>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#262626" stroke-width="2">
            <polygon points="5,3 19,12 5,21 5,3"/>
          </svg>
        </div>
      </div>

      <!-- Likes -->
      ${metrics.likes > 0 ? `
        <div style="padding: 0 16px 8px 16px;">
          <div style="font-weight: 600; color: #262626; font-size: 14px;">${formatNumber(metrics.likes)} likes</div>
        </div>
      ` : ''}

      <!-- Content -->
      ${content ? `
        <div style="padding: 0 16px 8px 16px;">
          <span style="font-weight: 600; color: #262626; margin-right: 8px;">${authorName}</span>
          <span style="color: #262626;">${content}</span>
        </div>
      ` : ''}

      <!-- Comments -->
      ${metrics.comments > 0 ? `
        <div style="padding: 0 16px 8px 16px;">
          <div style="color: #8e8e8e; font-size: 14px;">View all ${formatNumber(metrics.comments)} comments</div>
        </div>
      ` : ''}

      <!-- Timestamp -->
      <div style="padding: 0 16px 16px 16px;">
        <div style="color: #8e8e8e; font-size: 10px; text-transform: uppercase; letter-spacing: 0.2px;">${timestamp}</div>
      </div>
    </div>
  `;
}

/**
 * Creates an authentic Reddit post HTML
 */
function createRedditPost(post: any, metrics: any): string {
  const authorName = post.authorName || post.author || 'user';
  const content = post.content || post.textContent || '';
  const timestamp = formatRedditTime(post.timestamp || post.savedAt);
  const mainImage = post.savedImage || (post.media && post.media[0]?.url) || '';
  const subreddit = post.subreddit || 'r/reddit';

  return `
    <div style="
      max-width: 640px;
      background: #ffffff;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      font-size: 14px;
      line-height: 21px;
    ">
      <!-- Vote Section & Content -->
      <div style="display: flex;">
        <!-- Vote Section -->
        <div style="
          width: 40px;
          background: #f8f9fa;
          padding: 8px 4px;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-right: 1px solid #edeff1;
        ">
          <svg width="16" height="16" viewBox="0 0 20 20" fill="#878a8c">
            <path d="M12.877 19H7.123A1.125 1.125 0 0 1 6 17.877V11H2.126a1.114 1.114 0 0 1-1.007-.7 1.249 1.249 0 0 1 .171-1.343L9.166.368a1.128 1.128 0 0 1 1.668.004l7.872 8.581a1.25 1.25 0 0 1 .176 1.348 1.113 1.113 0 0 1-1.005.7H14v6.877A1.125 1.125 0 0 1 12.877 19z"/>
          </svg>
          <div style="font-size: 12px; font-weight: 700; color: #1c1c1c; margin: 4px 0;">
            ${metrics.likes > 0 ? formatNumber(metrics.likes) : '•'}
          </div>
          <svg width="16" height="16" viewBox="0 0 20 20" fill="#878a8c">
            <path d="M10 20a1.122 1.122 0 0 1-.834-.372l-7.872-8.581A1.251 1.251 0 0 1 1.118 9.7 1.114 1.114 0 0 1 2.123 9H6V2.123A1.125 1.125 0 0 1 7.123 1h5.754A1.125 1.125 0 0 1 14 2.123V9h3.874a1.114 1.114 0 0 1 1.007.7 1.25 1.25 0 0 1-.171 1.343l-7.876 8.589A1.128 1.128 0 0 1 10 20z"/>
          </svg>
        </div>

        <!-- Main Content -->
        <div style="flex: 1; padding: 8px 8px 8px 8px;">
          <!-- Header -->
          <div style="display: flex; align-items: center; margin-bottom: 6px; font-size: 12px; color: #787c82;">
            <span style="font-weight: 700; color: #1c1c1c;">${subreddit}</span>
            <span style="margin: 0 4px;">•</span>
            <span>Posted by</span>
            <span style="margin-left: 4px; color: #1c1c1c;">u/${authorName}</span>
            <span style="margin: 0 4px;">•</span>
            <span>${timestamp}</span>
          </div>

          <!-- Title/Content -->
          ${content ? `
            <div style="color: #1c1c1c; font-size: 18px; font-weight: 500; line-height: 22px; margin-bottom: 8px;">
              ${content}
            </div>
          ` : ''}

          <!-- Image -->
          ${mainImage ? `
            <div style="margin-bottom: 8px;">
              <img src="${mainImage}" style="max-width: 100%; height: auto; border-radius: 4px;" />
            </div>
          ` : ''}

          <!-- Action Bar -->
          <div style="display: flex; align-items: center; gap: 8px; margin-top: 8px;">
            <div style="display: flex; align-items: center; gap: 4px; color: #878a8c; font-size: 12px; font-weight: 700; padding: 2px 4px; border-radius: 2px; cursor: pointer;">
              <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                <path d="M7.725 19.872a.718.718 0 0 1-.607-.328.725.725 0 0 1-.118-.397V16H3.625A2.63 2.63 0 0 1 1 13.375v-9.75A2.629 2.629 0 0 1 3.625 1h12.75A2.63 2.63 0 0 1 19 3.625v9.75A2.63 2.63 0 0 1 16.375 16H9.77l-1.663 3.507a.725.725 0 0 1-.382.365.715.715 0 0 1-.3 0z"/>
              </svg>
              <span>${metrics.comments > 0 ? `${formatNumber(metrics.comments)} Comments` : 'Comment'}</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px; color: #878a8c; font-size: 12px; font-weight: 700; padding: 2px 4px; border-radius: 2px; cursor: pointer;">
              <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                <path d="M18.5 10a1.5 1.5 0 0 1-1.5 1.5H13v5a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 7 16.5v-5H3a1.5 1.5 0 0 1-1.5-1.5 1.5 1.5 0 0 1 1.5-1.5h4V3A1.5 1.5 0 0 1 8.5 1.5h3A1.5 1.5 0 0 1 13 3v5.5h4A1.5 1.5 0 0 1 18.5 10z"/>
              </svg>
              <span>Share</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px; color: #878a8c; font-size: 12px; font-weight: 700; padding: 2px 4px; border-radius: 2px; cursor: pointer;">
              <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                <path d="M1.75 10.5a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5H2.5a.75.75 0 0 1-.75-.75z"/>
                <path d="M1.75 6.5a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H2.5a.75.75 0 0 1-.75-.75z"/>
                <path d="M1.75 14.5a.75.75 0 0 1 .75-.75h9.5a.75.75 0 0 1 0 1.5h-9.5a.75.75 0 0 1-.75-.75z"/>
              </svg>
              <span>Save</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Creates an authentic Pinterest post HTML
 */
function createPinterestPost(post: any, metrics: any): string {
  const authorAvatar = post.authorAvatar || post.authorImage || '';
  const authorName = post.authorName || post.author || '';
  const content = post.content || post.textContent || '';
  const timestamp = formatPinterestTime(post.timestamp || post.savedAt);
  const mainImage = post.savedImage || (post.media && post.media[0]?.url) || '';

  return `
    <div style="
      max-width: 236px;
      background: #ffffff;
      border-radius: 16px;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      font-size: 14px;
      line-height: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    ">
      <!-- Image -->
      ${mainImage ? `
        <div style="position: relative; width: 100%; border-radius: 16px 16px 0 0; overflow: hidden;">
          <img src="${mainImage}" style="width: 100%; height: auto; display: block;" />
          <!-- Save Button Overlay -->
          <div style="
            position: absolute;
            top: 8px;
            right: 8px;
            background: #e60023;
            color: white;
            padding: 8px 16px;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            opacity: 0.9;
          ">
            Save
          </div>
        </div>
      ` : ''}

      <!-- Content -->
      <div style="padding: 8px;">
        ${content ? `
          <div style="color: #211922; font-size: 14px; line-height: 20px; margin-bottom: 8px; font-weight: 400;">
            ${content}
          </div>
        ` : ''}

        <!-- Author Info -->
        <div style="display: flex; align-items: center; margin-top: 8px;">
          <div style="
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e60023;
            margin-right: 8px;
            overflow: hidden;
            flex-shrink: 0;
          ">
            ${authorAvatar ? `<img src="${authorAvatar}" style="width: 100%; height: 100%; object-fit: cover;" />` :
              `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px;">${authorName ? authorName.charAt(0).toUpperCase() : 'P'}</div>`}
          </div>
          ${authorName ? `
          <div style="flex: 1; min-width: 0;">
            <div style="color: #5f5f5f; font-size: 12px; line-height: 16px;">${authorName}</div>
            <div style="color: #767676; font-size: 12px; line-height: 16px;">${timestamp}</div>
          </div>
          ` : `
          <div style="flex: 1; min-width: 0;">
            <div style="color: #767676; font-size: 12px; line-height: 16px;">${timestamp}</div>
          </div>
          `}
        </div>

        <!-- No Action Buttons for Pinterest as they don't show meaningful engagement metrics -->
      </div>
    </div>
  `;
}

/**
 * Helper function to format Pinterest timestamp
 */
function formatPinterestTime(timestamp: string): string {
  if (!timestamp) return '1h';

  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s`;
    const diffInMinutes = Math.round(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    const diffInHours = Math.round(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h`;
    const diffInDays = Math.round(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d`;
    if (diffInDays < 30) return `${Math.round(diffInDays / 7)}w`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  } catch (e) {
    return '1h';
  }
}

/**
 * Captures a post card element and downloads it as an image
 * @param postCardElement The post card DOM element
 * @param postId The ID of the post for filename
 * @param platform The platform name for filename
 * @param post The post data object
 */
export async function downloadPostCardAsImage(
  postCardElement: HTMLElement,
  postId: string,
  platform: string,
  post?: any
): Promise<void> {
  try {
    if (post && ['X/Twitter', 'LinkedIn', 'Instagram', 'Reddit', 'pinterest'].includes(platform)) {
      // Create authentic platform-styled post
      const { element: authenticElement, cleanup } = createAuthenticPostElement(postCardElement, platform, post);

      try {
        // Wait a moment for styles to apply
        await new Promise(resolve => setTimeout(resolve, 200));

        const dataUrl = await captureElementAsImage(authenticElement, {
          backgroundColor: '#ffffff',
          scale: 3, // Higher quality for social media sharing
          useCORS: true,
          allowTaint: true
        });

        const filename = `${platform}-post-${postId}-${Date.now()}`;
        downloadImageAsFile(dataUrl, filename);
      } finally {
        cleanup();
      }
    } else {
      // Fallback to original post card capture
      const dataUrl = await captureElementAsImage(postCardElement, {
        backgroundColor: '#ffffff',
        scale: 3,
        useCORS: true,
        allowTaint: true
      });

      const filename = `${platform}-post-${postId}-${Date.now()}`;
      downloadImageAsFile(dataUrl, filename);
    }
  } catch (error) {
    console.error('Error downloading post card as image:', error);
    throw error;
  }
}

/**
 * Captures a post card element and copies it to clipboard
 * @param postCardElement The post card DOM element
 * @param platform The platform name for styling
 * @param post The post data object
 */
export async function copyPostCardAsImage(
  postCardElement: HTMLElement,
  platform?: string,
  post?: any
): Promise<void> {
  try {
    if (post && platform && ['X/Twitter', 'LinkedIn', 'Instagram', 'Reddit', 'pinterest'].includes(platform)) {
      // Create authentic platform-styled post
      const { element: authenticElement, cleanup } = createAuthenticPostElement(postCardElement, platform, post);

      try {
        // Wait a moment for styles to apply
        await new Promise(resolve => setTimeout(resolve, 200));

        const dataUrl = await captureElementAsImage(authenticElement, {
          backgroundColor: '#ffffff',
          scale: 3, // Higher quality for social media sharing
          useCORS: true,
          allowTaint: true
        });

        await copyImageToClipboard(dataUrl);
      } finally {
        cleanup();
      }
    } else {
      // Fallback to original post card capture
      const dataUrl = await captureElementAsImage(postCardElement, {
        backgroundColor: '#ffffff',
        scale: 3,
        useCORS: true,
        allowTaint: true
      });

      await copyImageToClipboard(dataUrl);
    }
  } catch (error) {
    console.error('Error copying post card as image:', error);
    throw error;
  }
}

/**
 * Finds the post card element for a given post ID
 * @param postId The ID of the post
 * @returns The post card element or null if not found
 */
export function findPostCardElement(postId: string): HTMLElement | null {
  // Try to find by data attribute first
  let element = document.querySelector(`[data-post-id="${postId}"]`) as HTMLElement;

  if (!element) {
    // Fallback: find by post card class and check content
    const postCards = document.querySelectorAll('.post-card') as NodeListOf<HTMLElement>;
    for (const card of postCards) {
      // Check if the card contains the post ID in any way
      if (card.innerHTML.includes(postId)) {
        element = card;
        break;
      }
    }
  }

  return element;
}
