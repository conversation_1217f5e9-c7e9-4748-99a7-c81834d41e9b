/**
 * Utility functions for image handling
 */

/**
 * Fetches an image from a URL and converts it to a data URL
 * This helps bypass CORS restrictions for Instagram images
 */
export async function fetchImageAsDataUrl(url: string): Promise<string> {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.instagram.com/',
        'Origin': 'https://www.instagram.com'
      }
    });

    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.status}`);
    }

    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('[imageUtils] Error fetching image:', error);
    throw error;
  }
}

/**
 * Retrieves an image from IndexedDB for a given post ID and URL
 * @param postId The ID of the post
 * @param url The URL of the image
 * @returns Promise that resolves to a data URL or null if not found
 */
export async function getImageFromIndexedDB(postId: string, url: string): Promise<string | null> {
  return new Promise((resolve) => {
    try {
      console.log(`[imageUtils] Attempting to retrieve image from IndexedDB for post ${postId}`);
      console.log(`[imageUtils] Image URL: ${url.substring(0, 50)}...`);

      // Open the IndexedDB database
      const request = indexedDB.open('social-saver-images', 2);

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        console.log("[imageUtils] IndexedDB upgrade needed, creating/updating schema");

        // Delete existing object store if it exists (to ensure clean schema)
        if (db.objectStoreNames.contains('images')) {
          db.deleteObjectStore('images');
        }

        // Create the images object store with proper schema
        const imageStore = db.createObjectStore('images', { keyPath: 'id' });

        // Add indexes for better querying
        imageStore.createIndex('postId', 'postId', { unique: false });
        imageStore.createIndex('createdAt', 'createdAt', { unique: false });

        console.log("[imageUtils] IndexedDB schema updated successfully");
      };

      request.onerror = (event) => {
        console.error('[imageUtils] Error opening IndexedDB:', event);
        resolve(null);
      };

      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Check if the 'images' object store exists
        if (!db.objectStoreNames.contains('images')) {
          console.warn('[imageUtils] No images object store found in IndexedDB');
          db.close();
          resolve(null);
          return;
        }

        // Create a transaction and get the images object store
        const transaction = db.transaction(['images'], 'readonly');
        const store = transaction.objectStore('images');

        // Get all records from the store
        const getAllRequest = store.getAll();

        getAllRequest.onsuccess = () => {
          const images = getAllRequest.result;

          // Log the available images for this post
          const postImages = images.filter(img => img.postId === postId);
          console.log(`[imageUtils] Found ${postImages.length} images for post ${postId} in IndexedDB`);
          if (postImages.length > 0) {
            console.log(`[imageUtils] First image ID: ${postImages[0].id}`);
            console.log(`[imageUtils] First image URL: ${postImages[0].url.substring(0, 50)}...`);
          }

          // Find the image that matches the post ID and URL
          // First try exact URL match
          let image = images.find(img =>
            img.postId === postId && img.url === url
          );

          // If not found, try sanitized URL in ID (without timestamp)
          if (!image) {
            const sanitizedUrl = url.replace(/[^a-zA-Z0-9]/g, '_');
            const idPrefix = `${postId}_${sanitizedUrl}`;

            image = images.find(img =>
              img.postId === postId && img.id.startsWith(idPrefix)
            );

            if (image) {
              console.log(`[imageUtils] Found image using ID prefix match: ${idPrefix}`);
            }
          }

          // For Instagram images, try matching by domain and post ID
          if (!image && (url.includes('instagram') || postId.length <= 12)) {
            console.log(`[imageUtils] Trying Instagram-specific matching for post ${postId}`);

            // We already have postImages from above
            if (postImages.length > 0) {
              // For Instagram, just return the first image if it's the main post image
              // This works because Instagram posts typically have 1-2 images
              if (url.includes('media') || (!url.includes('profile') && !url.includes('avatar'))) {
                // For post images, use the first non-profile image
                const nonProfileImages = postImages.filter(img =>
                  !img.url.includes('profile') && !img.id.includes('profile')
                );

                if (nonProfileImages.length > 0) {
                  image = nonProfileImages[0];
                  console.log(`[imageUtils] Using first non-profile image for Instagram post ${postId}`);
                } else {
                  image = postImages[0]; // Fallback to first image
                  console.log(`[imageUtils] Using first available image for Instagram post ${postId}`);
                }
              } else if (url.includes('profile') || url.includes('avatar')) {
                // For profile pictures, try to find one that has 'profile' in the URL or ID
                image = postImages.find(img =>
                  img.url.includes('profile') || img.id.includes('profile') ||
                  img.url.includes('avatar') || img.id.includes('avatar')
                );

                if (image) {
                  console.log(`[imageUtils] Found profile image for Instagram post ${postId}`);
                } else {
                  // If no profile image found, just use the first image as a fallback
                  image = postImages[0];
                  console.log(`[imageUtils] No profile image found, using first image as fallback for ${postId}`);
                }
              }
            }
          }

          // Last resort: try just matching by postId and looking for URL fragments
          if (!image) {
            const urlFragment = url.split('/').pop()?.split('?')[0];
            if (urlFragment && urlFragment.length > 5) {
              image = images.find(img =>
                img.postId === postId &&
                (img.url.includes(urlFragment) || img.id.includes(urlFragment))
              );

              if (image) {
                console.log(`[imageUtils] Found image using URL fragment match: ${urlFragment}`);
              }
            }
          }

          if (image && image.blob) {
            // Convert blob to data URL
            const reader = new FileReader();
            reader.onloadend = () => {
              console.log(`[imageUtils] Successfully retrieved image from IndexedDB for post ${postId}`);
              resolve(reader.result as string);
            };
            reader.onerror = () => {
              console.error('[imageUtils] Error reading blob from IndexedDB');
              resolve(null);
            };
            reader.readAsDataURL(image.blob);
          } else {
            console.log(`[imageUtils] No matching image found in IndexedDB for post ${postId}`);
            resolve(null);
          }

          db.close();
        };

        getAllRequest.onerror = (event) => {
          console.error('[imageUtils] Error getting images from IndexedDB:', event);
          db.close();
          resolve(null);
        };
      };


    } catch (error) {
      console.error('[imageUtils] Error in getImageFromIndexedDB:', error);
      resolve(null);
    }
  });
}

/**
 * Comprehensive function to get an image from any available source
 * @param postId The ID of the post
 * @param url The URL of the image
 * @returns Promise that resolves to a data URL or null if not found
 */
export async function getImage(postId: string, url: string): Promise<string | null> {
  try {
    // If it's already a data URL, return it directly
    if (url.startsWith('data:')) {
      return url;
    }

    // First try to get the image from IndexedDB
    const imageFromDB = await getImageFromIndexedDB(postId, url);
    if (imageFromDB) {
      return imageFromDB;
    }

    // If not found in IndexedDB, try to fetch it via the background script
    return new Promise((resolve) => {
      const requestId = Date.now().toString();

      // Set up a listener for the response
      const messageListener = (message: any) => {
        if (message.action === 'FETCH_IMAGE_RESULT' && message.requestId === requestId) {
          // Remove the listener once we get a response
          chrome.runtime.onMessage.removeListener(messageListener);

          if (message.status === 'success' && message.dataUrl) {
            resolve(message.dataUrl);
          } else {
            console.error(`[imageUtils] Background fetch failed: ${message.message}`);
            resolve(null);
          }
        }
      };

      // Add the listener
      chrome.runtime.onMessage.addListener(messageListener);

      // Set a timeout to remove the listener if we don't get a response
      setTimeout(() => {
        chrome.runtime.onMessage.removeListener(messageListener);
        console.warn(`[imageUtils] Background fetch timed out for ${url}`);
        resolve(null);
      }, 5000);

      // Send the fetch request with postId
      chrome.runtime.sendMessage({
        action: 'FETCH_IMAGE',
        url,
        postId, // Include the postId for IndexedDB lookup
        requestId
      }).catch(error => {
        console.error(`[imageUtils] Error sending fetch request: ${error}`);
        chrome.runtime.onMessage.removeListener(messageListener);
        resolve(null);
      });
    });
  } catch (error) {
    console.error('[imageUtils] Error in getImage:', error);
    return null;
  }
}
