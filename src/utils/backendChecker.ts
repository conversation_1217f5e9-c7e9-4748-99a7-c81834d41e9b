/**
 * Backend Status Checker
 * 
 * Utility to check if the backend API is running and accessible
 */

const API_URL = 'https://api.notely.social';

export interface BackendStatus {
  isOnline: boolean;
  endpoints: {
    health: boolean;
    auth: boolean;
    billing: boolean;
  };
  errors: string[];
}

/**
 * Check if the backend is accessible
 */
export const checkBackendStatus = async (): Promise<BackendStatus> => {
  const status: BackendStatus = {
    isOnline: false,
    endpoints: {
      health: false,
      auth: false,
      billing: false
    },
    errors: []
  };

  try {
    // Check health endpoint
    try {
      const healthResponse = await fetch(`${API_URL}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      status.endpoints.health = healthResponse.ok;
      if (!healthResponse.ok) {
        status.errors.push(`Health endpoint returned ${healthResponse.status}`);
      }
    } catch (error) {
      status.errors.push(`Health endpoint failed: ${error}`);
    }

    // Check auth endpoint (should return 401 without token)
    try {
      const authResponse = await fetch(`${API_URL}/auth/me`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      // 401 is expected without auth token, so this means the endpoint exists
      status.endpoints.auth = authResponse.status === 401 || authResponse.ok;
      if (authResponse.status !== 401 && !authResponse.ok) {
        status.errors.push(`Auth endpoint returned unexpected status: ${authResponse.status}`);
      }
    } catch (error) {
      status.errors.push(`Auth endpoint failed: ${error}`);
    }

    // Check billing plans endpoint
    try {
      const billingResponse = await fetch(`${API_URL}/billing/plans`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      status.endpoints.billing = billingResponse.ok;
      if (!billingResponse.ok) {
        status.errors.push(`Billing endpoint returned ${billingResponse.status}`);
      }
    } catch (error) {
      status.errors.push(`Billing endpoint failed: ${error}`);
    }

    // Backend is considered online if at least health endpoint works
    status.isOnline = status.endpoints.health;

  } catch (error) {
    status.errors.push(`General backend check failed: ${error}`);
  }

  return status;
};

/**
 * Log backend status to console
 */
export const logBackendStatus = async (): Promise<void> => {
  console.log('🔍 Checking backend status...');
  const status = await checkBackendStatus();
  
  console.log('📊 Backend Status Report:');
  console.log(`   🌐 Online: ${status.isOnline ? '✅' : '❌'}`);
  console.log(`   💚 Health: ${status.endpoints.health ? '✅' : '❌'}`);
  console.log(`   🔐 Auth: ${status.endpoints.auth ? '✅' : '❌'}`);
  console.log(`   💳 Billing: ${status.endpoints.billing ? '✅' : '❌'}`);
  
  if (status.errors.length > 0) {
    console.log('❌ Errors:');
    status.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (!status.isOnline) {
    console.log('💡 Troubleshooting tips:');
    console.log('   1. Check if Railway backend is deployed and running');
    console.log('   2. Verify API_URL is correct: https://api.notely.social');
    console.log('   3. Check Railway logs for errors');
    console.log('   4. Ensure CORS is configured for extension domain');
  }
};

/**
 * Check specific endpoint
 */
export const checkEndpoint = async (endpoint: string): Promise<boolean> => {
  try {
    const response = await fetch(`${API_URL}${endpoint}`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });
    return response.ok || response.status === 401; // 401 is OK for auth endpoints
  } catch (error) {
    console.error(`Endpoint ${endpoint} check failed:`, error);
    return false;
  }
};
