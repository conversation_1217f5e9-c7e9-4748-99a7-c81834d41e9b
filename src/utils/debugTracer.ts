/**
 * Debug Tracer Utility
 * 
 * Provides detailed tracing and logging functionality for background scripts and service workers.
 * Helps diagnose issues by tracking execution flow and capturing error contexts.
 */

// Configuration for the debug tracer
interface DebugTracerConfig {
  enabled: boolean;        // Master switch to enable/disable all tracing
  logToConsole: boolean;   // Whether to output to console
  logToStorage: boolean;   // Whether to store logs in chrome.storage.local
  maxLogEntries: number;   // Maximum number of log entries to keep in storage
  detailLevel: number;     // 0=errors only, 1=+warnings, 2=+info, 3=+debug, 4=+verbose
}

// Structure of a log entry
interface LogEntry {
  timestamp: string;
  level: 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'VERBOSE';
  context: string;
  message: string;
  data?: unknown;
  stack?: string;
}

// Default configuration
const DEFAULT_CONFIG: DebugTracerConfig = {
  enabled: true,
  logToConsole: true,
  logToStorage: true,
  maxLogEntries: 1000,
  detailLevel: 3  // Default to DEBUG level
};

class DebugTracer {
  private config: DebugTracerConfig;
  private logs: LogEntry[] = [];
  private traceTimers: Record<string, number> = {};

  constructor(initialConfig: Partial<DebugTracerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...initialConfig };
    this.initializeFromStorage();
  }

  // Load config from chrome.storage if available
  private async initializeFromStorage() {
    try {
      const stored = await chrome.storage.local.get(['debugTracerConfig']);
      if (stored?.debugTracerConfig) {
        this.config = { ...this.config, ...stored.debugTracerConfig };
      }
    } catch (error) {
      console.error('Failed to load debug tracer config from storage:', error);
    }
  }

  // Save the current configuration to storage
  public async saveConfig() {
    try {
      await chrome.storage.local.set({ debugTracerConfig: this.config });
    } catch (error) {
      console.error('Failed to save debug tracer config to storage:', error);
    }
  }

  // Update config (enables runtime configuration)
  public updateConfig(newConfig: Partial<DebugTracerConfig>) {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
  }

  // Clear all logs
  public async clearLogs() {
    this.logs = [];
    try {
      await chrome.storage.local.set({ debugTracerLogs: [] });
    } catch (error) {
      console.error('Failed to clear debug logs from storage:', error);
    }
  }

  // Get current logs
  public getLogs() {
    return [...this.logs]; // Return a copy to prevent external modification
  }

  // Main logging method
  private log(level: LogEntry['level'], context: string, message: string, data?: unknown) {
    if (!this.config.enabled) return;

    // Check if we should log based on detail level
    const levelMap = { 'ERROR': 0, 'WARN': 1, 'INFO': 2, 'DEBUG': 3, 'VERBOSE': 4 };
    if (levelMap[level] > this.config.detailLevel) return;

    // Create log entry
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      context,
      message,
      data: data ? structuredClone(data) : undefined
    };

    // Add stack trace for errors
    if (level === 'ERROR' && data instanceof Error) {
      entry.stack = data.stack;
    }

    // Add to in-memory logs
    this.logs.push(entry);
    
    // Trim logs if we exceed the maximum
    if (this.logs.length > this.config.maxLogEntries) {
      this.logs = this.logs.slice(-this.config.maxLogEntries);
    }

    // Log to console if enabled
    if (this.config.logToConsole) {
      const logFn = {
        'ERROR': console.error,
        'WARN': console.warn,
        'INFO': console.info,
        'DEBUG': console.debug,
        'VERBOSE': console.log
      }[level];
      
      logFn?.(`[${entry.timestamp}] [${level}] [${context}] ${message}`, data || '');
    }

    // Save to storage if enabled
    if (this.config.logToStorage) {
      this.saveLogsToStorage();
    }
  }

  // Helper to save logs to chrome.storage
  private async saveLogsToStorage() {
    try {
      await chrome.storage.local.set({ debugTracerLogs: this.logs });
    } catch (error) {
      console.error('Failed to save debug logs to storage:', error);
    }
  }

  // Public logging methods
  public error(context: string, message: string, data?: unknown) {
    this.log('ERROR', context, message, data);
  }

  public warn(context: string, message: string, data?: unknown) {
    this.log('WARN', context, message, data);
  }

  public info(context: string, message: string, data?: unknown) {
    this.log('INFO', context, message, data);
  }

  public debug(context: string, message: string, data?: unknown) {
    this.log('DEBUG', context, message, data);
  }

  public verbose(context: string, message: string, data?: unknown) {
    this.log('VERBOSE', context, message, data);
  }

  // Start a timing operation
  public startTrace(traceName: string) {
    this.traceTimers[traceName] = performance.now();
    this.debug('TRACE', `Starting trace: ${traceName}`);
  }

  // End a timing operation and log the duration
  public endTrace(traceName: string, context: string = 'TRACE') {
    const startTime = this.traceTimers[traceName];
    if (startTime) {
      const duration = performance.now() - startTime;
      this.debug(context, `Trace ${traceName} completed in ${duration.toFixed(2)}ms`);
      delete this.traceTimers[traceName];
      return duration;
    } else {
      this.warn(context, `Trace ${traceName} has no start time`);
      return -1;
    }
  }
  
  // Log incoming message for debugging
  public logIncomingMessage(message: Record<string, unknown>, sender: chrome.runtime.MessageSender) {
    this.debug('MESSAGE_RECEIVED', 'Received message', {
      action: message.action,
      messageSize: JSON.stringify(message).length,
      sender: {
        tab: sender.tab ? { id: sender.tab.id, url: sender.tab.url } : undefined,
        frameId: sender.frameId,
        origin: sender.origin
      }
    });
    
    // Add detailed logging for very verbose debugging
    this.verbose('MESSAGE_DETAIL', 'Message content', message);
  }
  
  // Log outgoing response for debugging
  public logOutgoingResponse(message: Record<string, unknown>, response: Record<string, unknown>) {
    this.debug('MESSAGE_RESPONSE', `Response to ${message.action}`, {
      status: response.status || 'unknown',
      responseSize: JSON.stringify(response).length
    });
    
    // Add detailed logging for very verbose debugging
    this.verbose('RESPONSE_DETAIL', 'Response content', response);
  }
  
  // Helper to track errors in async functions
  public async trackAsync<T>(context: string, fn: () => Promise<T>, description: string): Promise<T> {
    try {
      this.startTrace(description);
      const result = await fn();
      this.endTrace(description, context);
      return result;
    } catch (error) {
      this.error(context, `Error in ${description}`, error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const debugTracer = new DebugTracer();

// Helper to easily toggle debug mode from other scripts
export function setDebugMode(enabled: boolean) {
  debugTracer.updateConfig({ enabled });
}

// Export additional helper to get debug status
export function isDebugModeEnabled(): boolean {
  return debugTracer['config'].enabled;
}
