/**
 * Utility for handling image loading with CORS bypass
 */

// Cache for storing fetched images to avoid redundant fetches
const imageCache: Record<string, string> = {};


/**
 * Fetches an image using the background script which has broader permissions
 * @param url The URL to fetch
 * @returns Promise that resolves to a data URL
 */
function fetchImageWithBackground(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({ action: 'FETCH_IMAGE', url }, response => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
        return;
      }

      if (response.status === 'success' && response.dataUrl) {
        resolve(response.dataUrl);
      } else {
        reject(new Error(response.message || 'Failed to fetch image'));
      }
    });
  });
}

/**
 * Attempts to load an image via a proxy to bypass CORS restrictions
 * @param url The original image URL
 * @returns A promise that resolves to a data URL
 */
export async function proxyImage(url: string): Promise<string> {
  // Check if URL is already a data URL
  if (url.startsWith('data:')) {
    console.log('[Image Proxy] URL is already a data URL, returning as is');
    return url;
  }

  // Check if image is in cache
  if (imageCache[url]) {
    console.log('[Image Proxy] Image found in cache, returning cached version');
    return imageCache[url];
  }

  // Try fetching through background script
  try {
    console.log('[Image Proxy] Attempting background fetch:', url.substring(0, 100) + '...');
    const dataUrl = await fetchImageWithBackground(url);
    console.log('[Image Proxy] Background fetch succeeded');

    // Cache the result
    imageCache[url] = dataUrl;
    return dataUrl;
  } catch (error) {
    console.error('[Image Proxy] Background fetch error:', error);
    throw error;
  }

  // If all methods fail, return a placeholder image
  console.error('[Image Proxy] All fetch attempts failed for:', url.substring(0, 100) + '...');
  return createImagePlaceholder(url);
}

/**
 * Creates a placeholder image with the URL embedded
 * This is useful for debugging and as a last resort
 */
function createImagePlaceholder(url: string): string {
  // Create a shortened version of the URL for display
  const shortUrl = url.length > 30 ? url.substring(0, 30) + '...' : url;

  // Create an SVG with the URL text
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
      <rect width="200" height="200" fill="#eee"/>
      <text x="50%" y="45%" font-size="14" text-anchor="middle" alignment-baseline="middle" font-family="monospace, sans-serif" fill="#888">Image Not Available</text>
      <text x="50%" y="55%" font-size="10" text-anchor="middle" alignment-baseline="middle" font-family="monospace, sans-serif" fill="#aaa">${shortUrl}</text>
    </svg>
  `;

  // Convert SVG to a data URL
  return 'data:image/svg+xml;base64,' + btoa(svg);
}
