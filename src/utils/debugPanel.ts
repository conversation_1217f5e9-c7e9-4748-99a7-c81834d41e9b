/**
 * Debug Panel Utility
 * 
 * Provides service worker debugging tools and an interface to view logs
 * and trace execution without breaking existing code.
 */

// Type for storing debug logs
interface DebugLog {
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  context: string;
  message: string;
  data?: unknown;
  source?: string;
}

// Storage key for debug logs
const STORAGE_KEY = 'serviceWorkerDebugLogs';
const MAX_LOGS = 500; // Maximum number of logs to keep

// Debug flag to enable/disable logging
let debugEnabled = true;

/**
 * Log a debug message
 * @param level Log level
 * @param context Context identifier
 * @param message Log message
 * @param data Optional data object
 */
export async function logDebug(
  level: 'error' | 'warn' | 'info' | 'debug',
  context: string,
  message: string,
  data?: Record<string, unknown> | string | number | boolean | null
): Promise<void> {
  if (!debugEnabled) return;

  const log: DebugLog = {
    timestamp: new Date().toISOString(),
    level,
    context,
    message,
    data: data ? JSON.parse(JSON.stringify(data)) : undefined,
    source: 'service-worker'
  };

  // Always log to console
  const consoleMethod = level === 'error' ? console.error :
                      level === 'warn' ? console.warn :
                      level === 'info' ? console.info : console.debug;
  
  consoleMethod(`[${log.timestamp}] [${level.toUpperCase()}] [${context}] ${message}`, data);
  
  // Save to storage
  try {
    const result = await chrome.storage.local.get(STORAGE_KEY);
    const logs: DebugLog[] = result[STORAGE_KEY] || [];
    
    logs.push(log);
    
    // Trim logs if exceeding maximum
    if (logs.length > MAX_LOGS) {
      logs.splice(0, logs.length - MAX_LOGS);
    }
    
    await chrome.storage.local.set({ [STORAGE_KEY]: logs });
  } catch (error) {
    console.error('Failed to save debug log to storage:', error);
  }
}

/**
 * Log an error message
 */
export function logError(context: string, message: string, data?: Record<string, unknown> | string | number | boolean | null): void {
  logDebug('error', context, message, data);
}

/**
 * Log a warning message
 */
export function logWarn(context: string, message: string, data?: Record<string, unknown> | string | number | boolean | null): void {
  logDebug('warn', context, message, data);
}

/**
 * Log an info message
 */
export function logInfo(context: string, message: string, data?: Record<string, unknown> | string | number | boolean | null): void {
  logDebug('info', context, message, data);
}

/**
 * Clear all debug logs
 */
export async function clearDebugLogs(): Promise<void> {
  try {
    await chrome.storage.local.set({ [STORAGE_KEY]: [] });
    console.log('Debug logs cleared');
  } catch (error) {
    console.error('Failed to clear debug logs:', error);
  }
}

/**
 * Enable or disable debug logging
 */
export async function setDebugEnabled(enabled: boolean): Promise<void> {
  debugEnabled = enabled;
  await chrome.storage.local.set({ debugEnabled });
  console.log(`Debug logging ${enabled ? 'enabled' : 'disabled'}`);
}

/**
 * Get all debug logs
 */
export async function getDebugLogs(): Promise<DebugLog[]> {
  try {
    const result = await chrome.storage.local.get(STORAGE_KEY);
    return result[STORAGE_KEY] || [];
  } catch (error) {
    console.error('Failed to get debug logs:', error);
    return [];
  }
}

/**
 * Create a timer to measure execution time
 */
export function createTimer(label: string): () => number {
  const start = performance.now();
  return () => {
    const end = performance.now();
    const duration = end - start;
    logInfo('TIMER', `${label}: ${duration.toFixed(2)}ms`);
    return duration;
  };
}

/**
 * Save error information for debugging
 */
export async function saveErrorInfo(error: Error | unknown, context: string): Promise<void> {
  const errorInfo = {
    message: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : 'No stack trace',
    context,
    timestamp: new Date().toISOString()
  };
  
  try {
    const result = await chrome.storage.local.get('errorInfos');
    const errorInfos = result.errorInfos || [];
    errorInfos.push(errorInfo);
    
    // Keep only the last 20 errors
    if (errorInfos.length > 20) {
      errorInfos.splice(0, errorInfos.length - 20);
    }
    
    await chrome.storage.local.set({ errorInfos });
    logError(context, 'Error saved for debugging', errorInfo);
  } catch (storageError) {
    console.error('Failed to save error info:', storageError);
  }
}

// Initialize debug status from storage
chrome.storage.local.get(['debugEnabled'], (result) => {
  if (result.debugEnabled !== undefined) {
    debugEnabled = Boolean(result.debugEnabled);
  }
});
