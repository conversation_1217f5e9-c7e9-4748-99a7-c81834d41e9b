// Storage utility functions

// API URL for backend
const API_URL = 'https://api.notely.social';

// Storage usage types
export interface StorageUsageType {
  usedMB: number;
  limitMB: number;
  usagePercentage: number;
  plan: 'free' | 'premium';
  isNearLimit: boolean;
  isOverLimit: boolean;
}

// Get authentication token from Chrome storage
export const getAuthToken = async (): Promise<string | null> => {
  try {
    const result = await chrome.storage.local.get(['token', 'authToken']);
    return result.authToken || result.token || null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Fetch real storage usage from backend API
export const getStorageUsage = async (): Promise<StorageUsageType> => {
  try {
    const token = await getAuthToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${API_URL}/auth/storage-usage`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Authentication token expired');
      }
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    return {
      usedMB: data.usedMB,
      limitMB: data.limitMB,
      usagePercentage: data.usagePercentage,
      plan: data.plan,
      isNearLimit: data.isNearLimit,
      isOverLimit: data.isOverLimit
    };
  } catch (error) {
    console.error('Error fetching storage usage:', error);
    throw error;
  }
};

export const getStorageUsageColor = (percentage: number): string => {
  if (percentage >= 90) return '#ef4444'; // red
  if (percentage >= 75) return '#f59e0b'; // yellow
  return '#10b981'; // green
};

export const getStorageUsageMessage = (data: StorageUsageType): string => {
  if (data.isOverLimit) return 'Storage limit exceeded. Please delete some posts or upgrade to Premium.';
  if (data.isNearLimit) return 'Storage is almost full. Consider deleting old posts.';
  return 'Storage usage is healthy.';
};

export const getRecommendedAction = (data: StorageUsageType): string | null => {
  if (data.isOverLimit) return 'Delete old posts or upgrade to Premium for more storage.';
  if (data.isNearLimit) return 'Consider cleaning up old posts to free up space.';
  return null;
};
