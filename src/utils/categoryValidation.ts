export const CATEGORY_CONSTRAINTS = {
  MIN_LENGTH: 1,
  MAX_LENGTH: 40,
  VALID_CHARS: /^[\p{L}\p{N}\p{P}\p{Emoji}\s]+$/u
};

export const validateCategoryName = (name: string): { 
  isValid: boolean; 
  error?: string 
} => {
  const trimmed = name.trim();
  
  if (trimmed.length < CATEGORY_CONSTRAINTS.MIN_LENGTH) {
    return { isValid: false, error: 'Category name cannot be empty' };
  }
  
  if (trimmed.length > CATEGORY_CONSTRAINTS.MAX_LENGTH) {
    return { 
      isValid: false, 
      error: `Category name must be ${CATEGORY_CONSTRAINTS.MAX_LENGTH} characters or less` 
    };
  }
  
  if (!CATEGORY_CONSTRAINTS.VALID_CHARS.test(trimmed)) {
    return { 
      isValid: false, 
      error: 'Category name can only contain letters, numbers, basic punctuation, and emoji' 
    };
  }
  
  return { isValid: true };
}; 