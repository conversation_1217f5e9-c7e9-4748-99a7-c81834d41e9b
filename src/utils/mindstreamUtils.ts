import { Post, Platform } from '../types';

export interface MindstreamInsight {
  id: string;
  type: 'trend' | 'pattern' | 'recommendation' | 'insight';
  title: string;
  description: string;
  confidence: number; // 0-1
  data?: any;
  createdAt: Date;
}

export interface ContentCluster {
  id: string;
  theme: string;
  posts: Post[];
  keywords: string[];
  platforms: Platform[];
  engagement: {
    total: number;
    average: number;
  };
}

export interface TrendingTopic {
  topic: string;
  count: number;
  platforms: Platform[];
  recentPosts: Post[];
  growth: number; // percentage
}

/**
 * Analyzes posts to generate AI insights for Mindstream
 */
export const generateMindstreamInsights = (posts: Post[]): MindstreamInsight[] => {
  const insights: MindstreamInsight[] = [];

  // Platform usage trends
  const platformInsight = analyzePlatformTrends(posts);
  if (platformInsight) insights.push(platformInsight);

  // Content patterns
  const contentInsight = analyzeContentPatterns(posts);
  if (contentInsight) insights.push(contentInsight);

  // Engagement patterns
  const engagementInsight = analyzeEngagementPatterns(posts);
  if (engagementInsight) insights.push(engagementInsight);

  // Time-based patterns
  const timeInsight = analyzeTimePatterns(posts);
  if (timeInsight) insights.push(timeInsight);

  return insights;
};

/**
 * Analyzes platform usage trends
 */
const analyzePlatformTrends = (posts: Post[]): MindstreamInsight | null => {
  if (posts.length < 5) return null;

  const platformCounts: Record<string, number> = {};
  const recentPosts = posts.filter(post => {
    const postDate = new Date(post.savedAt || post.timestamp || '');
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return postDate >= weekAgo;
  });

  recentPosts.forEach(post => {
    platformCounts[post.platform] = (platformCounts[post.platform] || 0) + 1;
  });

  const topPlatform = Object.entries(platformCounts)
    .sort(([,a], [,b]) => b - a)[0];

  if (!topPlatform) return null;

  const percentage = Math.round((topPlatform[1] / recentPosts.length) * 100);

  return {
    id: 'platform-trend',
    type: 'trend',
    title: `${topPlatform[0]} Dominance`,
    description: `${percentage}% of your recent saves are from ${topPlatform[0]}. Consider diversifying your content sources.`,
    confidence: 0.8,
    data: { platform: topPlatform[0], percentage },
    createdAt: new Date()
  };
};

/**
 * Analyzes content patterns
 */
const analyzeContentPatterns = (posts: Post[]): MindstreamInsight | null => {
  if (posts.length < 10) return null;

  // Analyze content length patterns
  const contentLengths = posts
    .filter(post => post.content)
    .map(post => post.content!.length);

  if (contentLengths.length === 0) return null;

  const avgLength = contentLengths.reduce((a, b) => a + b, 0) / contentLengths.length;
  const longPosts = contentLengths.filter(length => length > avgLength * 1.5).length;
  const shortPosts = contentLengths.filter(length => length < avgLength * 0.5).length;

  let insight = '';
  if (longPosts > shortPosts) {
    insight = 'You prefer in-depth, detailed content. Consider creating comprehensive guides.';
  } else if (shortPosts > longPosts) {
    insight = 'You gravitate toward bite-sized content. Quick tips and summaries might work well.';
  } else {
    insight = 'You have a balanced mix of content lengths. This diversity is great for different audiences.';
  }

  return {
    id: 'content-pattern',
    type: 'pattern',
    title: 'Content Length Preference',
    description: insight,
    confidence: 0.7,
    data: { avgLength: Math.round(avgLength), longPosts, shortPosts },
    createdAt: new Date()
  };
};

/**
 * Analyzes engagement patterns
 */
const analyzeEngagementPatterns = (posts: Post[]): MindstreamInsight | null => {
  const postsWithStats = posts.filter(post => 
    post.stats && (post.stats.likes || post.stats.comments || post.stats.shares)
  );

  if (postsWithStats.length < 5) return null;

  const engagementScores = postsWithStats.map(post => {
    const stats = post.stats!;
    return (stats.likes || 0) + (stats.comments || 0) * 2 + (stats.shares || 0) * 3;
  });

  const avgEngagement = engagementScores.reduce((a, b) => a + b, 0) / engagementScores.length;
  const topPost = postsWithStats[engagementScores.indexOf(Math.max(...engagementScores))];

  return {
    id: 'engagement-pattern',
    type: 'insight',
    title: 'High-Engagement Content',
    description: `Your top post has ${Math.round(avgEngagement * 2)} engagement points. Posts from ${topPost.platform} tend to perform well.`,
    confidence: 0.6,
    data: { topPost: topPost.id, platform: topPost.platform, avgEngagement },
    createdAt: new Date()
  };
};

/**
 * Analyzes time-based patterns
 */
const analyzeTimePatterns = (posts: Post[]): MindstreamInsight | null => {
  if (posts.length < 20) return null;

  const hourCounts: Record<number, number> = {};
  
  posts.forEach(post => {
    const date = new Date(post.savedAt || post.timestamp || '');
    const hour = date.getHours();
    hourCounts[hour] = (hourCounts[hour] || 0) + 1;
  });

  const peakHour = Object.entries(hourCounts)
    .sort(([,a], [,b]) => b - a)[0];

  if (!peakHour) return null;

  const hour = parseInt(peakHour[0]);
  const timeOfDay = hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening';

  return {
    id: 'time-pattern',
    type: 'pattern',
    title: 'Peak Activity Time',
    description: `You're most active in the ${timeOfDay} (${hour}:00). Consider scheduling content creation during this time.`,
    confidence: 0.7,
    data: { peakHour: hour, timeOfDay, count: peakHour[1] },
    createdAt: new Date()
  };
};

/**
 * Clusters posts by content similarity
 */
export const clusterPostsByContent = (posts: Post[]): ContentCluster[] => {
  // Simple keyword-based clustering
  const clusters: ContentCluster[] = [];
  const processedPosts = new Set<string>();

  posts.forEach(post => {
    if (processedPosts.has(post.id) || !post.content) return;

    const keywords = extractKeywords(post.content);
    const similarPosts = posts.filter(p => 
      p.id !== post.id && 
      !processedPosts.has(p.id) && 
      p.content &&
      hasCommonKeywords(extractKeywords(p.content), keywords, 2)
    );

    if (similarPosts.length >= 2) {
      const clusterPosts = [post, ...similarPosts];
      const allKeywords = Array.from(new Set(
        clusterPosts.flatMap(p => extractKeywords(p.content || ''))
      ));

      const totalEngagement = clusterPosts.reduce((sum, p) => {
        const stats = p.stats;
        return sum + (stats?.likes || 0) + (stats?.comments || 0) + (stats?.shares || 0);
      }, 0);

      clusters.push({
        id: `cluster-${clusters.length}`,
        theme: keywords.slice(0, 3).join(', '),
        posts: clusterPosts,
        keywords: allKeywords,
        platforms: Array.from(new Set(clusterPosts.map(p => p.platform))),
        engagement: {
          total: totalEngagement,
          average: totalEngagement / clusterPosts.length
        }
      });

      clusterPosts.forEach(p => processedPosts.add(p.id));
    }
  });

  return clusters.sort((a, b) => b.engagement.total - a.engagement.total);
};

/**
 * Identifies trending topics from recent posts
 */
export const identifyTrendingTopics = (posts: Post[]): TrendingTopic[] => {
  const recentPosts = posts.filter(post => {
    const postDate = new Date(post.savedAt || post.timestamp || '');
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return postDate >= weekAgo;
  });

  const olderPosts = posts.filter(post => {
    const postDate = new Date(post.savedAt || post.timestamp || '');
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return postDate >= twoWeeksAgo && postDate < weekAgo;
  });

  const recentTopics = getTopicCounts(recentPosts);
  const olderTopics = getTopicCounts(olderPosts);

  const trendingTopics: TrendingTopic[] = [];

  Object.entries(recentTopics).forEach(([topic, recentCount]) => {
    const olderCount = olderTopics[topic] || 0;
    const growth = olderCount > 0 ? ((recentCount - olderCount) / olderCount) * 100 : 100;

    if (recentCount >= 2 && growth > 0) {
      const topicPosts = recentPosts.filter(post => 
        post.content?.toLowerCase().includes(topic.toLowerCase())
      );

      trendingTopics.push({
        topic,
        count: recentCount,
        platforms: Array.from(new Set(topicPosts.map(p => p.platform))),
        recentPosts: topicPosts.slice(0, 3),
        growth
      });
    }
  });

  return trendingTopics.sort((a, b) => b.growth - a.growth).slice(0, 5);
};

/**
 * Extracts keywords from text content
 */
const extractKeywords = (text: string): string[] => {
  const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those']);
  
  return text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3 && !stopWords.has(word))
    .slice(0, 10);
};

/**
 * Checks if two keyword arrays have common keywords
 */
const hasCommonKeywords = (keywords1: string[], keywords2: string[], minCommon: number): boolean => {
  const common = keywords1.filter(k => keywords2.includes(k));
  return common.length >= minCommon;
};

/**
 * Gets topic counts from posts
 */
const getTopicCounts = (posts: Post[]): Record<string, number> => {
  const topicCounts: Record<string, number> = {};
  
  posts.forEach(post => {
    if (!post.content) return;
    
    const keywords = extractKeywords(post.content);
    keywords.forEach(keyword => {
      topicCounts[keyword] = (topicCounts[keyword] || 0) + 1;
    });
  });

  return topicCounts;
};
