/**
 * Simple toast notification utility
 */

export interface ToastOptions {
  type?: 'success' | 'error' | 'info';
  duration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

/**
 * Shows a toast notification
 * @param message The message to display
 * @param options Toast configuration options
 */
export function showToast(message: string, options: ToastOptions = {}): void {
  const {
    type = 'info',
    duration = 3000,
    position = 'top-right'
  } = options;

  // Create toast element
  const toast = document.createElement('div');
  toast.className = getToastClasses(type, position);
  toast.textContent = message;

  // Add to DOM
  document.body.appendChild(toast);

  // Trigger animation
  setTimeout(() => {
    toast.classList.add('opacity-100', 'translate-y-0');
    toast.classList.remove('opacity-0', 'translate-y-2');
  }, 10);

  // Remove after duration
  setTimeout(() => {
    toast.classList.add('opacity-0', 'translate-y-2');
    toast.classList.remove('opacity-100', 'translate-y-0');
    
    setTimeout(() => {
      if (toast.parentNode) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, duration);
}

/**
 * Gets the CSS classes for a toast based on type and position
 */
function getToastClasses(type: string, position: string): string {
  const baseClasses = 'fixed z-50 px-4 py-2 rounded-lg shadow-lg text-white text-sm font-medium transition-all duration-300 opacity-0 translate-y-2';
  
  const typeClasses = {
    success: 'bg-green-500',
    error: 'bg-red-500',
    info: 'bg-blue-500'
  };

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  return `${baseClasses} ${typeClasses[type as keyof typeof typeClasses]} ${positionClasses[position as keyof typeof positionClasses]}`;
}

/**
 * Convenience functions for different toast types
 */
export const toast = {
  success: (message: string, options?: Omit<ToastOptions, 'type'>) => 
    showToast(message, { ...options, type: 'success' }),
  
  error: (message: string, options?: Omit<ToastOptions, 'type'>) => 
    showToast(message, { ...options, type: 'error' }),
  
  info: (message: string, options?: Omit<ToastOptions, 'type'>) => 
    showToast(message, { ...options, type: 'info' })
};
