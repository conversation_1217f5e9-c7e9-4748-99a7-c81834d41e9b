import React, { useState } from 'react';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  selectedEmoji?: string;
  className?: string;
  compact?: boolean; // For use in modals or constrained spaces
}

// Organized emoji categories for easy selection
const EMOJI_CATEGORIES = {
  'Symbols': ['📚', '🔖', '📝', '📊', '📈', '📉', '🎯', '⭐', '🔥', '💡', '🎨', '🔧', '⚙️', '🔍', '📱', '💻'],
  'Objects': ['📦', '🎁', '📋', '📌', '📎', '🗂️', '📁', '🗃️', '🗄️', '📇', '🗳️', '🗞️', '📰', '📄', '📃', '📑'],
  'Business': ['💼', '💰', '💳', '💎', '🏆', '🥇', '🥈', '🥉', '🎖️', '🏅', '📊', '📈', '📉', '💹', '🏢', '🏭'],
  'Education': ['🎓', '📚', '📖', '📝', '✏️', '🖊️', '🖋️', '📐', '📏', '🧮', '🔬', '🧪', '🧬', '🔭', '🎒', '📓'],
  'Technology': ['💻', '🖥️', '⌨️', '🖱️', '🖨️', '📱', '☎️', '📞', '📟', '📠', '📡', '🔌', '🔋', '💾', '💿', '📀'],
  'Creative': ['🎨', '🖌️', '🖍️', '✨', '🌟', '💫', '⭐', '🌈', '🎭', '🎪', '🎨', '🖼️', '🎬', '📷', '📸', '🎥'],
  'Health': ['🏥', '⚕️', '💊', '💉', '🩺', '🦷', '🧠', '❤️', '🫀', '🫁', '🦴', '👁️', '🧬', '🔬', '🧪', '⚗️'],
  'Travel': ['✈️', '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️'],
  'Food': ['🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅'],
  'Nature': ['🌱', '🌿', '🍀', '🌳', '🌲', '🌴', '🌵', '🌾', '🌻', '🌺', '🌸', '🌼', '🌷', '🥀', '🌹', '🏵️'],
  'Sports': ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍'],
  'Music': ['🎵', '🎶', '🎼', '🎹', '🥁', '🎷', '🎺', '🎸', '🪕', '🎻', '🪗', '🎤', '🎧', '📻', '🎚️', '🎛️']
};

const EmojiPicker: React.FC<EmojiPickerProps> = ({
  onEmojiSelect,
  selectedEmoji = '',
  className = '',
  compact = false
}) => {
  const [activeCategory, setActiveCategory] = useState<string>('Symbols');
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'bottom' | 'top'>('bottom');

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    setIsOpen(false);
  };

  const handleToggleOpen = () => {
    if (!isOpen) {
      // Check if there's enough space below, otherwise position above
      const button = document.activeElement as HTMLElement;
      if (button) {
        const rect = button.getBoundingClientRect();
        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;

        // Picker height is approximately 280px (tabs + grid + close button)
        if (spaceBelow < 280 && spaceAbove > 280) {
          setDropdownPosition('top');
        } else {
          setDropdownPosition('bottom');
        }
      }
    }
    setIsOpen(!isOpen);
  };

  const categories = Object.keys(EMOJI_CATEGORIES);

  return (
    <div className={`relative ${className}`}>
      {/* Emoji Button */}
      <button
        type="button"
        onClick={handleToggleOpen}
        className="w-16 px-2 py-1 border border-gray-300 rounded text-sm text-center focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white hover:bg-gray-50"
      >
        {selectedEmoji || '😀'}
      </button>

      {/* Emoji Picker Dropdown */}
      {isOpen && (
        <div className={`absolute ${dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'} ${compact ? 'right-0' : 'left-0'} bg-white border border-gray-300 rounded-lg shadow-lg z-[60] ${compact ? 'w-64' : 'w-72'} max-w-[90vw]`}>
          {/* Category Tabs */}
          <div className={`flex flex-wrap border-b border-gray-200 p-2 gap-1 ${compact ? 'max-h-16' : 'max-h-20'} overflow-y-auto`}>
            {categories.map(category => (
              <button
                key={category}
                type="button"
                onClick={() => setActiveCategory(category)}
                className={`px-2 py-1 text-xs rounded transition-colors flex-shrink-0 ${
                  activeCategory === category
                    ? 'bg-purple-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Emoji Grid */}
          <div className={`p-2 ${compact ? 'max-h-32' : 'max-h-40'} overflow-y-auto`}>
            <div className={`grid ${compact ? 'grid-cols-6' : 'grid-cols-8'} gap-1`}>
              {EMOJI_CATEGORIES[activeCategory as keyof typeof EMOJI_CATEGORIES]?.map(emoji => (
                <button
                  key={emoji}
                  type="button"
                  onClick={() => handleEmojiClick(emoji)}
                  className={`${compact ? 'w-6 h-6 text-xs' : 'w-7 h-7 text-sm'} hover:bg-gray-100 rounded transition-colors flex items-center justify-center ${
                    selectedEmoji === emoji ? 'bg-purple-100 ring-2 ring-purple-500' : ''
                  }`}
                  title={emoji}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>

          {/* Close Button */}
          <div className="border-t border-gray-200 p-2">
            <button
              type="button"
              onClick={() => setIsOpen(false)}
              className="w-full px-3 py-1 text-xs text-gray-600 hover:text-gray-800"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Backdrop to close picker */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[55]"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default EmojiPicker;
