import React, { useState } from 'react';
import { Post } from '../types';
import { PlatformLogo } from './PlatformLogo';
import { formatDistanceToNow } from 'date-fns';

interface ThreadCardProps {
  threadPosts: Post[];
  onDelete: (postId: string) => void;
  onOpenDetails: (post: Post) => void;
  isForCapture?: boolean;
}

const ThreadCard: React.FC<ThreadCardProps> = ({
  threadPosts,
  onDelete,
  onOpenDetails,
  isForCapture = false
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  if (!threadPosts || threadPosts.length === 0) {
    return null;
  }

  // Sort posts by thread position
  const sortedPosts = [...threadPosts].sort((a, b) =>
    (a.threadPosition || 0) - (b.threadPosition || 0)
  );

  const firstPost = sortedPosts[0];
  const threadLength = threadPosts.length;
  const platform = firstPost.platform;

  // Handle clicking the thread card - open PostViewerFullScreen with first post
  const handleCardClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onOpenDetails(firstPost); // Open with the first post, thread data will be handled in PostViewerFullScreen
  };

  const handleDeleteThread = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Delete all posts in the thread
    threadPosts.forEach(post => {
      if (post.id) {
        onDelete(post.id);
      }
    });
  };

  // Handle Toggle Expansion for content preview
  const toggleExpansion = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    setIsExpanded(!isExpanded);
  };

  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Unknown time';
    }
  };

  // Format number for display (e.g., 1500 -> 1.5K)
  const formatNumber = (num: number | undefined): string => {
    if (num === undefined) return '0';
    if (num === 0) return '0';
    if (num < 1000) return num.toString();
    return (num / 1000).toFixed(1) + 'K';
  };

  // Get engagement metrics from the first post
  const getEngagementMetrics = (post: Post) => {
    const stats = post.stats;
    const interactions = (post as any).interactions;

    return {
      comments: stats?.comments ?? interactions?.replies ?? 0,
      shares: stats?.shares ?? interactions?.reposts ?? 0,
      likes: stats?.likes ?? interactions?.likes ?? 0,
      views: stats?.views ?? 0
    };
  };

  const metrics = getEngagementMetrics(firstPost);

  // Visual truncation handled by CSS, expansion only for very long posts
  const EXPANSION_THRESHOLD = 500;
  const postContentLength = firstPost.content?.length || 0;
  const needsExpansionButton = postContentLength > EXPANSION_THRESHOLD;

  return (
    <article
      className={`post-card notely-card relative bg-notely-card border border-notely-border rounded-notely-xl shadow-notely-md hover:shadow-notely-lg hover:-translate-y-2 group break-inside-avoid mb-4 overflow-hidden cursor-pointer notely-filter-transition
      ${platform === 'X/Twitter' ? 'border-t-[3px] border-t-black' : ''}
      ${platform === 'LinkedIn' ? 'border-t-[3px] border-t-blue-600' : ''}
      ${platform === 'Reddit' ? 'border-t-[3px] border-t-orange-500' : ''}
      ${platform === 'Instagram' ? 'border-t-[3px] border-t-purple-500' : ''}
      ${platform === 'pinterest' ? 'border-t-[3px] border-t-red-600' : ''}
      ${platform === 'Web' ? 'border-t-[3px] border-t-green-500' : ''}`}
      onClick={handleCardClick}
    >
      {/* Thread Indicator - Small badge at top */}
      {!isForCapture && (
        <div className="absolute top-2 left-2 z-10">
          <div className="bg-notely-accent text-white text-[11px] font-medium rounded-md px-2 py-1">
            🧵 {threadLength}
          </div>
        </div>
      )}

      {/* Platform Logo */}
      {!isForCapture && (
        <div className="absolute top-2 right-2 z-10 opacity-70 group-hover:opacity-100 transition-opacity">
          <PlatformLogo platform={platform} className="w-5 h-5 text-gray-600" />
        </div>
      )}

      {/* Delete Button */}
      {!isForCapture && (
        <button
          onClick={handleDeleteThread}
          className="absolute top-8 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
          title="Delete entire thread"
        >
          ×
        </button>
      )}

      <div className="px-4 py-3">
        {/* Author Info */}
        <div className="flex items-start gap-2.5 mb-2.5">
          {firstPost.authorAvatar && (
            <img
              src={firstPost.authorAvatar}
              alt={firstPost.author}
              className="w-9 h-9 rounded-full object-cover flex-shrink-0"
            />
          )}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-lg font-semibold leading-tight text-notely-text-primary truncate">
                {firstPost.authorName || firstPost.author}
              </span>
              {firstPost.authorHandle && (
                <span className="text-notely-text-secondary text-sm leading-relaxed">
                  @{firstPost.authorHandle}
                </span>
              )}
            </div>
            <div className="text-sm text-notely-text-muted leading-relaxed">
              {formatTimeAgo(firstPost.savedAt)}
            </div>
          </div>
        </div>

        {/* Post Content */}
        <div className="mb-2.5">
          <div className="text-notely-text-primary text-sm leading-relaxed">
            {/* Proper JavaScript truncation instead of broken CSS line-clamp */}
            {isExpanded ? firstPost.content : 
              firstPost.content && firstPost.content.length > 300 ? 
                firstPost.content.substring(0, 300).trim() + '...' : 
                firstPost.content
            }
          </div>

          {/* Show more button - opens popup */}
          {firstPost.content && firstPost.content.length > 300 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onOpenDetails(firstPost.id);
              }}
              className="text-notely-accent hover:text-notely-accent-hover text-sm font-medium mt-1 notely-filter-transition"
            >
              Show more
            </button>
          )}
        </div>

        {/* Media */}
        {firstPost.media && firstPost.media.length > 0 && (
          <div className="mb-2.5">
            <img
              src={firstPost.media[0].url}
              alt={firstPost.media[0].altText || 'Thread image'}
              className={`w-full object-cover rounded-lg cursor-pointer ${
                platform === 'Instagram' ? 'aspect-square' : 'max-h-64'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedImage(firstPost.media![0].url);
              }}
            />
          </div>
        )}

        {/* Engagement Metrics - Hide for Pinterest as they don't provide meaningful engagement data */}
        {platform !== 'pinterest' && (metrics.comments > 0 || metrics.shares > 0 || metrics.likes > 0 || metrics.views > 0) && (
          <div className="flex items-center justify-between text-notely-text-muted text-sm leading-relaxed">
            <div className="flex items-center space-x-4">
              {/* Comments */}
              {metrics.comments > 0 && (
                <div className="flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                  </svg>
                  <span className="font-medium">{formatNumber(metrics.comments)}</span>
                </div>
              )}

              {/* Shares */}
              {metrics.shares > 0 && (
                <div className="flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 1l4 4-4 4" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 11V9a4 4 0 0 1 4-4h14" />
                  </svg>
                  <span className="font-medium">{formatNumber(metrics.shares)}</span>
                </div>
              )}

              {/* Likes */}
              {metrics.likes > 0 && (
                <div className="flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
                  </svg>
                  <span className="font-medium">{formatNumber(metrics.likes)}</span>
                </div>
              )}

              {/* Views */}
              {metrics.views > 0 && (
                <div className="flex items-center space-x-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                    <circle cx="12" cy="12" r="3" />
                  </svg>
                  <span className="font-medium">{formatNumber(metrics.views)}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setSelectedImage(null)}
        >
          <div className="max-w-4xl max-h-4xl p-4">
            <img
              src={selectedImage}
              alt="Thread image"
              className="max-w-full max-h-full object-contain"
            />
          </div>
        </div>
      )}
    </article>
  );
};

export default ThreadCard;
