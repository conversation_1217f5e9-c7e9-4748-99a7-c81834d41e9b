import React, { useState, useEffect, useCallback } from 'react';
import { Post } from '../types';
import { getAllCategories, saveAllCategories, getAllTags, saveAllTags, getSavedPosts, savePost } from '../storage';
import { formatForDisplay } from '../utils/formatUtils';
import Widget from './Widget';
import { useTranslation } from '../hooks/useTranslation';
import { UserCategoryPreferences } from '../services/userCategoryPreferences';
import { CategoryService } from '../services/categoryService';
import { CategorySyncService } from '../services/categorySync';
import { validateCategoryName } from '../utils/categoryValidation';

interface CategoryTagManagerProps {
  posts: Post[];
  isDragging?: boolean;
  onRemove?: () => void;
}

interface CategoryItem {
  id: string;
  name: string;
  postCount: number;
  isEditing: boolean;
  isUserCreated: boolean;
}

interface TagItem {
  id: string;
  name: string;
  postCount: number;
  isEditing: boolean;
  isUserCreated: boolean;
}

interface MergeResult {
  originalCategory: string;
  mergedIntoCategory: string;
  affectedPosts: number;
}

const CategoryTagManager: React.FC<CategoryTagManagerProps> = ({
  posts,
  isDragging = false,
  onRemove
}) => {
  const { t } = useTranslation();
  const [activeView, setActiveView] = useState<'categories' | 'category-detail'>('categories');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [tags, setTags] = useState<TagItem[]>([]);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [editingValue, setEditingValue] = useState<string>('');
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newItemName, setNewItemName] = useState<string>('');
  const [mergeSuggestions, setMergeSuggestions] = useState<MergeResult[]>([]);
  const [showMergeSuggestions, setShowMergeSuggestions] = useState(false);
  const [error, setError] = useState<string>('');

  console.log('[CategoryTagManager] Component re-rendered. Current categories state (first 3):', JSON.stringify(categories.slice(0, 3), null, 2));

  // Load categories and tags from storage and posts
  const loadCategoriesAndTags = useCallback(async () => {
    console.log(`[CategoryTagManager] ENTERING loadCategoriesAndTags. Posts length: ${posts.length}`);
    try {
      // Get stored categories and tags
      console.log('[CategoryTagManager] BEFORE Promise.all for stored C/T.');
      const [storedCategories, storedTags] = await Promise.all([
        getAllCategories(),
        getAllTags()
      ]);
      console.log('[CategoryTagManager] Stored categories:', storedCategories);
      console.log('[CategoryTagManager] Stored tags:', storedTags);

      // Extract categories and tags from posts
      const postCategories = new Set<string>();
      const postTags = new Set<string>();
      const categoryCounts = new Map<string, number>();
      const tagCounts = new Map<string, number>();
      
      // Create a map to track which posts belong to which categories and tags
      const categoryToPostsMap = new Map<string, Set<string>>();
      const tagToPostsMap = new Map<string, Set<string>>();
      
      // Process each post
      console.log('[CategoryTagManager] Processing posts for counts. Number of posts:', posts.length);
      posts.forEach(post => {
        const postId = post.id || Math.random().toString(); // Fallback if post has no ID
        
        // Process categories
        if (post.categories && Array.isArray(post.categories)) {
          post.categories.forEach(cat => {
            if (typeof cat === 'string' && cat.trim()) {
              // Add category to the set of unique categories
              postCategories.add(cat);
              
              // Add this post to the set of posts for this category
              if (!categoryToPostsMap.has(cat)) {
                categoryToPostsMap.set(cat, new Set<string>());
              }
              categoryToPostsMap.get(cat)!.add(postId);
            }
          });
        }
        
        // Process tags
        if (post.tags && Array.isArray(post.tags)) {
          post.tags.forEach(tag => {
            if (typeof tag === 'string' && tag.trim()) {
              // Add tag to the set of unique tags
              postTags.add(tag);
              
              // Add this post to the set of posts for this tag
              if (!tagToPostsMap.has(tag)) {
                tagToPostsMap.set(tag, new Set<string>());
              }
              tagToPostsMap.get(tag)!.add(postId);
            }
          });
        }
      });
      
      // Calculate counts based on the number of unique posts per category/tag
      categoryToPostsMap.forEach((postIds, category) => {
        categoryCounts.set(category, postIds.size);
      });
      
      tagToPostsMap.forEach((postIds, tag) => {
        tagCounts.set(tag, postIds.size);
      });
      console.log('[CategoryTagManager] categoryToPostsMap:', categoryToPostsMap);
      console.log('[CategoryTagManager] tagToPostsMap:', tagToPostsMap);
      console.log('[CategoryTagManager] Calculated categoryCounts:', categoryCounts);
      console.log('[CategoryTagManager] Calculated tagCounts:', tagCounts);

      // Merge stored and post-derived categories
      const allCategories = new Set([...storedCategories, ...postCategories]);
      console.log('[CategoryTagManager] Merged allCategories (stored + from posts):', allCategories);
      const categoryItems: CategoryItem[] = Array.from(allCategories).map(catName => {
        const countFromMap = categoryCounts.get(catName);
        const finalCount = countFromMap === undefined ? 0 : countFromMap;
        console.log(`[CategoryTagManager] Mapping category: '${catName}'. Raw count from categoryCounts.get('${catName}'): ${countFromMap}. Final postCount for item: ${finalCount}.`);
        // For specific categories, log the entire categoryCounts map to see its state
        if (catName === 'ai_data' || catName === 'technology' || catName === 'career_productivity') {
            console.log(`[CategoryTagManager] Full categoryCounts map when processing '${catName}':`, new Map(categoryCounts));
        }
        return {
          id: catName,
          name: catName,
          postCount: finalCount,
          isEditing: false,
          isUserCreated: storedCategories.includes(catName)
        };
      });

      // Merge stored and post-derived tags
      const allTags = new Set([...storedTags, ...postTags]);
      console.log('[CategoryTagManager] Merged allTags (stored + from posts):', allTags);
      const tagItems: TagItem[] = Array.from(allTags).map(tag => ({
        id: tag,
        name: tag,
        postCount: tagCounts.get(tag) || 0,
        isEditing: false,
        isUserCreated: storedTags.includes(tag)
      }));

      // Sort by post count (descending) then alphabetically
      categoryItems.sort((a, b) => {
        if (b.postCount !== a.postCount) return b.postCount - a.postCount;
        return a.name.localeCompare(b.name);
      });

      tagItems.sort((a, b) => {
        if (b.postCount !== a.postCount) return b.postCount - a.postCount;
        return a.name.localeCompare(b.name);
      });

      console.log('[CategoryTagManager] Final categoryItems to be set to state (SNAPSHOT - first 5):', JSON.stringify(categoryItems.slice(0, 5), null, 2));
      console.log('[CategoryTagManager] Final tagItems to be set to state (first 5):', tagItems.slice(0, 5).map(ti => ({ name: ti.name, count: ti.postCount })));
      setCategories(categoryItems);
      setTags(tagItems);
      console.log('[CategoryTagManager] AFTER setCategories. categoryItems length:', categoryItems.length); // Log after set

      // Get merge suggestions
      const suggestions = await analyzeCategoriesForMerging(posts);
      setMergeSuggestions(suggestions);
    } catch (error) {
      console.error("[CategoryTagManager] ERROR in loadCategoriesAndTags:", error);
    }
  }, [posts]);

  useEffect(() => {
    console.log(`[CategoryTagManager] useEffect triggered. Posts length: ${posts.length}. Calling loadCategoriesAndTags.`);
    console.log(`[CategoryTagManager] Inspecting posts prop inside useEffect (first 3):`, JSON.stringify(posts.slice(0, 3), null, 2));
    loadCategoriesAndTags();
  }, [loadCategoriesAndTags, posts]);

  // Start editing an item
  const startEditing = (id: string, currentName: string) => {
    if (activeView === 'categories') {
      setCategories(prev => prev.map(cat =>
        cat.id === id ? { ...cat, isEditing: true } : { ...cat, isEditing: false }
      ));
    } else {
      setTags(prev => prev.map(tag =>
        tag.id === id ? { ...tag, isEditing: true } : { ...tag, isEditing: false }
      ));
    }
    setEditingValue(currentName);
  };

  // Update posts when category/tag names change
  const updatePostsWithNewName = async (oldName: string, newName: string, type: 'categories' | 'tags') => {
    try {
      const allPosts = await getSavedPosts();
      const updatedPosts: Post[] = [];

      for (const post of allPosts) {
        let needsUpdate = false;
        const updatedPost = { ...post };

        if (type === 'categories' && post.categories) {
          const updatedCategories = post.categories.map(cat => {
            if (cat === oldName) {
              needsUpdate = true;
              return newName;
            }
            return cat;
          });
          if (needsUpdate) {
            updatedPost.categories = updatedCategories;
          }
        } else if (type === 'tags' && post.tags) {
          const updatedTags = post.tags.map(tag => {
            if (tag === oldName) {
              needsUpdate = true;
              return newName;
            }
            return tag;
          });
          if (needsUpdate) {
            updatedPost.tags = updatedTags;
          }
        }

        if (needsUpdate) {
          updatedPosts.push(updatedPost);
        }
      }

      // Save all updated posts
      for (const post of updatedPosts) {
        await savePost(post);
      }

      console.log(`[CategoryTagManager] Updated ${updatedPosts.length} posts with new ${type.slice(0, -1)} name: ${oldName} → ${newName}`);
    } catch (error) {
      console.error('[CategoryTagManager] Error updating posts with new name:', error);
    }
  };

  // Handle user category actions
  const handleUserCategoryAction = async (
    action: 'rename' | 'delete' | 'merge' | 'keep',
    categoryName: string,
    newName?: string
  ) => {
    await UserCategoryPreferences.recordUserDecision({
      originalName: categoryName,
      newName: newName || null,
      timestamp: Date.now(),
      type: action
    });

    // If this was a merge action, record it in merge history
    if (action === 'merge' && newName) {
      await UserCategoryPreferences.recordMerge({
        sourceCategory: categoryName,
        targetCategory: newName,
        timestamp: Date.now(),
        isUserInitiated: true
      });
    }
  };

  // Save edited item
  const saveEdit = async (id: string) => {
    const newName = editingValue.trim();
    if (!newName || newName === id) {
      cancelEdit();
      return;
    }

    try {
      if (activeView === 'categories') {
        // Record user's decision to rename
        await handleUserCategoryAction('rename', id, newName);
        
        const updatedCategories = categories.map(cat =>
          cat.id === id ? { ...cat, name: newName, id: newName, isEditing: false } : cat
        );
        setCategories(updatedCategories);
        await updatePostsWithNewName(id, newName, 'categories');
        await saveAllCategories(updatedCategories.map(cat => cat.name));
      } else {
        const updatedTags = tags.map(tag =>
          tag.id === id ? { ...tag, name: newName, id: newName, isEditing: false, isUserCreated: true } : tag
        );
        setTags(updatedTags);

        // Update posts with the new tag name
        await updatePostsWithNewName(id, newName, 'tags');

        // Save to storage
        const tagNames = updatedTags.map(tag => tag.name);
        await saveAllTags(tagNames);
      }
    } catch (error) {
      console.error('[CategoryTagManager] Error saving edit:', error);
    }

    setEditingValue('');
  };

  // Cancel editing
  const cancelEdit = () => {
    if (activeView === 'categories') {
      setCategories(prev => prev.map(cat => ({ ...cat, isEditing: false })));
    } else {
      setTags(prev => prev.map(tag => ({ ...tag, isEditing: false })));
    }
    setEditingValue('');
  };

  // Delete an item
  const deleteItem = async (id: string) => {
    try {
      if (activeView === 'categories') {
        const categoryService = CategoryService.getInstance();
        await categoryService.deleteCategory(id);
        setCategories(prev => prev.filter(cat => cat.id !== id));
        
        // Sync and validate after deletion
        const syncService = CategorySyncService.getInstance();
        await syncService.syncCategoryCounts();
        const integrity = await syncService.validateCategoryIntegrity();
        if (integrity.hasIssues) {
          console.warn('Category integrity issues:', integrity.issues);
        }
      } else {
        const updatedTags = tags.filter(tag => tag.id !== id);
        setTags(updatedTags);

        const tagNames = updatedTags.map(tag => tag.name);
        await saveAllTags(tagNames);
      }
    } catch (error) {
      console.error('[CategoryTagManager] Error deleting item:', error);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, itemId: string) => {
    setDraggedItem(itemId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetId) {
      setDraggedItem(null);
      return;
    }

    try {
      if (activeView === 'categories') {
        const draggedIndex = categories.findIndex(cat => cat.id === draggedItem);
        const targetIndex = categories.findIndex(cat => cat.id === targetId);

        if (draggedIndex !== -1 && targetIndex !== -1) {
          const newCategories = [...categories];
          const [draggedCategory] = newCategories.splice(draggedIndex, 1);
          newCategories.splice(targetIndex, 0, draggedCategory);

          setCategories(newCategories);

          // Save new order
          const categoryNames = newCategories.map(cat => cat.name);
          await saveAllCategories(categoryNames);
        }
      } else {
        const draggedIndex = tags.findIndex(tag => tag.id === draggedItem);
        const targetIndex = tags.findIndex(tag => tag.id === targetId);

        if (draggedIndex !== -1 && targetIndex !== -1) {
          const newTags = [...tags];
          const [draggedTag] = newTags.splice(draggedIndex, 1);
          newTags.splice(targetIndex, 0, draggedTag);

          setTags(newTags);

          // Save new order
          const tagNames = newTags.map(tag => tag.name);
          await saveAllTags(tagNames);
        }
      }
    } catch (error) {
      console.error('[CategoryTagManager] Error reordering items:', error);
    }
    
    setDraggedItem(null);
  };

  // Add new item
  const addNewItem = async () => {
    const rawName = newItemName.trim();
    if (!rawName) return;

    try {
      if (activeView === 'categories') {
        const categoryService = CategoryService.getInstance();
        const result = await categoryService.addCategory(rawName);
        
        if ('error' in result) {
          setError(result.error);
          return;
        }
        
        // Update local state
        setCategories(prev => [...prev, result]);
        setNewItemName('');
        setError('');
        
        // Sync counts and cleanup
        const syncService = CategorySyncService.getInstance();
        await syncService.syncCategoryCounts();
        await syncService.cleanupOrphanedCategories();
      } else {
        // Handle tags similarly...
      }
    } catch (err) {
      setError('Failed to add item. Please try again.');
      console.error('Error adding item:', err);
    }
  };

  // Get tags for a specific category
  const getTagsForCategory = useCallback((categoryName: string) => {
    const relevantTagNames = new Set<string>();
    posts.forEach(post => {
      if (post.categories && post.categories.includes(categoryName)) {
        if (post.tags) {
          post.tags.forEach(tag => relevantTagNames.add(tag));
        }
      }
    });

    // Filter the main `tags` state to get only the relevant TagItems
    const filteredTags = tags.filter(tag => relevantTagNames.has(tag.name));
    return filteredTags;
  }, [posts, tags]); // Dependencies for useCallback

  // Add new merge categories function
  const mergeCategories = async (sourceCategory: string, targetCategory: string) => {
    try {
      await handleUserCategoryAction('merge', sourceCategory, targetCategory);
      
      // Update all posts with the source category to use the target category
      await updatePostsWithNewName(sourceCategory, targetCategory, 'categories');
      
      // Remove the source category
      const updatedCategories = categories.filter(cat => cat.id !== sourceCategory);
      setCategories(updatedCategories);
      await saveAllCategories(updatedCategories.map(cat => cat.name));
    } catch (error) {
      console.error('[CategoryTagManager] Error merging categories:', error);
    }
  };

  // Add merge suggestions UI after the add form
  const renderMergeSuggestions = () => {
    if (!showMergeSuggestions || mergeSuggestions.length === 0) return null;

    return (
      <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-yellow-800">
            Category Merge Suggestions
          </h3>
          <button
            onClick={() => setShowMergeSuggestions(false)}
            className="text-yellow-600 hover:text-yellow-800"
          >
            <span className="sr-only">Close</span>
            ×
          </button>
        </div>
        <div className="space-y-2">
          {mergeSuggestions.map((suggestion, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-2 bg-white rounded border border-yellow-100"
            >
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{suggestion.originalCategory}</span>
                <svg className="w-4 h-4 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
                <span className="text-sm font-medium text-gray-800">{suggestion.mergedIntoCategory}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-500">
                  {suggestion.affectedPosts} posts affected
                </span>
                <button
                  onClick={() => mergeCategories(suggestion.originalCategory, suggestion.mergedIntoCategory)}
                  className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded hover:bg-green-200"
                >
                  Apply
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderCategoryCard = (item: CategoryItem) => {
    console.log('[CategoryTagManager] renderCategoryCard called for item:', JSON.stringify(item));
    const isDraggingThis = draggedItem === item.id;
    const categoryTags = getTagsForCategory(item.name);

    return (
      <div
        key={item.id}
        className={`
          group relative bg-notely-surface/60 rounded-xl
          transition-all duration-200 hover:translate-y-[-2px] hover:shadow-notely-sm
          ${isDraggingThis ? 'opacity-50 scale-95 rotate-2' : ''}
          ${item.isEditing ? 'ring-2 ring-notely-accent/50' : ''}
        `}
        draggable={!item.isEditing}
        onDragStart={(e) => handleDragStart(e, item.id)}
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e, item.id)}
      >
        {/* Category Content */}
        <div className="p-4">
          {item.isEditing ? (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={editingValue}
                onChange={(e) => setEditingValue(e.target.value)}
                className="flex-1 px-3 py-2 rounded-lg bg-notely-surface text-notely-text
                  placeholder-notely-text-muted focus:outline-none focus:ring-2
                  focus:ring-notely-accent/50 border border-notely-border/20"
                placeholder="Category name..."
                onKeyPress={(e) => e.key === 'Enter' && saveEdit(item.id)}
                autoFocus
              />
              <button
                onClick={() => saveEdit(item.id)}
                className="px-3 py-2 rounded-lg bg-gradient-to-r from-notely-sky to-notely-lavender
                  text-white hover:shadow-notely-sm transition-all duration-200 hover:scale-105"
              >
                Save
              </button>
              <button
                onClick={cancelEdit}
                className="px-3 py-2 rounded-lg bg-notely-surface text-notely-text-muted
                  hover:text-notely-text-secondary hover:bg-notely-card/80 transition-all duration-200"
              >
                Cancel
              </button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-notely-text-primary">
                  {formatForDisplay(item.name)}
                </span>
                <span className="text-xs bg-notely-surface/60 text-notely-text-muted px-2 py-0.5 rounded-full">
                  {item.postCount} posts
                </span>
              </div>
              <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => startEditing(item.id, item.name)}
                  className="p-1.5 rounded-md bg-notely-sky/10 text-notely-sky hover:bg-notely-sky/20 transition-all duration-200 hover:scale-110"
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                <button
                  onClick={() => deleteItem(item.id)}
                  className="p-1.5 rounded-md bg-red-500/10 text-red-500 hover:bg-red-500/20 transition-all duration-200 hover:scale-110"
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Category Tags */}
          {categoryTags.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-1.5">
              {categoryTags.map((tag) => (
                <span
                  key={tag.id}
                  className="inline-flex items-center px-4 py-2 rounded-full text-sm
                    bg-gradient-to-r from-[#8B5CF6] to-[#6366F1] text-white"
                >
                  {formatForDisplay(tag.name)}
                  <span className="ml-1.5 text-white/90">({tag.postCount})</span>
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Widget
      title={t('widgets.organizeContent')}
      onRemove={onRemove}
      isDragging={isDragging}
    >
      <div className="space-y-4">
        {/* Add New Category Button */}
        <div className="flex justify-start">
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center px-3 py-1.5 rounded-lg
              bg-notely-accent/10 text-notely-accent hover:bg-notely-accent/20
              transition-all duration-200 text-sm font-medium hover:scale-105"
          >
            + New Category
          </button>
        </div>

        {/* Add Form */}
        {showAddForm && (
          <div className="flex items-center gap-2 mb-4">
            <input
              type="text"
              value={newItemName}
              onChange={(e) => setNewItemName(e.target.value)}
              className="flex-1 px-3 py-2 rounded-lg bg-notely-surface text-notely-text
                placeholder-notely-text-muted focus:outline-none focus:ring-2
                focus:ring-notely-accent/50 border border-notely-border/20"
              placeholder="Enter category name..."
            />
            <button
              onClick={addNewItem}
              className="px-4 py-2 rounded-lg bg-gradient-to-r from-notely-sky to-notely-lavender
                text-white hover:shadow-notely-sm transition-all duration-200 hover:scale-105"
            >
              Add
            </button>
            <button
              onClick={() => {
                setShowAddForm(false);
                setNewItemName('');
              }}
              className="px-4 py-2 rounded-lg bg-notely-surface text-notely-text-muted
                hover:text-notely-text-secondary hover:bg-notely-card/80 transition-all duration-200"
            >
              Cancel
            </button>
          </div>
        )}

        {/* Categories Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-h-[500px] overflow-y-auto pr-2 notely-scrollbar">
          {categories.map(item => renderCategoryCard(item))}
        </div>
      </div>
    </Widget>
  );
};

export default CategoryTagManager;
