import React, { useEffect, useState } from 'react';
import { Post } from '../types';
import { getSavedPosts, updatePostDetails, getAllCategories, getAllTags, saveAllCategories, saveAllTags } from '../storage';
import MultiItemInput from './MultiItemInput';

interface PostDetailModalProps {
  isOpen: boolean;
  postId: string | null;
  onClose: () => void;
  onSave: (postId: string, details: { categories?: string[], tags?: string[] }) => void;
}

const LoadingSpinner: React.FC = () => (
  <div className="flex justify-center items-center h-40">
    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

const PostDetailModal: React.FC<PostDetailModalProps> = ({
  isOpen,
  postId,
  onClose,
  onSave,
}) => {
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  
  const [allCategories, setAllCategories] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);

  // Load post data when modal opens
  useEffect(() => {
    if (!isOpen || !postId) return;

    const loadData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Load post data
        const allPosts = await getSavedPosts();
        const foundPost = allPosts.find(p => p.id === postId);
        
        if (!foundPost) {
          throw new Error('Post not found');
        }
        
        setPost(foundPost);

        // Load categories and tags
        const categories = await getAllCategories();
        const tags = await getAllTags();
        setAllCategories(categories);
        setAllTags(tags);
      } catch (err) {
        console.error('[PostDetailModal] Error loading data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load post details');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [isOpen, postId]);

  const handleCategoryChange = async (newCategories: string[]) => {
    if (!post) return;
    const validCategories = newCategories.slice(0, 3);
    
    // Update local state
    setPost(prev => ({
      ...prev!,
      categories: validCategories
    }));
    setHasChanges(true);

    // Update master list
    try {
      const existingCategories = await getAllCategories();
      const updatedCategories = [...new Set([...existingCategories, ...validCategories])];
      await saveAllCategories(updatedCategories);
      setAllCategories(updatedCategories);
    } catch (err) {
      console.error("[PostDetailModal] Error updating master categories:", err);
    }
  };

  const handleTagChange = async (newTags: string[]) => {
    if (!post) return;
    const validTags = newTags.slice(0, 5);
    
    // Update local state
    setPost(prev => ({
      ...prev!,
      tags: validTags
    }));
    setHasChanges(true);

    // Update master list
    try {
      const existingTags = await getAllTags();
      const updatedTags = [...new Set([...existingTags, ...validTags])];
      await saveAllTags(updatedTags);
      setAllTags(updatedTags);
    } catch (err) {
      console.error("[PostDetailModal] Error updating master tags:", err);
    }
  };

  const handleSave = async () => {
    if (!post || !postId || !hasChanges) return;
    
    try {
      await onSave(postId, {
        categories: post.categories || [],
        tags: post.tags || []
      });
      setHasChanges(false);
      onClose();
    } catch (err) {
      console.error("[PostDetailModal] Error saving changes:", err);
      setError("Failed to save changes");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="notely-card bg-notely-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-[#2F2F2F] flex justify-between items-center">
          <h2 className="text-xl font-semibold leading-tight text-notely-text-primary">Edit Post Details</h2>
          <button
            onClick={onClose}
            className="text-notely-text-muted hover:text-notely-text-secondary focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-8rem)]">
          {/* Left Column - Post Card */}
          <div className="w-1/2 border-r border-[#2F2F2F] px-6 py-4 overflow-y-auto">
            {loading ? (
              <LoadingSpinner />
            ) : error ? (
              <div className="text-red-600 bg-red-50 p-4 rounded">
                {error}
              </div>
            ) : post ? (
              <div className="post-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md bg-notely-card">
                {/* Post Header */}
                <div className="px-4 py-3 flex items-start">
                  {/* Avatar */}
                  <div className="flex-shrink-0">
                    <div 
                      className="w-12 h-12 rounded-full bg-gray-200 bg-cover bg-center"
                      style={{ backgroundImage: post.authorAvatar ? `url(${post.authorAvatar})` : undefined }}
                    />
                  </div>
                  {/* Author Info */}
                  <div className="ml-3 flex-grow">
                    <div className="flex items-center flex-wrap">
                      <span className="font-bold text-[15px] mr-1 text-notely-text-primary">
                        {post.authorName || post.authorHandle || 'Unknown Author'}
                      </span>
                      {post.authorHandle && (
                        <span className="text-notely-text-muted text-[15px]">
                          @{post.authorHandle}
                        </span>
                      )}
                    </div>
                    <span className="text-notely-text-muted text-[15px]">
                      {new Date(post.timestamp || post.savedAt || '').toLocaleDateString()}
                    </span>
                  </div>
                </div>

                {/* Post Content */}
                <div className="px-4 py-2">
                  {post.content && (
                    <p className="text-[15px] whitespace-pre-wrap break-words text-notely-text-primary">
                      {post.content}
                    </p>
                  )}
                </div>

                {/* Post Media */}
                {(post.savedImage || (post.media && post.media.length > 0)) && (
                  <div className="px-4 py-2">
                    <div className="rounded-2xl overflow-hidden bg-gray-100 relative group">
                      <img
                        src={post.savedImage || post.media?.[0].url}
                        alt={post.media?.[0].alt || ''}
                        className="w-full h-auto"
                      />
                      {/* Add View Full Size button */}
                      <a
                        href={post.savedImage || post.media?.[0].url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="absolute bottom-2 right-2 bg-black/75 text-white px-3 py-1.5 rounded-md text-sm font-medium 
                                 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-black/90"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="flex items-center space-x-1">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                          </svg>
                          <span>View Full Size</span>
                        </div>
                      </a>
                    </div>
                  </div>
                )}

                {/* Post Stats */}
                {/* Hide stats for Pinterest as they don't provide meaningful engagement data */}
                {post.platform !== 'pinterest' && (post.stats || (post as any).interactions) && (
                  (post.stats?.comments && post.stats.comments > 0) ||
                  (post.stats?.shares && post.stats.shares > 0) ||
                  (post.stats?.likes && post.stats.likes > 0) ||
                  ((post as any).interactions?.replies && (post as any).interactions.replies > 0) ||
                  ((post as any).interactions?.reposts && (post as any).interactions.reposts > 0) ||
                  ((post as any).interactions?.likes && (post as any).interactions.likes > 0)
                ) && (
                  <div className="px-4 py-3 border-t border-[#2F2F2F]">
                    <div className="flex items-center text-notely-text-muted space-x-6">
                      {((post.stats?.comments && post.stats.comments > 0) || ((post as any).interactions?.replies && (post as any).interactions.replies > 0)) && (
                        <div className="flex items-center">
                          <span className="text-sm">
                            {post.stats?.comments || (post as any).interactions?.replies || 0} comments
                          </span>
                        </div>
                      )}
                      {((post.stats?.shares && post.stats.shares > 0) || ((post as any).interactions?.reposts && (post as any).interactions.reposts > 0)) && (
                        <div className="flex items-center">
                          <span className="text-sm">
                            {post.stats?.shares || (post as any).interactions?.reposts || 0} shares
                          </span>
                        </div>
                      )}
                      {((post.stats?.likes && post.stats.likes > 0) || ((post as any).interactions?.likes && (post as any).interactions.likes > 0)) && (
                        <div className="flex items-center">
                          <span className="text-sm">
                            {post.stats?.likes || (post as any).interactions?.likes || 0} likes
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-notely-text-muted text-center py-4">
                No post data available
              </div>
            )}
          </div>

          {/* Right Column - Categories and Tags */}
          <div className="w-1/2 p-6 overflow-y-auto bg-notely-card">
            <div className="space-y-6">
              {/* Categories Section */}
              <div>
                <h3 className="text-xl font-semibold leading-tight text-notely-text-primary mb-4">Categories</h3>
                <MultiItemInput
                  label="Add categories (Max 3)"
                  items={post?.categories || []}
                  allItems={allCategories}
                  maxItems={3}
                  placeholder="Type a category..."
                  onChange={handleCategoryChange}
                />
                <p className="mt-2 text-sm text-notely-text-muted leading-relaxed">
                  Organize your posts with up to 3 categories
                </p>
              </div>

              {/* Tags Section */}
              <div>
                <h3 className="text-xl font-semibold leading-tight text-notely-text-primary mb-4">Tags</h3>
                <MultiItemInput
                  label="Add tags (Max 5)"
                  items={post?.tags || []}
                  allItems={allTags}
                  maxItems={5}
                  placeholder="Type a tag..."
                  onChange={handleTagChange}
                />
                <p className="mt-2 text-sm text-notely-text-muted leading-relaxed">
                  Add up to 5 tags to make your posts easier to find
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-[#2F2F2F] flex justify-end gap-2 bg-notely-card">
          <button
            onClick={onClose}
            className="px-4 py-2 text-notely-text-muted hover:text-notely-text-primary font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={loading || !hasChanges}
            className={`px-6 py-2 rounded-md text-white font-medium transition-colors 
              ${loading ? 'bg-gray-400 cursor-not-allowed' : 
                !hasChanges ? 'bg-gray-300 cursor-not-allowed' :
                'bg-notely-accent hover:bg-notely-accent/90'}`}
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default PostDetailModal; 