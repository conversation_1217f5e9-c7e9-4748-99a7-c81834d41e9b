import React from 'react';
import { Post } from '../types';
import DailyWisdom from './DailyWisdom';
import TagsCategoriesWidget from './TagsCategoriesWidget';

interface MindstreamSidebarProps {
  posts: Post[];
  className?: string;
  onOpenPost?: (post: Post) => void;
}

const MindstreamSidebar: React.FC<MindstreamSidebarProps> = ({
  posts,
  className = '',
  onOpenPost
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Daily Wisdom */}
      <DailyWisdom
        className="w-full"
        onQuoteClick={(quote) => {
          console.log('Quote clicked:', quote);
        }}
        onOpenPost={onOpenPost}
      />
      
      {/* Tags & Categories */}
      <div className="notely-card bg-notely-card rounded-notely-xl shadow-notely-md hover:shadow-notely-lg notely-filter-transition px-6 py-4 overflow-hidden">
        <TagsCategoriesWidget posts={posts} />
      </div>
    </div>
  );
};

export default MindstreamSidebar;
