import React, { useState } from 'react';
import { toast } from '../utils/toast';

interface CreateThreadWidgetProps {
  className?: string;
}

const CreateThreadWidget: React.FC<CreateThreadWidgetProps> = ({ className = '' }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [showAISuggestions, setShowAISuggestions] = useState(false);

  const handleGenerateHook = () => {
    if (!content.trim()) {
      toast.error('Please enter some content first');
      return;
    }
    toast.info('Generate Hook feature coming soon!');
    setShowAISuggestions(true);
  };

  const handleRewrite = () => {
    if (!content.trim()) {
      toast.error('Please enter some content first');
      return;
    }
    toast.info('Rewrite feature coming soon!');
    setShowAISuggestions(true);
  };

  const handlePublish = () => {
    if (!title.trim() || !content.trim()) {
      toast.error('Please enter both title and content');
      return;
    }
    toast.info('Publish feature coming soon!');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Title Input */}
      <div>
        <input
          type="text"
          placeholder="Thread title..."
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full px-4 py-3 bg-notely-surface border border-notely-border rounded-xl text-notely-text-primary placeholder-notely-text-tertiary focus:outline-none focus:ring-2 focus:ring-notely-accent focus:border-transparent transition-all"
        />
      </div>

      {/* Content Textarea */}
      <div>
        <textarea
          placeholder="Start writing or use AI…"
          value={content}
          onChange={(e) => setContent(e.target.value)}
          rows={6}
          className="w-full px-4 py-3 bg-notely-surface border border-notely-border rounded-xl text-notely-text-primary placeholder-notely-text-tertiary focus:outline-none focus:ring-2 focus:ring-notely-accent focus:border-transparent transition-all resize-none"
        />
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <button
          onClick={handleGenerateHook}
          className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-4 py-2 text-sm font-medium hover:scale-105 transition-transform"
        >
          Generate Hook
        </button>
        <button
          onClick={handleRewrite}
          className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-4 py-2 text-sm font-medium hover:scale-105 transition-transform"
        >
          Rewrite
        </button>
        <button
          onClick={handlePublish}
          className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-4 py-2 text-sm font-medium hover:scale-105 transition-transform"
        >
          Publish
        </button>
      </div>

      {/* AI Suggestions Area (hidden by default) */}
      {showAISuggestions && (
        <div className="bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-xl px-6 py-4 backdrop-blur-sm">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-notely-surface border border-notely-border rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs">✨</span>
            </div>
            <div>
              <p className="uppercase text-xs opacity-70 tracking-wide font-medium mb-2">AI Suggestions</p>
              <p className="text-sm leading-relaxed text-notely-text-secondary">
                AI suggestions will appear here based on your content...
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateThreadWidget;
