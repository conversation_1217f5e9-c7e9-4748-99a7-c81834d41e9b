import React from 'react';

interface ToggleProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  className?: string;
}

export const Toggle: React.FC<ToggleProps> = ({ enabled, onChange, className = '' }) => {
  return (
    <div className={`relative inline-block ${className}`}>
      <button
        type="button"
        className={`
          relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border border-notely-border/10 dark:border-notely-border-dark/20 
          transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-notely-lavender/30 focus:ring-offset-2
          ${enabled ? 'bg-notely-lavender shadow-notely-md dark:shadow-notely-lg' : 'bg-notely-surface shadow-notely-sm dark:bg-notely-dark-hover'}
        `}
        role="switch"
        aria-checked={enabled}
        onClick={() => onChange(!enabled)}
      >
        <span 
          className={`
            pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white dark:bg-notely-surface shadow-notely-sm dark:shadow-notely-md ring-0
            transition-all duration-200 ease-in-out
            ${enabled ? 'translate-x-5' : 'translate-x-0'}
          `}
        />
      </button>
    </div>
  );
};

export default Toggle;
