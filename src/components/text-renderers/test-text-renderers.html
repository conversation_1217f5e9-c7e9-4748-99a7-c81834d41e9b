<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Renderers Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .platform-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .text-sm { font-size: 14px; }
        .text-gray-700 { color: #374151; }
        .text-gray-500 { color: #6b7280; }
        .text-blue-500 { color: #3b82f6; }
        .leading-relaxed { line-height: 1.625; }
        .italic { font-style: italic; }
        .font-medium { font-weight: 500; }
        .ml-1 { margin-left: 0.25rem; }
        .whitespace-pre-wrap { white-space: pre-wrap; }
        button {
            background: none;
            border: none;
            cursor: pointer;
            text-decoration: underline;
        }
        button:hover {
            text-decoration: none;
        }
        button:focus {
            outline: 1px solid #3b82f6;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>Text Renderers Test</h1>
    <p>This page tests the platform-specific text renderers with different text lengths.</p>
    
    <div id="root"></div>

    <script type="text/babel">
        // Mock BaseText component
        const BaseText = ({ text, className = '', maxLength = 250, showMoreText = 'More', showLessText = 'Less' }) => {
            const [isExpanded, setIsExpanded] = React.useState(false);
            
            if (!text || text.trim() === '') {
                return React.createElement('div', { className: `text-gray-500 italic ${className}` }, 'No text content');
            }

            const needsTruncation = text.length > maxLength;
            const displayText = needsTruncation && !isExpanded 
                ? text.substring(0, maxLength).trim() 
                : text;

            const toggleExpansion = () => {
                setIsExpanded(!isExpanded);
            };

            return React.createElement('div', { className: `text-sm text-gray-700 leading-relaxed ${className}` },
                React.createElement('span', { className: 'whitespace-pre-wrap' }, displayText),
                needsTruncation && !isExpanded && [
                    React.createElement('span', { key: 'ellipsis' }, '...'),
                    React.createElement('button', {
                        key: 'more',
                        onClick: toggleExpansion,
                        className: 'text-blue-500 font-medium hover:underline ml-1 focus:outline-none focus:ring-1 focus:ring-blue-500 rounded',
                        'aria-label': `Show ${showMoreText.toLowerCase()}`
                    }, showMoreText)
                ],
                needsTruncation && isExpanded && React.createElement('button', {
                    onClick: toggleExpansion,
                    className: 'text-blue-500 font-medium hover:underline ml-1 focus:outline-none focus:ring-1 focus:ring-blue-500 rounded',
                    'aria-label': `Show ${showLessText.toLowerCase()}`
                }, showLessText)
            );
        };

        // Platform-specific text components
        const TwitterText = ({ text, className = '' }) => {
            return React.createElement(BaseText, {
                text,
                className,
                maxLength: 250,
                showMoreText: 'Show more',
                showLessText: 'Show less'
            });
        };

        const RedditText = ({ text, className = '' }) => {
            return React.createElement(BaseText, {
                text,
                className,
                maxLength: 250,
                showMoreText: 'Read more',
                showLessText: 'Read less'
            });
        };

        const InstagramText = ({ text, className = '' }) => {
            return React.createElement(BaseText, {
                text,
                className,
                maxLength: 250,
                showMoreText: 'more',
                showLessText: 'less'
            });
        };

        const PinterestText = ({ text, className = '' }) => {
            return React.createElement(BaseText, {
                text,
                className,
                maxLength: 250,
                showMoreText: 'See more',
                showLessText: 'See less'
            });
        };

        const LinkedInText = ({ text, className = '' }) => {
            return React.createElement(BaseText, {
                text,
                className,
                maxLength: 250,
                showMoreText: 'See more',
                showLessText: 'See less'
            });
        };

        // Test data
        const shortText = "This is a short text that doesn't need truncation.";
        const longText = "This is a very long text that should be truncated after 250 characters. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.";

        // Test component
        const TestApp = () => {
            return React.createElement('div', null,
                React.createElement('div', { className: 'test-container' },
                    React.createElement('div', { className: 'platform-title' }, 'Twitter/X - Short Text'),
                    React.createElement(TwitterText, { text: shortText })
                ),
                React.createElement('div', { className: 'test-container' },
                    React.createElement('div', { className: 'platform-title' }, 'Twitter/X - Long Text'),
                    React.createElement(TwitterText, { text: longText })
                ),
                React.createElement('div', { className: 'test-container' },
                    React.createElement('div', { className: 'platform-title' }, 'Reddit - Long Text'),
                    React.createElement(RedditText, { text: longText })
                ),
                React.createElement('div', { className: 'test-container' },
                    React.createElement('div', { className: 'platform-title' }, 'Instagram - Long Text'),
                    React.createElement(InstagramText, { text: longText })
                ),
                React.createElement('div', { className: 'test-container' },
                    React.createElement('div', { className: 'platform-title' }, 'Pinterest - Long Text'),
                    React.createElement(PinterestText, { text: longText })
                ),
                React.createElement('div', { className: 'test-container' },
                    React.createElement('div', { className: 'platform-title' }, 'LinkedIn - Long Text'),
                    React.createElement(LinkedInText, { text: longText })
                ),
                React.createElement('div', { className: 'test-container' },
                    React.createElement('div', { className: 'platform-title' }, 'Empty Text Test'),
                    React.createElement(TwitterText, { text: '' })
                )
            );
        };

        // Render the test app
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(React.createElement(TestApp));
    </script>
</body>
</html>
