import React from 'react';
import { BaseText } from './BaseText';

interface TwitterTextProps {
  text: string;
  className?: string;
  hasImages?: boolean;
}

/**
 * Twitter/X-specific text renderer
 * Uses standard truncation with Twitter-style "Show more" behavior
 */
export const TwitterText: React.FC<TwitterTextProps> = ({ text, className = '', hasImages = false }) => {
  return (
    <BaseText 
      text={text}
      className={className}
      maxLength={250}
      showMoreText="Show more"
      showLessText="Show less"
      hasImages={hasImages}
    />
  );
};
