# Text Renderers

This directory contains platform-specific text rendering components that provide consistent truncation and "More" functionality across different social media platforms.

## Architecture

The text rendering system is organized as follows:

```
text-renderers/
  BaseText.tsx           # Base component with common truncation logic
  TwitterText.tsx        # Twitter/X-specific text renderer
  RedditText.tsx         # Reddit-specific text renderer
  InstagramText.tsx      # Instagram-specific text renderer
  PinterestText.tsx      # Pinterest-specific text renderer
  LinkedInText.tsx       # LinkedIn-specific text renderer
  README.md              # This documentation
  test-text-renderers.html # Manual test page
```

## Components

### BaseText
The foundation component that provides:
- Consistent 250-character truncation
- Working "More"/"Less" toggle functionality
- Proper accessibility attributes
- Responsive styling

### Platform-Specific Renderers
Each platform has its own renderer that extends `BaseText` with platform-appropriate:
- "More" button text (e.g., "Show more" for Twitter, "Read more" for Reddit)
- Any future platform-specific styling or behavior

## Usage

### In PostViewerFullScreen.tsx
```tsx
import { PostContentRenderer } from './PostContentRenderer';

<PostContentRenderer 
  source={post.platform} 
  text={post.text || post.content || ''} 
  className="optional-custom-classes"
/>
```

### Direct Usage (if needed)
```tsx
import { TwitterText } from './text-renderers/TwitterText';

<TwitterText text="Your text content here" className="custom-classes" />
```

## Features

✅ **Consistent Truncation**: All platforms use 250-character limit
✅ **Working "More" Functionality**: Proper expand/collapse behavior
✅ **Platform-Specific Labels**: Each platform uses appropriate button text
✅ **Accessibility**: Proper ARIA labels and focus management
✅ **Responsive**: Works well on all screen sizes
✅ **Fallback Handling**: Graceful handling of empty or missing text

## Platform Button Text

| Platform  | More Button | Less Button |
|-----------|-------------|-------------|
| Twitter/X | "Show more" | "Show less" |
| Reddit    | "Read more" | "Read less" |
| Instagram | "more"      | "less"      |
| Pinterest | "See more"  | "See less"  |
| LinkedIn  | "See more"  | "See less"  |

## Testing

Open `test-text-renderers.html` in a browser to manually test all platform renderers with different text lengths.

## Future Enhancements

Potential improvements that could be added:
- Platform-specific text formatting (hashtags, mentions, etc.)
- Different truncation lengths per platform
- Rich text support (links, formatting)
- Animation transitions for expand/collapse
- Custom truncation strategies (word boundaries, sentence boundaries)

## Migration Notes

This refactoring replaced the previous inconsistent text handling in `PostViewerFullScreen.tsx`:
- **Before**: Used `line-clamp-3` CSS with no "More" functionality
- **After**: Consistent 250-character truncation with working "More" buttons
- **Benefit**: All platforms now have the same reliable text display behavior
