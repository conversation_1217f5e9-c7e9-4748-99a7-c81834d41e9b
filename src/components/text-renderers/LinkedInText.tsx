import React from 'react';
import { BaseText } from './BaseText';

interface LinkedInTextProps {
  text: string;
  className?: string;
  hasImages?: boolean;
}

/**
 * LinkedIn-specific text renderer
 * Uses standard truncation with LinkedIn-style "See more" behavior
 */
export const LinkedInText: React.FC<LinkedInTextProps> = ({ text, className = '', hasImages = false }) => {
  return (
    <BaseText 
      text={text}
      className={className}
      maxLength={250}
      showMoreText="See more"
      showLessText="See less"
      hasImages={hasImages}
    />
  );
};
