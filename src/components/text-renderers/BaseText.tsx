import React, { useState } from 'react';

interface BaseTextProps {
  text: string;
  className?: string;
  maxLength?: number;
  showMoreText?: string;
  showLessText?: string;
  hasImages?: boolean; // New prop to determine if post has images
}

/**
 * Base text component with consistent truncation and "More" functionality
 * All platform-specific text renderers extend this behavior
 */
export const BaseText: React.FC<BaseTextProps> = ({
  text,
  className = '',
  maxLength = 250,
  showMoreText = 'More',
  showLessText = 'Less',
  hasImages = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (!text || text.trim() === '') {
    return <div className={`text-notely-text-secondary italic ${className}`}>No text content</div>;
  }

  const needsTruncation = text.length > maxLength;
  const shouldShowTruncation = needsTruncation && hasImages; // Only show truncation if post has images
  const displayText = shouldShowTruncation && !isExpanded 
    ? text.substring(0, maxLength).trim() 
    : text;

  const toggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={`text-sm text-notely-text-primary leading-relaxed ${className}`}>
      <span className="whitespace-pre-wrap">{displayText}</span>
      {shouldShowTruncation && !isExpanded && (
        <>
          <span>...</span>
          <button
            onClick={toggleExpansion}
            className="text-notely-accent font-medium hover:underline ml-1 focus:outline-none focus:ring-1 focus:ring-notely-accent rounded"
            aria-label={`Show ${showMoreText.toLowerCase()}`}
          >
            {showMoreText}
          </button>
        </>
      )}
      {shouldShowTruncation && isExpanded && (
        <button
          onClick={toggleExpansion}
          className="text-notely-accent font-medium hover:underline ml-1 focus:outline-none focus:ring-1 focus:ring-notely-accent rounded"
          aria-label={`Show ${showLessText.toLowerCase()}`}
        >
          {showLessText}
        </button>
      )}
    </div>
  );
};
