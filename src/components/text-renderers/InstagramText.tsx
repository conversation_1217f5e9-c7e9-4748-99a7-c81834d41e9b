import React from 'react';
import { BaseText } from './BaseText';

interface InstagramTextProps {
  text: string;
  className?: string;
  hasImages?: boolean;
}

/**
 * Instagram-specific text renderer
 * Uses standard truncation with Instagram-style "more" behavior
 */
export const InstagramText: React.FC<InstagramTextProps> = ({ text, className = '', hasImages = false }) => {
  return (
    <BaseText 
      text={text}
      className={className}
      maxLength={250}
      showMoreText="more"
      showLessText="less"
      hasImages={hasImages}
    />
  );
};
