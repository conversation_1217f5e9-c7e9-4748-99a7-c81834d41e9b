import React, { useState, useEffect } from 'react';
import { getTodaysWisdom, addSampleQuote, clearWisdomStorage } from '../services/wisdomService';
import { getSavedPosts } from '../storage';
import { formatForDisplay } from '../utils/formatUtils';
import type { WisdomQuote } from '../types/wisdom';
import type { Post } from '../types';

interface DailyWisdomProps {
  className?: string;
  onQuoteClick?: (quote: WisdomQuote) => void;
  onOpenPost?: (post: Post) => void;
}

const DailyWisdom: React.FC<DailyWisdomProps> = ({ className = '', onQuoteClick, onOpenPost }) => {
  const [quote, setQuote] = useState<WisdomQuote | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Debug: Check if onOpenPost is passed
  console.log('DailyWisdom: Component initialized with onOpenPost:', !!onOpenPost);

  useEffect(() => {
    console.log('DailyWisdom: useEffect - onOpenPost available:', !!onOpenPost);
  }, [onOpenPost]);

  useEffect(() => {
    const loadTodaysWisdom = async () => {
      try {
        setIsLoading(true);
        const todaysQuote = await getTodaysWisdom();
        setQuote(todaysQuote);
      } catch (err) {
        console.error('DailyWisdom: Failed to load daily wisdom:', err);
        setError('Failed to load daily wisdom. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    loadTodaysWisdom();
  }, []);

  const handleClick = async () => {
    console.log('DailyWisdom: Click detected on wisdom widget');
    console.log('DailyWisdom: Full quote data:', quote);
    console.log('DailyWisdom: Quote extractedFrom:', quote?.extractedFrom);
    console.log('DailyWisdom: Quote relatedPostIds:', quote?.relatedPostIds);
    console.log('DailyWisdom: Quote source_link:', quote?.source_link);
    console.log('DailyWisdom: onOpenPost function available:', !!onOpenPost);

    if (!quote) return;

    // If wisdom is from a post and we have onOpenPost, try to open the original post
    if (quote.extractedFrom === 'post' && onOpenPost) {
      try {
        const savedPosts = await getSavedPosts();
        
        // Try to find the post using all available IDs and links
        let originalPost = null;

        // First try to find by relatedPostIds
        if (quote.relatedPostIds && quote.relatedPostIds.length > 0) {
          for (const postId of quote.relatedPostIds) {
            if (!postId) continue;
            
            // Try to find by exact ID match first
            originalPost = savedPosts.find(post => 
              post.id === postId || 
              post.originalPostId === postId
            );
            
            if (originalPost) break;
            
            // If not found, try to find by ID in permalink or source_link
            originalPost = savedPosts.find(post =>
              post.permalink?.includes(postId) ||
              post.source_link?.includes(postId)
            );
            
            if (originalPost) break;
          }
        }

        // If still not found and we have a source_link, try to match by that
        if (!originalPost && quote.source_link) {
          originalPost = savedPosts.find(post =>
            post.permalink === quote.source_link ||
            post.source_link === quote.source_link ||
            post.permalink?.includes(quote.source_link) ||
            post.source_link?.includes(quote.source_link)
          );
        }

        if (originalPost) {
          console.log('DailyWisdom: Found original post:', {
            id: originalPost.id,
            originalPostId: originalPost.originalPostId,
            permalink: originalPost.permalink,
            platform: originalPost.platform,
            source_link: originalPost.source_link
          });
          onOpenPost(originalPost);
          return;
        } else {
          console.log('DailyWisdom: Could not find original post. Available posts:', savedPosts.map(p => ({
            id: p.id,
            originalPostId: p.originalPostId,
            permalink: p.permalink,
            platform: p.platform,
            source_link: p.source_link
          })));
        }
      } catch (error) {
        console.error('Failed to find original post:', error);
      }
    }

    // Fallback to quote click handler
    if (onQuoteClick) {
      onQuoteClick(quote);
    }
  };

  if (isLoading) {
    return (
      <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-xl shadow-notely-md notely-breathing-lg ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-notely-surface rounded w-3/4"></div>
          <div className="h-4 bg-notely-surface rounded w-1/2"></div>
          <div className="h-4 bg-notely-surface rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-xl shadow-notely-md notely-breathing-lg text-red-500 ${className}`}>
        {error}
      </div>
    );
  }

  if (!quote) {
    return (
      <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-xl shadow-notely-md notely-breathing-lg ${className}`}>
        <div className="text-center space-y-3">
          <p className="text-sm text-notely-text-muted leading-relaxed mb-4">No wisdom quotes available.</p>
          <button
            onClick={async () => {
              try {
                const newQuote = await addSampleQuote();
                setQuote(newQuote);
              } catch (error) {
                console.error('Failed to add sample quote:', error);
                setError('Failed to add sample quote. Please try again.');
              }
            }}
            className="notely-btn-primary text-sm px-4 py-2 notely-filter-transition hover:scale-105 mr-2"
          >
            Add Sample Quote
          </button>
          <button
            onClick={async () => {
              try {
                setIsLoading(true);
                await clearWisdomStorage();
                const newQuote = await getTodaysWisdom();
                setQuote(newQuote);
              } catch (error) {
                console.error('Failed to regenerate wisdom:', error);
                setError('Failed to regenerate wisdom. Please try again.');
              } finally {
                setIsLoading(false);
              }
            }}
            className="notely-btn-primary text-sm px-4 py-2 notely-filter-transition hover:scale-105"
          >
            Regenerate Wisdom
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`notely-card bg-notely-card border border-notely-border rounded-notely-xl shadow-notely-md hover:shadow-notely-lg hover:-translate-y-2 group break-inside-avoid mb-6 overflow-hidden cursor-pointer notely-filter-transition ${className}`}
      onClick={handleClick}
      aria-label="Daily wisdom quote"
    >
      <div className="px-6 py-4">
        <div className="flex flex-col h-full">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-4">
              <div className="uppercase text-xs text-notely-lavender tracking-wide font-semibold">
                Daily Wisdom
              </div>
              <button
                onClick={async (e) => {
                  e.stopPropagation();
                  try {
                    setIsLoading(true);
                    await clearWisdomStorage();
                    const newQuote = await getTodaysWisdom();
                    setQuote(newQuote);
                  } catch (error) {
                    console.error('Failed to regenerate wisdom:', error);
                    setError('Failed to regenerate wisdom. Please try again.');
                  } finally {
                    setIsLoading(false);
                  }
                }}
                className="text-xs text-notely-text-muted hover:text-notely-lavender transition-colors"
                title="Regenerate wisdom"
              >
                🔄
              </button>
            </div>
            <blockquote className="notely-quote text-notely-text-primary text-lg font-medium leading-relaxed mb-6">
              "{quote.text}"
            </blockquote>
            {quote.author && (
              <div className="text-right text-sm text-notely-text-muted leading-relaxed">
                — {quote.author}
              </div>
            )}
          </div>
          <div className="mt-6 pt-4 border-t border-notely-border">
            <div className="flex flex-wrap gap-2">
              {quote.tags?.slice(0, 3).map((tag: string) => (
                <span
                  key={tag}
                  className="inline-flex items-center text-[11px] font-medium rounded-md px-2 py-1 bg-notely-lavender/20 text-notely-lavender border border-notely-lavender/30"
                >
                  {formatForDisplay(tag)}
                </span>
              ))}
              {quote.extractedFrom === 'post' && (
                <span className="inline-flex items-center text-[11px] font-medium rounded-md px-2 py-1 bg-blue-500/20 text-blue-400 border border-blue-500/30">
                  From Post
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Export as both default and named export for flexibility
export { DailyWisdom };
export default DailyWisdom;
