import React from 'react';

interface WidgetProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  span?: 1 | 2; // Column span
  onRemove?: () => void;
  isDragging?: boolean;
  onDragStart?: (e: React.DragEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
  onDragOver?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent, id: string) => void;
}

const Widget: React.FC<WidgetProps> = ({
  title,
  children,
  className = '',
  span = 1,
  onRemove,
  isDragging = false,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop
}) => {
  const spanClass = span === 2 ? 'col-span-2' : 'col-span-1';

  return (
    <div
      className={`
        notely-card bg-notely-card rounded-notely-lg shadow-notely-md border border-notely-border px-6 py-4
        ${spanClass}
        ${isDragging ? 'opacity-50 scale-95' : 'hover:shadow-notely-lg'}
        notely-filter-transition
        ${className}
      `}
    >
      {title && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold leading-tight text-notely-text-primary">{title}</h3>
          <div className="flex items-center space-x-2">
            <div
              className="text-notely-text-tertiary hover:text-notely-text-secondary notely-filter-transition cursor-grab active:cursor-grabbing p-1"
              title="Drag to reorder"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </div>
            {onRemove && (
              <button
                onClick={onRemove}
                className="text-notely-text-tertiary hover:text-red-500 notely-filter-transition notely-quick-action"
                title="Remove widget"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
};

export default Widget;
