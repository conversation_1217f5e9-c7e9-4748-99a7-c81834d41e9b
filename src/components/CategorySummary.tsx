import React, { useState, useEffect } from 'react';
import { Post } from '../types';
import { formatForDisplay } from '../utils/formatUtils';

interface CategorySummaryProps {
  category: string;
  posts: Post[];
  className?: string;
}

const CategorySummary: React.FC<CategorySummaryProps> = ({
  category,
  posts,
  className = ''
}) => {
  const [summary, setSummary] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (posts.length === 0) {
      setSummary('');
      return;
    }

    const generateSummary = async () => {
      setIsLoading(true);
      try {
        // Simple summary generation based on posts
        const postTexts = posts.map(p => p.content || '').filter(Boolean);
        const totalPosts = posts.length;
        const platforms = [...new Set(posts.map(p => p.platform))];
        
        // Create a simple summary without AI for now
        let summaryText = `📊 **${formatForDisplay(category)}** category contains ${totalPosts} post${totalPosts > 1 ? 's' : ''} from ${platforms.join(', ')}.`;
        
        if (postTexts.length > 0) {
          const avgLength = Math.round(postTexts.reduce((sum, text) => sum + text.length, 0) / postTexts.length);
          summaryText += `\n\n📝 Average post length: ${avgLength} characters.`;
        }

        // Add platform breakdown
        const platformCounts = platforms.map(platform => {
          const count = posts.filter(p => p.platform === platform).length;
          return `${platform}: ${count}`;
        });
        summaryText += `\n\n🔗 Platform breakdown: ${platformCounts.join(', ')}.`;

        setSummary(summaryText);
      } catch (error) {
        console.error('Error generating category summary:', error);
        setSummary(`📊 **${formatForDisplay(category)}** category contains ${posts.length} post${posts.length > 1 ? 's' : ''}.`);
      } finally {
        setIsLoading(false);
      }
    };

    generateSummary();
  }, [category, posts]);

  if (posts.length === 0) {
    return null;
  }

  return (
    <div className={`notely-card bg-notely-card rounded-notely-xl border border-notely-border/10 dark:border-notely-border-dark/20 overflow-hidden shadow-notely-md hover:shadow-notely-lg notely-filter-transition ${className}`}>
      {/* Header */}
      <div className="px-4 py-3 border-b border-notely-border/10 dark:border-notely-border-dark/20">
        <h3 className="text-sm font-semibold text-notely-text-primary flex items-center">
          <div className="w-6 h-6 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 border border-notely-border/5 dark:border-purple-500/30 shadow-notely-xs flex items-center justify-center flex-shrink-0 mr-2">
            <span className="text-white text-xs">🏷️</span>
          </div>
          {formatForDisplay(category)}
        </h3>
      </div>

      {/* Content */}
      <div className="px-4 py-3">
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div>
            <span className="text-xs text-notely-text-muted">Analyzing posts...</span>
          </div>
        ) : (
          <div className="prose prose-sm max-w-none">
            <div className="text-notely-text-primary text-xs leading-relaxed whitespace-pre-line">
              {summary}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-4 py-3 border-t border-notely-border/10 dark:border-notely-border-dark/20">
        <div className="text-[10px] text-notely-text-muted">
          {posts.length} post{posts.length > 1 ? 's' : ''} in this category
        </div>
      </div>
    </div>
  );
};

export default CategorySummary;
