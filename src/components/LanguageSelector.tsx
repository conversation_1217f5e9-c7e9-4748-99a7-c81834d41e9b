import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { SUPPORTED_LOCALES, SupportedLocale } from '../utils/translation';

interface LanguageSelectorProps {
  className?: string;
  showLabel?: boolean;
}

const LANGUAGE_NAMES: Record<SupportedLocale, string> = {
  en: 'English',
  tr: 'Türkçe',
  fr: 'Français',
  de: 'Deutsch',
  es: 'Español'
};

const LANGUAGE_FLAGS: Record<SupportedLocale, string> = {
  en: '🇺🇸',
  tr: '🇹🇷',
  fr: '🇫🇷',
  de: '🇩🇪',
  es: '🇪🇸'
};

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ 
  className = '', 
  showLabel = true 
}) => {
  const { t, locale, changeLocale } = useTranslation();

  const handleLanguageChange = async (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLocale = event.target.value as SupportedLocale;
    try {
      await changeLocale(newLocale);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showLabel && (
        <label htmlFor="language-select" className="text-sm font-medium text-gray-700">
          {t('settings.language')}:
        </label>
      )}
      <div className="relative">
        <select
          id="language-select"
          value={locale}
          onChange={handleLanguageChange}
          className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {SUPPORTED_LOCALES.map((lang) => (
            <option key={lang} value={lang}>
              {LANGUAGE_FLAGS[lang]} {LANGUAGE_NAMES[lang]}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default LanguageSelector;
