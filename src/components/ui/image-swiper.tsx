import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel"
import "../../styles/carousel-override.css"

interface ImageSwiperProps {
  images: string[]
  className?: string
  imageClassName?: string
  showControls?: boolean
  showDots?: boolean
  aspectRatio?: "square" | "video" | "auto"
  onImageClick?: (index: number) => void
}

const ImageSwiper = React.forwardRef<HTMLDivElement, ImageSwiperProps>(
  ({
    images,
    className,
    imageClassName,
    showControls = true,
    showDots = true,
    aspectRatio = "auto",
    onImageClick,
    ...props
  }, ref) => {
    const [currentIndex, setCurrentIndex] = React.useState(0)
    const [api, setApi] = React.useState<CarouselApi>()

    React.useEffect(() => {
      if (!api) {
        return
      }

      api.on("select", () => {
        setCurrentIndex(api.selectedScrollSnap())
      })
    }, [api])

    if (!images || images.length === 0) {
      return null
    }

    // If only one image, render without carousel
    if (images.length === 1) {
      return (
        <div
          ref={ref}
          className={cn(
            "relative overflow-hidden rounded-xl w-full flex items-center justify-center", 
            aspectRatio === "auto" ? "h-full" : "",
            className
          )}
          style={{ 
            aspectRatio: aspectRatio === "square" ? "1 / 1" : aspectRatio === "video" ? "16 / 9" : undefined,
            minHeight: aspectRatio === "square" ? "200px" : aspectRatio === "video" ? "150px" : undefined,
            height: aspectRatio === "auto" ? '100%' : undefined,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          {...props}
        >
          <img
            src={images[0]}
            alt="Post image"
            className={cn(
              aspectRatio === "auto" 
                ? "max-h-full max-w-full object-contain cursor-pointer"
                : "w-full h-full object-cover cursor-pointer",
              imageClassName
            )}
            style={{ 
              objectPosition: 'center center', 
              objectFit: aspectRatio === "auto" ? 'contain' : 'cover' 
            }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onImageClick?.(0);
            }}
          />
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn(
          "relative w-full flex items-center justify-center group image-swiper-container", 
          aspectRatio === "auto" ? "h-full" : "",
          className
        )}
        style={{
          aspectRatio: aspectRatio === "square" ? "1 / 1" : aspectRatio === "video" ? "16 / 9" : undefined,
          minHeight: aspectRatio === "square" ? "200px" : aspectRatio === "video" ? "150px" : undefined,
          height: aspectRatio === "auto" ? '100%' : undefined,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        {...props}
      >
        <Carousel setApi={setApi} className={cn("w-full", aspectRatio === "auto" ? "h-full" : "")}>
          <CarouselContent 
            className={cn("-ml-4", aspectRatio === "auto" ? "h-full" : "")} 
            style={{ 
              height: aspectRatio === "auto" ? '100%' : undefined,
              aspectRatio: aspectRatio === "square" ? "1 / 1" : aspectRatio === "video" ? "16 / 9" : undefined,
              minHeight: aspectRatio === "square" ? "200px" : aspectRatio === "video" ? "150px" : undefined
            }} 
            data-carousel-content
          >
            {images.map((image, index) => (
              <CarouselItem 
                key={index} 
                className={cn("pl-4", aspectRatio === "auto" ? "h-full" : "")} 
                style={{ 
                  height: aspectRatio === "auto" ? '100%' : undefined,
                  aspectRatio: aspectRatio === "square" ? "1 / 1" : aspectRatio === "video" ? "16 / 9" : undefined,
                  minHeight: aspectRatio === "square" ? "200px" : aspectRatio === "video" ? "150px" : undefined,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                data-carousel-item
              >
                <div 
                  className="carousel-image-container w-full h-full flex items-center justify-center"
                  style={{ 
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <img
                    src={image}
                    alt={`Post image ${index + 1}`}
                    className={cn(
                      aspectRatio === "auto" 
                        ? "max-h-full max-w-full object-contain cursor-pointer"
                        : "w-full h-full object-cover cursor-pointer",
                      imageClassName
                    )}
                    style={{ 
                      objectPosition: 'center center', 
                      objectFit: aspectRatio === "auto" ? 'contain' : 'cover' 
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onImageClick?.(index);
                    }}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>

          {showControls && images.length > 1 && (
            <>
              <CarouselPrevious
                className="left-3 w-7 h-7 bg-black/60 border-0 text-white/80 hover:text-accent hover:bg-black/80 transition-all duration-200 shadow-lg backdrop-blur-sm opacity-0 group-hover:opacity-100"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent triggering parent click handlers
                  api?.scrollPrev();
                }}
              />
              <CarouselNext
                className="right-3 w-7 h-7 bg-black/60 border-0 text-white/80 hover:text-accent hover:bg-black/80 transition-all duration-200 shadow-lg backdrop-blur-sm opacity-0 group-hover:opacity-100"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent triggering parent click handlers
                  api?.scrollNext();
                }}
              />
            </>
          )}
        </Carousel>

        {showDots && images.length > 1 && (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 bg-black/40 px-3 py-2 rounded-full backdrop-blur-sm">
            {images.map((_, index) => (
              <button
                key={index}
                className={cn(
                  "w-2 h-2 rounded-full transition-all duration-200",
                  index === currentIndex
                    ? "bg-accent scale-110"
                    : "bg-white/40 hover:bg-white/60 hover:scale-105"
                )}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent triggering parent click handlers
                  api?.scrollTo(index);
                }}
              />
            ))}
          </div>
        )}

        {images.length > 1 && (
          <div className="absolute top-4 right-4 bg-black/60 text-white text-sm font-medium px-3 py-1.5 rounded-full backdrop-blur-sm shadow-lg">
            {currentIndex + 1} / {images.length}
          </div>
        )}
      </div>
    )
  }
)

ImageSwiper.displayName = "ImageSwiper"

export { ImageSwiper }
export type { ImageSwiperProps }
