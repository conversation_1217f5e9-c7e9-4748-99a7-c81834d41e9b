import React, { useEffect, useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import * as apiService from '../../services/apiService';

interface Subscription {
  plan: string;
  status: string;
  currentPeriodEnd: string;
}

const UserProfile: React.FC = () => {
  const { user, isLoading, logout } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSubscription = async () => {
      if (!user) return;
      
      setIsLoadingSubscription(true);
      setError(null);
      
      try {
        const subscriptionData = await apiService.getUserSubscription();
        setSubscription(subscriptionData);
      } catch (err) {
        console.error('Error fetching subscription:', err);
        setError('Failed to load subscription information');
      } finally {
        setIsLoadingSubscription(false);
      }
    };

    fetchSubscription();
  }, [user]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-4">
        <p className="text-center text-gray-600">Not signed in</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex items-center mb-4">
        <img 
          src={user.picture || 'https://via.placeholder.com/50'} 
          alt="Profile" 
          className="w-12 h-12 rounded-full mr-4"
        />
        <div>
          <h3 className="font-semibold">{user.name}</h3>
          <p className="text-sm text-gray-600">{user.email}</p>
        </div>
      </div>
      
      <div className="mb-4">
        <h4 className="font-semibold text-sm text-gray-700 mb-1">Subscription</h4>
        {isLoadingSubscription ? (
          <p className="text-sm text-gray-500">Loading subscription info...</p>
        ) : error ? (
          <p className="text-sm text-red-500">{error}</p>
        ) : subscription ? (
          <div className="text-sm">
            <p>
              <span className="font-medium">Plan:</span>{' '}
              <span className={`${subscription.plan === 'free' ? 'text-gray-600' : 'text-green-600 font-semibold'}`}>
                {subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1)}
              </span>
            </p>
            <p>
              <span className="font-medium">Status:</span>{' '}
              <span className={`${subscription.status === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
              </span>
            </p>
            {subscription.plan !== 'free' && (
              <p className="text-xs text-gray-500 mt-1">
                Renews on: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
              </p>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-500">No subscription information available</p>
        )}
      </div>
      
      <div className="flex justify-between">
        <button 
          onClick={logout}
          className="text-sm text-red-600 hover:text-red-800"
        >
          Sign Out
        </button>
        
        <button
          onClick={() => chrome.tabs.create({ url: 'settings.html' })}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Settings
        </button>
      </div>
    </div>
  );
};

export default UserProfile; 