import React from 'react';
import { useAuth } from '../../context/AuthContext';

const LoginButton: React.FC = () => {
  const { login, isLoading } = useAuth();

  const handleLogin = async () => {
    try {
      await login();
    } catch (error) {
      console.error('<PERSON><PERSON> failed:', error);
    }
  };

  return (
    <button 
      onClick={handleLogin} 
      disabled={isLoading}
      className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
    >
      {isLoading ? 'Logging in...' : 'Log In'}
    </button>
  );
};

export default LoginButton; 