import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { useTranslation } from '../../hooks/useTranslation';

const LogoutButton: React.FC = () => {
  const { logout, isLoading } = useAuth();
  const { t } = useTranslation();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <button 
      onClick={handleLogout} 
      disabled={isLoading}
      className="bg-red-500 hover:bg-red-600 text-white font-medium text-sm px-4 py-2 rounded-lg transition-all duration-150 ease-out transform hover:scale-105"
    >
      {isLoading ? t('auth.loggingOut') : t('auth.logout')}
    </button>
  );
};

export default LogoutButton; 