import React, { useState } from 'react';

const TextareaTest: React.FC = () => {
  const [notes, setNotes] = useState<string>('');

  return (
    <div className="p-4 border border-gray-300 rounded-lg m-4">
      <h3 className="text-lg font-semibold mb-2">Textarea Test Component</h3>
      <textarea
        id="test-textarea"
        rows={3}
        value={notes}
        onChange={(e) => {
          setNotes(e.target.value);
        }}
        onFocus={() => {}}
        onBlur={() => {}}
        placeholder="Type something here to test..."
        className="w-full p-2 border border-gray-300 rounded-md text-sm bg-white text-black focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        style={{ minHeight: '80px' }}
      />
      <div className="mt-2">
        <p className="text-sm text-gray-600">Current value: "{notes}"</p>
        <p className="text-sm text-gray-600">Length: {notes.length}</p>
      </div>
    </div>
  );
};

export default TextareaTest;
