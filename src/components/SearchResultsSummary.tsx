import React, { useState } from 'react';
import {
  SemanticSearchResponse,
  SearchResult,
  getPlatformIcon,
  formatSimilarity,
  formatSearchDate,
  truncateText,
  highlightSearchTerms
} from '../services/semanticSearchService';
import { formatForDisplay } from '../utils/formatUtils';

interface SearchResultsSummaryProps {
  searchResults: SemanticSearchResponse;
  onClearResults: () => void;
  className?: string;
}

const SearchResultsSummary: React.FC<SearchResultsSummaryProps> = ({
  searchResults,
  onClearResults,
  className = ''
}) => {
  const [showAllResults, setShowAllResults] = useState(false);
  const [savedSummary, setSavedSummary] = useState(false);

  const { results, summary, totalResults, query, searchType, stats } = searchResults;
  const displayResults = showAllResults ? results : results.slice(0, 3);

  // Get search type display info
  const getSearchTypeInfo = () => {
    switch (searchType) {
      case 'vector':
        return { label: 'Vector Search', color: 'text-notely-mint', icon: '🚀' };
      case 'hybrid':
        return { label: 'Hybrid Search', color: 'text-notely-sky', icon: '⚡' };
      case 'cosine_fallback':
        return { label: 'Similarity Search', color: 'text-notely-coral', icon: '📊' };
      case 'text_fallback':
      case 'text':
        return { label: 'Text Search', color: 'text-notely-sage', icon: '🔍' };
      default:
        return { label: 'Search', color: 'text-notely-text-secondary', icon: '🔍' };
    }
  };

  const searchTypeInfo = getSearchTypeInfo();

  const handleViewResults = () => {
    setShowAllResults(!showAllResults);
  };

  const handleSaveSummary = () => {
    // Create a summary text to save
    const summaryText = `Search: "${query}"\n\n${summary.overview}\n\nKey Findings:\n${summary.keyFindings.map(f => `• ${f}`).join('\n')}\n\nRelated Topics:\n${summary.relatedTopics.map(t => `• ${t}`).join('\n')}\n\nSuggested Actions:\n${summary.suggestedActions.map(a => `• ${a}`).join('\n')}`;
    
    // Copy to clipboard
    navigator.clipboard.writeText(summaryText).then(() => {
      setSavedSummary(true);
      setTimeout(() => setSavedSummary(false), 2000);
    }).catch(err => {
      console.error('Failed to copy summary:', err);
    });
  };

  const handlePostClick = (post: SearchResult['post']) => {
    // Open the original post in a new tab
    if (post.permalink) {
      window.open(post.permalink, '_blank');
    }
  };

  if (totalResults === 0) {
    return (
      <div className={`${className}`}>
        <div className="bg-notely-surface border border-notely-border rounded-xl p-6 text-center">
          <div className="mb-4">
            <svg className="h-12 w-12 text-notely-text-muted mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-notely-text-primary mb-2">No results found</h3>
          <p className="text-notely-text-secondary mb-4">
            No posts match your search for "<span className="font-medium">{query}</span>".
          </p>
          <button
            onClick={onClearResults}
            className="notely-btn-secondary px-4 py-2 text-sm"
          >
            Try a different search
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className} notely-filter-transition`}>
      {/* Summary Box */}
      <div className="bg-gradient-to-br from-notely-surface to-notely-card border border-notely-mint/20 rounded-xl p-6 mb-6 shadow-lg">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-notely-mint/20 rounded-lg">
              <svg className="h-5 w-5 text-notely-mint" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-notely-text-primary">
                Search Results for "{query}"
              </h3>
              <div className="flex items-center space-x-3 text-sm text-notely-text-secondary">
                <span>Found {totalResults} relevant post{totalResults !== 1 ? 's' : ''}</span>
                <span className="text-notely-border">•</span>
                <span className={`flex items-center space-x-1 ${searchTypeInfo.color}`}>
                  <span>{searchTypeInfo.icon}</span>
                  <span>{searchTypeInfo.label}</span>
                </span>
                {stats?.averageScore && (
                  <>
                    <span className="text-notely-border">•</span>
                    <span>Avg: {(stats.averageScore * 100).toFixed(0)}%</span>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <button
            onClick={onClearResults}
            className="p-2 hover:bg-notely-surface rounded-lg transition-colors duration-200"
            title="Clear search"
          >
            <svg className="h-4 w-4 text-notely-text-muted" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Overview */}
        <div className="mb-4">
          <p className="text-notely-text-primary leading-relaxed">
            {summary.overview}
          </p>
        </div>

        {/* Key Findings */}
        {summary.keyFindings.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-notely-text-secondary mb-2 uppercase tracking-wide">
              Key Findings
            </h4>
            <ul className="space-y-1">
              {summary.keyFindings.map((finding, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-notely-coral mt-1">•</span>
                  <span className="text-sm text-notely-text-primary">{finding}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Related Topics */}
        {summary.relatedTopics.length > 0 && (
          <div className="mb-4">
            <h4 className="uppercase text-xs text-notely-text-muted tracking-wide font-semibold mb-2">
              Related Topics
            </h4>
            <div className="flex flex-wrap gap-2">
              {summary.relatedTopics.map((topic, index) => (
                <span
                  key={index}
                  className="text-[11px] font-medium rounded-md px-2 py-1 bg-notely-sky/20 text-notely-sky border border-notely-sky/30"
                >
                  {topic}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3 pt-4 border-t border-notely-border">
          <button
            onClick={handleViewResults}
            className="notely-btn-primary px-4 py-2 text-sm flex items-center space-x-2"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span>{showAllResults ? 'Show Less' : 'View All Results'}</span>
          </button>

          <button
            onClick={handleSaveSummary}
            className={`notely-btn-secondary px-4 py-2 text-sm flex items-center space-x-2 ${
              savedSummary ? 'bg-notely-mint/20 text-notely-mint border-notely-mint/30' : ''
            }`}
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {savedSummary ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              )}
            </svg>
            <span>{savedSummary ? 'Copied!' : 'Save Summary'}</span>
          </button>

          <button
            onClick={onClearResults}
            className="notely-btn-secondary px-4 py-2 text-sm flex items-center space-x-2"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span>Ask Again</span>
          </button>
        </div>
      </div>

      {/* Search Results */}
      {showAllResults && (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-notely-text-primary mb-4">
            Detailed Results ({totalResults})
          </h4>
          
          {displayResults.map((result, index) => (
            <div
              key={result.post.id}
              className="bg-notely-surface border border-notely-border rounded-xl p-4 hover:border-notely-sky/30 transition-all duration-200 cursor-pointer"
              onClick={() => handlePostClick(result.post)}
            >
              {/* Post Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getPlatformIcon(result.post.platform)}</span>
                  <div>
                    <p className="text-sm font-medium text-notely-text-primary">
                      {result.post.authorName || 'Unknown Author'}
                    </p>
                    <p className="text-xs text-notely-text-muted">
                      {result.post.platform} • {formatSearchDate(result.post.savedAt)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-notely-text-muted">
                    {formatSimilarity(result.score || result.similarity || 0)} match
                  </span>
                  <svg className="h-4 w-4 text-notely-text-muted" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
              </div>

              {/* Post Content */}
              <div className="mb-3">
                {result.post.title && (
                  <h5 className="font-medium text-notely-text-primary mb-2">
                    {result.post.title}
                  </h5>
                )}
                {result.post.content && (
                  <p 
                    className="text-sm text-notely-text-secondary leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html: highlightSearchTerms(truncateText(result.post.content, 200), query)
                    }}
                  />
                )}
              </div>

              {/* Tags and Categories */}
              {(result.post.categories?.length || result.post.tags?.length) && (
                <div className="flex flex-wrap gap-2">
                  {result.post.categories?.map((category, idx) => (
                    <span
                      key={idx}
                      className="px-2 py-1 bg-notely-lavender/20 text-notely-lavender text-xs rounded-full border border-notely-lavender/30"
                    >
                      {formatForDisplay(category)}
                    </span>
                  ))}
                  {result.post.tags?.slice(0, 3).map((tag, idx) => (
                    <span
                      key={idx}
                      className="px-2 py-1 bg-notely-sage/20 text-notely-sage text-xs rounded-full border border-notely-sage/30"
                    >
                      #{formatForDisplay(tag)}
                    </span>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchResultsSummary;
