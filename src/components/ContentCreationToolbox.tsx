import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';
import { Post } from '../types';

interface ContentCreationToolboxProps {
  isDragging: boolean;
  onRemove: () => void;
  posts?: Post[];
}

interface Tool {
  id: string;
  name: string;
  description: string;
  icon: string;
  shortcut?: string;
  color: string;
  bgColor: string;
  hoverColor: string;
  comingSoon?: boolean;
}



const ContentCreationToolbox: React.FC<ContentCreationToolboxProps> = ({ isDragging, onRemove, posts = [] }) => {
  const { t } = useTranslation();
  const [hoveredTool, setHoveredTool] = useState<string | null>(null);

  const tools: Tool[] = [
    {
      id: 'hook-generator',
      name: 'Hook Generator',
      description: 'Create compelling opening lines that grab attention instantly',
      icon: '🎣',
      shortcut: 'H',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      hoverColor: 'hover:bg-blue-500/20'
    },
    {
      id: 'thread-composer',
      name: 'Thread Composer',
      description: 'Build engaging Twitter/X threads with perfect flow and structure',
      icon: '🧵',
      shortcut: 'T',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      hoverColor: 'hover:bg-purple-500/20'
    },
    {
      id: 'reels-caption',
      name: 'Reels Caption Writer',
      description: 'Generate catchy captions for Instagram Reels and TikTok videos',
      icon: '🎬',
      shortcut: 'R',
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/10',
      hoverColor: 'hover:bg-pink-500/20'
    },
    {
      id: 'format-converter',
      name: 'Format Converter',
      description: 'Transform content between formats: tweet → blog → video script',
      icon: '🔄',
      shortcut: 'F',
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      hoverColor: 'hover:bg-green-500/20'
    },
    {
      id: 'quote-visualizer',
      name: 'Quote Visualizer',
      description: 'Turn powerful quotes into beautiful visual graphics',
      icon: '🎨',
      shortcut: 'Q',
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
      hoverColor: 'hover:bg-orange-500/20'
    },
    {
      id: 'content-optimizer',
      name: 'Content Optimizer',
      description: 'Enhance your content for better engagement and reach',
      icon: '⚡',
      shortcut: 'O',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/10',
      hoverColor: 'hover:bg-yellow-500/20'
    }
  ];

  const handleToolClick = (tool: Tool) => {
    if (tool.comingSoon) {
      toast.info(`${tool.name} coming soon! 🚀`);
    } else {
      toast.success(`${tool.name} activated! ✨`);
    }
  };





  const handleKeyboardShortcut = (event: React.KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      const key = event.key.toLowerCase();
      const tool = tools.find(t => t.shortcut?.toLowerCase() === key);
      if (tool) {
        event.preventDefault();
        handleToolClick(tool);
      }
    }
  };

  return (
    <div 
      className="space-y-4"
      onKeyDown={handleKeyboardShortcut}
      tabIndex={0}
    >
      {/* Header with description */}
      <div className="text-center">
        <p className="text-sm text-notely-text-muted leading-relaxed mb-4">
          Your creative toolkit for content generation and optimization
        </p>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-2 gap-3">
        {tools.map((tool) => (
          <div
            key={tool.id}
            className={`
              relative group cursor-pointer rounded-xl
              notely-card bg-notely-card border border-notely-border shadow-notely-md
              transition-all duration-300 ease-out
              hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1
              ${hoveredTool === tool.id ? 'scale-[1.02] shadow-notely-lg -translate-y-1' : ''}
              focus:outline-none focus:ring-2 focus:ring-notely-accent/50
            `}
            onClick={() => handleToolClick(tool)}
            onMouseEnter={() => setHoveredTool(tool.id)}
            onMouseLeave={() => setHoveredTool(null)}
            tabIndex={0}
            role="button"
            aria-label={`${tool.name}: ${tool.description}`}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleToolClick(tool);
              }
            }}
          >


            {/* Keyboard Shortcut */}
            {tool.shortcut && (
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <span className="bg-notely-surface text-notely-text-tertiary text-xs px-1.5 py-0.5 rounded border border-notely-border font-mono">
                  ⌘{tool.shortcut}
                </span>
              </div>
            )}

            <div className="px-6 py-4">
              {/* Icon */}
              <div className="flex items-center justify-center mb-3">
                <div className={`
                  w-12 h-12 rounded-notely-lg flex items-center justify-center
                  bg-notely-surface border border-notely-border
                  group-hover:scale-110 transition-transform duration-200
                `}>
                  <span className="text-2xl">{tool.icon}</span>
                </div>
              </div>

              {/* Content */}
              <div className="text-center space-y-3">
                <h4 className="text-xl font-semibold leading-tight text-notely-text-primary">
                  {tool.name}
                </h4>
                <p className="text-sm text-notely-text-muted leading-relaxed">
                  {tool.description}
                </p>
              </div>

              {/* Hover Effect Overlay */}
              <div className={`
                absolute inset-0 rounded-notely-lg opacity-0 group-hover:opacity-100
                transition-opacity duration-300 pointer-events-none
                bg-gradient-to-br from-transparent via-transparent to-notely-accent/5
              `} />

              {/* Active State Indicator */}
              {hoveredTool === tool.id && (
                <div className="absolute bottom-2 left-2 right-2">
                  <div className="h-0.5 bg-gradient-to-r from-transparent via-indigo-500 to-transparent rounded-full opacity-60" />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Footer Tips */}
      <div className="mt-6 px-6 py-4 notely-card bg-notely-card border border-notely-border rounded-xl shadow-notely-sm">
        <div className="flex items-center space-x-2 text-sm text-notely-text-secondary leading-relaxed">
          <span className="w-4 h-4 bg-notely-surface rounded flex items-center justify-center">
            💡
          </span>
          <span>
            Use <kbd className="px-1 py-0.5 bg-notely-surface border border-notely-border rounded font-mono text-notely-text-primary">⌘ + Key</kbd> for quick access
          </span>
        </div>
      </div>

    </div>
  );
};

export default ContentCreationToolbox;
