import React, { useState, useEffect } from 'react';
import { getImage } from '../utils/imageUtils';

interface ProxyImageProps {
  src: string;
  alt?: string;
  className?: string;
  width?: number | string;
  height?: number | string;
  onLoad?: () => void;
  onError?: (error: Error) => void;
  fallbackSrc?: string;
  postId?: string;
}

const ProxyImage: React.FC<ProxyImageProps> = ({
  src,
  alt = '',
  className = '',
  width,
  height,
  onLoad,
  onError,
  fallbackSrc,
  postId = 'unknown'
}) => {
  const [imageSrc, setImageSrc] = useState<string | undefined>(undefined);
  const [error, setError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  useEffect(() => {
    let isMounted = true;

    const loadImage = async () => {
      try {
        // If src is already a data URL, use it directly
        if (src && src.startsWith('data:')) {
          setImageSrc(src);
          onLoad?.();
          return;
        }

        // Use our unified image retrieval function
        const imageData = await getImage(postId, src);

        if (!isMounted) return;

        if (imageData) {
          setImageSrc(imageData);
          onLoad?.();
        } else if (fallbackSrc) {
          // Try the fallback source if provided
          setImageSrc(fallbackSrc);
        } else {
          throw new Error('Failed to load image from all sources');
        }
      } catch (err) {
        if (!isMounted) return;

        const errorObj = err instanceof Error ? err : new Error('Unknown error loading image');
        setError(true);
        setErrorMessage(errorObj.message);
        onError?.(errorObj);

        // Try fallback if available
        if (fallbackSrc) {
          setImageSrc(fallbackSrc);
        }
      }
    };

    loadImage();

    return () => {
      isMounted = false;
    };
  }, [src, postId, fallbackSrc, onLoad, onError]);

  if (error && !imageSrc) {
    return (
      <div
        className={`bg-gray-100 rounded flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <span className="text-sm text-gray-500">{errorMessage || 'Failed to load image'}</span>
      </div>
    );
  }

  // Show the image
  return (
    <img
      src={imageSrc}
      alt={alt}
      className={`proxy-image ${className}`}
      style={{ width, height }}
      onLoad={() => onLoad?.()}
      onError={(e) => {
        console.error('[ProxyImage] Image failed to load after setting src:', src);
        setError(true);

        // Try fallback if available and not already using it
        if (fallbackSrc && imageSrc !== fallbackSrc) {
          setImageSrc(fallbackSrc);
        } else {
          // Hide the broken image
          e.currentTarget.style.display = 'none';
        }
      }}
    />
  );
};

export default ProxyImage;
