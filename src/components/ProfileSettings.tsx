import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from '../hooks/useTranslation';
import * as authService from '../services/authService';

const ProfileSettings: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { t } = useTranslation();
  const [displayName, setDisplayName] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user) {
      setDisplayName(user.displayName || user.name || '');
    }
  }, [user]);

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError('');
      
      const response = await fetch('https://api.notely.social/auth/update-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await authService.getToken()}`
        },
        body: JSON.stringify({ displayName })
      });

      if (!response.ok) {
        throw new Error(t('error.generic'));
      }

      const updatedUser = await response.json();
      
      // Update local state first
      setDisplayName(updatedUser.displayName || updatedUser.name || '');
      
      // Then update the global auth context with the full user object
      updateUser(updatedUser);
      
      setIsEditing(false);
    } catch (err) {
      console.error('Error updating display name:', err);
      setError(t('error.generic'));
    } finally {
      setIsSaving(false);
    }
  };

  if (!user) {
    return (
      <div className="notely-card rounded-lg p-6 shadow-sm" style={{ backgroundColor: 'var(--notely-card)' }}>
        <p className="text-center" style={{ color: 'var(--notely-text-secondary)' }}>
          {t('auth.loginRequired')}
        </p>
      </div>
    );
  }

  return (
    <div className="notely-card rounded-lg p-6 shadow-sm" style={{ backgroundColor: 'var(--notely-card)' }}>
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--notely-text-primary)' }}>
        {t('settings.profile')}
      </h3>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4">
          <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-4">
        {/* Email */}
        <div>
          <label className="block text-sm font-medium mb-1" style={{ color: 'var(--notely-text-secondary)' }}>
            {t('auth.email')}
          </label>
          <input
            type="email"
            value={user.email}
            disabled
            className="w-full px-3 py-2 rounded-md"
            style={{
              backgroundColor: 'var(--notely-surface)',
              border: '1px solid var(--notely-border)',
              color: 'var(--notely-text-primary)'
            }}
          />
        </div>

        {/* Display Name */}
        <div>
          <label className="block text-sm font-medium mb-1" style={{ color: 'var(--notely-text-secondary)' }}>
            {t('settings.displayName')}
          </label>
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              disabled={!isEditing}
              className="flex-1 px-3 py-2 rounded-md"
              style={{
                backgroundColor: 'var(--notely-surface)',
                border: '1px solid var(--notely-border)',
                color: 'var(--notely-text-primary)'
              }}
            />
            {isEditing ? (
              <div className="flex gap-2">
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {isSaving ? t('common.saving') : t('common.save')}
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setDisplayName(user.displayName || user.name || '');
                  }}
                  disabled={isSaving}
                  className="px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
                  style={{ color: 'var(--notely-text-primary)' }}
                >
                  {t('common.cancel')}
                </button>
              </div>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                {t('common.edit')}
              </button>
            )}
          </div>
        </div>

        {/* Plan */}
        <div>
          <label className="block text-sm font-medium mb-1" style={{ color: 'var(--notely-text-secondary)' }}>
            {t('settings.plan')}
          </label>
          <div className="flex items-center gap-3">
            <div className="px-3 py-1 rounded-full text-sm font-medium" style={{
              backgroundColor: user.plan === 'premium' ? 'var(--notely-accent-surface)' : 'var(--notely-surface)',
              color: user.plan === 'premium' ? 'var(--notely-accent-text)' : 'var(--notely-text-primary)',
              border: '1px solid var(--notely-border)'
            }}>
              {(user.plan || 'free').toUpperCase()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings; 