import React, { useState, useMemo } from 'react';
import { Post } from '../types';
import { PlatformLogo } from './PlatformLogo';
import { toast } from '../utils/toast';
import CaptureInspirationModal from './CaptureInspirationModal';

interface InspirationFeedWidgetProps {
  posts: Post[];
  className?: string;
}

const InspirationFeedWidget: React.FC<InspirationFeedWidgetProps> = ({ 
  posts, 
  className = '' 
}) => {
  const [selectedPosts, setSelectedPosts] = useState<Set<string>>(new Set());
  const [showCaptureModal, setShowCaptureModal] = useState(false);

  // Get older posts for inspiration feed (rediscover forgotten content)
  const inspirationPosts = useMemo(() => {
    if (posts.length === 0) return [];

    // Filter out very recent posts (last 7 days) to focus on older, forgotten content
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const olderPosts = posts.filter(post => {
      const postDate = new Date(post.savedAt || post.timestamp || post.createdAt || '');
      return postDate < sevenDaysAgo;
    });

    // If we have older posts, show a random selection for variety
    // If not enough older posts, fall back to showing older posts first
    if (olderPosts.length >= 10) {
      // Shuffle older posts to provide variety and serendipity
      const shuffled = [...olderPosts].sort(() => Math.random() - 0.5);
      return shuffled.slice(0, 10);
    } else if (olderPosts.length > 0) {
      // Show all older posts first, then fill with slightly newer ones
      const newerPosts = posts
        .filter(post => {
          const postDate = new Date(post.savedAt || post.timestamp || post.createdAt || '');
          return postDate >= sevenDaysAgo;
        })
        .sort((a, b) => new Date(a.savedAt || a.timestamp || a.createdAt || '').getTime() - new Date(b.savedAt || b.timestamp || b.createdAt || '').getTime()) // oldest first
        .slice(0, 10 - olderPosts.length);

      return [...olderPosts, ...newerPosts].slice(0, 10);
    } else {
      // No older posts available, show oldest posts first (better than newest for inspiration)
      return posts
        .sort((a, b) => new Date(a.savedAt || a.timestamp || a.createdAt || '').getTime() - new Date(b.savedAt || b.timestamp || b.createdAt || '').getTime())
        .slice(0, 10);
    }
  }, [posts]);

  const handlePostSelect = (postId: string) => {
    setSelectedPosts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(postId)) {
        newSet.delete(postId);
      } else {
        newSet.add(postId);
      }
      return newSet;
    });
  };



  const getPostPreview = (post: Post): string => {
    const content = post.content || post.title || '';
    return content.length > 100 ? content.substring(0, 100) + '...' : content;
  };

  const getTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  const getPlatformGradient = (platform?: string): string => {
    switch (platform?.toLowerCase()) {
      case 'twitter':
      case 'x':
        return 'bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600';
      case 'instagram':
        return 'bg-gradient-to-br from-purple-400 via-pink-500 to-orange-500';
      case 'linkedin':
        return 'bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800';
      case 'youtube':
        return 'bg-gradient-to-br from-red-500 via-red-600 to-red-700';
      case 'tiktok':
        return 'bg-gradient-to-br from-black via-gray-800 to-pink-500';
      case 'facebook':
        return 'bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700';
      case 'reddit':
        return 'bg-gradient-to-br from-orange-500 via-red-500 to-red-600';
      case 'pinterest':
        return 'bg-gradient-to-br from-red-500 via-pink-500 to-red-600';
      default:
        return 'bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500';
    }
  };

  if (inspirationPosts.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="relative mb-6">
          <div className="w-20 h-20 mx-auto bg-gradient-to-br from-purple-400 via-pink-400 to-indigo-400 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-3xl">✨</span>
          </div>
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce"></div>
          <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-blue-400 rounded-full animate-pulse"></div>
        </div>

        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Your Inspiration Awaits</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed max-w-sm mx-auto">
          No older posts to rediscover yet. Save more posts and come back in a week to rediscover forgotten gems that could spark your next great idea.
        </p>

        <div className="mt-6 flex justify-center">
          <div className="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-full px-4 py-2">
            <span className="text-xs font-medium text-purple-700 dark:text-purple-300">
              💡 Tip: Posts become inspiration after 7 days
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Posts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {inspirationPosts.map((post) => (
          <div
            key={post.id}
            className={`
              relative cursor-pointer rounded-xl overflow-hidden transition-all duration-300
              bg-notely-card border border-notely-border/20 shadow-lg
              hover:scale-[1.03] hover:shadow-2xl hover:-translate-y-2
              ${selectedPosts.has(post.id || '')
                ? 'ring-2 ring-blue-500 bg-blue-500/10 shadow-2xl'
                : ''
              }
            `}
            onClick={() => handlePostSelect(post.id || '')}
          >
            {/* Hero Image Section */}
            <div className="relative h-32 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 overflow-hidden">
              {/* Post Image or Generated Visual */}
              {post.media && post.media[0]?.url ? (
                <img
                  src={post.media[0].url}
                  alt={post.media[0].alt || 'Post image'}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback to gradient background if image fails
                    e.currentTarget.style.display = 'none';
                  }}
                />
              ) : (
                /* Visual Pattern Based on Platform */
                <div className={`w-full h-full flex items-center justify-center ${getPlatformGradient(post.platform)}`}>
                  <div className="text-center">
                    <PlatformLogo
                      platform={post.platform || 'Web'}
                      className="w-8 h-8 text-white/80 mx-auto mb-1"
                    />
                    <div className="text-xs text-white/60 uppercase tracking-wider font-medium">
                      {post.platform || 'Web'}
                    </div>
                  </div>
                  {/* Decorative Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-2 left-2 w-4 h-4 rounded-full bg-white"></div>
                    <div className="absolute top-6 right-4 w-2 h-2 rounded-full bg-white"></div>
                    <div className="absolute bottom-4 left-6 w-3 h-3 rounded-full bg-white"></div>
                    <div className="absolute bottom-2 right-2 w-5 h-5 rounded-full bg-white"></div>
                  </div>
                </div>
              )}

              {/* Time Badge */}
              <div className="absolute top-2 left-2">
                <span className="px-2 py-1 bg-black/50 backdrop-blur-sm text-white text-xs rounded-full font-medium">
                  {getTimeAgo(post.savedAt || post.timestamp || post.createdAt || '')}
                </span>
              </div>

              {/* Selection indicator */}
              <div className="absolute top-2 right-2">
                <div className={`
                  w-6 h-6 rounded-full border-2 transition-all backdrop-blur-sm
                  ${selectedPosts.has(post.id || '')
                    ? 'bg-blue-500 border-blue-400/50 shadow-lg scale-110'
                    : 'border-white/30 bg-white/10 hover:bg-white/20 dark:border-white/20 dark:bg-white/5 dark:hover:bg-white/10'
                  }
                `}>
                  {selectedPosts.has(post.id || '') && (
                    <svg className="w-4 h-4 text-white m-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="p-4">
              {/* Post content preview */}
              <h3 className="text-sm font-medium text-gray-900 dark:text-white leading-relaxed line-clamp-3 mb-3">
                {getPostPreview(post)}
              </h3>

              {/* Tags and Category */}
              <div className="flex flex-wrap gap-1 mb-3">
                {post.category && (
                  <span className="px-2 py-1 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 text-purple-700 dark:text-purple-300 text-xs rounded-full font-medium">
                    {post.category}
                  </span>
                )}
                {post.tags && post.tags.slice(0, 2).map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                    {tag}
                  </span>
                ))}
              </div>

              {/* Author Info */}
              {post.authorName && (
                <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  {post.authorAvatar && (
                    <img
                      src={post.authorAvatar}
                      alt={post.authorName}
                      className="w-4 h-4 rounded-full"
                      onError={(e) => { e.currentTarget.style.display = 'none'; }}
                    />
                  )}
                  <span className="truncate">@{post.authorName}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Capture Button */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50">
        <div className="text-center mb-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">✨ Capture Your Inspiration</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
            Transform your saved posts into threads, summaries, hashtags, and more with AI assistance
          </p>
        </div>

        <div className="flex justify-center">
          <button
            onClick={() => setShowCaptureModal(true)}
            className="group relative overflow-hidden bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl px-8 py-4 text-base font-medium transition-all duration-300 hover:scale-105 hover:shadow-xl shadow-purple-500/25"
          >
            <div className="flex items-center justify-center space-x-3">
              <span className="text-xl">🎯</span>
              <span>Capture Inspiration</span>
              <svg className="w-5 h-5 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </div>
            <div className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></div>
          </button>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            💡 Select posts above, then click Capture to explore AI-powered content creation options
          </p>
        </div>
      </div>

      {/* Selection info */}
      {selectedPosts.size > 0 && (
        <div className="text-center bg-blue-50 dark:bg-blue-900/20 rounded-xl p-3 border border-blue-200 dark:border-blue-700">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {selectedPosts.size} post{selectedPosts.size !== 1 ? 's' : ''} selected
            </p>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Ready to transform into content
          </p>
        </div>
      )}

      {/* Capture Modal */}
      {showCaptureModal && (
        <CaptureInspirationModal
          posts={inspirationPosts}
          onClose={() => setShowCaptureModal(false)}
        />
      )}
    </div>
  );
};

export default InspirationFeedWidget;
