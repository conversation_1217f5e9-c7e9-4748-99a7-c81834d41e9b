<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Normalization Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #3b82f6;
            background: #f8fafc;
        }
        .input {
            font-weight: bold;
            color: #1f2937;
        }
        .output {
            color: #059669;
            margin-top: 5px;
        }
        .error {
            color: #dc2626;
            margin-top: 5px;
        }
        .valid {
            color: #059669;
        }
        .invalid {
            color: #dc2626;
        }
    </style>
</head>
<body>
    <h1>URL Normalization Test</h1>
    <p>This page tests the URL normalization function used in the SavedBookmarksWidget.</p>
    
    <div class="test-container">
        <h2>Test Cases</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>Interactive Test</h2>
        <input 
            type="text" 
            id="url-input" 
            placeholder="Enter a URL to test..." 
            style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; margin-bottom: 10px;"
        >
        <div id="interactive-result"></div>
    </div>

    <script>
        // Copy of the normalizeUrl function from SavedBookmarksWidget
        function normalizeUrl(input) {
            if (!input.trim()) return '';
            
            let url = input.trim();
            
            // Remove any leading/trailing whitespace
            url = url.trim();
            
            // If it already has a protocol, use it as is
            if (url.startsWith('http://') || url.startsWith('https://')) {
                return url;
            }
            
            // If it starts with www., add https://
            if (url.startsWith('www.')) {
                return `https://${url}`;
            }
            
            // If it looks like a domain (contains a dot), add https://
            if (url.includes('.') && !url.includes(' ')) {
                return `https://${url}`;
            }
            
            // Otherwise, assume it needs https:// prefix
            return `https://${url}`;
        }

        // Copy of the validateUrl function
        function validateUrl(input) {
            if (!input.trim()) {
                return { isValid: false, normalizedUrl: '', error: 'URL is required' };
            }

            try {
                const normalizedUrl = normalizeUrl(input);
                new URL(normalizedUrl); // This will throw if invalid
                return { isValid: true, normalizedUrl, error: '' };
            } catch (error) {
                return { isValid: false, normalizedUrl: '', error: 'Please enter a valid URL (e.g., google.com or https://google.com)' };
            }
        }

        // Test cases
        const testCases = [
            // Valid cases that should work
            { input: 'google.com', expected: 'https://google.com' },
            { input: 'www.google.com', expected: 'https://www.google.com' },
            { input: 'https://google.com', expected: 'https://google.com' },
            { input: 'http://google.com', expected: 'http://google.com' },
            { input: 'github.com', expected: 'https://github.com' },
            { input: 'www.github.com', expected: 'https://www.github.com' },
            { input: 'bolt.new', expected: 'https://bolt.new' },
            { input: 'example.org', expected: 'https://example.org' },
            { input: 'subdomain.example.com', expected: 'https://subdomain.example.com' },
            { input: 'https://www.example.com/path?query=1', expected: 'https://www.example.com/path?query=1' },
            
            // Edge cases
            { input: '  google.com  ', expected: 'https://google.com' },
            { input: 'localhost:3000', expected: 'https://localhost:3000' },
            
            // Invalid cases
            { input: '', expected: 'ERROR' },
            { input: '   ', expected: 'ERROR' },
            { input: 'not a url', expected: 'ERROR' },
            { input: 'just text', expected: 'ERROR' },
        ];

        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const validation = validateUrl(testCase.input);
                const passed = testCase.expected === 'ERROR' 
                    ? !validation.isValid 
                    : validation.isValid && validation.normalizedUrl === testCase.expected;
                
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <div class="input">Input: "${testCase.input}"</div>
                    <div class="output">Expected: ${testCase.expected}</div>
                    <div class="output">Got: ${validation.isValid ? validation.normalizedUrl : 'ERROR: ' + validation.error}</div>
                    <div class="${passed ? 'valid' : 'invalid'}">
                        ${passed ? '✅ PASS' : '❌ FAIL'}
                    </div>
                `;
                resultsContainer.appendChild(testDiv);
            });

            // Add summary
            const summaryDiv = document.createElement('div');
            summaryDiv.style.marginTop = '20px';
            summaryDiv.style.padding = '15px';
            summaryDiv.style.backgroundColor = passCount === totalCount ? '#d1fae5' : '#fee2e2';
            summaryDiv.style.borderRadius = '8px';
            summaryDiv.innerHTML = `
                <strong>Test Summary: ${passCount}/${totalCount} tests passed</strong>
                ${passCount === totalCount ? '🎉 All tests passed!' : '⚠️ Some tests failed'}
            `;
            resultsContainer.appendChild(summaryDiv);
        }

        // Interactive testing
        function setupInteractiveTest() {
            const input = document.getElementById('url-input');
            const result = document.getElementById('interactive-result');

            input.addEventListener('input', (e) => {
                const value = e.target.value;
                if (!value) {
                    result.innerHTML = '';
                    return;
                }

                const validation = validateUrl(value);
                result.innerHTML = `
                    <div style="padding: 10px; border-radius: 4px; background: ${validation.isValid ? '#d1fae5' : '#fee2e2'};">
                        <div><strong>Input:</strong> "${value}"</div>
                        ${validation.isValid 
                            ? `<div class="valid"><strong>✅ Valid:</strong> ${validation.normalizedUrl}</div>`
                            : `<div class="invalid"><strong>❌ Invalid:</strong> ${validation.error}</div>`
                        }
                    </div>
                `;
            });
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            runTests();
            setupInteractiveTest();
        });
    </script>
</body>
</html>
