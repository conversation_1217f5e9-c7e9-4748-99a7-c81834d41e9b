import React, { useState, useEffect } from 'react';
import { testSearchSystem } from '../services/semanticSearchService';

interface SearchSystemStatusProps {
  className?: string;
}

const SearchSystemStatus: React.FC<SearchSystemStatusProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<{
    loading: boolean;
    data: {
      status: string;
      vectorSearchAvailable: boolean;
      totalPosts: number;
      postsWithEmbeddings: number;
      embeddingCoverage: number;
      hasOpenAIKey: boolean;
    } | null;
    error: string | null;
  }>({
    loading: true,
    data: null,
    error: null
  });

  const loadStatus = async () => {
    setStatus(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const data = await testSearchSystem();
      setStatus({ loading: false, data, error: null });
    } catch (error) {
      setStatus({
        loading: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to load status'
      });
    }
  };

  useEffect(() => {
    loadStatus();
  }, []);

  const getStatusColor = (available: boolean) => {
    return available ? 'text-notely-mint' : 'text-notely-coral';
  };

  const getStatusIcon = (available: boolean) => {
    return available ? '✅' : '❌';
  };

  const getCoverageColor = (coverage: number) => {
    if (coverage >= 80) return 'text-notely-mint';
    if (coverage >= 50) return 'text-notely-coral';
    return 'text-notely-sage';
  };

  if (status.loading) {
    return (
      <div className={`bg-notely-surface border border-notely-border rounded-xl p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-notely-accent"></div>
          <span className="text-notely-text-secondary">Checking search system status...</span>
        </div>
      </div>
    );
  }

  if (status.error) {
    return (
      <div className={`bg-notely-surface border border-notely-coral/30 rounded-xl p-6 ${className}`}>
        <div className="flex items-center space-x-3 mb-4">
          <span className="text-2xl">⚠️</span>
          <div>
            <h3 className="text-lg font-semibold text-notely-text-primary">System Check Failed</h3>
            <p className="text-sm text-notely-text-secondary">Unable to verify search system status</p>
          </div>
        </div>
        <p className="text-sm text-notely-coral mb-4">{status.error}</p>
        <button
          onClick={loadStatus}
          className="notely-btn-secondary px-4 py-2 text-sm"
        >
          Retry Check
        </button>
      </div>
    );
  }

  const { data } = status;
  if (!data) return null;

  return (
    <div className={`bg-notely-surface border border-notely-border rounded-xl p-6 ${className}`}>
      <div className="flex items-center space-x-3 mb-6">
        <span className="text-2xl">🔍</span>
        <div>
          <h3 className="text-lg font-semibold text-notely-text-primary">Search System Status</h3>
          <p className="text-sm text-notely-text-secondary">Vector search and AI capabilities</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Vector Search Status */}
        <div className="bg-notely-card border border-notely-border rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-notely-text-secondary">Vector Search</span>
            <span className={getStatusColor(data.vectorSearchAvailable)}>
              {getStatusIcon(data.vectorSearchAvailable)}
            </span>
          </div>
          <p className={`text-lg font-semibold ${getStatusColor(data.vectorSearchAvailable)}`}>
            {data.vectorSearchAvailable ? 'Available' : 'Not Available'}
          </p>
          {!data.vectorSearchAvailable && (
            <p className="text-xs text-notely-text-muted mt-1">
              MongoDB Atlas Vector Search index required
            </p>
          )}
        </div>

        {/* OpenAI API Status */}
        <div className="bg-notely-card border border-notely-border rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-notely-text-secondary">OpenAI API</span>
            <span className={getStatusColor(data.hasOpenAIKey)}>
              {getStatusIcon(data.hasOpenAIKey)}
            </span>
          </div>
          <p className={`text-lg font-semibold ${getStatusColor(data.hasOpenAIKey)}`}>
            {data.hasOpenAIKey ? 'Configured' : 'Missing'}
          </p>
          {!data.hasOpenAIKey && (
            <p className="text-xs text-notely-text-muted mt-1">
              Required for embedding generation
            </p>
          )}
        </div>

        {/* Total Posts */}
        <div className="bg-notely-card border border-notely-border rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-notely-text-secondary">Total Posts</span>
            <span className="text-notely-text-muted">📚</span>
          </div>
          <p className="text-lg font-semibold text-notely-text-primary">
            {data.totalPosts.toLocaleString()}
          </p>
        </div>

        {/* Embedding Coverage */}
        <div className="bg-notely-card border border-notely-border rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-notely-text-secondary">AI Coverage</span>
            <span className="text-notely-text-muted">🧠</span>
          </div>
          <p className={`text-lg font-semibold ${getCoverageColor(data.embeddingCoverage)}`}>
            {data.embeddingCoverage.toFixed(1)}%
          </p>
          <p className="text-xs text-notely-text-muted mt-1">
            {data.postsWithEmbeddings} of {data.totalPosts} posts processed
          </p>
        </div>
      </div>

      {/* Recommendations */}
      <div className="border-t border-notely-border pt-4">
        <h4 className="uppercase text-xs text-notely-text-muted tracking-wide font-semibold mb-3">
          Recommendations
        </h4>
        <div className="space-y-2">
          {!data.vectorSearchAvailable && (
            <div className="flex items-start space-x-2">
              <span className="text-notely-coral mt-1">•</span>
              <span className="text-sm text-notely-text-primary leading-relaxed">
                Set up MongoDB Atlas Vector Search index for optimal performance
              </span>
            </div>
          )}
          {!data.hasOpenAIKey && (
            <div className="flex items-start space-x-2">
              <span className="text-notely-coral mt-1">•</span>
              <span className="text-sm text-notely-text-primary leading-relaxed">
                Configure OpenAI API key for AI-powered features
              </span>
            </div>
          )}
          {data.embeddingCoverage < 50 && (
            <div className="flex items-start space-x-2">
              <span className="text-notely-sage mt-1">•</span>
              <span className="text-sm text-notely-text-primary">
                Save more posts to improve search quality and AI insights
              </span>
            </div>
          )}
          {data.vectorSearchAvailable && data.hasOpenAIKey && data.embeddingCoverage >= 50 && (
            <div className="flex items-start space-x-2">
              <span className="text-notely-mint mt-1">•</span>
              <span className="text-sm text-notely-text-primary">
                Your search system is optimally configured! 🎉
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-between items-center mt-6 pt-4 border-t border-notely-border">
        <span className="text-xs text-notely-text-muted">
          Last checked: {new Date().toLocaleTimeString()}
        </span>
        <button
          onClick={loadStatus}
          className="notely-btn-secondary px-3 py-1 text-xs"
        >
          Refresh
        </button>
      </div>
    </div>
  );
};

export default SearchSystemStatus;
