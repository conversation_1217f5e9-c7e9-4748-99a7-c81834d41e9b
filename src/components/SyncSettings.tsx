/**
 * Sync Settings Component
 * Allows users to manage cross-device synchronization
 */

import React, { useState, useEffect } from 'react';
import {
  getUnifiedSyncStatus,
  setUnifiedSyncEnabled,
  syncUnifiedDataFromCloud,
  syncUnifiedDataToCloud,
  SyncStatus
} from '../services/cloudSyncService';

interface SyncSettingsProps {
  className?: string;
}

const SyncSettings: React.FC<SyncSettingsProps> = ({ className = '' }) => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Load initial sync status
    const loadSyncStatus = async () => {
      const status = await getUnifiedSyncStatus();
      setSyncStatus(status);
    };

    loadSyncStatus();

    // Poll for status updates every 30 seconds
    const interval = setInterval(loadSyncStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleToggleSync = async () => {
    if (!syncStatus) return;

    setIsLoading(true);
    try {
      await setUnifiedSyncEnabled(!syncStatus.enabled);
      // Refresh status
      const newStatus = await getUnifiedSyncStatus();
      setSyncStatus(newStatus);
    } catch (error) {
      console.error('Error toggling sync:', error);
      // You might want to show a toast notification here
    } finally {
      setIsLoading(false);
    }
  };

  const handleForceSyncNow = async () => {
    setIsLoading(true);
    try {
      // Perform bidirectional sync
      await syncUnifiedDataFromCloud();
      await syncUnifiedDataToCloud();

      // Refresh status
      const newStatus = await getUnifiedSyncStatus();
      setSyncStatus(newStatus);
    } catch (error) {
      console.error('Error forcing sync:', error);
      // You might want to show a toast notification here
    } finally {
      setIsLoading(false);
    }
  };

  const formatLastSync = (lastSync: Date | null): string => {
    if (!lastSync) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - lastSync.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  };

  const getSyncStatusColor = (): string => {
    if (!syncStatus) return 'text-gray-500';
    if (syncStatus.error) return 'text-red-500';
    if (!syncStatus.enabled) return 'text-gray-500';
    if (syncStatus.pendingChanges) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getSyncStatusText = (): string => {
    if (!syncStatus) return 'Loading...';
    if (syncStatus.error) return 'Sync Error';
    if (!syncStatus.enabled) return 'Sync Disabled';
    if (syncStatus.pendingChanges) return 'Pending Changes';
    return 'Synced';
  };

  if (!syncStatus) {
    return (
      <div className={`notely-card p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`notely-card p-4 space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold notely-heading">Cross-Device Sync</h3>
          <p className="text-sm text-notely-text-muted">
            Sync your categories, bookmarks, and chat history across devices
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${getSyncStatusColor().replace('text-', 'bg-')}`}></div>
          <span className={`text-sm font-medium ${getSyncStatusColor()}`}>
            {getSyncStatusText()}
          </span>
        </div>
      </div>

      {/* Toggle Switch */}
      <div className="flex items-center justify-between">
        <div>
          <label className="text-sm font-medium notely-text-primary">
            Enable Cloud Sync
          </label>
          <p className="text-xs text-notely-text-muted">
            Requires login to sync across devices
          </p>
        </div>
        <button
          onClick={handleToggleSync}
          disabled={isLoading}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-notely-primary focus:ring-offset-2 ${
            syncStatus.enabled
              ? 'bg-notely-primary'
              : 'bg-gray-200 dark:bg-gray-700'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              syncStatus.enabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {/* Sync Details */}
      {syncStatus.enabled && (
        <div className="space-y-3 pt-2 border-t border-notely-border/20">
          <div className="flex items-center justify-between">
            <span className="text-sm text-notely-text-secondary">Last Sync:</span>
            <span className="text-sm font-medium notely-text-primary">
              {formatLastSync(syncStatus.lastSync)}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-notely-text-secondary">Version:</span>
            <span className="text-sm font-medium notely-text-primary">
              {syncStatus.version}
            </span>
          </div>

          {syncStatus.error && (
            <div className="p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <p className="text-sm text-red-600 dark:text-red-400">
                {syncStatus.error}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={handleForceSyncNow}
              disabled={isLoading}
              className="flex-1 px-3 py-2 text-sm font-medium text-notely-primary border border-notely-primary rounded-md hover:bg-notely-primary hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Syncing...' : 'Sync Now'}
            </button>

            <button
              onClick={() => setShowDetails(!showDetails)}
              className="px-3 py-2 text-sm font-medium text-notely-text-secondary border border-notely-border/20 rounded-md hover:bg-notely-surface transition-colors"
            >
              {showDetails ? 'Hide' : 'Details'}
            </button>
          </div>

          {/* Detailed Information */}
          {showDetails && (
            <div className="space-y-2 p-3 bg-notely-surface/50 rounded-md text-xs">
              <div className="flex justify-between">
                <span className="text-notely-text-muted">Device ID:</span>
                <span className="font-mono text-notely-text-secondary">
                  {syncStatus.deviceId.substring(0, 12)}...
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-notely-text-muted">Pending Changes:</span>
                <span className={syncStatus.pendingChanges ? 'text-yellow-600' : 'text-green-600'}>
                  {syncStatus.pendingChanges ? 'Yes' : 'No'}
                </span>
              </div>

              <div className="pt-2 border-t border-notely-border/10">
                <p className="text-notely-text-muted">
                  Sync includes: Categories, Bookmarks, Chat History, and Settings
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Info for disabled sync */}
      {!syncStatus.enabled && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
          <div className="flex items-start space-x-2">
            <div className="w-5 h-5 text-blue-500 mt-0.5">
              <svg fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Enable sync to access your data across devices
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                Your categories, bookmarks, and chat history will be securely synced to the cloud and available on all your devices where you're logged in.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SyncSettings;
