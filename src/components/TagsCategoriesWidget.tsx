import React, { useState, useMemo } from 'react';
import { Post } from '../types';
import { formatForDisplay } from '../utils/formatUtils';
import { categoryStorage } from '../services/categoryStorageManager';
import { getAllTags, saveAllTags } from '../storage';
import { syncUnifiedDataToCloud } from '../services/cloudSyncService';

interface TagsCategoriesWidgetProps {
  posts: Post[];
  className?: string;
}

const TagsCategoriesWidget: React.FC<TagsCategoriesWidgetProps> = ({ 
  posts, 
  className = '' 
}) => {
  const [activeView, setActiveView] = useState<'categories' | 'tags'>('categories');
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const [newItemInput, setNewItemInput] = useState<string>('');
  const [showNewItemInput, setShowNewItemInput] = useState<boolean>(false);
  const [allCategories, setAllCategories] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);

  // Load all categories and tags from storage
  React.useEffect(() => {
    const loadData = async () => {
      try {
        // Get categories from unified storage (user custom categories)
        const customCategories = await categoryStorage.getAllCategories();
        
        // Get core categories from posts (the ones shown in All tab)
        const coreCategories = new Set<string>();
        posts.forEach(post => {
          if (post.categories && Array.isArray(post.categories)) {
            post.categories.forEach(category => {
              if (category && category.trim() !== '') {
                // Convert core category slugs to display format
                const displayName = category.replace(/_/g, ' ').split(' ')
                  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ');
                coreCategories.add(displayName);
              }
            });
          }
        });
        
        // Combine both systems: core categories from posts + user custom categories
        const allCategoryNames = [
          ...Array.from(coreCategories),
          ...customCategories.map(cat => cat.name)
        ];
        
        // Remove duplicates and sort
        const uniqueCategories = [...new Set(allCategoryNames)].sort();
        setAllCategories(uniqueCategories);
        
        console.log('[TagsCategoriesWidget] Core categories from posts:', Array.from(coreCategories));
        console.log('[TagsCategoriesWidget] Custom categories:', customCategories.map(cat => cat.name));
        console.log('[TagsCategoriesWidget] Combined categories:', uniqueCategories);
        
        // Get tags from storage (user created tags)
        const storedTags = await getAllTags();
        
        // Get tags from posts
        const postTags = new Set<string>();
        posts.forEach(post => {
          if (post.tags && Array.isArray(post.tags)) {
            post.tags.forEach(tag => {
              if (tag && tag.trim() !== '') {
                postTags.add(tag);
              }
            });
          }
        });
        
        // Combine both systems: tags from posts + user stored tags
        const allTagNames = [
          ...Array.from(postTags),
          ...storedTags
        ];
        
        // Remove duplicates and sort
        const uniqueTags = [...new Set(allTagNames)].sort();
        setAllTags(uniqueTags);
        
        console.log('[TagsCategoriesWidget] Tags from posts:', Array.from(postTags));
        console.log('[TagsCategoriesWidget] Stored tags:', storedTags);
        console.log('[TagsCategoriesWidget] Combined tags:', uniqueTags);
        
        // Trigger cloud sync after loading categories
        await syncUnifiedDataToCloud();
      } catch (error) {
        console.error('Error loading categories/tags:', error);
      }
    };
    loadData();
  }, [posts]); // Add posts as dependency so it updates when posts change

  // Calculate category and tag statistics
  const { categories, tags } = useMemo(() => {
    const categoryMap = new Map<string, number>();
    const tagMap = new Map<string, number>();

    posts.forEach(post => {
      // Count categories
      if (post.categories && Array.isArray(post.categories)) {
        post.categories.forEach(category => {
          categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
        });
      }

      // Count tags
      if (post.tags && Array.isArray(post.tags)) {
        post.tags.forEach(tag => {
          tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
        });
      }
    });

    // Convert to sorted arrays
    const sortedCategories = Array.from(categoryMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 12); // Show top 12

    const sortedTags = Array.from(tagMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20); // Show top 20

    return {
      categories: sortedCategories,
      tags: sortedTags
    };
  }, [posts]);

  // Edit functions
  const handleEditStart = (item: string) => {
    setEditingItem(item);
    setEditValue(item);
  };

  const handleEditSubmit = async () => {
    if (!editingItem || !editValue.trim()) return;

    try {
      if (activeView === 'categories') {
        // Update category in unified storage
        const allCats = await categoryStorage.getAllCategories();
        const categoryToUpdate = allCats.find(c => c.name === editingItem);
        if (categoryToUpdate) {
          await categoryStorage.updateCategory(categoryToUpdate.id, {
            ...categoryToUpdate,
            name: editValue.trim()
          });
        } else {
          // If it's not in custom categories, add it as a new custom category
          await categoryStorage.addCategory({
            name: editValue.trim(),
            emoji: '📝',
            tags: []
          });
        }
        
        // Refresh categories from storage
        const updatedCats = await categoryStorage.getAllCategories();
        
        // Recombine with core categories
        const coreCategories = new Set<string>();
        posts.forEach(post => {
          if (post.categories && Array.isArray(post.categories)) {
            post.categories.forEach(category => {
              if (category && category.trim() !== '') {
                const displayName = category.replace(/_/g, ' ').split(' ')
                  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ');
                coreCategories.add(displayName);
              }
            });
          }
        });
        
        const allCategoryNames = [
          ...Array.from(coreCategories),
          ...updatedCats.map(cat => cat.name)
        ];
        const uniqueCategories = [...new Set(allCategoryNames)].sort();
        setAllCategories(uniqueCategories);
      } else {
        // Handle tags - add to stored tags and refresh the combined list
        const storedTags = await getAllTags();
        const updatedStoredTags = storedTags.includes(editingItem) 
          ? storedTags.map(tag => tag === editingItem ? editValue.trim() : tag)
          : [...storedTags, editValue.trim()];
        
        await saveAllTags(updatedStoredTags);
        
        // Refresh the combined tags list
        const postTags = new Set<string>();
        posts.forEach(post => {
          if (post.tags && Array.isArray(post.tags)) {
            post.tags.forEach(tag => {
              if (tag && tag.trim() !== '') {
                postTags.add(tag);
              }
            });
          }
        });
        
        const allTagNames = [
          ...Array.from(postTags),
          ...updatedStoredTags
        ];
        const uniqueTags = [...new Set(allTagNames)].sort();
        setAllTags(uniqueTags);
      }
      
      setEditingItem(null);
      setEditValue('');
    } catch (error) {
      console.error('Error updating item:', error);
    }
  };

  const handleEditCancel = () => {
    setEditingItem(null);
    setEditValue('');
  };

  const deleteItem = async (itemName: string) => {
    if (!confirm(`Are you sure you want to delete "${itemName}"?`)) return;

    try {
      if (activeView === 'categories') {
        // Delete from unified storage
        const allCats = await categoryStorage.getAllCategories();
        const categoryToDelete = allCats.find(c => c.name === itemName);
        if (categoryToDelete) {
          await categoryStorage.deleteCategory(categoryToDelete.id);
        }
        
        // Refresh categories
        const updatedCats = await categoryStorage.getAllCategories();
        const coreCategories = new Set<string>();
        posts.forEach(post => {
          if (post.categories && Array.isArray(post.categories)) {
            post.categories.forEach(category => {
              if (category && category.trim() !== '') {
                const displayName = category.replace(/_/g, ' ').split(' ')
                  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ');
                coreCategories.add(displayName);
              }
            });
          }
        });
        
        const allCategoryNames = [
          ...Array.from(coreCategories),
          ...updatedCats.map(cat => cat.name)
        ];
        const uniqueCategories = [...new Set(allCategoryNames)].sort();
        setAllCategories(uniqueCategories);
      } else {
        // Handle tags - remove from stored tags and refresh the combined list
        const storedTags = await getAllTags();
        const updatedStoredTags = storedTags.filter(tag => tag !== itemName);
        await saveAllTags(updatedStoredTags);
        
        // Refresh the combined tags list (post tags will remain)
        const postTags = new Set<string>();
        posts.forEach(post => {
          if (post.tags && Array.isArray(post.tags)) {
            post.tags.forEach(tag => {
              if (tag && tag.trim() !== '') {
                postTags.add(tag);
              }
            });
          }
        });
        
        const allTagNames = [
          ...Array.from(postTags),
          ...updatedStoredTags
        ];
        const uniqueTags = [...new Set(allTagNames)].sort();
        setAllTags(uniqueTags);
      }
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const handleAddItem = async () => {
    if (!newItemInput.trim()) return;

    try {
      if (activeView === 'categories') {
        // Add to unified storage
        await categoryStorage.addCategory({
          name: newItemInput.trim(),
          emoji: '📝', // Default emoji
          tags: []
        });
        
        // Refresh categories
        const updatedCats = await categoryStorage.getAllCategories();
        const coreCategories = new Set<string>();
        posts.forEach(post => {
          if (post.categories && Array.isArray(post.categories)) {
            post.categories.forEach(category => {
              if (category && category.trim() !== '') {
                const displayName = category.replace(/_/g, ' ').split(' ')
                  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ');
                coreCategories.add(displayName);
              }
            });
          }
        });
        
        const allCategoryNames = [
          ...Array.from(coreCategories),
          ...updatedCats.map(cat => cat.name)
        ];
        const uniqueCategories = [...new Set(allCategoryNames)].sort();
        setAllCategories(uniqueCategories);
      } else {
        // Handle tags - add to stored tags and refresh the combined list
        const storedTags = await getAllTags();
        const updatedStoredTags = [...storedTags, newItemInput.trim()];
        await saveAllTags(updatedStoredTags);
        
        // Refresh the combined tags list
        const postTags = new Set<string>();
        posts.forEach(post => {
          if (post.tags && Array.isArray(post.tags)) {
            post.tags.forEach(tag => {
              if (tag && tag.trim() !== '') {
                postTags.add(tag);
              }
            });
          }
        });
        
        const allTagNames = [
          ...Array.from(postTags),
          ...updatedStoredTags
        ];
        const uniqueTags = [...new Set(allTagNames)].sort();
        setAllTags(uniqueTags);
      }
      
      setNewItemInput('');
      setShowNewItemInput(false);
    } catch (error) {
      console.error('Error adding item:', error);
    }
  };

  const getCategoryColor = (index: number): string => {
    const colors = [
      'bg-blue-500/10 text-blue-600 border-blue-200',
      'bg-green-500/10 text-green-600 border-green-200',
      'bg-purple-500/10 text-purple-600 border-purple-200',
      'bg-orange-500/10 text-orange-600 border-orange-200',
      'bg-pink-500/10 text-pink-600 border-pink-200',
      'bg-cyan-500/10 text-cyan-600 border-cyan-200',
    ];
    return colors[index % colors.length];
  };

  const getTagColor = (index: number): string => {
    const colors = [
      'bg-indigo-500/10 text-indigo-600',
      'bg-emerald-500/10 text-emerald-600',
      'bg-rose-500/10 text-rose-600',
      'bg-amber-500/10 text-amber-600',
      'bg-violet-500/10 text-violet-600',
      'bg-teal-500/10 text-teal-600',
    ];
    return colors[index % colors.length];
  };

  if (categories.length === 0 && tags.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <span className="text-4xl mb-4 block">🏷️</span>
        <p className="text-sm text-notely-text-muted leading-relaxed">No categories or tags yet</p>
        <p className="text-sm text-notely-text-muted leading-relaxed mt-2">
          Save some posts to see your content organization
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <h2 className="text-xl font-semibold mb-4 text-notely-text-primary">Tags & Categories</h2>
      
      {/* View Toggle */}
      <div className="flex items-center justify-start mb-4">
        <div className="bg-notely-surface/60 rounded-xl p-1 flex shadow-notely-sm">
          <button
            onClick={() => setActiveView('categories')}
            className={`
              px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200
              ${activeView === 'categories'
                ? 'bg-gradient-to-r from-notely-sky to-notely-lavender text-white shadow-notely-sm'
                : 'text-notely-text-muted hover:text-notely-text-secondary hover:bg-notely-card/80'
              }
            `}
          >
            Categories ({allCategories.length})
          </button>
          <button
            onClick={() => setActiveView('tags')}
            className={`
              px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200
              ${activeView === 'tags'
                ? 'bg-gradient-to-r from-notely-sky to-notely-lavender text-white shadow-notely-sm'
                : 'text-notely-text-muted hover:text-notely-text-secondary hover:bg-notely-card/80'
              }
            `}
          >
            Tags ({allTags.length})
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2 notely-scrollbar">
        {activeView === 'categories' ? (
          <div className="flex flex-wrap gap-2 items-start justify-start">
            {allCategories.map((category, index) => {
              const postCount = posts.filter(post => post.categories?.includes(category) || post.category === category).length;
              
              return (
                <div key={category} className="relative group">
                  {editingItem === category ? (
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={editValue}
                        onChange={(e) => setEditValue(e.target.value)}
                        className="bg-notely-surface text-notely-text border border-notely-border/20 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-notely-accent/50"
                        onKeyPress={(e) => e.key === 'Enter' && handleEditSubmit()}
                        onKeyDown={(e) => e.key === 'Escape' && handleEditCancel()}
                        autoFocus
                      />
                      <button
                        onClick={handleEditSubmit}
                        className="p-1.5 rounded-lg bg-gradient-to-r from-notely-sky to-notely-lavender text-white hover:shadow-notely-sm transition-all duration-200"
                      >
                        Save
                      </button>
                      <button
                        onClick={handleEditCancel}
                        className="p-1.5 rounded-lg bg-notely-surface text-notely-text-muted hover:text-notely-text-secondary hover:bg-notely-card/80 transition-all duration-200"
                      >
                        Cancel
                      </button>
                    </div>
                  ) : (
                    <div
                      className={`
                        inline-flex items-center px-4 py-2 rounded-full
                        bg-gradient-to-r from-[#8B5CF6] to-[#6366F1] hover:opacity-90
                        text-white cursor-pointer transition-all duration-200
                        hover:scale-105 group
                      `}
                      onClick={() => handleEditStart(category)}
                    >
                      <span className="text-sm font-medium">{formatForDisplay(category)}</span>
                      {postCount > 0 && (
                        <span className="ml-2 text-xs bg-black/20 text-white/90 px-2 py-0.5 rounded-full">
                          {postCount}
                        </span>
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteItem(category);
                        }}
                        className="ml-2 opacity-0 group-hover:opacity-100 p-1 rounded-full bg-black/20 text-white/90 hover:bg-black/30 transition-all duration-200 hover:scale-110"
                        title="Delete category"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-wrap gap-2 items-start justify-start">
            {allTags.map((tag, index) => {
              const postCount = posts.filter(post => post.tags?.includes(tag)).length;
              return (
                <div key={tag} className="relative group">
                  {editingItem === tag ? (
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={editValue}
                        onChange={(e) => setEditValue(e.target.value)}
                        className="bg-notely-surface text-notely-text border border-notely-border/20 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-notely-accent/50"
                        onKeyPress={(e) => e.key === 'Enter' && handleEditSubmit()}
                        onKeyDown={(e) => e.key === 'Escape' && handleEditCancel()}
                        autoFocus
                      />
                      <button
                        onClick={handleEditSubmit}
                        className="p-1.5 rounded-lg bg-gradient-to-r from-notely-sky to-notely-lavender text-white hover:shadow-notely-sm transition-all duration-200"
                      >
                        Save
                      </button>
                      <button
                        onClick={handleEditCancel}
                        className="p-1.5 rounded-lg bg-notely-surface text-notely-text-muted hover:text-notely-text-secondary hover:bg-notely-card/80 transition-all duration-200"
                      >
                        Cancel
                      </button>
                    </div>
                  ) : (
                    <div
                      className={`
                        inline-flex items-center px-4 py-2 rounded-full
                        bg-gradient-to-r from-[#8B5CF6] to-[#6366F1] hover:opacity-90
                        text-white cursor-pointer transition-all duration-200
                        hover:scale-105 group
                      `}
                      onClick={() => handleEditStart(tag)}
                    >
                      <span className="text-sm font-medium">{formatForDisplay(tag)}</span>
                      {postCount > 0 && (
                        <span className="ml-2 text-xs bg-black/20 text-white/90 px-2 py-0.5 rounded-full">
                          {postCount}
                        </span>
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteItem(tag);
                        }}
                        className="ml-2 opacity-0 group-hover:opacity-100 p-1 rounded-full bg-black/20 text-white/90 hover:bg-black/30 transition-all duration-200 hover:scale-110"
                        title="Delete tag"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Add New Button */}
        <button
          onClick={() => setShowNewItemInput(true)}
          className="mt-4 inline-flex items-center px-3 py-1.5 rounded-lg
            bg-notely-accent/10 text-notely-accent hover:bg-notely-accent/20
            transition-all duration-200 text-sm font-medium hover:scale-105"
        >
          + Add New {activeView === 'categories' ? 'Category' : 'Tag'}
        </button>

        {/* New Item Input */}
        {showNewItemInput && (
          <div className="mt-4 flex items-center gap-2">
            <input
              type="text"
              value={newItemInput}
              onChange={(e) => setNewItemInput(e.target.value)}
              placeholder={`Enter new ${activeView === 'categories' ? 'category' : 'tag'} name...`}
              className="flex-1 px-3 py-2 rounded-lg bg-notely-surface text-notely-text
                placeholder-notely-text-muted focus:outline-none focus:ring-2
                focus:ring-notely-accent/50 border border-notely-border/20"
            />
            <button
              onClick={handleAddItem}
              className="px-4 py-2 rounded-lg bg-gradient-to-r from-notely-sky to-notely-lavender
                text-white hover:shadow-notely-sm transition-all duration-200 hover:scale-105"
            >
              Add
            </button>
            <button
              onClick={() => {
                setShowNewItemInput(false);
                setNewItemInput('');
              }}
              className="px-4 py-2 rounded-lg bg-notely-surface text-notely-text-muted
                hover:text-notely-text-secondary hover:bg-notely-card/80 transition-all duration-200"
            >
              Cancel
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TagsCategoriesWidget;
