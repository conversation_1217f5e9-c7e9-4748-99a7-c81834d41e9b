import React, { useState, useEffect, useMemo } from 'react';
import { Post, Platform } from '../types';
import { SmartRecapData } from '../types/contentSuggestions';
import { PlatformLogo } from './PlatformLogo';
import { getContentConversionStats } from '../services/contentConversionService';
import { generateMindstreamInsights } from '../utils/mindstreamUtils';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';

interface SmartRecapProps {
  posts: Post[];
  className?: string;
}

const SmartRecap: React.FC<SmartRecapProps> = ({ posts, className = '' }) => {
  const { t } = useTranslation();
  const [recapData, setRecapData] = useState<SmartRecapData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Calculate smart recap data
  const calculateSmartRecap = async (): Promise<SmartRecapData> => {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    // Filter posts from the last week
    const weeklyPosts = posts.filter(post => {
      const postDate = new Date(post.savedAt || post.timestamp || '');
      return postDate >= oneWeekAgo;
    });

    // Calculate total saves and growth
    const totalSaves = weeklyPosts.length;
    
    // Calculate previous week for growth comparison
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
    const previousWeekPosts = posts.filter(post => {
      const postDate = new Date(post.savedAt || post.timestamp || '');
      return postDate >= twoWeeksAgo && postDate < oneWeekAgo;
    });

    const weeklyGrowth = previousWeekPosts.length > 0
      ? Math.round(((totalSaves - previousWeekPosts.length) / previousWeekPosts.length) * 100)
      : totalSaves > 0 ? 100 : 0;

    // Find top platform
    const platformCounts: Record<string, number> = {};
    weeklyPosts.forEach(post => {
      platformCounts[post.platform] = (platformCounts[post.platform] || 0) + 1;
    });

    const topPlatformEntry = Object.entries(platformCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    const topPlatform = topPlatformEntry ? {
      name: topPlatformEntry[0],
      count: topPlatformEntry[1],
      percentage: Math.round((topPlatformEntry[1] / totalSaves) * 100)
    } : null;

    // Find top category
    const categoryCounts: Record<string, number> = {};
    weeklyPosts.forEach(post => {
      if (post.categories && post.categories.length > 0) {
        post.categories.forEach(category => {
          categoryCounts[category] = (categoryCounts[category] || 0) + 1;
        });
      }
    });

    const topCategoryEntry = Object.entries(categoryCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    const topCategory = topCategoryEntry ? {
      name: topCategoryEntry[0],
      count: topCategoryEntry[1]
    } : null;

    // Calculate time saved (2 minutes per post)
    const minutesSaved = totalSaves * 2;
    const timeSaved = minutesSaved >= 60
      ? `${Math.floor(minutesSaved / 60)}h ${minutesSaved % 60}m`
      : `${minutesSaved}m`;

    // Get content conversion stats
    const contentConversions = await getContentConversionStats();

    // Generate AI insight
    const insights = generateMindstreamInsights(posts);
    const aiInsight = generateSmartInsight(weeklyPosts, insights);

    // Generate suggested actions
    const suggestedActions = generateSuggestedActions(weeklyPosts, contentConversions, topPlatform);

    return {
      totalSaves,
      weeklyGrowth,
      topPlatform,
      topCategory,
      timeSaved,
      contentConversions,
      aiInsight,
      suggestedActions
    };
  };

  // Load recap data
  useEffect(() => {
    const loadRecapData = async () => {
      setIsLoading(true);
      try {
        const data = await calculateSmartRecap();
        setRecapData(data);
      } catch (error) {
        console.error('Error calculating smart recap:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadRecapData();
  }, [posts]);

  // Generate smart AI insight
  const generateSmartInsight = (weeklyPosts: Post[], insights: any[]): string | null => {
    if (weeklyPosts.length === 0) return null;

    // Check for AI-related content
    const aiRelatedPosts = weeklyPosts.filter(post => {
      const content = (post.content || '').toLowerCase();
      return content.includes('ai') || content.includes('artificial intelligence') || 
             content.includes('machine learning') || content.includes('gpt') ||
             content.includes('chatbot') || content.includes('automation');
    });

    if (aiRelatedPosts.length >= 3) {
      return `Your last ${aiRelatedPosts.length} saves are AI-related. Would you like to turn them into a thread?`;
    }

    // Check for platform concentration
    const platformCounts: Record<string, number> = {};
    weeklyPosts.forEach(post => {
      platformCounts[post.platform] = (platformCounts[post.platform] || 0) + 1;
    });

    const dominantPlatform = Object.entries(platformCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (dominantPlatform && dominantPlatform[1] >= weeklyPosts.length * 0.7) {
      return `You're heavily focused on ${dominantPlatform[0]} content. Consider diversifying your sources.`;
    }

    // Check for content length patterns
    const longPosts = weeklyPosts.filter(post => (post.content || '').length > 500);
    if (longPosts.length >= 3) {
      return `You're saving detailed content. These could make great newsletter material.`;
    }

    // Use existing insights if available
    if (insights.length > 0) {
      return insights[0].description;
    }

    return null;
  };

  // Generate suggested actions based on data
  const generateSuggestedActions = (
    weeklyPosts: Post[], 
    conversions: any, 
    topPlatform: any
  ): string[] => {
    const actions: string[] = [];

    // Suggest thread generation if enough content
    if (weeklyPosts.length >= 5) {
      actions.push('Generate Thread');
    }

    // Always suggest planning
    actions.push('Plan This Week');

    // Suggest export if there are conversions
    if (conversions.totalConversions > 0) {
      actions.push('Export All Drafts');
    } else {
      actions.push('Create First Draft');
    }

    return actions.slice(0, 3); // Limit to 3 actions
  };

  // Handle action clicks
  const handleActionClick = (action: string) => {
    switch (action) {
      case 'Generate Thread':
        toast.info('Thread generation feature coming soon!');
        break;
      case 'Plan This Week':
        toast.info('Weekly planning feature coming soon!');
        break;
      case 'Export All Drafts':
        toast.info('Draft export feature coming soon!');
        break;
      case 'Create First Draft':
        toast.info('Navigate to "Use What You Saved" to create your first draft!');
        break;
      default:
        toast.info('Feature coming soon!');
    }
  };

  // Format platform name
  const formatPlatformName = (platform: string): string => {
    switch (platform) {
      case 'pinterest': return 'Pinterest';
      case 'X/Twitter': return 'X/Twitter';
      default: return platform;
    }
  };

  // Format category name
  const formatCategoryName = (category: string): string => {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (isLoading) {
    return (
      <div className={`notely-card bg-notely-card rounded-notely-lg shadow-notely-md border border-notely-border px-6 py-4 ${className}`}>
        <div className="animate-pulse space-y-4 p-6">
          <div className="h-6 bg-notely-surface rounded w-1/3"></div>
          <div className="space-y-3">
            <div className="h-4 bg-notely-surface rounded w-full"></div>
            <div className="h-4 bg-notely-surface rounded w-2/3"></div>
            <div className="h-4 bg-notely-surface rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!recapData) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <span className="text-4xl mb-4 block">📊</span>
        <p className="text-sm text-notely-text-muted leading-relaxed">No data available for Smart Recap</p>
        <p className="text-sm text-notely-text-muted leading-relaxed mt-2">Save some posts to see your weekly insights</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Time Period Badge */}
      <div className="flex justify-end">
        <div className="uppercase text-xs text-notely-text-muted tracking-wide bg-notely-surface px-3 py-1 rounded-notely-full">
          Last 7 days
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        {/* Total Saves */}
        <div className="notely-card bg-notely-card border border-notely-border rounded-xl px-6 py-4 hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1 transition-all shadow-notely-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="uppercase text-xs text-notely-text-muted tracking-wide font-medium">Total Saves</p>
              <p className="text-xl font-semibold leading-tight text-notely-text-primary">{recapData.totalSaves}</p>
              {recapData.weeklyGrowth !== 0 && (
                <p className={`text-sm leading-relaxed ${recapData.weeklyGrowth > 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {recapData.weeklyGrowth > 0 ? '+' : ''}{recapData.weeklyGrowth}%
                </p>
              )}
            </div>
            <div className="w-8 h-8 bg-notely-surface border border-notely-border rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-notely-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Time Saved */}
        <div className="notely-card bg-notely-card border border-notely-border rounded-xl px-6 py-4 hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1 transition-all shadow-notely-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="uppercase text-xs text-notely-text-muted tracking-wide font-medium">Time Saved</p>
              <p className="text-xl font-semibold leading-tight text-notely-text-primary">{recapData.timeSaved}</p>
              <p className="text-sm text-notely-text-secondary leading-relaxed">Browsing time</p>
            </div>
            <div className="w-8 h-8 bg-notely-surface border border-notely-border rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-notely-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Platform and Category */}
      <div className="space-y-3">
        {recapData.topPlatform && (
          <div className="flex items-center justify-between notely-card bg-notely-card border border-notely-border rounded-xl px-6 py-4 hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1 transition-all shadow-notely-md">
            <div className="flex items-center space-x-3">
              <PlatformLogo platform={recapData.topPlatform.name as Platform} className="w-5 h-5" />
              <div>
                <p className="text-xl font-semibold leading-tight text-notely-text-primary">Most Saved Platform</p>
                <p className="text-sm text-notely-text-secondary leading-relaxed">{formatPlatformName(recapData.topPlatform.name)}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xl font-semibold leading-tight text-notely-text-primary">{recapData.topPlatform.count}</p>
              <p className="text-sm text-notely-text-secondary leading-relaxed">{recapData.topPlatform.percentage}%</p>
            </div>
          </div>
        )}

        {recapData.topCategory && (
          <div className="flex items-center justify-between notely-card bg-notely-card border border-notely-border rounded-xl px-6 py-4 hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1 transition-all shadow-notely-md">
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 bg-notely-surface border border-notely-border rounded-lg flex items-center justify-center">
                <span className="text-xs">🏷️</span>
              </div>
              <div>
                <p className="text-xl font-semibold leading-tight text-notely-text-primary">Popular Category</p>
                <p className="text-sm text-notely-text-secondary leading-relaxed">{formatCategoryName(recapData.topCategory.name)}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xl font-semibold leading-tight text-notely-text-primary">{recapData.topCategory.count}</p>
              <p className="text-sm text-notely-text-secondary leading-relaxed">posts</p>
            </div>
          </div>
        )}

        {/* Content Converted */}
        <div className="flex items-center justify-between notely-card bg-notely-card border border-notely-border rounded-xl px-6 py-4 hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1 transition-all shadow-notely-md">
          <div className="flex items-center space-x-3">
            <div className="w-5 h-5 bg-notely-surface border border-notely-border rounded-lg flex items-center justify-center">
              <span className="text-xs">🔁</span>
            </div>
            <div>
              <p className="text-xl font-semibold leading-tight text-notely-text-primary">Content Converted</p>
              <p className="text-sm text-notely-text-secondary leading-relaxed">Bookmarks to content</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xl font-semibold leading-tight text-notely-text-primary">{recapData.contentConversions.weeklyConversions}</p>
            <p className="text-sm text-notely-text-secondary leading-relaxed">this week</p>
          </div>
        </div>
      </div>

      {/* AI Insight */}
      {recapData.aiInsight && (
        <div className="bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-xl px-6 py-4 backdrop-blur-sm">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-notely-surface border border-notely-border rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs">🧠</span>
            </div>
            <div>
              <p className="uppercase text-xs opacity-70 tracking-wide font-medium mb-2">AI Insights</p>
              <p className="text-sm leading-relaxed">{recapData.aiInsight}</p>
            </div>
          </div>
        </div>
      )}

      {/* Suggested Actions */}
      <div>
        <p className="uppercase text-xs opacity-70 tracking-wide font-medium mb-4">📌 Suggested Actions</p>
        <div className="flex flex-wrap gap-3">
          {recapData.suggestedActions.map((action, index) => (
            <button
              key={index}
              onClick={() => handleActionClick(action)}
              className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-4 py-2 text-sm font-medium hover:scale-105 transition-transform"
            >
              {action}
            </button>
          ))}
        </div>
      </div>


    </div>
  );
};

export default SmartRecap;
