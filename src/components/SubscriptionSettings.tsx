import React, { useState, useEffect } from 'react';
import * as subscriptionService from '../services/subscriptionService';
import * as authService from '../services/authService';

interface SubscriptionSettingsProps {
  className?: string;
}

export const SubscriptionSettings: React.FC<SubscriptionSettingsProps> = ({ className = '' }) => {
  const [user, setUser] = useState<any>(null);
  const [subscription, setSubscription] = useState<any>(null);
  const [availablePlans, setAvailablePlans] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadUserData();
    loadAvailablePlans();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      const userData = await authService.getUser();
      setUser(userData);

      if (userData?.plan === 'premium') {
        const subscriptionData = await subscriptionService.getSubscriptionDetails();
        setSubscription(subscriptionData);
      }
    } catch (err) {
      console.error('Error loading user data:', err);
      setError('Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const loadAvailablePlans = async () => {
    try {
      const plans = await subscriptionService.getAvailablePlans();
      setAvailablePlans(plans);

      // If we got fallback plans, show a warning
      if (plans.length > 0 && plans[0].priceId?.includes('fallback')) {
        setError('Backend connection issue - using offline mode. Some features may be limited.');
      }
    } catch (err) {
      console.error('Error loading available plans:', err);
      setError('Failed to load subscription plans. Please check your connection.');
    }
  };

  const handlePlanChange = async (plan: any) => {
    try {
      setLoading(true);
      setError(null);

      if (plan.id === 'free') {
        // Handle downgrade to free - cancel subscription
        const success = await subscriptionService.cancelSubscription();
        if (success) {
          await loadUserData(); // Refresh data
        } else {
          setError('Failed to downgrade to free plan');
        }
      } else if (user?.plan === 'free') {
        // Handle upgrade from free - create checkout session
        const checkoutUrl = await subscriptionService.createCheckoutSession(plan.priceId);
        if (checkoutUrl) {
          window.open(checkoutUrl, '_blank');
        } else {
          setError('Failed to create checkout session');
        }
      } else {
        // Handle plan change (monthly <-> yearly)
        const success = await subscriptionService.changePlan(plan.priceId);
        if (success) {
          await loadUserData(); // Refresh data
        } else {
          setError('Failed to change subscription plan');
        }
      }
    } catch (err) {
      console.error('Error changing plan:', err);
      setError('Failed to change subscription plan');
    } finally {
      setLoading(false);
    }
  };

  const handleManageBilling = async () => {
    try {
      setLoading(true);
      setError(null);

      const portalUrl = await subscriptionService.createPortalSession();
      if (portalUrl) {
        window.open(portalUrl, '_blank');
      } else {
        setError('Failed to open billing portal');
      }
    } catch (err) {
      console.error('Error opening billing portal:', err);
      setError('Failed to open billing portal');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will still have access until the end of your current billing period.')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const success = await subscriptionService.cancelSubscription();
      if (success) {
        await loadUserData(); // Refresh data
      } else {
        setError('Failed to cancel subscription');
      }
    } catch (err) {
      console.error('Error canceling subscription:', err);
      setError('Failed to cancel subscription');
    } finally {
      setLoading(false);
    }
  };

  const handleReactivateSubscription = async () => {
    try {
      setLoading(true);
      setError(null);

      const success = await subscriptionService.reactivateSubscription();
      if (success) {
        await loadUserData(); // Refresh data
      } else {
        setError('Failed to reactivate subscription');
      }
    } catch (err) {
      console.error('Error reactivating subscription:', err);
      setError('Failed to reactivate subscription');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  const getCurrentPlanId = () => {
    if (user?.plan === 'free') return 'free';
    if (!subscription) return 'free';

    // Determine current plan based on subscription price ID
    const currentPriceId = subscription.priceId;
    const monthlyPlan = availablePlans.find(p => p.id === 'monthly');
    const yearlyPlan = availablePlans.find(p => p.id === 'yearly');

    if (currentPriceId === monthlyPlan?.priceId) return 'monthly';
    if (currentPriceId === yearlyPlan?.priceId) return 'yearly';
    return 'free';
  };

  if (!user) {
    return (
      <div className={`p-4 ${className}`}>
        <p className="text-center text-gray-600">Please log in to manage your subscription</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="notely-card rounded-lg p-6 shadow-sm" style={{ backgroundColor: 'var(--notely-card)', color: 'var(--notely-text-primary)' }}>
        <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--notely-text-primary)' }}>Subscription & Billing</h3>
        
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4">
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          {/* Current Plan */}
          <div>
            <label className="block text-sm font-medium" style={{ color: 'var(--notely-text-secondary)' }}>
              Current Plan
            </label>
            <div className="mt-1 flex items-center justify-between">
              <div>
                <p className="text-sm capitalize" style={{ color: 'var(--notely-text-primary)' }}>
                  {user.plan} {user.subscriptionStatus && `(${user.subscriptionStatus})`}
                </p>
                {subscription && subscription.cancelAtPeriodEnd && (
                  <p className="text-xs text-orange-600 dark:text-orange-400">
                    Cancels on {formatDate(subscription.currentPeriodEnd)}
                  </p>
                )}
              </div>
              
              {user.plan === 'free' && availablePlans.length > 0 && (
                <button
                  onClick={() => {
                    // Find the monthly plan as default upgrade option
                    const monthlyPlan = availablePlans.find(p => p.id === 'monthly');
                    if (monthlyPlan) {
                      handlePlanChange(monthlyPlan);
                    }
                  }}
                  disabled={loading}
                  className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white text-sm font-medium rounded-md px-4 py-2 hover:from-purple-600 hover:to-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Loading...' : 'Upgrade to Premium'}
                </button>
              )}
            </div>
          </div>

          {/* Premium Plan Details */}
          {user.plan === 'premium' && subscription && (
            <div className="border-t pt-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Status:</span>
                  <span className="ml-2 capitalize">{subscription.status}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Next billing:</span>
                  <span className="ml-2">{formatDate(subscription.currentPeriodEnd)}</span>
                </div>
              </div>
              
              <div className="mt-4 flex space-x-3">
                <button
                  onClick={handleManageBilling}
                  disabled={loading}
                  className="bg-blue-600 text-white text-sm font-medium rounded-md px-4 py-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Loading...' : 'Manage Billing'}
                </button>
                
                {subscription.cancelAtPeriodEnd ? (
                  <button
                    onClick={handleReactivateSubscription}
                    disabled={loading}
                    className="bg-green-600 text-white text-sm font-medium rounded-md px-4 py-2 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Loading...' : 'Reactivate'}
                  </button>
                ) : (
                  <button
                    onClick={handleCancelSubscription}
                    disabled={loading}
                    className="bg-red-600 text-white text-sm font-medium rounded-md px-4 py-2 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Loading...' : 'Cancel Subscription'}
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Available Plans */}
          {availablePlans.length > 0 && (
            <div className="border-t pt-4" style={{ borderColor: 'var(--notely-border)' }}>
              <h4 className="text-sm font-medium mb-4" style={{ color: 'var(--notely-text-secondary)' }}>
                Available Plans
              </h4>
              <div className="space-y-3">
                {availablePlans.map((plan) => {
                  const isCurrentPlan = getCurrentPlanId() === plan.id;
                  const isDowngrade = plan.id === 'free' && user.plan === 'premium';
                  const isUpgrade = plan.id !== 'free' && user.plan === 'free';
                  const isPlanChange = plan.id !== 'free' && user.plan === 'premium' && !isCurrentPlan;

                  return (
                    <div
                      key={plan.id}
                      className={`border rounded-lg p-4`}
                      style={{
                        backgroundColor: isCurrentPlan ? 'var(--notely-surface)' : 'var(--notely-card)',
                        borderColor: isCurrentPlan ? 'var(--notely-accent)' : 'var(--notely-border)',
                        color: 'var(--notely-text-primary)'
                      }}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h5 className="font-medium" style={{ color: 'var(--notely-text-primary)' }}>
                              {plan.name}
                            </h5>
                            {isCurrentPlan && (
                              <span className="text-xs px-2 py-1 rounded-full" style={{ 
                                backgroundColor: 'var(--notely-accent)',
                                color: 'var(--notely-text-inverse)'
                              }}>
                                Current
                              </span>
                            )}
                          </div>
                          <p className="text-lg font-semibold mt-1" style={{ color: 'var(--notely-text-primary)' }}>
                            {plan.price === 0 ? 'Free' : `$${plan.price}${plan.interval ? `/${plan.interval}` : ''}`}
                          </p>
                          <ul className="text-sm mt-2 space-y-1" style={{ color: 'var(--notely-text-secondary)' }}>
                            {plan.features.map((feature: string, index: number) => (
                              <li key={index} className="flex items-center">
                                <span className="mr-2 text-green-500">✓</span>
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {!isCurrentPlan && (
                          <button
                            onClick={() => handlePlanChange(plan)}
                            disabled={loading}
                            className={`ml-4 px-4 py-2 text-sm font-medium rounded-md disabled:opacity-50 disabled:cursor-not-allowed ${
                              isDowngrade
                                ? 'bg-red-600 hover:bg-red-700'
                                : isUpgrade
                                ? 'bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600'
                                : 'bg-blue-600 hover:bg-blue-700'
                            }`}
                            style={{ color: 'white' }}
                          >
                            {loading ? 'Loading...' :
                             isDowngrade ? 'Downgrade' :
                             isUpgrade ? 'Upgrade' :
                             'Switch'}
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionSettings;
