import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

interface User {
  _id: string;
  email: string;
  name: string;
  plan: 'free' | 'premium';
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  adsDisabled: boolean;
  createdAt: string;
  lastLogin: string;
}

interface UsageStats {
  aiTokens: { total: number; byOperation: Record<string, number> };
  storage: { totalMB: number; byOperation: Record<string, number> };
}

export const AdminDashboard: React.FC = () => {
  const { user, getToken } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    subscriptionStatus: '',
    plan: '',
    search: '',
  });
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userStats, setUserStats] = useState<UsageStats | null>(null);

  useEffect(() => {
    console.log('[ADMIN DEBUG] AdminDashboard useEffect - user:', { email: user?.email, role: user?.role, isAdmin: user?.role === 'admin' });
    if (user?.role === 'admin') {
      fetchUsers();
    }
  }, [user, filters]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await fetch(`/admin/users?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      } else {
        throw new Error('Failed to fetch users');
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserStats = async (userId: string) => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}?days=30`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserStats(data.usage);
      }
    } catch (err) {
      console.error('Error fetching user stats:', err);
    }
  };

  const toggleUserAds = async (userId: string, adsDisabled: boolean) => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/ads`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ adsDisabled }),
      });

      if (response.ok) {
        await fetchUsers();
      } else {
        throw new Error('Failed to update ads setting');
      }
    } catch (err) {
      console.error('Error updating ads:', err);
      setError('Failed to update ads setting');
    }
  };

  const sendWarningEmail = async (userId: string, type: 'storage' | 'tokens') => {
    try {
      const token = await getToken();
      const response = await fetch(`/admin/users/${userId}/warn`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          type, 
          message: `Your ${type} usage is approaching the limit.` 
        }),
      });

      if (response.ok) {
        alert(`Warning email sent for ${type} usage`);
      } else {
        throw new Error('Failed to send warning email');
      }
    } catch (err) {
      console.error('Error sending warning:', err);
      setError('Failed to send warning email');
    }
  };

  if (user?.role !== 'admin') {
    console.log('[ADMIN DEBUG] AdminDashboard access denied - user:', { email: user?.email, role: user?.role });
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">Access denied. Admin privileges required.</p>
          <p className="text-red-600 text-sm mt-2">Current user: {user?.email || 'Not logged in'} | Role: {user?.role || 'None'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Admin Dashboard</h1>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <h2 className="text-lg font-semibold mb-4">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Subscription Status
            </label>
            <select
              value={filters.subscriptionStatus}
              onChange={(e) => setFilters(prev => ({ ...prev, subscriptionStatus: e.target.value }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="">All</option>
              <option value="trialing">Trialing</option>
              <option value="active">Active</option>
              <option value="past_due">Past Due</option>
              <option value="canceled">Canceled</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Plan
            </label>
            <select
              value={filters.plan}
              onChange={(e) => setFilters(prev => ({ ...prev, plan: e.target.value }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="">All</option>
              <option value="free">Free</option>
              <option value="premium">Premium</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Email or name..."
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <h2 className="text-lg font-semibold p-4 border-b">Users</h2>
        
        {loading ? (
          <div className="p-4">Loading...</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Plan</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ads</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user._id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm">{user.email}</td>
                    <td className="px-4 py-3 text-sm capitalize">{user.plan}</td>
                    <td className="px-4 py-3 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        user.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :
                        user.subscriptionStatus === 'trialing' ? 'bg-blue-100 text-blue-800' :
                        user.subscriptionStatus === 'past_due' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {user.subscriptionStatus}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <button
                        onClick={() => toggleUserAds(user._id, !user.adsDisabled)}
                        className={`px-2 py-1 rounded text-xs ${
                          user.adsDisabled 
                            ? 'bg-red-100 text-red-800' 
                            : 'bg-green-100 text-green-800'
                        }`}
                      >
                        {user.adsDisabled ? 'Disabled' : 'Enabled'}
                      </button>
                    </td>
                    <td className="px-4 py-3 text-sm space-x-2">
                      <button
                        onClick={() => {
                          setSelectedUser(user);
                          fetchUserStats(user._id);
                        }}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        View Stats
                      </button>
                      <button
                        onClick={() => sendWarningEmail(user._id, 'storage')}
                        className="text-orange-600 hover:text-orange-800"
                      >
                        Warn Storage
                      </button>
                      <button
                        onClick={() => sendWarningEmail(user._id, 'tokens')}
                        className="text-purple-600 hover:text-purple-800"
                      >
                        Warn Tokens
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* User Stats Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">
              Usage Stats: {selectedUser.email}
            </h3>
            
            {userStats && (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">AI Token Usage (30 days)</h4>
                  <p className="text-sm text-gray-600">Total: {userStats.aiTokens.total}</p>
                  {Object.entries(userStats.aiTokens.byOperation).map(([op, count]) => (
                    <p key={op} className="text-xs text-gray-500">
                      {op}: {count}
                    </p>
                  ))}
                </div>
                
                <div>
                  <h4 className="font-medium">Storage Usage (30 days)</h4>
                  <p className="text-sm text-gray-600">Total: {userStats.storage.totalMB.toFixed(2)} MB</p>
                  {Object.entries(userStats.storage.byOperation).map(([op, mb]) => (
                    <p key={op} className="text-xs text-gray-500">
                      {op}: {mb.toFixed(2)} MB
                    </p>
                  ))}
                </div>
              </div>
            )}
            
            <button
              onClick={() => {
                setSelectedUser(null);
                setUserStats(null);
              }}
              className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
