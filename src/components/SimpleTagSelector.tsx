import React from 'react';
import { formatForDisplay } from '../utils/formatUtils';

interface SimpleTagSelectorProps {
  tags: string[];
  selectedTag: string | null;
  onTagSelect: (tag: string | null) => void;
  className?: string;
}

const SimpleTagSelector: React.FC<SimpleTagSelectorProps> = ({
  tags,
  selectedTag,
  onTagSelect,
  className = ''
}) => {
  if (tags.length === 0) {
    return null;
  }

  const getButtonStyles = (isSelected: boolean) => {
    const baseStyles = 'ui-tag-chip transition-all duration-200 focus:outline-none ui-focus-visible';

    if (isSelected) {
      return `${baseStyles} active`;
    }

    return baseStyles;
  };

  return (
    <div className={`flex flex-wrap gap-1.5 py-2 ${className}`}>
      {tags.map((tag, index) => (
        <button
          key={tag}
          onClick={() => onTagSelect(selectedTag === tag ? null : tag)}
          className={`${getButtonStyles(selectedTag === tag)} notely-tag-button`}
          style={{ animationDelay: `${index * 0.05 + 0.1}s` }}
        >
          {formatForDisplay(tag)}
        </button>
      ))}
    </div>
  );
};

export default SimpleTagSelector;
