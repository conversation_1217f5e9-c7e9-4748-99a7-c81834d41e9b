import React, { useState } from 'react';

interface SidebarProps {
  user?: {
    name?: string;
    profilePicture?: string;
  };
  onNavigate: (section: string) => void;
  activeSection: string;
  newWisdomCount?: number;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  user, 
  onNavigate, 
  activeSection,
  newWisdomCount = 0
}) => {
  const [isAISectionCollapsed, setIsAISectionCollapsed] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const toggleAISection = () => {
    setIsAISectionCollapsed(!isAISectionCollapsed);
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const getNavItemClasses = (section: string) => {
    return `flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
      activeSection === section
        ? 'bg-blue-50/80 text-blue-700 shadow-sm'
        : 'hover:bg-gray-100/80 text-gray-700 hover:text-gray-900'
    }`;
  };

  return (
    <aside
      className={`bg-notely-surface/95 backdrop-blur-md border-r border-notely-border/20 h-screen transition-all duration-300 flex flex-col
                 ${isSidebarCollapsed ? 'w-16' : 'w-64'}
                 shadow-[0_4px_30px_rgba(0,0,0,0.1)]`}
    >
      {/* Brand Header */}
      <div className="flex items-center justify-between p-4 border-b border-notely-border/20">
        {!isSidebarCollapsed ? (
          <div className="flex items-center space-x-2">
            <img src="/notely.svg" alt="Notely" className="w-8 h-8" />
            <h1 className="text-xl font-semibold text-gray-800">Notely</h1>
          </div>
        ) : (
          <img src="/notely.svg" alt="Notely" className="w-8 h-8 mx-auto" />
        )}
        <button 
          onClick={toggleSidebar} 
          className="text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          {isSidebarCollapsed ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        <div className="mb-6">
          <button 
            className={getNavItemClasses('discover')}
            onClick={() => onNavigate('discover')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
            </svg>
            {!isSidebarCollapsed && <span>Discover</span>}
          </button>
          
          <button 
            className={getNavItemClasses('popular')}
            onClick={() => onNavigate('popular')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
            </svg>
            {!isSidebarCollapsed && <span>Popular</span>}
          </button>
          
          <button 
            className={getNavItemClasses('bookmarked')}
            onClick={() => onNavigate('bookmarked')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
            </svg>
            {!isSidebarCollapsed && <span>Bookmarked</span>}
          </button>
          
          <button 
            className={getNavItemClasses('readLater')}
            onClick={() => onNavigate('readLater')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            {!isSidebarCollapsed && <span>Read Later</span>}
          </button>
        </div>

        {/* AI Insights Section */}
        <div className="pt-4 border-t border-notely-border/20">
          {!isSidebarCollapsed && (
            <div className="flex items-center justify-between mb-2 px-3">
              <h2 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">AI Insights</h2>
              <button 
                onClick={toggleAISection}
                className="text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                {isAISectionCollapsed ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            </div>
          )}

          {(!isAISectionCollapsed || isSidebarCollapsed) && (
            <div className="space-y-1">
              <button 
                className={getNavItemClasses('dailyWisdom')}
                onClick={() => onNavigate('dailyWisdom')}
              >
                <div className="relative">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                  {newWisdomCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                      {newWisdomCount}
                    </span>
                  )}
                </div>
                {!isSidebarCollapsed && <span>Daily Wisdom</span>}
              </button>
              
              <button 
                className={getNavItemClasses('trends')}
                onClick={() => onNavigate('trends')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                </svg>
                {!isSidebarCollapsed && <span>Trends & Analytics</span>}
              </button>
              
              <button 
                className={getNavItemClasses('categories')}
                onClick={() => onNavigate('categories')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                </svg>
                {!isSidebarCollapsed && <span>Categories</span>}
              </button>
            </div>
          )}
        </div>
      </nav>

      {/* User Profile Section */}
      <div className="p-4 border-t border-gray-200/50">
        <div className={`flex ${isSidebarCollapsed ? 'justify-center' : 'items-center space-x-3'}`}>
          {user?.profilePicture ? (
            <img 
              src={user.profilePicture} 
              alt={user.name || 'User'} 
              className="w-8 h-8 rounded-full object-cover border border-gray-300" 
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
            </div>
          )}
          
          {!isSidebarCollapsed && (
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-800 truncate">{user?.name || 'Guest User'}</p>
              <button 
                className={getNavItemClasses('settings')}
                onClick={() => onNavigate('settings')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
                <span>Settings</span>
              </button>
              
              <button 
                className={getNavItemClasses('logout')}
                onClick={() => onNavigate('logout')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V4a1 1 0 00-1-1H3zm9 4a1 1 0 10-2 0v4.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 10-1.414-1.414L12 11.586V7z" clipRule="evenodd" />
                </svg>
                <span>Log Out</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
