import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Post } from '../types';
import Widget from './Widget';
import EmojiPicker from './EmojiPicker';
import { extractAIBookmarks } from '../services/aiService';
import { formatForDisplay } from '../utils/formatUtils';
import { useTranslation } from '../hooks/useTranslation';
import { extractUrlsFromContent, fetchWebsiteMetadata, normalizeUrl, isDuplicateUrl } from '../services/urlExtractionService';

interface SavedBookmarksWidgetProps {
  posts: Post[];
  isDragging: boolean;
  onRemove: () => void;
}

interface BookmarkItem {
  id: string;
  url: string;
  domain: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  favicon?: string;
  post: Post;
}

// Storage keys
const PROCESSED_POSTS_KEY = 'bookmark_processed_posts';
const MANUAL_BOOKMARKS_KEY = 'manual_bookmarks';
const CUSTOM_CATEGORIES_KEY = 'custom_categories';
const NEW_POSTS_THRESHOLD = 10; // Process bookmarks when we have 10 new posts

// Helper functions for managing processed posts tracking
const getProcessedPosts = async (): Promise<Set<string>> => {
  try {
    const result = await chrome.storage.local.get([PROCESSED_POSTS_KEY]);
    const processedIds = result[PROCESSED_POSTS_KEY] || [];
    return new Set(processedIds);
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error getting processed posts:', error);
    return new Set();
  }
};

const saveProcessedPosts = async (processedIds: Set<string>): Promise<void> => {
  try {
    await chrome.storage.local.set({ [PROCESSED_POSTS_KEY]: Array.from(processedIds) });
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error saving processed posts:', error);
  }
};

// Helper functions for managing manual bookmarks
const getManualBookmarks = async (): Promise<BookmarkItem[]> => {
  try {
    const result = await chrome.storage.local.get([MANUAL_BOOKMARKS_KEY]);
    return result[MANUAL_BOOKMARKS_KEY] || [];
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error getting manual bookmarks:', error);
    return [];
  }
};

const saveManualBookmarks = async (bookmarks: BookmarkItem[]): Promise<void> => {
  try {
    await chrome.storage.local.set({ [MANUAL_BOOKMARKS_KEY]: bookmarks });
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error saving manual bookmarks:', error);
  }
};

// Helper functions for managing custom categories
const getCustomCategories = async (): Promise<Array<{ name: string; emoji: string; tags: string[] }>> => {
  try {
    const result = await chrome.storage.local.get([CUSTOM_CATEGORIES_KEY]);
    return result[CUSTOM_CATEGORIES_KEY] || [];
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error getting custom categories:', error);
    return [];
  }
};

const saveCustomCategories = async (categories: Array<{ name: string; emoji: string; tags: string[] }>): Promise<void> => {
  try {
    await chrome.storage.local.set({ [CUSTOM_CATEGORIES_KEY]: categories });
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error saving custom categories:', error);
  }
};

const SavedBookmarksWidget: React.FC<SavedBookmarksWidgetProps> = ({
  posts,
  isDragging,
  onRemove
}) => {
  const { t } = useTranslation();
  const [activeCategory, setActiveCategory] = useState('Current Affairs');
  const [activeTag, setActiveTag] = useState('All');
  const [aiBookmarks, setAiBookmarks] = useState<BookmarkItem[]>([]);
  const [manualBookmarks, setManualBookmarks] = useState<BookmarkItem[]>([]);
  const [isLoadingBookmarks, setIsLoadingBookmarks] = useState(false);
  const [processedPostIds, setProcessedPostIds] = useState<Set<string>>(new Set());
  const [newPostsCount, setNewPostsCount] = useState(0);

  // UI state for modals and forms
  const [showAddBookmarkModal, setShowAddBookmarkModal] = useState(false);
  const [showEditCategoriesModal, setShowEditCategoriesModal] = useState(false);
  const [customCategories, setCustomCategories] = useState<Array<{ name: string; emoji: string; tags: string[] }>>([]);

  // Category editing state
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editCategoryForm, setEditCategoryForm] = useState({
    name: '',
    emoji: '',
    tags: [] as string[]
  });
  const [newTagInput, setNewTagInput] = useState('');

  // New category form state
  const [newCategoryForm, setNewCategoryForm] = useState({
    name: '',
    emoji: '📚',
    tags: ''
  });

  // Form state for adding bookmarks
  const [newBookmarkForm, setNewBookmarkForm] = useState({
    url: '',
    title: '',
    description: '',
    category: 'Work & Knowledge',
    tags: [] as string[]
  });

  // Form validation and error state
  const [formErrors, setFormErrors] = useState({
    url: '',
    title: ''
  });

  // Default categories - can be extended with custom ones
  const defaultCategories = [
    { name: 'Current Affairs', emoji: '📢', tags: ['News', 'Politics', 'World', 'Economy'] },
    { name: 'Work & Knowledge', emoji: '💼', tags: ['Tools', 'Business', 'Education', 'Technology'] },
    { name: 'Social Trends', emoji: '📱', tags: ['Platforms', 'Entertainment', 'Culture'] },
    { name: 'Lifestyle & Opinions', emoji: '💬', tags: ['Health', 'Food', 'Travel', 'Personal'] }
  ];

  // Combine default and custom categories
  const categories = useMemo(() => {
    return [...defaultCategories, ...customCategories];
  }, [customCategories]);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      // Load processed posts
      const processed = await getProcessedPosts();
      setProcessedPostIds(processed);
      console.log(`[SavedBookmarksWidget] Loaded ${processed.size} previously processed posts`);

      // Load manual bookmarks
      const manualBMs = await getManualBookmarks();
      setManualBookmarks(manualBMs);
      console.log(`[SavedBookmarksWidget] Loaded ${manualBMs.length} manual bookmarks`);

      // Load custom categories
      const customCats = await getCustomCategories();
      setCustomCategories(customCats);
      console.log(`[SavedBookmarksWidget] Loaded ${customCats.length} custom categories`);
    };
    loadData();
  }, []);

  // Track new posts and trigger AI processing when threshold is reached
  useEffect(() => {
    if (posts.length === 0 || processedPostIds.size === 0) return;

    // Find new posts that haven't been processed yet
    const newPosts = posts.filter(post =>
      post.id &&
      post.content &&
      post.content.length >= 20 &&
      !processedPostIds.has(post.id)
    );

    setNewPostsCount(newPosts.length);
    console.log(`[SavedBookmarksWidget] Found ${newPosts.length} new unprocessed posts (threshold: ${NEW_POSTS_THRESHOLD})`);

    // Only process if we have enough new posts
    if (newPosts.length >= NEW_POSTS_THRESHOLD) {
      console.log(`[SavedBookmarksWidget] Threshold reached! Processing ${newPosts.length} new posts for bookmark extraction`);
      processNewPostsForBookmarks(newPosts);
    }
  }, [posts, processedPostIds]);

  // Function to process new posts for bookmark extraction
  const processNewPostsForBookmarks = useCallback(async (newPosts: Post[]) => {
    if (newPosts.length === 0) return;

    setIsLoadingBookmarks(true);
    const extractedBookmarks: BookmarkItem[] = [];
    const newProcessedIds = new Set(processedPostIds);

    try {
      console.log(`[SavedBookmarksWidget] 🔗 Processing ${newPosts.length} posts for URL and AI bookmark extraction`);
      console.log(`[SavedBookmarksWidget] 📊 Current bookmarks: ${aiBookmarks.length} AI + ${manualBookmarks.length} manual`);

      // Get existing bookmark URLs to prevent duplicates
      const existingUrls = [...aiBookmarks, ...manualBookmarks].map(b => b.url);

      // Process new posts for bookmark extraction
      for (const post of newPosts) {
        if (!post.content || post.content.length < 20) continue;

        try {
          // 1. First extract URLs directly from content
          const urlCandidates = await extractBookmarkCandidates(post.content);
          console.log(`[SavedBookmarksWidget] Found ${urlCandidates.length} URL candidates in post ${post.id}`);

          // Process URL candidates
          for (const candidate of urlCandidates) {
            // Skip if duplicate
            if (isDuplicateUrl(candidate.normalizedUrl, existingUrls)) {
              console.log(`[SavedBookmarksWidget] Skipping duplicate URL: ${candidate.domain}`);
              continue;
            }

            // Auto-categorize based on content and metadata
            const category = await categorizeBookmark(candidate, post);
            const tags = generateBookmarkTags(candidate, post);

            extractedBookmarks.push({
              id: `auto-${post.id}-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
              url: candidate.normalizedUrl,
              domain: candidate.domain,
              title: candidate.title,
              description: candidate.description,
              category: category,
              tags: [...tags, 'Auto'], // Add 'Auto' tag to distinguish from manual
              favicon: candidate.favicon,
              post
            });

            // Add to existing URLs to prevent duplicates within this batch
            existingUrls.push(candidate.normalizedUrl);
          }

          // 2. Also run AI extraction for additional context (but filter out duplicates)
          const aiResults = await extractAIBookmarks(post.content);

          aiResults.forEach((aiBookmark, index) => {
            // Skip if duplicate
            if (isDuplicateUrl(aiBookmark.url, existingUrls)) {
              console.log(`[SavedBookmarksWidget] Skipping duplicate AI URL: ${aiBookmark.url}`);
              return;
            }

            // Map AI categories to our widget categories (including custom ones)
            let widgetCategory = 'Lifestyle & Opinions';
            let tags = ['Personal'];

            // First check custom categories for matches
            const customMatch = customCategories.find(cat =>
              cat.tags.some(tag =>
                tag.toLowerCase() === aiBookmark.category.toLowerCase() ||
                aiBookmark.category.toLowerCase().includes(tag.toLowerCase())
              )
            );

            if (customMatch) {
              widgetCategory = customMatch.name;
              tags = [aiBookmark.category, 'AI']; // Use the AI category as tag + AI tag
            } else {
              // Fallback to default category mapping
              switch (aiBookmark.category) {
                case 'News':
                  widgetCategory = 'Current Affairs';
                  tags = ['News', 'AI'];
                  break;
                case 'Business':
                  widgetCategory = 'Work & Knowledge';
                  tags = ['Business', 'AI'];
                  break;
                case 'Education':
                  widgetCategory = 'Work & Knowledge';
                  tags = ['Education', 'AI'];
                  break;
                case 'Technology':
                case 'Tools':
                  widgetCategory = 'Work & Knowledge';
                  tags = ['Tools', 'Technology', 'AI'];
                  break;
                case 'Platforms':
                  widgetCategory = 'Social Trends';
                  tags = ['Platforms', 'AI'];
                  break;
                case 'Entertainment':
                  widgetCategory = 'Social Trends';
                  tags = ['Entertainment', 'AI'];
                  break;
                default:
                  tags = [aiBookmark.category, 'AI'];
              }
            }

            try {
              const domain = new URL(aiBookmark.url).hostname.replace('www.', '');
              extractedBookmarks.push({
                id: `ai-${post.id}-${index}`,
                url: aiBookmark.url,
                domain,
                title: aiBookmark.name,
                description: aiBookmark.description,
                category: widgetCategory,
                tags,
                favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=16`,
                post
              });

              // Add to existing URLs to prevent duplicates within this batch
              existingUrls.push(aiBookmark.url);
            } catch (error) {
              console.warn(`[SavedBookmarksWidget] Invalid AI bookmark URL: ${aiBookmark.url}`);
            }
          });

          // Mark this post as processed
          newProcessedIds.add(post.id);
        } catch (error) {
          console.error(`Error extracting bookmarks from post ${post.id}:`, error);
          // Still mark as processed to avoid retrying failed posts
          newProcessedIds.add(post.id);
        }
      }



      // Add new bookmarks to existing ones (incremental update)
      setAiBookmarks(prevBookmarks => [...prevBookmarks, ...extractedBookmarks]);

      // Update processed posts tracking
      setProcessedPostIds(newProcessedIds);
      await saveProcessedPosts(newProcessedIds);

      // Reset new posts count
      setNewPostsCount(0);

    } catch (error) {
      console.error('Error in AI bookmark extraction:', error);
    } finally {
      setIsLoadingBookmarks(false);
    }
  }, [processedPostIds]);

  // Function to handle manual processing of new posts
  const handleProcessNow = useCallback(() => {
    const newPosts = posts.filter(post =>
      post.id &&
      post.content &&
      post.content.length >= 20 &&
      !processedPostIds.has(post.id)
    );
    if (newPosts.length > 0) {
      processNewPostsForBookmarks(newPosts);
    }
  }, [posts, processedPostIds, processNewPostsForBookmarks]);

  // Manual bookmark management functions
  // Helper function to normalize and validate URLs
  const normalizeUrl = (input: string): string => {
    if (!input.trim()) return '';

    let url = input.trim();

    // Remove any leading/trailing whitespace
    url = url.trim();

    // If it already has a protocol, use it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If it starts with www., add https://
    if (url.startsWith('www.')) {
      return `https://${url}`;
    }

    // If it looks like a domain (contains a dot), add https://
    if (url.includes('.') && !url.includes(' ')) {
      return `https://${url}`;
    }

    // Otherwise, assume it needs https:// prefix
    return `https://${url}`;
  };

  const addManualBookmark = useCallback(async (bookmarkData: {
    url: string;
    title: string;
    description: string;
    category: string;
    tags: string[];
  }) => {
    try {
      // Normalize the URL
      const normalizedUrl = normalizeUrl(bookmarkData.url);

      // Validate the normalized URL
      let urlObj: URL;
      try {
        urlObj = new URL(normalizedUrl);
      } catch (error) {
        throw new Error('Invalid URL format');
      }

      const newBookmark: BookmarkItem = {
        id: `manual-${Date.now()}`,
        url: normalizedUrl,
        domain: urlObj.hostname.replace('www.', ''),
        title: bookmarkData.title,
        description: bookmarkData.description,
        category: bookmarkData.category,
        tags: bookmarkData.tags,
        favicon: `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`,
        post: { id: 'manual', platform: 'Manual', content: 'Manually added bookmark' } as any
      };

      const updatedBookmarks = [...manualBookmarks, newBookmark];
      setManualBookmarks(updatedBookmarks);
      await saveManualBookmarks(updatedBookmarks);

      console.log('[SavedBookmarksWidget] Added manual bookmark:', newBookmark.title);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error adding manual bookmark:', error);
      throw error; // Re-throw to handle in the form
    }
  }, [manualBookmarks]);

  const removeBookmark = useCallback(async (bookmarkId: string) => {
    try {
      // Remove from manual bookmarks if it's a manual bookmark
      if (bookmarkId.startsWith('manual-')) {
        const updatedManualBookmarks = manualBookmarks.filter(b => b.id !== bookmarkId);
        setManualBookmarks(updatedManualBookmarks);
        await saveManualBookmarks(updatedManualBookmarks);
      } else {
        // Remove from AI bookmarks
        const updatedAiBookmarks = aiBookmarks.filter(b => b.id !== bookmarkId);
        setAiBookmarks(updatedAiBookmarks);
      }
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error removing bookmark:', error);
    }
  }, [manualBookmarks, aiBookmarks]);

  const addCustomCategory = useCallback(async (categoryData: {
    name: string;
    emoji: string;
    tags: string[];
  }) => {
    try {
      const updatedCategories = [...customCategories, categoryData];
      setCustomCategories(updatedCategories);
      await saveCustomCategories(updatedCategories);
      console.log('[SavedBookmarksWidget] Added custom category:', categoryData.name);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error adding custom category:', error);
    }
  }, [customCategories]);

  const removeCustomCategory = useCallback(async (categoryName: string) => {
    try {
      const updatedCategories = customCategories.filter(c => c.name !== categoryName);
      setCustomCategories(updatedCategories);
      await saveCustomCategories(updatedCategories);
      console.log('[SavedBookmarksWidget] Removed custom category:', categoryName);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error removing custom category:', error);
    }
  }, [customCategories]);

  const updateCustomCategory = useCallback(async (oldName: string, categoryData: {
    name: string;
    emoji: string;
    tags: string[];
  }) => {
    try {
      const updatedCategories = customCategories.map(c =>
        c.name === oldName ? categoryData : c
      );
      setCustomCategories(updatedCategories);
      await saveCustomCategories(updatedCategories);
      console.log('[SavedBookmarksWidget] Updated custom category:', oldName, '->', categoryData.name);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error updating custom category:', error);
    }
  }, [customCategories]);

  const startEditingCategory = useCallback((category: { name: string; emoji: string; tags: string[] }) => {
    setEditingCategory(category.name);
    setEditCategoryForm({
      name: category.name,
      emoji: category.emoji,
      tags: [...category.tags]
    });
  }, []);

  const cancelEditingCategory = useCallback(() => {
    setEditingCategory(null);
    setEditCategoryForm({ name: '', emoji: '', tags: [] });
    setNewTagInput('');
  }, []);

  const saveEditingCategory = useCallback(async () => {
    if (!editingCategory || !editCategoryForm.name || !editCategoryForm.emoji || editCategoryForm.tags.length === 0) {
      return;
    }

    await updateCustomCategory(editingCategory, editCategoryForm);
    cancelEditingCategory();
  }, [editingCategory, editCategoryForm, updateCustomCategory, cancelEditingCategory]);

  const addTagToEditingCategory = useCallback(() => {
    const trimmedTag = newTagInput.trim();
    if (trimmedTag && !editCategoryForm.tags.includes(trimmedTag)) {
      setEditCategoryForm(prev => ({
        ...prev,
        tags: [...prev.tags, trimmedTag]
      }));
      setNewTagInput('');
    }
  }, [newTagInput, editCategoryForm.tags]);

  const removeTagFromEditingCategory = useCallback((tagToRemove: string) => {
    setEditCategoryForm(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  }, []);

  const handleNewCategorySubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    const { name, emoji, tags } = newCategoryForm;
    const tagArray = tags.split(',').map(t => t.trim()).filter(t => t);

    if (name && emoji && tagArray.length > 0) {
      await addCustomCategory({ name, emoji, tags: tagArray });
      // Reset form
      setNewCategoryForm({ name: '', emoji: '📚', tags: '' });
    }
  }, [newCategoryForm, addCustomCategory]);

  // URL validation and preview functions
  const validateUrl = (input: string): { isValid: boolean; normalizedUrl: string; error: string } => {
    if (!input.trim()) {
      return { isValid: false, normalizedUrl: '', error: 'URL is required' };
    }

    try {
      const normalizedUrl = normalizeUrl(input);
      new URL(normalizedUrl); // This will throw if invalid
      return { isValid: true, normalizedUrl, error: '' };
    } catch (error) {
      return { isValid: false, normalizedUrl: '', error: 'Please enter a valid URL (e.g., google.com or https://google.com)' };
    }
  };

  // Handle URL input change with real-time validation
  const handleUrlChange = (value: string) => {
    setNewBookmarkForm(prev => ({ ...prev, url: value }));

    // Clear previous errors
    setFormErrors(prev => ({ ...prev, url: '' }));

    // Validate if user has typed something
    if (value.trim()) {
      const validation = validateUrl(value);
      if (!validation.isValid) {
        setFormErrors(prev => ({ ...prev, url: validation.error }));
      }
    }
  };

  // Combine AI-extracted bookmarks and manual bookmarks
  const bookmarks = useMemo(() => {
    // Combine all bookmarks
    const allBookmarks = [...manualBookmarks, ...aiBookmarks];

    // Add test data if no real bookmarks found and not loading
    if (allBookmarks.length === 0 && !isLoadingBookmarks && posts.length === 0) {
      console.log('[SavedBookmarksWidget] No bookmarks found, adding test data');
      return [
        {
          id: 'test-1',
          url: 'https://bolt.new',
          domain: 'bolt.new',
          title: 'Bolt.new',
          description: 'AI-powered web development platform for rapid prototyping.',
          category: 'Work & Knowledge',
          tags: ['Tools'],
          favicon: 'https://www.google.com/s2/favicons?domain=bolt.new&sz=16',
          post: { id: 'test', platform: 'X/Twitter', content: 'Check out bolt.new for AI web development!' } as any
        },
        {
          id: 'test-2',
          url: 'https://github.com',
          domain: 'github.com',
          title: 'GitHub',
          description: 'Code repositories and developer collaboration platform.',
          category: 'Work & Knowledge',
          tags: ['Tools', 'Technology'],
          favicon: 'https://www.google.com/s2/favicons?domain=github.com&sz=16',
          post: { id: 'test2', platform: 'LinkedIn', content: 'GitHub is essential for developers' } as any
        }
      ];
    }

    return allBookmarks;
  }, [aiBookmarks, manualBookmarks, isLoadingBookmarks, posts.length]);

  // Filter bookmarks by active category and tag
  const filteredBookmarks = useMemo(() => {
    let filtered = bookmarks.filter(bookmark => bookmark.category === activeCategory);

    if (activeTag !== 'All') {
      filtered = filtered.filter(bookmark => bookmark.tags.includes(activeTag));
    }

    return filtered.slice(0, 6); // Limit to 6 items
  }, [bookmarks, activeCategory, activeTag]);

  // Get tag options from both category defaults and actual bookmarks in this category
  const activeTagOptions = useMemo(() => {
    const categoryTags = categories.find(cat => cat.name === activeCategory)?.tags || [];
    const bookmarkTags = bookmarks
      .filter(bookmark => bookmark.category === activeCategory)
      .flatMap(bookmark => bookmark.tags || []);

    // Combine and deduplicate tags
    const allTags = [...new Set([...categoryTags, ...bookmarkTags])];
    return allTags.sort();
  }, [categories, bookmarks, activeCategory]);

  // Helper function to categorize bookmarks based on content and metadata
  const categorizeBookmark = useCallback(async (candidate: any, post: Post): Promise<string> => {
    const content = `${post.content || ''} ${candidate.title} ${candidate.description}`.toLowerCase();

    // Check custom categories first
    for (const category of customCategories) {
      if (category.tags.some(tag => content.includes(tag.toLowerCase()))) {
        return category.name;
      }
    }

    // Check default categories
    if (content.includes('news') || content.includes('politics') || content.includes('world') || content.includes('economy')) {
      return 'Current Affairs';
    }
    if (content.includes('tool') || content.includes('business') || content.includes('education') || content.includes('technology') || content.includes('software') || content.includes('app')) {
      return 'Work & Knowledge';
    }
    if (content.includes('platform') || content.includes('social') || content.includes('entertainment') || content.includes('culture')) {
      return 'Social Trends';
    }

    // Default fallback
    return 'Lifestyle & Opinions';
  }, [customCategories]);

  // Helper function to generate tags for bookmarks
  const generateBookmarkTags = useCallback((candidate: any, post: Post): string[] => {
    const content = `${post.content || ''} ${candidate.title} ${candidate.description}`.toLowerCase();
    const tags: string[] = [];

    // Technology-related tags
    if (content.includes('ai') || content.includes('artificial intelligence')) tags.push('AI');
    if (content.includes('tool') || content.includes('software')) tags.push('Tools');
    if (content.includes('technology') || content.includes('tech')) tags.push('Technology');
    if (content.includes('business')) tags.push('Business');
    if (content.includes('education') || content.includes('learning')) tags.push('Education');
    if (content.includes('news')) tags.push('News');
    if (content.includes('platform')) tags.push('Platforms');
    if (content.includes('entertainment')) tags.push('Entertainment');

    // Domain-based tags
    const domain = candidate.domain.toLowerCase();
    if (domain.includes('github')) tags.push('Development');
    if (domain.includes('youtube')) tags.push('Video');
    if (domain.includes('medium') || domain.includes('blog')) tags.push('Articles');

    // Ensure we have at least one tag
    if (tags.length === 0) {
      tags.push('Website');
    }

    // Limit to 4 tags max
    return tags.slice(0, 4);
  }, []);

  console.log('[SavedBookmarksWidget] Rendering widget with', filteredBookmarks.length, 'filtered bookmarks');

  return (
    <div className="h-[450px] flex flex-col">
      {/* Category Filters */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={() => {
            setActiveCategory('All');
            setActiveTag('All');
          }}
          className={`notely-pill flex items-center text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
            activeCategory === 'All'
              ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
              : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
          }`}
        >
          <span className="mr-1">🔍</span>
          All
        </button>
        {categories.map((category) => (
          <button
            key={category.name}
            onClick={() => {
              setActiveCategory(category.name);
              setActiveTag('All');
            }}
            className={`notely-pill flex items-center text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
              activeCategory === category.name
                ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
                : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
            }`}
          >
            <span className="mr-1">{category.emoji}</span>
            {category.name}
          </button>
        ))}
      </div>

      {/* Tag Filters */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={() => setActiveTag('All')}
          className={`notely-pill text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
            activeTag === 'All'
              ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
              : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
          }`}
        >
          All
        </button>
        {activeTagOptions.map((tag) => (
          <button
            key={tag}
            onClick={() => setActiveTag(tag)}
            className={`notely-pill text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
              activeTag === tag
                ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
                : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
            }`}
          >
            {tag}
          </button>
        ))}
      </div>

      {/* Actions and Status */}
      <div className="flex items-center justify-end space-x-2 mb-4">
        {/* Add Bookmark Button */}
        <button
          onClick={() => {
            setShowAddBookmarkModal(true);
            // Reset form errors when opening modal
            setFormErrors({ url: '', title: '' });
          }}
          className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-3 py-1 text-sm font-medium hover:scale-105 notely-filter-transition shadow-notely-sm hover:shadow-notely-md border border-transparent"
          title={t('widgets.addBookmark')}
        >
          + {t('widgets.addBookmark')}
        </button>

        {/* Edit Categories Button */}
        <button
          onClick={() => setShowEditCategoriesModal(true)}
          className="text-sm font-medium rounded-full bg-purple-500 text-white px-3 py-1 hover:bg-purple-600 notely-filter-transition hover:scale-105 shadow-notely-sm hover:shadow-notely-md border border-transparent"
          title={t('widgets.editCategories')}
        >
          {t('widgets.editCategories')}
        </button>

        {/* Status and AI Processing */}
        {newPostsCount > 0 && (
          <>
            <span className="uppercase text-xs text-notely-text-muted tracking-wide bg-notely-surface px-2 py-1 rounded-notely-full">
              {newPostsCount}/{NEW_POSTS_THRESHOLD} new
            </span>
            {newPostsCount >= 3 && ( // Allow manual trigger when we have at least 3 posts
              <button
                onClick={handleProcessNow}
                disabled={isLoadingBookmarks}
                className="text-sm font-medium rounded-full bg-blue-500 text-white px-3 py-1 hover:bg-blue-600 notely-filter-transition hover:scale-105 shadow-notely-sm hover:shadow-notely-md border border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoadingBookmarks ? t('widgets.processing') : t('widgets.processNow')}
              </button>
            )}
          </>
        )}
      </div>

      {/* Bookmarks Grid */}
      <div className="flex-1 overflow-y-auto pr-1">
            <button
              key={category.name}
              onClick={() => {
                setActiveCategory(category.name);
                setActiveTag('All');
              }}
              className={`notely-pill flex items-center text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
                activeCategory === category.name
                  ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
                  : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
              }`}
            >
              <span className="mr-1">{category.emoji}</span>
              {category.name}
            </button>
          ))}
        </div>

        {/* Tag Filters */}
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => setActiveTag('All')}
            className={`notely-pill text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
              activeTag === 'All'
                ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
                : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
            }`}
          >
            All
          </button>
          {activeTagOptions.map((tag) => (
            <button
              key={tag}
              onClick={() => setActiveTag(tag)}
              className={`notely-pill text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
                activeTag === tag
                  ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
                  : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
              }`}
            >
              {tag}
            </button>
          ))}
        </div>

        {/* Actions and Status */}
        <div className="flex items-center justify-end space-x-2 mb-4">
          {/* Add Bookmark Button */}
          <button
            onClick={() => {
              setShowAddBookmarkModal(true);
              // Reset form errors when opening modal
              setFormErrors({ url: '', title: '' });
            }}
            className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-3 py-1 text-sm font-medium hover:scale-105 notely-filter-transition shadow-notely-sm hover:shadow-notely-md border border-transparent"
            title={t('widgets.addBookmark')}
          >
            + {t('widgets.addBookmark')}
          </button>

          {/* Edit Categories Button */}
          <button
            onClick={() => setShowEditCategoriesModal(true)}
            className="text-sm font-medium rounded-full bg-purple-500 text-white px-3 py-1 hover:bg-purple-600 notely-filter-transition hover:scale-105 shadow-notely-sm hover:shadow-notely-md border border-transparent"
            title={t('widgets.editCategories')}
          >
            {t('widgets.editCategories')}
          </button>

          {/* Status and AI Processing */}
          {newPostsCount > 0 && (
            <>
              <span className="uppercase text-xs text-notely-text-muted tracking-wide bg-notely-surface px-2 py-1 rounded-notely-full">
                {newPostsCount}/{NEW_POSTS_THRESHOLD} new
              </span>
              {newPostsCount >= 3 && ( // Allow manual trigger when we have at least 3 posts
                <button
                  onClick={() => {
                    const newPosts = posts.filter(post =>
                      post.id &&
                      post.content &&
                      post.content.length >= 20 &&
                      !processedPostIds.has(post.id)
                    );
                    if (newPosts.length > 0) {
                      processNewPostsForBookmarks(newPosts);
                    }
                  }}
                  disabled={isLoadingBookmarks}
                  className="notely-btn-primary text-[11px] font-medium rounded-md px-2 py-1 disabled:opacity-50 disabled:cursor-not-allowed notely-filter-transition hover:scale-105"
                  title={t('widgets.processNow')}
                >
                  {t('widgets.processNow')}
                </button>
              )}
            </>
          )}
      </div>

      {/* Category Pills */}
      <div className="flex flex-wrap gap-2 mb-4">
        {categories.map((category) => (
          <button
            key={category.name}
            onClick={() => {
              setActiveCategory(category.name);
              setActiveTag('All');
            }}
            className={`notely-pill flex items-center text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
              activeCategory === category.name
                ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
                : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
            }`}
          >
            <span className="mr-1">{category.emoji}</span>
            {category.name}
          </button>
        ))}
      </div>

      {/* Tag Filters */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={() => setActiveTag('All')}
          className={`notely-pill text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
            activeTag === 'All'
              ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
              : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
          }`}
        >
          All
        </button>
        {activeTagOptions.map((tag) => (
          <button
            key={tag}
            onClick={() => setActiveTag(tag)}
            className={`notely-pill text-sm font-medium rounded-full px-3 py-1 whitespace-nowrap notely-filter-transition hover:scale-105 border border-notely-border/10 dark:border-notely-border-dark/20 ${
              activeTag === tag
                ? 'bg-notely-accent text-white font-semibold shadow-notely-md border-transparent'
                : 'bg-notely-surface text-notely-text-muted hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-sm'
            }`}
          >
            {tag}
          </button>
        ))}
      </div>

      {/* Bookmarks Grid */}
      <div className="flex-1 overflow-y-auto">
        {isLoadingBookmarks ? (
          <div className="flex-1 flex items-center justify-center text-center">
            <div>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto mb-2"></div>
              <p className="text-sm text-notely-text-secondary">{t('widgets.extractingBookmarks')}</p>
              <p className="text-xs text-notely-text-tertiary mt-1">{t('widgets.processingPosts')}</p>
        <div className="flex-1 overflow-y-auto">
          {isLoadingBookmarks ? (
            <div className="flex-1 flex items-center justify-center text-center">
              <div>
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto mb-2"></div>
                <p className="text-sm text-notely-text-secondary">{t('widgets.extractingBookmarks')}</p>
                <p className="text-xs text-notely-text-tertiary mt-1">{t('widgets.processingPosts')}</p>
              </div>
            </div>
          ) : filteredBookmarks.length > 0 ? (
            <div className="columns-1 lg:columns-2 gap-4 space-y-4">
              {filteredBookmarks.map((bookmark) => (
                <div
                  key={bookmark.id}
                  className="break-inside-avoid bg-gray-100/80 text-gray-800 dark:bg-white/5 dark:text-white/80 backdrop-blur-sm rounded-xl px-6 py-4 hover:scale-[1.02] transition-all group relative"
                >
                  {/* Delete button - only show on hover */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeBookmark(bookmark.id);
                    }}
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                    title="Delete bookmark"
                  >
                    ×
                  </button>

                  {/* Clickable area for opening bookmark */}
                  <div
                    className="cursor-pointer"
                    onClick={() => window.open(bookmark.url, '_blank')}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center flex-1 min-w-0">
                        {bookmark.favicon && (
                          <img
                            src={bookmark.favicon}
                            alt=""
                            className="w-4 h-4 mr-2 flex-shrink-0"
                            onError={(e) => { e.currentTarget.style.display = 'none'; }}
                          />
                        )}
                        <span className="text-xl font-semibold leading-tight truncate group-hover:opacity-80 transition-opacity text-gray-900 dark:text-gray-100">
                          {bookmark.title}
                        </span>
                      </div>
                      <svg className="w-4 h-4 opacity-50 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </div>
                    <p className="text-sm opacity-70 leading-relaxed">
                      {bookmark.description}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <p className="text-sm opacity-70 leading-relaxed">
                        {bookmark.domain}
                      </p>
                      <div className="flex items-center space-x-2">
                        {/* Source indicator */}
                        {bookmark.id.startsWith('manual-') && (
                          <span className="text-[10px] font-medium px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                            Manual
                          </span>
                        )}
                        {bookmark.id.startsWith('auto-') && (
                          <span className="text-[10px] font-medium px-2 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 rounded-full">
                            Auto
                          </span>
                        )}
                        {bookmark.id.startsWith('ai-') && (
                          <span className="text-[10px] font-medium px-2 py-1 bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 rounded-full">
                            AI
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Tags */}
                    {bookmark.tags && bookmark.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {bookmark.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="text-[10px] font-medium px-2 py-1 bg-gray-200/80 text-gray-700 dark:bg-gray-700/50 dark:text-gray-300 rounded-md"
                          >
                            {tag}
                          </span>
                        ))}
                        {bookmark.tags.length > 3 && (
                          <span className="text-[10px] font-medium px-2 py-1 bg-gray-200/80 text-gray-700 dark:bg-gray-700/50 dark:text-gray-300 rounded-md">
                            +{bookmark.tags.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center text-center">
              <div>
                <span className="text-4xl mb-2 block">🔖</span>
                {newPostsCount > 0 ? (
                  <>
                    <p className="text-sm text-notely-text-muted leading-relaxed">
                      {newPostsCount} new posts ready for processing
                    </p>
                    <p className="text-sm text-notely-text-muted leading-relaxed mt-1">
                      AI bookmark extraction will run when you have {NEW_POSTS_THRESHOLD} new posts
                    </p>
                    <p className="text-sm text-notely-text-muted leading-relaxed">
                      ({NEW_POSTS_THRESHOLD - newPostsCount} more posts needed)
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-sm text-notely-text-muted leading-relaxed">{t('widgets.noBookmarksCategory')}</p>
                    <p className="text-sm text-notely-text-muted leading-relaxed mt-1">
                      {t('widgets.savePostsForBookmarks')}
                    </p>
                    <p className="text-sm text-notely-text-muted leading-relaxed">
                      {t('widgets.aiProcessingInfo').replace('{threshold}', NEW_POSTS_THRESHOLD.toString())}
                    </p>
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Add Bookmark Modal */}
      {showAddBookmarkModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="notely-card rounded-lg px-6 py-4 w-96 max-w-[90vw]">
            <h3 className="text-xl font-semibold leading-tight mb-4 notely-heading">{t('widgets.addBookmark')}</h3>

            <form onSubmit={async (e) => {
              e.preventDefault();

              // Validate form
              const urlValidation = validateUrl(newBookmarkForm.url);
              if (!urlValidation.isValid) {
                setFormErrors(prev => ({ ...prev, url: urlValidation.error }));
                return;
              }

              if (!newBookmarkForm.title.trim()) {
                setFormErrors(prev => ({ ...prev, title: 'Title is required' }));
                return;
              }

              try {
                await addManualBookmark({
                  ...newBookmarkForm,
                  url: urlValidation.normalizedUrl // Use normalized URL
                });

                // Reset form and close modal
                setNewBookmarkForm({
                  url: '',
                  title: '',
                  description: '',
                  category: 'Work & Knowledge',
                  tags: []
                });
                setFormErrors({ url: '', title: '' });
                setShowAddBookmarkModal(false);
              } catch (error) {
                // Handle error (could show error message to user)
                console.error('Failed to add bookmark:', error);
                setFormErrors(prev => ({ ...prev, url: 'Failed to add bookmark. Please check the URL and try again.' }));
              }
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block uppercase text-xs text-gray-700 dark:text-gray-300 tracking-wide mb-1">{t('widgets.url')} *</label>
                  <input
                    type="text"
                    value={newBookmarkForm.url}
                    onChange={(e) => handleUrlChange(e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                      formErrors.url
                        ? 'border-red-300 dark:border-red-600 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                    }`}
                    placeholder="google.com, www.github.com, or https://example.com"
                    required
                  />
                  {formErrors.url && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.url}</p>
                  )}
                  {newBookmarkForm.url && !formErrors.url && (
                    <p className="text-green-600 text-xs mt-1">
                      ✓ Will be saved as: {validateUrl(newBookmarkForm.url).normalizedUrl}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block uppercase text-xs text-gray-700 dark:text-gray-300 tracking-wide mb-1">{t('widgets.title')} *</label>
                  <input
                    type="text"
                    value={newBookmarkForm.title}
                    onChange={(e) => {
                      setNewBookmarkForm(prev => ({ ...prev, title: e.target.value }));
                      // Clear title error when user starts typing
                      if (formErrors.title) {
                        setFormErrors(prev => ({ ...prev, title: '' }));
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                      formErrors.title
                        ? 'border-red-300 dark:border-red-600 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                    }`}
                    placeholder="Bookmark title"
                    required
                  />
                  {formErrors.title && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.title}</p>
                  )}
                </div>

                <div>
                  <label className="block uppercase text-xs text-gray-700 dark:text-gray-300 tracking-wide mb-1">{t('widgets.description')}</label>
                  <textarea
                    value={newBookmarkForm.description}
                    onChange={(e) => setNewBookmarkForm(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300/10 dark:border-gray-600/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    placeholder="Brief description"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block uppercase text-xs text-gray-700 dark:text-gray-300 tracking-wide mb-1">Category</label>
                  <select
                    value={newBookmarkForm.category}
                    onChange={(e) => setNewBookmarkForm(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300/10 dark:border-gray-600/10 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    {categories.map(cat => (
                      <option key={cat.name} value={cat.name}>{cat.emoji} {cat.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowAddBookmarkModal(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300/10 dark:border-gray-600/10 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  {t('widgets.cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                >
                  {t('widgets.addBookmark')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Categories Modal */}
      {showEditCategoriesModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="notely-card rounded-lg p-6 w-[500px] max-w-[90vw] max-h-[80vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4 notely-heading">Manage Categories</h3>

            {/* Custom Categories List */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Custom Categories</h4>
              {customCategories.length > 0 ? (
                <div className="space-y-3">
                  {customCategories.map(category => (
                    <div key={category.name} className="border border-gray-200/10 dark:border-gray-600/10 rounded-lg p-3 bg-gray-50 dark:bg-gray-700">
                      {editingCategory === category.name ? (
                        /* Edit Mode */
                        <div className="space-y-3">
                          <div className="flex space-x-2">
                            <input
                              type="text"
                              value={editCategoryForm.name}
                              onChange={(e) => setEditCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                              className="flex-1 px-2 py-1 border border-gray-300/10 dark:border-gray-600/10 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100"
                              placeholder="Category name"
                            />
                            <EmojiPicker
                              selectedEmoji={editCategoryForm.emoji}
                              onEmojiSelect={(emoji) => setEditCategoryForm(prev => ({ ...prev, emoji }))}
                              compact={true}
                            />
                          </div>

                          {/* Tags Management */}
                          <div>
                            <div className="flex flex-wrap gap-1 mb-2">
                              {editCategoryForm.tags.map(tag => (
                                <span
                                  key={tag}
                                  className="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                                >
                                  {tag}
                                  <button
                                    onClick={() => removeTagFromEditingCategory(tag)}
                                    className="ml-1 text-purple-600 hover:text-purple-800"
                                  >
                                    ×
                                  </button>
                                </span>
                              ))}
                            </div>
                            <div className="flex space-x-2">
                              <input
                                type="text"
                                value={newTagInput}
                                onChange={(e) => setNewTagInput(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTagToEditingCategory())}
                                className="flex-1 px-2 py-1 border border-gray-300/10 dark:border-gray-600/10 rounded text-xs focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100"
                                placeholder="Add new tag"
                              />
                              <button
                                onClick={addTagToEditingCategory}
                                className="px-2 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600"
                              >
                                Add
                              </button>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={cancelEditingCategory}
                              className="px-3 py-1 text-gray-600 dark:text-gray-300 border border-gray-300/10 dark:border-gray-600/10 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={saveEditingCategory}
                              className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                            >
                              Save
                            </button>
                          </div>
                        </div>
                      ) : (
                        /* View Mode */
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-1">
                              <span className="text-lg mr-2">{category.emoji}</span>
                              <span className="font-medium text-sm text-gray-900 dark:text-gray-100">{category.name}</span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {category.tags.map(tag => (
                                <span
                                  key={tag}
                                  className="inline-block px-2 py-0.5 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                          <div className="flex space-x-2 ml-3">
                            <button
                              onClick={() => startEditingCategory(category)}
                              className="text-blue-500 hover:text-blue-700 text-sm"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => removeCustomCategory(category.name)}
                              className="text-red-500 hover:text-red-700 text-sm"
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">No custom categories yet</p>
              )}
            </div>

            {/* Add New Category Form */}
            <form onSubmit={handleNewCategorySubmit}>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Add New Category</h4>
              <div className="space-y-3">
                <input
                  type="text"
                  value={newCategoryForm.name}
                  onChange={(e) => setNewCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Category name"
                  className="w-full px-3 py-2 border border-gray-300/10 dark:border-gray-600/10 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  required
                />
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap">Emoji:</span>
                  <EmojiPicker
                    selectedEmoji={newCategoryForm.emoji}
                    onEmojiSelect={(emoji) => setNewCategoryForm(prev => ({ ...prev, emoji }))}
                    className="flex-shrink-0"
                    compact={true}
                  />
                </div>
                <input
                  type="text"
                  value={newCategoryForm.tags}
                  onChange={(e) => setNewCategoryForm(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="Tags (comma-separated, e.g., Design, Art, Creative)"
                  className="w-full px-3 py-2 border border-gray-300/10 dark:border-gray-600/10 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  required
                />
                <button
                  type="submit"
                  className="w-full px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 text-sm"
                >
                  Add Category
                </button>
              </div>
            </form>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowEditCategoriesModal(false);
                  cancelEditingCategory(); // Reset editing state when closing modal
                  setNewCategoryForm({ name: '', emoji: '📚', tags: '' }); // Reset new category form
                }}
                className="px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300/10 dark:border-gray-600/10 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SavedBookmarksWidget;
