import React from 'react';
import { ContentCategorization } from '../types';
import { formatForDisplay } from '../utils/formatUtils';

interface ContentCategorySelectorProps {
  categorization: ContentCategorization | null;
  selectedCategory: string | null;
  onCategorySelect: (category: string | null) => void;
  isLoading?: boolean;
  className?: string;
}

const ContentCategorySelector: React.FC<ContentCategorySelectorProps> = ({
  categorization,
  selectedCategory,
  onCategorySelect,
  isLoading = false,
  className = ''
}) => {
  if (isLoading) {
    return (
      <div className={`flex flex-wrap gap-2 py-2 ${className}`}>
        <div className="animate-pulse flex space-x-2">
          <div className="h-8 bg-gray-200 rounded-full w-16"></div>
          <div className="h-8 bg-gray-200 rounded-full w-20"></div>
          <div className="h-8 bg-gray-200 rounded-full w-24"></div>
          <div className="h-8 bg-gray-200 rounded-full w-18"></div>
        </div>
      </div>
    );
  }

  if (!categorization || Object.keys(categorization.categories).length === 0) {
    return (
      <div className={`flex flex-wrap gap-2 py-2 ${className}`}>
        <button
          onClick={() => onCategorySelect(null)}
          className="flex items-center justify-center px-4 py-1.5 rounded-full text-sm font-medium bg-blue-600 text-white"
        >
          All Posts
        </button>
      </div>
    );
  }

  const categories = Object.keys(categorization.categories);

  const getButtonStyles = (category: string | null, isSelected: boolean) => {
    const baseStyles = 'flex items-center justify-center px-4 py-1.5 rounded-full text-sm font-medium transition-all duration-150 focus:outline-none';

    if (!isSelected) {
      return `${baseStyles} bg-notely-surface text-notely-text-secondary border border-notely-border/20 hover:bg-notely-card hover:border-notely-border/30 hover:text-notely-text-primary`;
    }
    
    // Selected state with category-specific colors
    if (category === null) {
      return `${baseStyles} bg-blue-600 text-white shadow-sm`;
    }
    
    // Different colors for different categories
    const colors = [
      'bg-purple-600 text-white',
      'bg-green-600 text-white', 
      'bg-orange-600 text-white',
      'bg-red-600 text-white',
      'bg-indigo-600 text-white',
      'bg-pink-600 text-white',
      'bg-teal-600 text-white'
    ];
    
    const categoryIndex = categories.indexOf(category || '');
    const colorClass = colors[categoryIndex % colors.length];
    
    return `${baseStyles} ${colorClass} shadow-sm`;
  };

  return (
    <div className={`${className}`}>
      <div className="flex flex-wrap gap-2 py-2">
        {/* All button */}
        <button
          onClick={() => onCategorySelect(null)}
          className={getButtonStyles(null, selectedCategory === null)}
        >
          <span className="mr-1.5">📊</span>
          All ({Object.values(categorization.categories).reduce((sum, cat) => sum + cat.posts.length, 0) + categorization.uncategorized.length})
        </button>

        {/* Category buttons */}
        {categories.map((category, index) => {
          const categoryData = categorization.categories[category];
          const isSelected = selectedCategory === category;
          
          // Get emoji based on category name
          const getEmoji = (catName: string) => {
            const name = catName.toLowerCase();
            if (name.includes('tech') || name.includes('innovation')) return '💻';
            if (name.includes('business') || name.includes('career')) return '💼';
            if (name.includes('health') || name.includes('wellness')) return '🏥';
            if (name.includes('education') || name.includes('learning')) return '📚';
            if (name.includes('entertainment') || name.includes('culture')) return '🎭';
            if (name.includes('news') || name.includes('politics')) return '📰';
            if (name.includes('lifestyle') || name.includes('personal')) return '🌟';
            if (name.includes('creative') || name.includes('art')) return '🎨';
            if (name.includes('finance') || name.includes('money')) return '💰';
            if (name.includes('travel') || name.includes('adventure')) return '✈️';
            if (name.includes('food') || name.includes('cooking')) return '🍽️';
            if (name.includes('fitness') || name.includes('sports')) return '💪';
            return '📝'; // Default
          };

          return (
            <button
              key={category}
              onClick={() => onCategorySelect(category)}
              className={getButtonStyles(category, isSelected)}
            >
              <span className="mr-1.5">{getEmoji(category)}</span>
              {formatForDisplay(category)} ({categoryData.posts.length})
            </button>
          );
        })}

        {/* Uncategorized button if there are uncategorized posts */}
        {categorization.uncategorized.length > 0 && (
          <button
            onClick={() => onCategorySelect('uncategorized')}
            className={getButtonStyles('uncategorized', selectedCategory === 'uncategorized')}
          >
            <span className="mr-1.5">❓</span>
            Other ({categorization.uncategorized.length})
          </button>
        )}
      </div>
    </div>
  );
};

export default ContentCategorySelector;
