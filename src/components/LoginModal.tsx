import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  // Add prop for successful login handling
  onLoginSuccess: (token: string, user: Record<string, unknown>) => void; // Pass token and user info
  preventAutoGoogleLogin?: boolean; // Flag to prevent auto-initiating Google login
}

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  onLoginSuccess, // Use the prop
  // We're keeping the prop for future use but not using it currently
  preventAutoGoogleLogin = false, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRegisterMode, setIsRegisterMode] = useState(false);

  if (!isOpen) return null;

  const handleLocalLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);
    console.log('Attempting local login with:', email);

    try {
      // Make API call to backend login endpoint
      const API_URL = 'https://api.notely.social';
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle API errors (e.g., 401 Unauthorized, 400 Bad Request)
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // Handle success
      if (data.token) {
        // TODO: Decode token to get basic user info or fetch from /auth/me
        const placeholderUser = { displayName: 'Logged In User' }; // Replace with actual user data later
        onLoginSuccess(data.token, placeholderUser);
        onClose(); // Close modal on success
      } else {
        throw new Error('Login successful, but no token received.');
      }

    } catch (error: unknown) {
      console.error("Login failed:", error);
      setError(error instanceof Error ? error.message : 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);
    console.log('Attempting registration with:', email);

    try {
      // Validate inputs
      if (!email || !password || !displayName) {
        throw new Error('All fields are required');
      }

      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      // Make API call to backend register endpoint
      const API_URL = 'https://api.notely.social';
      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, displayName }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle API errors (e.g., 400 Bad Request for existing email)
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // Handle success
      if (data.token) {
        const placeholderUser = { displayName: displayName };
        onLoginSuccess(data.token, placeholderUser);
        onClose(); // Close modal on success
      } else {
        throw new Error('Registration successful, but no token received.');
      }

    } catch (error: unknown) {
      console.error("Registration failed:", error);
      setError(error instanceof Error ? error.message : 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    console.log('Opening Google login window...');
    setError(null);
    setIsLoading(true);

    // Flag is already set in the dashboard component
    // Just ensure it's still set here in case someone navigates away and back
    sessionStorage.setItem('acceptGoogleAuth', 'true');
    localStorage.setItem('acceptingAuthMessages', 'true');

    try {
    // Define the backend URL for Google OAuth initiation
      // Add 'prompt=select_account' to force the account selection screen
      const API_URL = process.env.API_URL || 'https://api.notely.social';
      const googleAuthUrl = `${API_URL}/auth/google?prompt=select_account`;

      // Open the URL in a new window/tab with proper styling for a Google auth popup
      const authWindow = window.open(
        googleAuthUrl,
        'googleAuthPopup',
        'width=500,height=600,resizable=yes,scrollbars=yes,status=yes'
      );

      if (!authWindow) {
        throw new Error('Popup blocked. Please allow popups for this site.');
      }

      // Clear loading state immediately since the popup is handling the process
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);

    } catch (error: unknown) {
      console.error('Error opening Google login popup:', error);
      setError(error instanceof Error ? error.message : 'Failed to open Google login window');
      setIsLoading(false);
      // Clear auth flag if there was an error
      sessionStorage.removeItem('acceptGoogleAuth');
      localStorage.removeItem('acceptingAuthMessages');
    }
  };

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
      onClick={handleOverlayClick}
    >
      <div className="notely-card w-full max-w-md mx-auto p-6 bg-notely-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg">
        {/* Header */}
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-notely-text-primary mb-2">
            {isRegisterMode ? 'Create Account' : 'Welcome back'}
          </h2>
          <p className="text-notely-text-muted">
            {isRegisterMode ? 'Join Notely today' : 'Sign in to continue'}
          </p>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-300 text-red-800 rounded-lg">
            {error}
          </div>
        )}

        {/* Form */}
        <form onSubmit={isRegisterMode ? handleRegister : handleLocalLogin} className="space-y-4 mb-6">
          {isRegisterMode && (
            <div>
              <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Display Name
              </label>
              <input
                type="text"
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                required
                disabled={isLoading}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 transition-colors"
                placeholder="Enter your name"
              />
            </div>
          )}
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={isLoading}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 transition-colors"
              placeholder="Enter your email"
            />
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={isLoading}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 transition-colors"
              placeholder={isRegisterMode ? "Min 6 characters" : "Enter password"}
            />
          </div>
          
          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 transition-colors"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white/30 border-t-white mr-2"></div>
                {isRegisterMode ? 'Creating...' : 'Signing in...'}
              </span>
            ) : (
              isRegisterMode ? 'Create Account' : 'Sign In'
            )}
          </button>
          
          {/* Forgot Password */}
          {!isRegisterMode && (
            <div className="text-center">
              <button
                type="button"
                onClick={() => {
                  if (window.location.protocol === 'chrome-extension:') {
                    window.open('https://notely.social/dashboard/forgot-password', '_blank');
                  } else {
                    window.location.href = '/dashboard/forgot-password';
                  }
                }}
                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline"
              >
                Forgot password?
              </button>
            </div>
          )}
        </form>

        {/* Google Login */}
        <button
          onClick={handleGoogleLogin}
          disabled={isLoading}
          className="w-full py-3 px-4 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 transition-colors font-medium flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          Continue with Google
        </button>

        {/* Toggle Mode */}
        <div className="mt-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">
            {isRegisterMode ? 'Already have an account?' : "Don't have an account?"}
          </p>
          <button
            onClick={() => {
              setIsRegisterMode(!isRegisterMode);
              setError(null);
              setEmail('');
              setPassword('');
              setDisplayName('');
            }}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline font-medium mt-1"
          >
            {isRegisterMode ? 'Sign In' : 'Create Account'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;