# UseWhatYouSavedWidget - Test Plan

## Manual Testing Guide

### Prerequisites
1. Have the Chrome extension loaded in developer mode
2. Ensure you have saved posts in your dashboard
3. Navigate to the Mindstream tab

### Test Scenarios

#### 1. Widget Loading and Display
**Expected Behavior:**
- Widget appears in Mindstream tab with title "💡 Use What You Saved"
- Shows loading spinner while analyzing content
- Displays suggestions or empty state based on available content

**Test Steps:**
1. Open dashboard and navigate to Mindstream tab
2. Scroll down to find the "Use What You Saved" widget
3. Verify widget loads without errors
4. Check console for any error messages

#### 2. Content Analysis
**Expected Behavior:**
- Analyzes recent posts (last 30 days)
- Generates suggestions based on content quality and length
- Shows confidence scores and engagement estimates

**Test Steps:**
1. Ensure you have at least 5 saved posts with varying content lengths
2. Refresh the dashboard to trigger analysis
3. Verify suggestions appear with confidence percentages
4. Check that suggestions match content characteristics

#### 3. Content Generation
**Expected Behavior:**
- "Generate" button creates AI-powered content
- Shows loading state during generation
- Displays generated content in preview area
- Handles errors gracefully

**Test Steps:**
1. Click "Generate" on a high-confidence suggestion
2. Wait for content generation to complete
3. Verify generated content appears in preview
4. Test with different suggestion types (thread, caption, etc.)

#### 4. Action Buttons
**Expected Behavior:**
- "Save as Draft" copies content to clipboard
- "Schedule" shows placeholder message and copies content
- Buttons are disabled until content is generated

**Test Steps:**
1. Generate content for a suggestion
2. Click "Save as Draft" and verify clipboard content
3. Click "Schedule" and verify placeholder message
4. Test button states before and after generation

#### 5. Error Handling
**Expected Behavior:**
- Graceful handling of API failures
- Clear error messages for users
- Widget continues functioning after errors

**Test Steps:**
1. Temporarily disable internet connection
2. Try generating content
3. Verify error message appears
4. Restore connection and test normal functionality

### Console Commands for Testing

#### Check Widget State
```javascript
// Check if widget is properly mounted
document.querySelector('[data-widget="use-what-you-saved"]');

// Check for any React errors
console.log('React errors:', window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.rendererInterfaces);
```

#### Simulate Different Post Types
```javascript
// Add test posts to storage for testing
const testPosts = [
  {
    id: 'test-thread-1',
    content: 'This is a long post with multiple points that would work well as a Twitter thread. First point about productivity. Second point about time management. Third point about work-life balance. This content has enough substance to be broken down into multiple tweets.',
    platform: 'LinkedIn',
    savedAt: new Date().toISOString(),
    interactions: { likes: 25, reposts: 5, replies: 8 }
  },
  {
    id: 'test-caption-1',
    content: 'Beautiful sunset at the beach today! Sometimes you need to pause and appreciate the simple moments in life. What brings you peace? 🌅',
    platform: 'Instagram',
    savedAt: new Date().toISOString(),
    media: [{ url: 'https://example.com/sunset.jpg', type: 'image' }],
    interactions: { likes: 150, reposts: 12, replies: 23 }
  },
  {
    id: 'test-quote-1',
    content: '"The only way to do great work is to love what you do." - Steve Jobs. This quote has always inspired me to pursue my passions.',
    platform: 'X/Twitter',
    savedAt: new Date().toISOString(),
    interactions: { likes: 45, reposts: 18, replies: 7 }
  }
];

// Save to storage
chrome.storage.sync.set({ savedPosts: testPosts });
```

#### Check Analysis Results
```javascript
// Import analysis function (in browser console after widget loads)
// This would need to be exposed for testing
console.log('Analysis results:', window.debugAnalysis);
```

### Expected Console Output

#### On Widget Load
```
[UseWhatYouSavedWidget] Analyzing 15 posts for content suggestions
[UseWhatYouSavedWidget] Generated 6 suggestions with average confidence 0.72
```

#### On Content Generation
```
[AI Service] generateContentSuggestion successful for thread
[UseWhatYouSavedWidget] Content generated for suggestion: test-thread-1-thread
```

#### On Error
```
[UseWhatYouSavedWidget] Error generating content: AI API key is missing
[Toast] Failed to generate content. Please try again.
```

### Performance Verification

#### Memory Usage
- Widget should not cause memory leaks
- Check browser dev tools Memory tab
- Monitor for increasing memory usage over time

#### API Calls
- Should only make API calls when "Generate" is clicked
- No unnecessary re-analysis on every render
- Proper caching of generated content

#### Rendering Performance
- Widget should render smoothly without lag
- Hover animations should be smooth
- No layout shifts during content loading

### Accessibility Testing

#### Keyboard Navigation
- All buttons should be keyboard accessible
- Tab order should be logical
- Focus indicators should be visible

#### Screen Reader Support
- Widget title should be announced
- Button purposes should be clear
- Loading states should be announced

### Browser Compatibility

#### Chrome (Primary)
- Test on latest Chrome version
- Verify extension APIs work correctly
- Check for any Chrome-specific issues

#### Edge/Firefox (Secondary)
- Test basic functionality if extension supports
- Verify no browser-specific errors

### Integration Testing

#### With Other Widgets
- Verify widget doesn't interfere with other Mindstream widgets
- Check proper spacing and layout
- Test reordering functionality

#### With Dashboard Features
- Ensure widget updates when new posts are saved
- Verify proper integration with theme switching
- Test with different language settings

### Troubleshooting Common Issues

#### Widget Not Appearing
1. Check if MindstreamWidgets includes the new widget type
2. Verify translation key exists
3. Check for JavaScript errors in console

#### No Suggestions Generated
1. Verify posts exist in storage
2. Check post content length (minimum 50 characters)
3. Ensure posts are from last 30 days

#### Content Generation Fails
1. Check OpenAI API key configuration
2. Verify internet connection
3. Check browser console for detailed error messages

#### Performance Issues
1. Check number of posts being analyzed (should be limited to 10)
2. Verify no infinite re-rendering loops
3. Monitor network requests for efficiency

### Success Criteria

✅ Widget loads without errors
✅ Content analysis produces relevant suggestions
✅ AI content generation works for all suggestion types
✅ Action buttons function correctly
✅ Error handling works gracefully
✅ Performance remains smooth with large datasets
✅ UI follows existing design patterns
✅ Accessibility standards are met
✅ Integration with other components works properly

### Reporting Issues

When reporting issues, include:
1. Browser version and extension version
2. Console error messages
3. Steps to reproduce
4. Expected vs actual behavior
5. Screenshots if applicable

### Future Test Scenarios

As new features are added:
- Draft management system testing
- Scheduling functionality testing
- Performance tracking verification
- Custom action type testing
- Batch processing validation
