# SavedBookmarksWidget Refactor - Test Plan

## Test Scenarios

### 1. **Initial Load (No Previous Data)**
**Expected Behavior:**
- Widget loads with test data (bolt.new, github.com)
- No processed posts in storage
- Status shows "0/10 new" or no status indicator
- Empty state explains batching system

**Test Steps:**
1. Clear Chrome storage: `chrome.storage.local.clear()`
2. Load dashboard with no saved posts
3. Verify test bookmarks appear
4. Check console for "Loaded 0 previously processed posts"

### 2. **Adding Posts Below Threshold**
**Expected Behavior:**
- Posts are tracked but not processed
- Status indicator shows progress (e.g., "3/10 new")
- No AI calls made
- Manual "Process Now" button appears when ≥3 posts

**Test Steps:**
1. Save 5 posts with content containing URLs
2. Check header shows "5/10 new"
3. Verify no AI processing occurs (check console)
4. Confirm "Process Now" button appears

### 3. **Reaching Threshold (10 Posts)**
**Expected Behavior:**
- AI processing triggers automatically
- Loading state shows "Processing new posts"
- Bookmarks are extracted and added
- Posts marked as processed
- Status resets to "0/10 new"

**Test Steps:**
1. Save 10 posts with URLs (e.g., "Check out https://example.com")
2. Watch for automatic AI processing
3. Verify new bookmarks appear
4. Check processed posts in storage
5. Confirm status indicator resets

### 4. **Manual Processing**
**Expected Behavior:**
- "Process Now" button works when ≥3 posts
- Processes all available new posts
- Updates processed tracking
- Resets counter

**Test Steps:**
1. Save 5 posts with URLs
2. Click "Process Now" button
3. Verify AI processing starts
4. Check bookmarks are added
5. Confirm posts marked as processed

### 5. **Persistence Across Sessions**
**Expected Behavior:**
- Processed posts remembered after reload
- No re-processing of old posts
- Only new posts count toward threshold

**Test Steps:**
1. Process some posts (reach threshold)
2. Reload dashboard/extension
3. Add 5 more new posts
4. Verify only new posts are counted
5. Check old posts not re-processed

### 6. **Error Handling**
**Expected Behavior:**
- Failed posts still marked as processed
- Widget continues working after errors
- Graceful fallback to test data

**Test Steps:**
1. Save posts with malformed content
2. Trigger processing
3. Verify errors logged but widget functional
4. Check failed posts marked as processed

## Console Commands for Testing

### Clear All Data
```javascript
// Clear processed posts tracking
chrome.storage.local.remove('bookmark_processed_posts');

// Clear all saved posts
chrome.storage.sync.clear();
chrome.storage.local.clear();
```

### Check Processed Posts
```javascript
// View currently processed posts
chrome.storage.local.get('bookmark_processed_posts').then(console.log);
```

### Simulate Posts
```javascript
// Add test posts to trigger processing
const testPosts = Array.from({length: 10}, (_, i) => ({
  id: `test-${i}`,
  platform: 'X/Twitter',
  content: `Check out https://example${i}.com - great tool for developers!`,
  author: 'TestUser',
  timestamp: new Date().toISOString(),
  permalink: `https://x.com/test/${i}`
}));

// Save posts to storage
chrome.storage.sync.set({ savedPosts: testPosts });
```

## Expected Console Output

### On Load
```
[SavedBookmarksWidget] Loaded 5 previously processed posts
[SavedBookmarksWidget] Found 3 new unprocessed posts (threshold: 10)
```

### On Threshold Reached
```
[SavedBookmarksWidget] Threshold reached! Processing 10 new posts for bookmark extraction
[SavedBookmarksWidget] Processing 10 posts for AI bookmark extraction
[SavedBookmarksWidget] Extracted 15 new AI bookmarks from 10 posts
```

### On Manual Trigger
```
[SavedBookmarksWidget] Manual processing triggered for 5 posts
[SavedBookmarksWidget] Extracted 8 new AI bookmarks from 5 posts
```

## Performance Verification

### Before Refactor (Baseline)
- Monitor AI API calls in Network tab
- Count calls per dashboard load
- Note re-processing of same posts

### After Refactor (Expected)
- 90%+ reduction in AI calls
- No duplicate processing
- Batch processing only

### Metrics to Track
1. **AI API Calls**: Should be ~1/10th of previous
2. **Load Time**: Faster due to no unnecessary processing
3. **Storage Usage**: Minimal increase for tracking
4. **User Experience**: Clear progress indicators

## Edge Cases

### 1. **Storage Quota Exceeded**
- Graceful fallback to in-memory tracking
- Continue functioning without persistence

### 2. **Malformed Posts**
- Skip posts without required fields
- Don't count toward threshold

### 3. **Network Failures**
- Retry logic for AI calls
- Mark as processed to avoid infinite retries

### 4. **Large Post Volumes**
- Efficient Set operations for tracking
- Batch processing prevents overwhelming API

## Success Criteria

✅ **Functional Requirements**
- [ ] Processes bookmarks only when threshold reached
- [ ] Tracks processed posts persistently
- [ ] Manual trigger works correctly
- [ ] No duplicate processing

✅ **Performance Requirements**
- [ ] 90%+ reduction in AI API calls
- [ ] Fast loading with large post counts
- [ ] Efficient storage usage

✅ **User Experience Requirements**
- [ ] Clear progress indicators
- [ ] Informative empty states
- [ ] Manual control option
- [ ] No breaking changes
