import React from 'react';
import { Post, Platform } from '../types';

interface WeeklyStatsProps {
  posts: Post[];
  className?: string;
}

interface WeeklyStatsData {
  totalSaves: number;
  topPlatform: Platform | null;
  topCategory: string | null;
  timeSaved: string;
  weeklyGrowth: number;
}

const WeeklyStats: React.FC<WeeklyStatsProps> = ({ posts, className = '' }) => {
  const calculateWeeklyStats = (): WeeklyStatsData => {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    // Filter posts from the last week
    const weeklyPosts = posts.filter(post => {
      const postDate = new Date(post.savedAt || post.timestamp || '');
      return postDate >= oneWeekAgo;
    });

    // Calculate total saves
    const totalSaves = weeklyPosts.length;

    // Find top platform
    const platformCounts: Record<string, number> = {};
    weeklyPosts.forEach(post => {
      platformCounts[post.platform] = (platformCounts[post.platform] || 0) + 1;
    });
    const topPlatform = Object.keys(platformCounts).reduce((a, b) =>
      platformCounts[a] > platformCounts[b] ? a : b,
      Object.keys(platformCounts)[0]
    ) as Platform || null;

    // Find top category
    const categoryCounts: Record<string, number> = {};
    weeklyPosts.forEach(post => {
      if (post.categories && Array.isArray(post.categories)) {
        post.categories.forEach(category => {
          if (category && category !== "0") {
            categoryCounts[category] = (categoryCounts[category] || 0) + 1;
          }
        });
      }
    });
    const topCategory = Object.keys(categoryCounts).reduce((a, b) =>
      categoryCounts[a] > categoryCounts[b] ? a : b,
      Object.keys(categoryCounts)[0]
    ) || null;

    // Calculate time saved (rough estimate: 2 minutes per post)
    const minutesSaved = totalSaves * 2;
    const timeSaved = minutesSaved >= 60
      ? `${Math.floor(minutesSaved / 60)}h ${minutesSaved % 60}m`
      : `${minutesSaved}m`;

    // Calculate weekly growth (compare to previous week)
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
    const previousWeekPosts = posts.filter(post => {
      const postDate = new Date(post.savedAt || post.timestamp || '');
      return postDate >= twoWeeksAgo && postDate < oneWeekAgo;
    });

    const weeklyGrowth = previousWeekPosts.length > 0
      ? Math.round(((totalSaves - previousWeekPosts.length) / previousWeekPosts.length) * 100)
      : totalSaves > 0 ? 100 : 0;

    return {
      totalSaves,
      topPlatform,
      topCategory,
      timeSaved,
      weeklyGrowth
    };
  };

  const stats = calculateWeeklyStats();

  const formatPlatformName = (platform: Platform | null): string => {
    if (!platform) return 'None';
    switch (platform) {
      case 'pinterest': return 'Pinterest';
      case 'X/Twitter': return 'X/Twitter';
      default: return platform;
    }
  };

  return (
    <div className={`notely-card bg-notely-card rounded-notely-lg shadow-notely-md border border-notely-border px-6 py-4 notely-filter-transition ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold leading-tight text-notely-text-primary">Weekly Stats</h3>
        <div className="uppercase text-xs text-notely-text-muted tracking-wide bg-notely-surface px-2 py-1 rounded-notely-full">
          Last 7 days
        </div>
      </div>

      <div className="space-y-4">
        {/* Total Saves */}
        <div className="flex items-center justify-between notely-filter-transition hover:bg-notely-surface rounded-notely-md p-2 -m-2">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-notely-accent/10 rounded-notely-md flex items-center justify-center mr-3">
              <svg className="w-4 h-4 text-notely-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-notely-text-primary notely-body">Total Saves</p>
              <p className="text-xs text-notely-text-secondary">Posts saved this week</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xl font-semibold leading-tight text-notely-text-primary">{stats.totalSaves}</p>
            {stats.weeklyGrowth !== 0 && (
              <p className={`text-sm leading-relaxed ${stats.weeklyGrowth > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {stats.weeklyGrowth > 0 ? '+' : ''}{stats.weeklyGrowth}%
              </p>
            )}
          </div>
        </div>

        {/* Top Platform */}
        <div className="flex items-center justify-between notely-filter-transition hover:bg-notely-surface rounded-notely-md p-2 -m-2">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-purple-500/10 rounded-notely-md flex items-center justify-center mr-3">
              <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-notely-text-primary notely-body">Top Platform</p>
              <p className="text-xs text-notely-text-secondary">Most saved from</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-semibold text-notely-text-primary">{formatPlatformName(stats.topPlatform)}</p>
          </div>
        </div>

        {/* Top Category */}
        {stats.topCategory && (
          <div className="flex items-center justify-between notely-filter-transition hover:bg-notely-surface rounded-notely-md p-2 -m-2">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-500/10 rounded-notely-md flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-notely-text-primary notely-body">Top Category</p>
                <p className="text-xs text-notely-text-secondary">Most popular tag</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-semibold text-notely-text-primary">{stats.topCategory}</p>
            </div>
          </div>
        )}

        {/* Time Saved */}
        <div className="flex items-center justify-between notely-filter-transition hover:bg-notely-surface rounded-notely-md p-2 -m-2">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-orange-500/10 rounded-notely-md flex items-center justify-center mr-3">
              <svg className="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-notely-text-primary notely-body">Time Saved</p>
              <p className="text-xs text-notely-text-secondary">Estimated browsing time</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-semibold text-notely-text-primary">{stats.timeSaved}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeeklyStats;
