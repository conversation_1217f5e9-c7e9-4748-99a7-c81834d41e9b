import React, { useState, useRef, useEffect } from 'react';
import { 
  performSemanticSearch, 
  getSearchSuggestions, 
  debounce,
  SemanticSearchResponse 
} from '../services/semanticSearchService';
import SearchResultsSummary from './SearchResultsSummary';

interface AskMyBookmarksProps {
  className?: string;
  onResultsChange?: (hasResults: boolean) => void;
}

const AskMyBookmarks: React.FC<AskMyBookmarksProps> = ({ 
  className = '', 
  onResultsChange 
}) => {
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SemanticSearchResponse | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFocused, setIsFocused] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load search suggestions on mount
  useEffect(() => {
    loadSuggestions();
  }, []);

  // Keyboard shortcut (Cmd+K / Ctrl+K)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        inputRef.current?.focus();
        setShowSuggestions(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadSuggestions = async () => {
    try {
      const newSuggestions = await getSearchSuggestions();
      setSuggestions(newSuggestions);
    } catch (error) {
      console.error('Failed to load search suggestions:', error);
    }
  };

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setSearchResults(null);
      setError(null);
      onResultsChange?.(false);
      return;
    }

    setIsSearching(true);
    setError(null);

    try {
      const results = await performSemanticSearch(searchQuery.trim(), 10);
      setSearchResults(results);
      onResultsChange?.(results.totalResults > 0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      setSearchResults(null);
      onResultsChange?.(false);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounced search function
  const debouncedSearch = debounce(performSearch, 500);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    debouncedSearch(value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    performSearch(suggestion);
    inputRef.current?.blur();
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    if (suggestions.length > 0 && !query.trim()) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => setShowSuggestions(false), 150);
  };

  const handleClearSearch = () => {
    setQuery('');
    setSearchResults(null);
    setError(null);
    onResultsChange?.(false);
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowSuggestions(false);
      inputRef.current?.blur();
    }
  };

  return (
    <div className={`w-full ${className}`} ref={containerRef}>
      {/* Search Input */}
      <div style={{ position: 'relative', zIndex: 9999 }} className="relative">
        <div className={`
          relative flex items-center
          bg-notely-surface border border-notely-border rounded-xl
          transition-all duration-300 ease-out
          ${isFocused ? 'border-notely-sky shadow-lg shadow-notely-sky/20' : 'hover:border-notely-mint/50'}
        `}>
          {/* Search Icon */}
          <div className="absolute left-4 flex items-center pointer-events-none">
            <svg 
              className={`h-5 w-5 transition-colors duration-200 ${
                isFocused ? 'text-notely-sky' : 'text-notely-text-muted'
              }`} 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>

          {/* Input Field */}
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onKeyDown={handleKeyDown}
            placeholder="Ask My Bookmarks…"
            className={`
              w-full pl-12 pr-20 py-4 
              bg-transparent border-none outline-none
              text-notely-text-primary placeholder-notely-text-muted
              font-medium text-base
            `}
          />

          {/* Loading/Clear Button */}
          <div className="absolute right-4 flex items-center space-x-2">
            {isSearching && (
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-notely-sky rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-notely-mint rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-notely-coral rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              </div>
            )}
            
            {query && !isSearching && (
              <button
                onClick={handleClearSearch}
                className="p-1 rounded-full hover:bg-notely-card transition-colors duration-200"
                title="Clear search"
              >
                <svg className="h-4 w-4 text-notely-text-muted hover:text-notely-text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}

            {/* Keyboard Shortcut Hint */}
            {!isFocused && !query && (
              <div className="hidden sm:flex items-center space-x-1 text-xs text-notely-text-muted">
                <kbd className="px-2 py-1 bg-notely-card border border-notely-border rounded text-xs">⌘K</kbd>
              </div>
            )}
          </div>
        </div>

        {/* Search Suggestions Dropdown */}
        {showSuggestions && suggestions.length > 0 && (
          <div 
            style={{ position: 'absolute', left: 0, right: 0, marginTop: '0.5rem', zIndex: 9999 }}
            className="absolute left-0 right-0 mt-2 z-[9999]"
          >
            <div 
              style={{ backgroundColor: '#1E1E1E', borderColor: '#333333' }}
              className="border border-notely-border rounded-xl shadow-2xl overflow-hidden"
            >
              <div 
                style={{ backgroundColor: '#2D2D2D', borderColor: '#333333' }}
                className="p-3 border-b border-notely-border"
              >
                <h4 
                  style={{ color: 'white' }}
                  className="text-sm font-medium"
                >
                  Suggested searches
                </h4>
              </div>
              <div className="max-h-64 overflow-y-auto">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    style={{ 
                      backgroundColor: '#1E1E1E', 
                      color: 'white',
                      borderColor: '#333333'
                    }}
                    className="w-full px-4 py-3 text-left hover:bg-opacity-80 transition-colors duration-150 border-b last:border-b-0"
                  >
                    <div className="flex items-center space-x-3">
                      <svg 
                        style={{ color: '#888888' }}
                        className="h-4 w-4" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <span style={{ color: 'white' }} className="text-sm">{suggestion}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4 p-4 bg-notely-coral/10 border border-notely-coral/20 rounded-xl">
          <div className="flex items-center space-x-2">
            <svg className="h-5 w-5 text-notely-coral" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-notely-coral font-medium">{error}</span>
          </div>
        </div>
      )}

      {/* Search Results */}
      {searchResults && (
        <SearchResultsSummary 
          searchResults={searchResults}
          onClearResults={handleClearSearch}
          className="mt-6"
        />
      )}
    </div>
  );
};

export default AskMyBookmarks;
