# SavedBookmarksWidget Manual Features - Test Plan

## Test Scenarios for Manual Bookmark Management

### 1. **Add Manual Bookmark**

#### Test Steps:
1. Open dashboard with SavedBookmarksWidget
2. Click green "+ Add" button in header
3. Fill form:
   - URL: `https://github.com`
   - Title: `GitHub`
   - Description: `Code repository platform`
   - Category: `Work & Knowledge`
4. Click "Add Bookmark"

#### Expected Results:
- ✅ Modal closes automatically
- ✅ New bookmark appears in "Work & Knowledge" category
- ✅ Bookmark shows "• Manual" indicator
- ✅ Favicon loads from GitHub
- ✅ Bookmark is clickable and opens GitHub
- ✅ Delete button (×) appears on hover

#### Storage Verification:
```javascript
// Check manual bookmarks in storage
chrome.storage.local.get('manual_bookmarks').then(console.log);
```

### 2. **Create Custom Category**

#### Test Steps:
1. Click purple "Categories" button
2. Fill "Add New Category" form:
   - Name: `Design Tools`
   - Emoji: `🎨`
   - Tags: `Design, Art, Creative, UI, UX`
3. Click "Add Category"

#### Expected Results:
- ✅ New category appears in custom categories list
- ✅ Category available in bookmark form dropdown
- ✅ Category appears in main category pills
- ✅ Can switch to new category and see empty state

#### Storage Verification:
```javascript
// Check custom categories in storage
chrome.storage.local.get('custom_categories').then(console.log);
```

### 3. **AI Uses Custom Category**

#### Test Steps:
1. Create custom category "Design Tools" (as above)
2. Save a post containing design-related content:
   - Example: "Check out this amazing Figma plugin for designers"
3. Wait for AI processing (or trigger manually)

#### Expected Results:
- ✅ AI extracts bookmark and categorizes it as "Design Tools"
- ✅ Bookmark appears in custom category, not default category
- ✅ Shows AI-extracted content with custom categorization

### 4. **Delete Bookmarks**

#### Test Steps:
1. Hover over any bookmark (manual or AI-extracted)
2. Click red "×" delete button
3. Verify bookmark disappears

#### Expected Results:
- ✅ Manual bookmarks: Removed from storage and UI
- ✅ AI bookmarks: Removed from component state
- ✅ No errors in console
- ✅ Other bookmarks remain unaffected

### 5. **Remove Custom Category**

#### Test Steps:
1. Open "Categories" modal
2. Click "Remove" next to a custom category
3. Verify category is removed

#### Expected Results:
- ✅ Category disappears from custom categories list
- ✅ Category no longer available in bookmark form
- ✅ Category pills updated (custom category removed)
- ✅ Existing bookmarks in that category remain visible

### 6. **Form Validation**

#### Test Steps:
1. Try to add bookmark with empty URL
2. Try to add bookmark with invalid URL
3. Try to add bookmark with empty title
4. Try to add category with empty fields

#### Expected Results:
- ✅ Form prevents submission with missing required fields
- ✅ URL validation works (must be valid URL format)
- ✅ Error states shown appropriately
- ✅ Form doesn't submit until all required fields filled

### 7. **Persistence Across Sessions**

#### Test Steps:
1. Add manual bookmarks and custom categories
2. Reload dashboard/extension
3. Verify data persists

#### Expected Results:
- ✅ Manual bookmarks load on component mount
- ✅ Custom categories load and appear in UI
- ✅ All functionality works after reload
- ✅ No data loss

### 8. **Mixed Bookmark Display**

#### Test Steps:
1. Have both AI-extracted and manual bookmarks
2. Switch between categories
3. Verify proper filtering and display

#### Expected Results:
- ✅ Both types of bookmarks display correctly
- ✅ Manual bookmarks show "• Manual" indicator
- ✅ AI bookmarks show normally
- ✅ Filtering works for both types
- ✅ Delete functionality works for both types

## Console Commands for Testing

### Clear All Data
```javascript
// Clear all bookmark-related data
chrome.storage.local.remove(['manual_bookmarks', 'custom_categories', 'bookmark_processed_posts']);
```

### Add Test Manual Bookmark
```javascript
// Add test manual bookmark directly to storage
const testBookmark = {
  id: 'manual-test-123',
  url: 'https://example.com',
  domain: 'example.com',
  title: 'Test Bookmark',
  description: 'Test description',
  category: 'Work & Knowledge',
  tags: ['Test'],
  favicon: 'https://www.google.com/s2/favicons?domain=example.com&sz=16',
  post: { id: 'manual', platform: 'Manual', content: 'Manually added bookmark' }
};

chrome.storage.local.get('manual_bookmarks').then(result => {
  const bookmarks = result.manual_bookmarks || [];
  bookmarks.push(testBookmark);
  chrome.storage.local.set({ manual_bookmarks: bookmarks });
});
```

### Add Test Custom Category
```javascript
// Add test custom category directly to storage
const testCategory = {
  name: 'Test Category',
  emoji: '🧪',
  tags: ['Test', 'Example', 'Demo']
};

chrome.storage.local.get('custom_categories').then(result => {
  const categories = result.custom_categories || [];
  categories.push(testCategory);
  chrome.storage.local.set({ custom_categories: categories });
});
```

### Check Storage Contents
```javascript
// View all bookmark-related storage
chrome.storage.local.get(['manual_bookmarks', 'custom_categories', 'bookmark_processed_posts']).then(console.log);
```

## Expected Console Output

### On Component Load
```
[SavedBookmarksWidget] Loaded 5 previously processed posts
[SavedBookmarksWidget] Loaded 2 manual bookmarks
[SavedBookmarksWidget] Loaded 1 custom categories
[SavedBookmarksWidget] Using AI bookmarks: 8 Manual bookmarks: 2
```

### On Manual Bookmark Add
```
[SavedBookmarksWidget] Added manual bookmark: GitHub
```

### On Custom Category Add
```
[SavedBookmarksWidget] Added custom category: Design Tools
```

### On Bookmark Delete
```
[SavedBookmarksWidget] Removed manual bookmark: manual-1234567890
```

### On Category Remove
```
[SavedBookmarksWidget] Removed custom category: Design Tools
```

## Performance Verification

### Metrics to Check
1. **Load Time**: Manual bookmarks should load instantly
2. **Storage Usage**: Minimal increase in storage usage
3. **UI Responsiveness**: No lag when adding/removing bookmarks
4. **Memory Usage**: No memory leaks from modal operations

### Expected Performance
- ✅ Manual bookmarks load in <100ms
- ✅ Modal operations complete in <50ms
- ✅ No noticeable UI lag
- ✅ Storage operations complete quickly

## Edge Cases to Test

### 1. **Large Numbers**
- Add 50+ manual bookmarks
- Create 10+ custom categories
- Verify performance remains good

### 2. **Special Characters**
- URLs with special characters
- Titles with emojis and unicode
- Category names with special characters

### 3. **Network Issues**
- Test with slow/no internet (favicon loading)
- Verify graceful fallback for favicon failures

### 4. **Storage Limits**
- Test near Chrome storage limits
- Verify graceful handling of storage errors

## Success Criteria

✅ **Functional Requirements**
- [ ] Can add manual bookmarks successfully
- [ ] Can create and remove custom categories
- [ ] AI uses custom categories for new bookmarks
- [ ] Can delete both manual and AI bookmarks
- [ ] All data persists across sessions

✅ **User Experience Requirements**
- [ ] Intuitive UI with clear visual indicators
- [ ] Responsive modals and forms
- [ ] Proper validation and error handling
- [ ] No breaking changes to existing functionality

✅ **Technical Requirements**
- [ ] Efficient storage usage
- [ ] No performance degradation
- [ ] Clean console output (no errors)
- [ ] Proper error handling and fallbacks
