import React, { useState, useEffect } from 'react';
import { WidgetConfig, WidgetPreset, WIDGET_PRESETS, DEFAULT_WIDGET_CONFIGS } from '../types/widgets';

interface WidgetSettingsProps {
  onSave: (configs: WidgetConfig[]) => void;
  onClose: () => void;
}

const WidgetSettings: React.FC<WidgetSettingsProps> = ({ onSave, onClose }) => {
  const [activeTab, setActiveTab] = useState<'presets' | 'custom' | 'individual'>('presets');
  const [widgetConfigs, setWidgetConfigs] = useState<WidgetConfig[]>(DEFAULT_WIDGET_CONFIGS);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  // Load saved widget configurations
  useEffect(() => {
    chrome.storage.local.get(['widgetConfigs'], (result) => {
      if (result.widgetConfigs) {
        setWidgetConfigs(result.widgetConfigs);
      }
    });
  }, []);

  const handlePresetSelect = (preset: WidgetPreset) => {
    setSelectedPreset(preset.id);
    setWidgetConfigs(preset.widgets);
  };

  const handleWidgetToggle = (widgetId: string) => {
    setWidgetConfigs(prev => 
      prev.map(widget => 
        widget.id === widgetId 
          ? { ...widget, enabled: !widget.enabled }
          : widget
      )
    );
  };

  const handleWidgetSettingChange = (widgetId: string, settingKey: string, value: any) => {
    setWidgetConfigs(prev => 
      prev.map(widget => 
        widget.id === widgetId 
          ? { 
              ...widget, 
              settings: { 
                ...widget.settings, 
                [settingKey]: value 
              }
            }
          : widget
      )
    );
  };

  const handleSave = () => {
    // Save to chrome storage
    chrome.storage.local.set({ widgetConfigs }, () => {
      onSave(widgetConfigs);
      onClose();
    });
  };

  const renderPresetTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Choose a Widget Layout</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {WIDGET_PRESETS.map((preset) => (
          <div
            key={preset.id}
            className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
              selectedPreset === preset.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handlePresetSelect(preset)}
          >
            <div className="text-center">
              <div className="text-3xl mb-2">{preset.icon}</div>
              <h4 className="font-semibold text-gray-800">{preset.name}</h4>
              <p className="text-sm text-gray-600 mt-1">{preset.description}</p>
              <div className="mt-3 text-xs text-gray-500">
                {preset.widgets.length} widgets included
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderIndividualTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Customize Individual Widgets</h3>
      <div className="space-y-4">
        {widgetConfigs.map((widget) => (
          <div key={widget.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <span className="text-xl">{widget.icon}</span>
                <div>
                  <h4 className="font-medium text-gray-800">{widget.title}</h4>
                  <p className="text-sm text-gray-600">{widget.description}</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={widget.enabled}
                  onChange={() => handleWidgetToggle(widget.id)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {widget.enabled && widget.type === 'fashion-gallery' && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-gray-700 mb-2">Visual Gallery Settings</h5>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Gallery Type</label>
                    <select
                      value={widget.settings?.mode || 'auto'}
                      onChange={(e) => handleWidgetSettingChange(widget.id, 'mode', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="auto">🤖 Auto-Detect (AI decides)</option>
                      <option value="fashion">👗 Fashion Gallery</option>
                      <option value="art">🎨 Art Gallery</option>
                      <option value="webdesign">💻 Web Design Gallery</option>
                      <option value="photography">📷 Photography Gallery</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Custom Title</label>
                    <input
                      type="text"
                      value={widget.settings?.title || widget.title}
                      onChange={(e) => handleWidgetSettingChange(widget.id, 'title', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                      placeholder="Enter custom title..."
                    />
                  </div>
                </div>
              </div>
            )}

            {widget.enabled && widget.type === 'ai-insights' && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-gray-700 mb-2">AI Insights Settings</h5>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`${widget.id}-trending`}
                      checked={widget.settings?.showTrendingTopics !== false}
                      onChange={(e) => handleWidgetSettingChange(widget.id, 'showTrendingTopics', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor={`${widget.id}-trending`} className="text-sm text-gray-600">Show Trending Topics</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`${widget.id}-insights`}
                      checked={widget.settings?.showSmartInsights !== false}
                      onChange={(e) => handleWidgetSettingChange(widget.id, 'showSmartInsights', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor={`${widget.id}-insights`} className="text-sm text-gray-600">Show Smart Insights</label>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">
                      Confidence Threshold: {Math.round((widget.settings?.confidenceThreshold || 0.6) * 100)}%
                    </label>
                    <input
                      type="range"
                      min="0.1"
                      max="1"
                      step="0.1"
                      value={widget.settings?.confidenceThreshold || 0.6}
                      onChange={(e) => handleWidgetSettingChange(widget.id, 'confidenceThreshold', parseFloat(e.target.value))}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            )}

            {widget.enabled && widget.type === 'bookmark-grid' && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-gray-700 mb-2">Bookmark Settings</h5>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`${widget.id}-auto-categorize`}
                      checked={widget.settings?.autoCategorizeDomains !== false}
                      onChange={(e) => handleWidgetSettingChange(widget.id, 'autoCategorizeDomains', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor={`${widget.id}-auto-categorize`} className="text-sm text-gray-600">Auto-categorize by domain</label>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Max Bookmarks</label>
                    <select
                      value={widget.settings?.maxBookmarks || 6}
                      onChange={(e) => handleWidgetSettingChange(widget.id, 'maxBookmarks', parseInt(e.target.value))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value={4}>4 bookmarks</option>
                      <option value={6}>6 bookmarks</option>
                      <option value={8}>8 bookmarks</option>
                      <option value={10}>10 bookmarks</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Widget Settings</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('presets')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === 'presets'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Presets
          </button>
          <button
            onClick={() => setActiveTab('individual')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === 'individual'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Individual Widgets
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'presets' && renderPresetTab()}
          {activeTab === 'individual' && renderIndividualTab()}
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default WidgetSettings;
