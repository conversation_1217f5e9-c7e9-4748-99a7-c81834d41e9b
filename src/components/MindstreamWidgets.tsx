import React from 'react';
import { Post } from '../types';
import ChatWithPosts from './ChatWithPosts';
import SmartWorkspace from './SmartWorkspace';
import SavedBookmarksWidget from './SavedBookmarksWidget';

interface MindstreamWidgetsProps {
  posts: Post[];
  className?: string;
  onOpenPost?: (post: Post) => void;
}

const MindstreamWidgets: React.FC<MindstreamWidgetsProps> = ({ posts, className = '', onOpenPost }) => {
  return (
    <div className={`space-y-8 ${className}`}>
      {/* AI Chat Interface */}
      <ChatWithPosts posts={posts} onOpenPost={onOpenPost} />

      {/* All the original SmartWorkspace content */}
      <SmartWorkspace posts={posts} />

      {/* Saved Bookmarks */}
      <div className="notely-card bg-notely-card rounded-notely-xl shadow-notely-md hover:shadow-notely-lg notely-filter-transition p-6 border border-notely-border/10 dark:border-notely-border-dark/20">
        <h2 className="text-xl font-semibold mb-4 notely-heading">📚 Saved Bookmarks</h2>
        <SavedBookmarksWidget
          posts={posts}
          isDragging={false}
          onRemove={() => {}}
        />
      </div>
    </div>
  );
};

export default MindstreamWidgets;
