import React from 'react';
import { CategoryOverview as CategoryOverviewType } from '../types';

interface CategoryOverviewProps {
  overview: CategoryOverviewType | null;
  className?: string;
}

const CategoryOverview: React.FC<CategoryOverviewProps> = ({ overview, className = '' }) => {
  if (!overview) {
    return (
      <div className={`ui-category-stats ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-notely-surface rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-notely-surface rounded w-3/4"></div>
            <div className="h-4 bg-notely-surface rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (overview.isLoading) {
    return (
      <div className={`ui-category-stats ${className}`}>
        <div className="ui-category-stats-header">
          <div className="ui-category-stats-icon">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          </div>
          <h3 className="ui-category-stats-title">
            Analyzing {overview.categoryName}...
          </h3>
        </div>
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (overview.error) {
    return (
      <div className={`ui-category-stats ${className}`}>
        <div className="ui-category-stats-header">
          <div className="ui-category-stats-icon" style={{ background: 'linear-gradient(135deg, #ef4444, #dc2626)' }}>
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="ui-category-stats-title">
            {overview.categoryName} Overview
          </h3>
        </div>
        <p className="text-red-600 text-sm">{overview.error}</p>
      </div>
    );
  }

  return (
    <div className={`ui-category-stats ${className}`}>
      {/* Header with Icon */}
      <div className="ui-category-stats-header">
        <div className="ui-category-stats-icon">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <div>
          <h3 className="ui-category-stats-title">
            {overview.categoryName}
          </h3>
          <span className="ui-caption-text">
            📄 {overview.postCount} posts • 📊 106 avg chars
          </span>
        </div>
      </div>

      {/* Statistics */}
      <div className="ui-category-stats-content">
        <div className="ui-category-stat-item">
          <span className="ui-category-stat-label">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Average post length
          </span>
          <span className="ui-category-stat-value">106 characters</span>
        </div>
        
        <div className="ui-category-stat-item">
          <span className="ui-category-stat-label">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
            </svg>
            Platform breakdown
          </span>
          <span className="ui-category-stat-value">X/Twitter: {overview.postCount}</span>
        </div>
      </div>

      {/* Summary */}
      <div className="mt-4">
        <h4 className="ui-body-text font-medium mb-2">📝 Summary</h4>
        <p className="ui-caption-text leading-relaxed">
          {overview.summary}
        </p>
      </div>

      {/* CTA Link */}
      <a href="#" className="ui-category-link">
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
        Explore {overview.postCount} posts
      </a>
    </div>
  );

      {/* Key Insights */}
      {overview.keyInsights.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Key Insights</h4>
          <ul className="space-y-2">
            {overview.keyInsights.map((insight, index) => (
              <li key={index} className="flex items-start">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-sm text-gray-600 leading-relaxed">{insight}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Patterns */}
      {overview.patterns && overview.patterns.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            Patterns
          </h4>
          <ul className="space-y-2">
            {overview.patterns.map((pattern, index) => (
              <li key={index} className="flex items-start">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-sm text-gray-600 leading-relaxed">{pattern}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* User Motivations */}
      {overview.userMotivations && overview.userMotivations.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
            Your Motivations
          </h4>
          <ul className="space-y-2">
            {overview.userMotivations.map((motivation, index) => (
              <li key={index} className="flex items-start">
                <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-sm text-gray-600 leading-relaxed">{motivation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Thematic Tags */}
      {overview.thematicTags.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Thematic Tags</h4>
          <div className="flex flex-wrap gap-2">
            {overview.thematicTags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 transition-colors"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-notely-border/20">
        <p className="text-xs text-gray-400">
          Generated {new Date(overview.generatedAt).toLocaleDateString()} at{' '}
          {new Date(overview.generatedAt).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </p>
      </div>
    </div>
  );
};

export default CategoryOverview;
