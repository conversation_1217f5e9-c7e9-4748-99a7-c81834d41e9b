import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Post } from '../types';
import { PlatformLogo } from './PlatformLogo';
import { PostContentRenderer } from './PostContentRenderer';
import { useAIEnrichment } from '../hooks/useAIEnrichment';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';

interface CaptureInspirationModalProps {
  posts: Post[];
  onClose: () => void;
}

const CaptureInspirationModal: React.FC<CaptureInspirationModalProps> = ({
  posts,
  onClose,
}) => {
  const { t } = useTranslation();
  const [selectedPosts, setSelectedPosts] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState<'create' | 'analyze' | 'export'>('create');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<string>('');
  const [contentType, setContentType] = useState<'thread' | 'summary' | 'hashtags' | 'newsletter'>('thread');
  const [isClosing, setIsClosing] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  
  const aiEnrichment = useAIEnrichment('free');

  // Handle close with animation
  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 300);
  }, [onClose]);

  // Handle escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };
  }, [handleClose]);

  // Handle post selection
  const handlePostSelect = (postId: string) => {
    const newSelected = new Set(selectedPosts);
    if (newSelected.has(postId)) {
      newSelected.delete(postId);
    } else {
      newSelected.add(postId);
    }
    setSelectedPosts(newSelected);
  };

  // Handle content generation
  const handleGenerate = async () => {
    if (selectedPosts.size === 0) {
      toast.error('Please select at least one post to generate content');
      return;
    }

    setIsGenerating(true);
    try {
      const selectedPostsData = posts.filter(post => selectedPosts.has(post.id || ''));
      const combinedContent = selectedPostsData.map(post => post.content || post.title || '').join('\n\n');

      let prompt = '';
      switch (contentType) {
        case 'thread':
          prompt = `Create a Twitter/X thread based on these insights: ${combinedContent}`;
          break;
        case 'summary':
          prompt = `Create a comprehensive summary of these posts: ${combinedContent}`;
          break;
        case 'hashtags':
          prompt = `Generate relevant hashtags for these posts: ${combinedContent}`;
          break;
        case 'newsletter':
          prompt = `Create a newsletter section based on these insights: ${combinedContent}`;
          break;
      }

      // Simulate AI generation (replace with actual AI service)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockContent = {
        thread: `🧵 Thread based on your inspiration:\n\n1/ Here's what I learned from my saved posts...\n\n2/ Key insight: ${combinedContent.substring(0, 100)}...\n\n3/ This changes everything because...\n\n4/ What do you think? 💭`,
        summary: `📝 Summary of Your Insights:\n\n${combinedContent.substring(0, 200)}...\n\nKey takeaways:\n• Point 1\n• Point 2\n• Point 3`,
        hashtags: `#inspiration #content #socialmedia #productivity #insights #mindstream #notely #ai #creativity #growth`,
        newsletter: `📧 Newsletter Section:\n\n**This Week's Insights**\n\n${combinedContent.substring(0, 150)}...\n\nWhat this means for you:\n- Actionable insight 1\n- Actionable insight 2`
      };

      setGeneratedContent(mockContent[contentType]);
      setActiveTab('export');
    } catch (error) {
      toast.error('Failed to generate content. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(generatedContent);
    toast.success('Content copied to clipboard!');
  };

  const getTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  const getPostPreview = (post: Post): string => {
    const content = post.content || post.title || '';
    return content.length > 100 ? content.substring(0, 100) + '...' : content;
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm transition-all duration-300 ${isClosing ? 'opacity-0' : 'opacity-100'}`}>
      <div 
        ref={modalRef}
        className={`w-full max-w-6xl max-h-[90vh] bg-notely-card border border-notely-border rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 ${isClosing ? 'scale-95 opacity-0' : 'scale-100 opacity-100'}`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-notely-border bg-gradient-to-r from-purple-500/10 to-pink-500/10">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-lg">✨</span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-notely-text-primary">Capture Inspiration</h2>
              <p className="text-sm text-notely-text-muted">Transform your saved posts into actionable content</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 rounded-full bg-notely-surface hover:bg-notely-border transition-colors flex items-center justify-center"
          >
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-notely-border bg-notely-surface/50">
          {[
            { id: 'create', label: 'Select & Create', icon: '🎯' },
            { id: 'analyze', label: 'AI Analysis', icon: '🧠' },
            { id: 'export', label: 'Export Content', icon: '📤' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 px-6 py-4 text-sm font-medium transition-colors flex items-center justify-center space-x-2 ${
                activeTab === tab.id
                  ? 'text-notely-accent border-b-2 border-notely-accent bg-notely-accent/5'
                  : 'text-notely-text-muted hover:text-notely-text-primary hover:bg-notely-surface'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'create' && (
            <div className="space-y-6">
              {/* Content Type Selection */}
              <div>
                <h3 className="text-lg font-semibold text-notely-text-primary mb-3">What would you like to create?</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {[
                    { id: 'thread', label: 'Twitter Thread', icon: '🧵', desc: 'Multi-tweet story' },
                    { id: 'summary', label: 'Summary', icon: '📝', desc: 'Key insights' },
                    { id: 'hashtags', label: 'Hashtags', icon: '#️⃣', desc: 'Relevant tags' },
                    { id: 'newsletter', label: 'Newsletter', icon: '📧', desc: 'Email content' }
                  ].map((type) => (
                    <button
                      key={type.id}
                      onClick={() => setContentType(type.id as any)}
                      className={`p-4 rounded-xl border-2 transition-all text-left ${
                        contentType === type.id
                          ? 'border-notely-accent bg-notely-accent/10'
                          : 'border-notely-border hover:border-notely-accent/50'
                      }`}
                    >
                      <div className="text-2xl mb-2">{type.icon}</div>
                      <div className="font-medium text-sm text-notely-text-primary">{type.label}</div>
                      <div className="text-xs text-notely-text-muted">{type.desc}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Post Selection */}
              <div>
                <h3 className="text-lg font-semibold text-notely-text-primary mb-3">
                  Select posts to include ({selectedPosts.size} selected)
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {posts.slice(0, 12).map((post) => (
                    <div
                      key={post.id}
                      onClick={() => handlePostSelect(post.id || '')}
                      className={`cursor-pointer rounded-xl p-4 border-2 transition-all ${
                        selectedPosts.has(post.id || '')
                          ? 'border-notely-accent bg-notely-accent/10'
                          : 'border-notely-border hover:border-notely-accent/50'
                      }`}
                    >
                      <div className="flex items-center space-x-2 mb-2">
                        <PlatformLogo platform={post.platform} className="w-4 h-4" />
                        <span className="text-xs text-notely-text-muted">{getTimeAgo(post.savedAt || post.timestamp || '')}</span>
                      </div>
                      <p className="text-sm text-notely-text-primary line-clamp-3">{getPostPreview(post)}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Generate Button */}
              <div className="flex justify-center">
                <button
                  onClick={handleGenerate}
                  disabled={selectedPosts.size === 0 || isGenerating}
                  className={`px-8 py-3 rounded-xl font-medium transition-all ${
                    selectedPosts.size > 0 && !isGenerating
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:scale-105 shadow-lg'
                      : 'bg-notely-surface text-notely-text-muted cursor-not-allowed'
                  }`}
                >
                  {isGenerating ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>Generating...</span>
                    </div>
                  ) : (
                    `Generate ${contentType.charAt(0).toUpperCase() + contentType.slice(1)}`
                  )}
                </button>
              </div>
            </div>
          )}

          {activeTab === 'analyze' && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🧠</span>
              </div>
              <h3 className="text-lg font-semibold text-notely-text-primary mb-2">AI Analysis Coming Soon</h3>
              <p className="text-notely-text-muted">Deep insights and pattern recognition for your saved content</p>
            </div>
          )}

          {activeTab === 'export' && (
            <div className="space-y-6">
              {generatedContent ? (
                <>
                  <div>
                    <h3 className="text-lg font-semibold text-notely-text-primary mb-3">Generated Content</h3>
                    <div className="bg-notely-surface rounded-xl p-4 border border-notely-border">
                      <pre className="whitespace-pre-wrap text-sm text-notely-text-primary font-mono">{generatedContent}</pre>
                    </div>
                  </div>
                  <div className="flex justify-center space-x-4">
                    <button
                      onClick={handleCopy}
                      className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl font-medium hover:scale-105 transition-transform"
                    >
                      📋 Copy to Clipboard
                    </button>
                    <button
                      onClick={() => setActiveTab('create')}
                      className="px-6 py-2 bg-notely-surface border border-notely-border text-notely-text-primary rounded-xl font-medium hover:bg-notely-border transition-colors"
                    >
                      Create Another
                    </button>
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📤</span>
                  </div>
                  <h3 className="text-lg font-semibold text-notely-text-primary mb-2">No Content Generated Yet</h3>
                  <p className="text-notely-text-muted">Go to the "Select & Create" tab to generate content first</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CaptureInspirationModal;
