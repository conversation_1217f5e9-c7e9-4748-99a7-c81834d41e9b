import React, { useState, useEffect } from 'react';
import { Post } from '../types';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';
import { generateActionIdeas } from '../services/inspireToDoService';

interface InspireToDoWidgetProps {
  posts: Post[];
  isDragging: boolean;
  onRemove: () => void;
}

interface ActionIdea {
  id: string;
  title: string;
  suggestedTool: string;
  status: 'todo' | 'draft' | 'done';
  createdAt: Date;
}

type TaskStatus = 'todo' | 'draft' | 'done';

const InspireToDoWidget: React.FC<InspireToDoWidgetProps> = ({ posts, isDragging, onRemove }) => {
  const { t } = useTranslation();
  const [actionIdeas, setActionIdeas] = useState<ActionIdea[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Get last 10 saved bookmarks
  const getRecentBookmarks = (): Post[] => {
    return posts
      .sort((a, b) => new Date(b.savedAt || b.timestamp || '').getTime() - new Date(a.savedAt || a.timestamp || '').getTime())
      .slice(0, 10);
  };

  // Generate action ideas from bookmarks
  const generateIdeas = async () => {
    const recentBookmarks = getRecentBookmarks();
    
    if (recentBookmarks.length === 0) {
      setActionIdeas([]);
      return;
    }

    setIsLoading(true);
    try {
      const ideas = await generateActionIdeas(recentBookmarks);
      const actionIdeasWithIds = ideas.map((idea, index) => ({
        id: `idea-${Date.now()}-${index}`,
        ...idea,
        status: 'todo' as TaskStatus,
        createdAt: new Date()
      }));
      
      setActionIdeas(actionIdeasWithIds);
      setLastUpdated(new Date());
      
      // Store in localStorage for persistence
      localStorage.setItem('inspireToDoIdeas', JSON.stringify(actionIdeasWithIds));
      localStorage.setItem('inspireToDoLastUpdated', new Date().toISOString());
      
      toast.success('New action ideas generated! ✨');
    } catch (error) {
      console.error('Failed to generate action ideas:', error);
      toast.error('Failed to generate ideas. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load persisted data on mount
  useEffect(() => {
    const savedIdeas = localStorage.getItem('inspireToDoIdeas');
    const savedLastUpdated = localStorage.getItem('inspireToDoLastUpdated');
    
    if (savedIdeas) {
      try {
        const parsedIdeas = JSON.parse(savedIdeas);
        setActionIdeas(parsedIdeas);
      } catch (error) {
        console.error('Failed to parse saved ideas:', error);
      }
    }
    
    if (savedLastUpdated) {
      setLastUpdated(new Date(savedLastUpdated));
    }
    
    // Auto-generate if no saved ideas or if it's been more than 24 hours
    const shouldAutoGenerate = !savedIdeas || 
      (savedLastUpdated && Date.now() - new Date(savedLastUpdated).getTime() > 24 * 60 * 60 * 1000);
    
    if (shouldAutoGenerate && posts.length > 0) {
      generateIdeas();
    }
  }, [posts.length]);

  // Cycle task status
  const cycleStatus = (ideaId: string) => {
    setActionIdeas(prev => {
      const updated = prev.map(idea => {
        if (idea.id === ideaId) {
          const statusCycle: Record<TaskStatus, TaskStatus> = {
            'todo': 'draft',
            'draft': 'done',
            'done': 'todo'
          };
          return { ...idea, status: statusCycle[idea.status] };
        }
        return idea;
      });
      
      // Persist updated status
      localStorage.setItem('inspireToDoIdeas', JSON.stringify(updated));
      return updated;
    });
  };

  // Open suggested tool
  const openTool = (suggestedTool: string) => {
    toast.info(`Opening ${suggestedTool}... 🚀`);
    // TODO: Implement tool opening logic
    console.log(`Opening tool: ${suggestedTool}`);
  };

  // Get status pill styling
  const getStatusPill = (status: TaskStatus) => {
    const styles = {
      todo: {
        icon: '⭕',
        text: 'To-Do',
        className: 'bg-gray-100/80 text-gray-800 dark:bg-white/5 dark:text-white/80 backdrop-blur-sm'
      },
      draft: {
        icon: '✍️',
        text: 'Draft',
        className: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      },
      done: {
        icon: '✅',
        text: 'Done',
        className: 'bg-green-500/10 text-green-400 border-green-500/20'
      }
    };
    return styles[status];
  };

  // Format time ago
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  // Empty state
  if (posts.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mb-4">
          <span className="text-6xl">💡</span>
        </div>
        <h4 className="text-xl font-semibold leading-tight text-notely-text-primary mb-2">
          No inspiration yet!
        </h4>
        <p className="text-sm text-notely-text-muted leading-relaxed">
          Save posts to get inspired with actionable tasks
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Header with refresh button and last updated */}
      <div className="flex items-center justify-between">
        {lastUpdated && (
          <p className="uppercase text-xs text-notely-text-muted tracking-wide">
            Updated {formatTimeAgo(lastUpdated)}
          </p>
        )}

        <button
          onClick={generateIdeas}
          disabled={isLoading}
          className="p-2 notely-card bg-notely-card border border-notely-border rounded-xl transition-all hover:scale-105 disabled:opacity-50 text-notely-text-primary"
          title="Refresh ideas"
        >
          <div className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`}>
            🔄
          </div>
        </button>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-notely-accent mx-auto mb-2"></div>
          <p className="text-xs text-notely-text-secondary">Generating ideas...</p>
        </div>
      )}

      {/* Action Ideas */}
      {!isLoading && actionIdeas.length > 0 && (
        <div className="space-y-3">
          {actionIdeas.map((idea, index) => {
            const statusPill = getStatusPill(idea.status);

            return (
              <div
                key={idea.id}
                className="notely-card bg-notely-card border border-notely-border rounded-xl px-4 py-3 hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1 transition-all duration-300 animate-fade-in shadow-notely-md"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                {/* Main Content */}
                <div className="flex items-start gap-3 mb-3">
                  {/* Icon */}
                  <div className="w-8 h-8 bg-notely-surface border border-notely-border rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm">🧠</span>
                  </div>

                  {/* Title */}
                  <div className="flex-1 min-w-0">
                    <p className="text-lg font-semibold leading-tight line-clamp-2 text-notely-text-primary">
                      {idea.title}
                    </p>
                  </div>

                  {/* Status Pill */}
                  <button
                    onClick={() => cycleStatus(idea.id)}
                    className="flex items-center space-x-1 text-[11px] font-medium rounded-lg px-2 py-1 bg-notely-surface border border-notely-border transition-all duration-200 hover:scale-105 flex-shrink-0 text-notely-text-secondary"
                  >
                    <span>{statusPill.icon}</span>
                    <span>{statusPill.text}</span>
                  </button>
                </div>

                {/* Tool Button */}
                <button
                  onClick={() => openTool(idea.suggestedTool)}
                  className="w-full text-left px-4 py-2 bg-notely-surface border border-notely-border rounded-lg hover:scale-[1.02] transition-all"
                >
                  <span className="text-sm text-notely-text-secondary leading-relaxed">
                    Open in <span className="font-medium text-notely-text-primary">{idea.suggestedTool}</span>
                  </span>
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* No Ideas State */}
      {!isLoading && actionIdeas.length === 0 && posts.length > 0 && (
        <div className="text-center py-6">
          <span className="text-3xl mb-3 block">🤔</span>
          <p className="text-sm text-notely-text-muted leading-relaxed mb-3">
            No action ideas yet
          </p>
          <button
            onClick={generateIdeas}
            className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-4 py-2 text-sm font-medium hover:scale-105 transition-transform"
          >
            Generate Ideas
          </button>
        </div>
      )}
    </div>
  );
};

export default InspireToDoWidget;
