import React from 'react';

interface PremiumBadgeProps {
  plan: 'free' | 'premium';
  subscriptionStatus?: 'trialing' | 'active' | 'past_due' | 'canceled';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const PremiumBadge: React.FC<PremiumBadgeProps> = ({ 
  plan, 
  subscriptionStatus = 'active',
  size = 'md',
  className = '' 
}) => {
  if (plan !== 'premium') {
    return null;
  }

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const getStatusColor = () => {
    switch (subscriptionStatus) {
      case 'active':
        return 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white';
      case 'trialing':
        return 'bg-gradient-to-r from-blue-500 to-purple-600 text-white';
      case 'past_due':
        return 'bg-gradient-to-r from-orange-500 to-red-600 text-white';
      case 'canceled':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white';
    }
  };

  const getStatusText = () => {
    switch (subscriptionStatus) {
      case 'active':
        return 'Premium';
      case 'trialing':
        return 'Trial';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Expired';
      default:
        return 'Premium';
    }
  };

  return (
    <span 
      className={`
        inline-flex items-center rounded-md font-medium notely-filter-transition hover:scale-105
        ${sizeClasses[size]}
        ${getStatusColor()}
        ${className}
      `}
    >
      ✨ {getStatusText()}
    </span>
  );
};

export default PremiumBadge;
