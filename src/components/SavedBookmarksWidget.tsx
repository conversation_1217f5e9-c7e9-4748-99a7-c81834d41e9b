import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Post } from '../types';
import Widget from './Widget';
import EmojiPicker from './EmojiPicker';
import { extractAIBookmarks } from '../services/aiService';
import { formatForDisplay } from '../utils/formatUtils';
import { useTranslation } from '../hooks/useTranslation';
import { extractUrlsFromContent, fetchWebsiteMetadata, normalizeUrl, isDuplicateUrl, extractBookmarkCandidates } from '../services/urlExtractionService';
import { categoryStorage } from '../services/categoryStorageManager';

interface SavedBookmarksWidgetProps {
  posts: Post[];
  isDragging: boolean;
  onRemove: () => void;
}

interface BookmarkItem {
  id: string;
  url: string;
  domain: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  favicon?: string;
  post: Post;
}

// Storage keys
const PROCESSED_POSTS_KEY = 'bookmark_processed_posts';
const MANUAL_BOOKMARKS_KEY = 'manual_bookmarks';
const AI_BOOKMARKS_KEY = 'ai_bookmarks';
const CUSTOM_CATEGORIES_KEY = 'custom_categories';
const NEW_POSTS_THRESHOLD = 1; // Process bookmarks when we have 1 new posts

// Helper functions for managing processed posts tracking
const getProcessedPosts = async (): Promise<Set<string>> => {
  try {
    const result = await chrome.storage.local.get([PROCESSED_POSTS_KEY]);
    const processedIds = result[PROCESSED_POSTS_KEY] || [];
    return new Set(processedIds);
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error getting processed posts:', error);
    return new Set();
  }
};

const saveProcessedPosts = async (processedIds: Set<string>): Promise<void> => {
  try {
    await chrome.storage.local.set({ [PROCESSED_POSTS_KEY]: Array.from(processedIds) });
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error saving processed posts:', error);
  }
};

// Helper functions for managing manual bookmarks
const getManualBookmarks = async (): Promise<BookmarkItem[]> => {
  try {
    const result = await chrome.storage.local.get([MANUAL_BOOKMARKS_KEY]);
    return result[MANUAL_BOOKMARKS_KEY] || [];
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error getting manual bookmarks:', error);
    return [];
  }
};

const saveManualBookmarks = async (bookmarks: BookmarkItem[]): Promise<void> => {
  try {
    await chrome.storage.local.set({ [MANUAL_BOOKMARKS_KEY]: bookmarks });
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error saving manual bookmarks:', error);
  }
};

// Helper functions for managing AI bookmarks
const getAiBookmarks = async (): Promise<BookmarkItem[]> => {
  try {
    const result = await chrome.storage.local.get([AI_BOOKMARKS_KEY]);
    return result[AI_BOOKMARKS_KEY] || [];
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error getting AI bookmarks:', error);
    return [];
  }
};

const saveAiBookmarks = async (bookmarks: BookmarkItem[]): Promise<void> => {
  try {
    await chrome.storage.local.set({ [AI_BOOKMARKS_KEY]: bookmarks });
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error saving AI bookmarks:', error);
  }
};

// Helper functions for managing custom categories
const getCustomCategories = async (): Promise<Array<{ name: string; emoji: string; tags: string[] }>> => {
  try {
    const result = await chrome.storage.local.get([CUSTOM_CATEGORIES_KEY]);
    return result[CUSTOM_CATEGORIES_KEY] || [];
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error getting custom categories:', error);
    return [];
  }
};

const saveCustomCategories = async (categories: Array<{ name: string; emoji: string; tags: string[] }>): Promise<void> => {
  try {
    await chrome.storage.local.set({ [CUSTOM_CATEGORIES_KEY]: categories });
  } catch (error) {
    console.error('[SavedBookmarksWidget] Error saving custom categories:', error);
  }
};

const SavedBookmarksWidget: React.FC<SavedBookmarksWidgetProps> = ({
  posts,
  isDragging,
  onRemove
}) => {
  const { t } = useTranslation();
  const [activeCategory, setActiveCategory] = useState('Current Affairs');
  const [activeTag, setActiveTag] = useState('All');
  const [aiBookmarks, setAiBookmarks] = useState<BookmarkItem[]>([]);
  const [manualBookmarks, setManualBookmarks] = useState<BookmarkItem[]>([]);
  const [isLoadingBookmarks, setIsLoadingBookmarks] = useState(false);
  const [processedPostIds, setProcessedPostIds] = useState<Set<string>>(new Set());
  const [newPostsCount, setNewPostsCount] = useState(0);

  // UI state for modals and forms
  const [showAddBookmarkModal, setShowAddBookmarkModal] = useState(false);
  const [showEditCategoriesModal, setShowEditCategoriesModal] = useState(false);
  const [showEditBookmarkModal, setShowEditBookmarkModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [bookmarkToDelete, setBookmarkToDelete] = useState<string | null>(null);
  const [bookmarkToEdit, setBookmarkToEdit] = useState<BookmarkItem | null>(null);
  const [customCategories, setCustomCategories] = useState<Array<{ name: string; emoji: string; tags: string[] }>>([]);

  // Category editing state
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editCategoryForm, setEditCategoryForm] = useState({
    name: '',
    emoji: '',
    tags: [] as string[]
  });
  const [newTagInput, setNewTagInput] = useState('');

  // New category form state
  const [newCategoryForm, setNewCategoryForm] = useState({
    name: '',
    emoji: '📚',
    tags: ''
  });

  // Form state for adding bookmarks
  const [newBookmarkForm, setNewBookmarkForm] = useState({
    url: '',
    title: '',
    description: '',
    category: 'Work & Knowledge',
    tags: [] as string[]
  });

  // Form state for editing bookmarks
  const [editBookmarkForm, setEditBookmarkForm] = useState({
    url: '',
    title: '',
    description: '',
    category: 'Work & Knowledge',
    tags: [] as string[]
  });

  // Form validation and error state
  const [formErrors, setFormErrors] = useState({
    url: '',
    title: ''
  });

  // Default categories - can be extended with custom ones
  const defaultCategories = [
    { name: 'Current Affairs', emoji: '📢', tags: ['News', 'Politics', 'World', 'Economy'] },
    { name: 'Work & Knowledge', emoji: '💼', tags: ['Tools', 'Business', 'Education', 'Technology'] },
    { name: 'Social Trends', emoji: '📱', tags: ['Platforms', 'Entertainment', 'Culture'] },
    { name: 'Lifestyle & Opinions', emoji: '💬', tags: ['Health', 'Food', 'Travel', 'Personal'] }
  ];

  // Combine default and custom categories
  const categories = useMemo(() => {
    const combined = [...defaultCategories, ...customCategories];
    console.log('[SavedBookmarksWidget] ===== CATEGORIES COMPUTATION =====');
    console.log('[SavedBookmarksWidget] Default categories count:', defaultCategories.length);
    console.log('[SavedBookmarksWidget] Custom categories count:', customCategories.length);
    console.log('[SavedBookmarksWidget] Custom categories:', customCategories);
    console.log('[SavedBookmarksWidget] Combined categories for dropdown:', combined.map(c => `${c.emoji} ${c.name}`));
    console.log('[SavedBookmarksWidget] Total categories:', combined.length);
    console.log('[SavedBookmarksWidget] ===============================');
    return combined;
  }, [customCategories]);

  // Update form category if current selection is invalid when custom categories change
  useEffect(() => {
    if (categories.length > 0 && !categories.find(cat => cat.name === newBookmarkForm.category)) {
      // If current category is not in the list, reset to first available
      setNewBookmarkForm(prev => ({ ...prev, category: categories[0].name }));
    }
  }, [categories, newBookmarkForm.category]);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      // Load processed posts
      const processed = await getProcessedPosts();
      setProcessedPostIds(processed);
      console.log(`[SavedBookmarksWidget] Loaded ${processed.size} previously processed posts`);

      // Load manual bookmarks
      const manualBMs = await getManualBookmarks();
      setManualBookmarks(manualBMs);
      console.log(`[SavedBookmarksWidget] Loaded ${manualBMs.length} manual bookmarks`);

      // Load AI bookmarks
      const aiBMs = await getAiBookmarks();
      setAiBookmarks(aiBMs);
      console.log(`[SavedBookmarksWidget] Loaded ${aiBMs.length} AI bookmarks`);

      // Load custom categories
      const customCats = await getCustomCategories();
      setCustomCategories(customCats);
      console.log(`[SavedBookmarksWidget] Loaded ${customCats.length} custom categories:`, customCats);
      
      // DEBUG: Let's check what's actually in storage
      try {
        const allStorage = await chrome.storage.local.get(null);
        console.log('[SavedBookmarksWidget] ALL Chrome storage contents:', allStorage);
        console.log('[SavedBookmarksWidget] Looking for custom_categories key:', allStorage.custom_categories);
      } catch (error) {
        console.error('[SavedBookmarksWidget] Error reading all storage:', error);
      }
    };
    loadData();

    // Subscribe to category changes from unified storage
    const unsubscribe = categoryStorage.subscribeToChanges(async (categories) => {
      const bookmarkCategories = categories
        .filter(cat => cat.emoji && cat.tags)
        .map(cat => ({
          name: cat.name,
          emoji: cat.emoji!,
          tags: cat.tags || []
        }));
      setCustomCategories(bookmarkCategories);
    });

    return unsubscribe;
  }, []);

  // Test function for debugging categories (accessible from console)
  useEffect(() => {
    // Debug function to clear processed posts and force reprocessing
    (window as any).clearBookmarkStorage = async () => {
      try {
        await chrome.storage.local.remove([PROCESSED_POSTS_KEY, AI_BOOKMARKS_KEY, MANUAL_BOOKMARKS_KEY]);
        console.log('Cleared bookmark storage. Refresh the page to reprocess posts.');
        return { success: true, message: 'Storage cleared' };
      } catch (error) {
        console.error('Error clearing storage:', error);
        return { success: false, error: error.message };
      }
    };

    (window as any).testBookmarkCategories = async () => {
      console.log('=== Testing Bookmark Categories ===');

      // Test 1: Check current categories
      console.log('1. Current categories state:', categories);
      console.log('2. Custom categories state:', customCategories);

      // Test 2: Check unified storage
      const allCategories = await categoryStorage.getAllCategories();
      console.log('3. All categories from unified storage:', allCategories);

      const bookmarkCategories = await categoryStorage.getBookmarkCategories();
      console.log('4. Bookmark categories from unified storage:', bookmarkCategories);

      // Test 3: Add a test category
      try {
        await categoryStorage.addCategory({
          name: 'Test AI Category',
          emoji: '🤖',
          tags: ['AI', 'Test', 'Machine Learning']
        });
        console.log('5. Successfully added test category');

        // Check if it appears
        const updatedCategories = await categoryStorage.getBookmarkCategories();
        console.log('6. Updated bookmark categories:', updatedCategories);

        return {
          success: true,
          categories: updatedCategories
        };
      } catch (error) {
        console.error('5. Error adding test category:', error);
        return {
          success: false,
          error: error.message
        };
      }
    };

    return () => {
      delete (window as any).testBookmarkCategories;
    };
  }, [categories, customCategories]);

  // Track new posts and trigger AI processing when threshold is reached
  useEffect(() => {
    if (posts.length === 0 || processedPostIds.size === 0) return;

    // Find new posts that haven't been processed yet
    const newPosts = posts.filter(post =>
      post.id &&
      post.content &&
      post.content.length >= 20 &&
      !processedPostIds.has(post.id)
    );

    setNewPostsCount(newPosts.length);
    console.log(`[SavedBookmarksWidget] Found ${newPosts.length} new unprocessed posts (threshold: ${NEW_POSTS_THRESHOLD})`);

    // Only process if we have enough new posts
    if (newPosts.length >= NEW_POSTS_THRESHOLD) {
      console.log(`[SavedBookmarksWidget] Threshold reached! Processing ${newPosts.length} new posts for bookmark extraction`);
      processNewPostsForBookmarks(newPosts);
    }
  }, [posts, processedPostIds]);

  // Function to process new posts for bookmark extraction
  const processNewPostsForBookmarks = useCallback(async (newPosts: Post[]) => {
    if (newPosts.length === 0) return;

    setIsLoadingBookmarks(true);
    const extractedBookmarks: BookmarkItem[] = [];
    const newProcessedIds = new Set(processedPostIds);

    try {
      console.log(`[SavedBookmarksWidget] 🔗 Processing ${newPosts.length} posts for URL and AI bookmark extraction`);
      console.log(`[SavedBookmarksWidget] 📊 Current bookmarks: ${aiBookmarks.length} AI + ${manualBookmarks.length} manual`);

      // Get existing bookmark URLs to prevent duplicates
      const existingUrls = [...aiBookmarks, ...manualBookmarks].map(b => b.url);

      // Process new posts for bookmark extraction
      for (const post of newPosts) {
        if (!post.content || post.content.length < 20) continue;

        let postHasValidBookmarks = false;
        
        try {
          // 1. First extract URLs directly from content
          const urlCandidates = await extractBookmarkCandidates(post.content);
          console.log(`[SavedBookmarksWidget] Found ${urlCandidates.length} URL candidates in post ${post.id}`);

          // Process URL candidates
          for (const candidate of urlCandidates) {
            // Skip if duplicate
            if (isDuplicateUrl(candidate.normalizedUrl, existingUrls)) {
              console.log(`[SavedBookmarksWidget] Skipping duplicate URL: ${candidate.domain}`);
              continue;
            }

            // Auto-categorize based on content and metadata
            const category = await categorizeBookmark(candidate, post);
            const tags = generateBookmarkTags(candidate, post);

            extractedBookmarks.push({
              id: `auto-${post.id}-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
              url: candidate.normalizedUrl,
              domain: candidate.domain,
              title: candidate.title,
              description: candidate.description,
              category: category,
              tags: [...tags, 'Auto'], // Add 'Auto' tag to distinguish from manual
              favicon: candidate.favicon,
              post
            });

            // Add to existing URLs to prevent duplicates within this batch
            existingUrls.push(candidate.normalizedUrl);
            postHasValidBookmarks = true;
          }

          // 2. Also run AI extraction for additional context (but filter out duplicates)
          try {
            const aiResults = await extractAIBookmarks(post.content);

            aiResults.forEach((aiBookmark, index) => {
              // Skip if duplicate
              if (isDuplicateUrl(aiBookmark.url, existingUrls)) {
                console.log(`[SavedBookmarksWidget] Skipping duplicate AI URL: ${aiBookmark.url}`);
                return;
              }

              // Map AI categories to our widget categories - PRIORITIZE USER'S CUSTOM CATEGORIES
              let widgetCategory = 'Lifestyle & Opinions';
              let tags = [];

              // Enhanced category matching - check content, AI category, and bookmark data
              const fullContent = `${post.content || ''} ${aiBookmark.name} ${aiBookmark.description} ${aiBookmark.category}`.toLowerCase();
              
              // PRIORITY 1: Direct custom category name match for AI content
              const directCustomMatch = customCategories.find(cat => 
                fullContent.includes('ai') || fullContent.includes('artificial intelligence') || 
                aiBookmark.category.toLowerCase().includes('ai') || aiBookmark.category.toLowerCase().includes('tool')
              );
              
              if (directCustomMatch && (fullContent.includes('ai') || fullContent.includes('artificial intelligence'))) {
                // If user has an AI category and this is AI-related content, use it
                const aiCategory = customCategories.find(cat => 
                  cat.name.toLowerCase().includes('ai') || 
                  cat.tags.some(tag => tag.toLowerCase() === 'ai')
                );
                if (aiCategory) {
                  widgetCategory = aiCategory.name;
                  tags = ['AI', 'Tools'];
                } else {
                  widgetCategory = directCustomMatch.name;
                  tags = [aiBookmark.category];
                }
              } else {
                // PRIORITY 2: Check if user's custom categories match the content/category
                const customMatch = customCategories.find(cat => {
                  // Check if category name matches AI category
                  if (cat.name.toLowerCase() === aiBookmark.category.toLowerCase()) {
                    return true;
                  }
                  // Check if any category tags match AI category or content
                  return cat.tags.some(tag => 
                    tag.toLowerCase() === aiBookmark.category.toLowerCase() ||
                    fullContent.includes(tag.toLowerCase()) ||
                    aiBookmark.category.toLowerCase().includes(tag.toLowerCase())
                  );
                });

                if (customMatch) {
                  widgetCategory = customMatch.name;
                  tags = [aiBookmark.category];
                } else {
                  // PRIORITY 3: Only use default mapping if no custom categories match
                  switch (aiBookmark.category.toLowerCase()) {
                    case 'news':
                      widgetCategory = 'Current Affairs';
                      tags = ['News'];
                      break;
                    case 'business':
                      widgetCategory = 'Work & Knowledge';
                      tags = ['Business'];
                      break;
                    case 'education':
                      widgetCategory = 'Work & Knowledge';
                      tags = ['Education'];
                      break;
                    case 'technology':
                    case 'tools':
                      // Check if user has AI category for tech tools
                      const aiCategory = customCategories.find(cat => 
                        cat.name.toLowerCase().includes('ai') || 
                        cat.tags.some(tag => tag.toLowerCase() === 'ai')
                      );
                      if (aiCategory && (fullContent.includes('ai') || fullContent.includes('artificial intelligence'))) {
                        widgetCategory = aiCategory.name;
                        tags = ['AI', 'Tools'];
                      } else {
                        widgetCategory = 'Work & Knowledge';
                        tags = ['Tools', 'Technology'];
                      }
                      break;
                    case 'platforms':
                      widgetCategory = 'Social Trends';
                      tags = ['Platforms'];
                      break;
                    case 'entertainment':
                      widgetCategory = 'Social Trends';
                      tags = ['Entertainment'];
                      break;
                    default:
                      // If it's AI-related and user has AI category, use it
                      const defaultAiCategory = customCategories.find(cat => 
                        cat.name.toLowerCase().includes('ai') || 
                        cat.tags.some(tag => tag.toLowerCase() === 'ai')
                      );
                      if (defaultAiCategory && (fullContent.includes('ai') || fullContent.includes('artificial intelligence'))) {
                        widgetCategory = defaultAiCategory.name;
                        tags = ['AI'];
                      } else {
                        tags = [aiBookmark.category];
                      }
                  }
                }
              }

              try {
                const domain = new URL(aiBookmark.url).hostname.replace('www.', '');
                extractedBookmarks.push({
                  id: `ai-${post.id}-${index}`,
                  url: aiBookmark.url,
                  domain,
                  title: aiBookmark.name,
                  description: aiBookmark.description,
                  category: widgetCategory,
                  tags,
                  favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=16`,
                  post
                });

                // Add to existing URLs to prevent duplicates within this batch
                existingUrls.push(aiBookmark.url);
                postHasValidBookmarks = true;
              } catch (error) {
                console.warn(`[SavedBookmarksWidget] Invalid AI bookmark URL: ${aiBookmark.url}`);
              }
            });
          } catch (aiError) {
            console.error(`AI extraction failed for post ${post.id}:`, aiError);
            // Don't fail the entire process if just AI extraction fails
          }

          // Only mark post as processed if we found bookmarks OR if content was properly scanned
          // This prevents reprocessing while allowing retry for network failures
          newProcessedIds.add(post.id);
          
        } catch (error) {
          console.error(`Error extracting bookmarks from post ${post.id}:`, error);
          // Only mark as processed if it's a permanent error (not network issues)
          if (!error.message?.includes('fetch') && !error.message?.includes('network')) {
            newProcessedIds.add(post.id);
          }
        }
      }

      // Add new bookmarks to existing ones (incremental update)
      const updatedAiBookmarks = [...aiBookmarks, ...extractedBookmarks];
      setAiBookmarks(updatedAiBookmarks);
      
      // Save AI bookmarks to storage
      await saveAiBookmarks(updatedAiBookmarks);
      console.log(`[SavedBookmarksWidget] Saved ${updatedAiBookmarks.length} AI bookmarks to storage`);

      // Update processed posts tracking
      setProcessedPostIds(newProcessedIds);
      await saveProcessedPosts(newProcessedIds);

      // Reset new posts count
      setNewPostsCount(0);

    } catch (error) {
      console.error('Error in AI bookmark extraction:', error);
    } finally {
      setIsLoadingBookmarks(false);
    }
  }, [processedPostIds]);

  // Manual bookmark management functions
  // Helper function to normalize and validate URLs
  const normalizeUrl = (input: string): string => {
    if (!input.trim()) return '';

    let url = input.trim();

    // Remove any leading/trailing whitespace
    url = url.trim();

    // If it already has a protocol, use it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If it starts with www., add https://
    if (url.startsWith('www.')) {
      return `https://${url}`;
    }

    // If it looks like a domain (contains a dot), add https://
    if (url.includes('.') && !url.includes(' ')) {
      return `https://${url}`;
    }

    // Otherwise, assume it needs https:// prefix
    return `https://${url}`;
  };

  const addManualBookmark = useCallback(async (bookmarkData: {
    url: string;
    title: string;
    description: string;
    category: string;
    tags: string[];
  }) => {
    try {
      // Normalize the URL
      const normalizedUrl = normalizeUrl(bookmarkData.url);

      // Validate the normalized URL
      let urlObj: URL;
      try {
        urlObj = new URL(normalizedUrl);
      } catch (error) {
        throw new Error('Invalid URL format');
      }

      const newBookmark: BookmarkItem = {
        id: `manual-${Date.now()}`,
        url: normalizedUrl,
        domain: urlObj.hostname.replace('www.', ''),
        title: bookmarkData.title,
        description: bookmarkData.description,
        category: bookmarkData.category,
        tags: bookmarkData.tags,
        favicon: `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`,
        post: { id: 'manual', platform: 'Manual', content: 'Manually added bookmark' } as any
      };

      const updatedBookmarks = [...manualBookmarks, newBookmark];
      setManualBookmarks(updatedBookmarks);
      await saveManualBookmarks(updatedBookmarks);

      console.log('[SavedBookmarksWidget] Added manual bookmark:', newBookmark.title);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error adding manual bookmark:', error);
      throw error; // Re-throw to handle in the form
    }
  }, [manualBookmarks]);

  const removeBookmark = useCallback(async (bookmarkId: string) => {
    try {
      // Remove from manual bookmarks if it's a manual bookmark
      if (bookmarkId.startsWith('manual-')) {
        const updatedManualBookmarks = manualBookmarks.filter(b => b.id !== bookmarkId);
        setManualBookmarks(updatedManualBookmarks);
        await saveManualBookmarks(updatedManualBookmarks);
      } else {
        // Remove from AI bookmarks
        const updatedAiBookmarks = aiBookmarks.filter(b => b.id !== bookmarkId);
        setAiBookmarks(updatedAiBookmarks);
        await saveAiBookmarks(updatedAiBookmarks);
      }
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error removing bookmark:', error);
    }
  }, [manualBookmarks, aiBookmarks]);

  const handleDeleteClick = useCallback((bookmarkId: string) => {
    setBookmarkToDelete(bookmarkId);
    setShowDeleteConfirmModal(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (bookmarkToDelete) {
      await removeBookmark(bookmarkToDelete);
      setBookmarkToDelete(null);
      setShowDeleteConfirmModal(false);
    }
  }, [bookmarkToDelete, removeBookmark]);

  const cancelDelete = useCallback(() => {
    setBookmarkToDelete(null);
    setShowDeleteConfirmModal(false);
  }, []);

  const handleEditClick = useCallback((bookmark: BookmarkItem) => {
    setBookmarkToEdit(bookmark);
    setEditBookmarkForm({
      url: bookmark.url,
      title: bookmark.title,
      description: bookmark.description,
      category: bookmark.category,
      tags: bookmark.tags
    });
    setShowEditBookmarkModal(true);
  }, []);

  const updateBookmark = useCallback(async (bookmarkData: {
    url: string;
    title: string;
    description: string;
    category: string;
    tags: string[];
  }) => {
    if (!bookmarkToEdit) return;

    try {
      // Normalize the URL
      const normalizedUrl = normalizeUrl(bookmarkData.url);

      // Validate the normalized URL
      let urlObj: URL;
      try {
        urlObj = new URL(normalizedUrl);
      } catch (error) {
        throw new Error('Invalid URL format');
      }

      const updatedBookmark: BookmarkItem = {
        ...bookmarkToEdit,
        url: normalizedUrl,
        domain: urlObj.hostname.replace('www.', ''),
        title: bookmarkData.title,
        description: bookmarkData.description,
        category: bookmarkData.category,
        tags: bookmarkData.tags,
        favicon: `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`
      };

      // Update in the appropriate list
      if (bookmarkToEdit.id.startsWith('manual-')) {
        const updatedManualBookmarks = manualBookmarks.map(b =>
          b.id === bookmarkToEdit.id ? updatedBookmark : b
        );
        setManualBookmarks(updatedManualBookmarks);
        await saveManualBookmarks(updatedManualBookmarks);
      } else {
        const updatedAiBookmarks = aiBookmarks.map(b =>
          b.id === bookmarkToEdit.id ? updatedBookmark : b
        );
        setAiBookmarks(updatedAiBookmarks);
      }

      console.log('[SavedBookmarksWidget] Updated bookmark:', updatedBookmark.title);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error updating bookmark:', error);
      throw error; // Re-throw to handle in the form
    }
  }, [bookmarkToEdit, manualBookmarks, aiBookmarks]);

  const cancelEdit = useCallback(() => {
    setBookmarkToEdit(null);
    setEditBookmarkForm({
      url: '',
      title: '',
      description: '',
      category: 'Work & Knowledge',
      tags: []
    });
    setShowEditBookmarkModal(false);
  }, []);

  const addCustomCategory = useCallback(async (categoryData: {
    name: string;
    emoji: string;
    tags: string[];
  }) => {
    try {
      const updatedCategories = [...customCategories, categoryData];
      setCustomCategories(updatedCategories);
      await saveCustomCategories(updatedCategories);
      console.log('[SavedBookmarksWidget] Added custom category:', categoryData.name);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error adding custom category:', error);
    }
  }, []);

  const removeCustomCategory = useCallback(async (categoryName: string) => {
    try {
      const updatedCategories = customCategories.filter(c => c.name !== categoryName);
      setCustomCategories(updatedCategories);
      await saveCustomCategories(updatedCategories);
      console.log('[SavedBookmarksWidget] Removed custom category:', categoryName);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error removing custom category:', error);
    }
  }, []);

  const updateCustomCategory = useCallback(async (oldName: string, categoryData: {
    name: string;
    emoji: string;
    tags: string[];
  }) => {
    try {
      const updatedCategories = customCategories.map(c =>
        c.name === oldName ? categoryData : c
      );
      setCustomCategories(updatedCategories);
      await saveCustomCategories(updatedCategories);
      console.log('[SavedBookmarksWidget] Updated custom category:', oldName, '->', categoryData.name);
    } catch (error) {
      console.error('[SavedBookmarksWidget] Error updating custom category:', error);
    }
  }, []);

  const startEditingCategory = useCallback((category: { name: string; emoji: string; tags: string[] }) => {
    setEditingCategory(category.name);
    setEditCategoryForm({
      name: category.name,
      emoji: category.emoji,
      tags: [...category.tags]
    });
  }, []);

  const cancelEditingCategory = useCallback(() => {
    setEditingCategory(null);
    setEditCategoryForm({ name: '', emoji: '', tags: [] });
    setNewTagInput('');
  }, []);

  const saveEditingCategory = useCallback(async () => {
    if (!editingCategory || !editCategoryForm.name || !editCategoryForm.emoji || editCategoryForm.tags.length === 0) {
      return;
    }

    await updateCustomCategory(editingCategory, editCategoryForm);
    cancelEditingCategory();
  }, [editingCategory, editCategoryForm, updateCustomCategory, cancelEditingCategory]);

  const addTagToEditingCategory = useCallback(() => {
    const trimmedTag = newTagInput.trim();
    if (trimmedTag && !editCategoryForm.tags.includes(trimmedTag)) {
      setEditCategoryForm(prev => ({
        ...prev,
        tags: [...prev.tags, trimmedTag]
      }));
      setNewTagInput('');
    }
  }, [newTagInput, editCategoryForm.tags]);

  const removeTagFromEditingCategory = useCallback((tagToRemove: string) => {
    setEditCategoryForm(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  }, []);

  const handleNewCategorySubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    const { name, emoji, tags } = newCategoryForm;
    const tagArray = tags.split(',').map(t => t.trim()).filter(t => t);

    if (name && emoji && tagArray.length > 0) {
      await addCustomCategory({ name, emoji, tags: tagArray });
      // Reset form
      setNewCategoryForm({ name: '', emoji: '📚', tags: '' });
    }
  }, [newCategoryForm, addCustomCategory]);

  // URL validation and preview functions
  const validateUrl = (input: string): { isValid: boolean; normalizedUrl: string; error: string } => {
    if (!input.trim()) {
      return { isValid: false, normalizedUrl: '', error: 'URL is required' };
    }

    try {
      const normalizedUrl = normalizeUrl(input);
      new URL(normalizedUrl); // This will throw if invalid
      return { isValid: true, normalizedUrl, error: '' };
    } catch (error) {
      return { isValid: false, normalizedUrl: '', error: 'Please enter a valid URL (e.g., google.com or https://google.com)' };
    }
  };

  // Handle URL input change with real-time validation
  const handleUrlChange = (value: string) => {
    setNewBookmarkForm(prev => ({ ...prev, url: value }));

    // Clear previous errors
    setFormErrors(prev => ({ ...prev, url: '' }));

    // Validate if user has typed something
    if (value.trim()) {
      const validation = validateUrl(value);
      if (!validation.isValid) {
        setFormErrors(prev => ({ ...prev, url: validation.error }));
      }
    }
  };

  // Combine AI-extracted bookmarks and manual bookmarks
  const bookmarks = useMemo(() => {
    // Combine all bookmarks
    const allBookmarks = [...manualBookmarks, ...aiBookmarks];

    // Add test data if no real bookmarks found and not loading
    if (allBookmarks.length === 0 && !isLoadingBookmarks && posts.length === 0) {
      console.log('[SavedBookmarksWidget] No bookmarks found, adding test data');
      return [
        {
          id: 'test-1',
          url: 'https://bolt.new',
          domain: 'bolt.new',
          title: 'Bolt.new',
          description: 'AI-powered web development platform for rapid prototyping.',
          category: 'Work & Knowledge',
          tags: ['Tools'],
          favicon: 'https://www.google.com/s2/favicons?domain=bolt.new&sz=16',
          post: { id: 'test', platform: 'X/Twitter', content: 'Check out bolt.new for AI web development!' } as any
        },
        {
          id: 'test-2',
          url: 'https://github.com',
          domain: 'github.com',
          title: 'GitHub',
          description: 'Code repositories and developer collaboration platform.',
          category: 'Work & Knowledge',
          tags: ['Tools', 'Technology'],
          favicon: 'https://www.google.com/s2/favicons?domain=github.com&sz=16',
          post: { id: 'test2', platform: 'LinkedIn', content: 'GitHub is essential for developers' } as any
        }
      ];
    }

    return allBookmarks;
  }, [aiBookmarks, manualBookmarks, isLoadingBookmarks, posts.length]);

  // Filter bookmarks by active category and tag
  const filteredBookmarks = useMemo(() => {
    let filtered = bookmarks.filter(bookmark => bookmark.category === activeCategory);

    if (activeTag !== 'All') {
      filtered = filtered.filter(bookmark => bookmark.tags.includes(activeTag));
    }

    return filtered.slice(0, 6); // Limit to 6 items
  }, [bookmarks, activeCategory, activeTag]);

  // Get tag options from both category defaults and actual bookmarks in this category
  const activeTagOptions = useMemo(() => {
    const categoryTags = categories.find(cat => cat.name === activeCategory)?.tags || [];
    const bookmarkTags = bookmarks
      .filter(bookmark => bookmark.category === activeCategory)
      .flatMap(bookmark => bookmark.tags || []);

    // Combine and deduplicate tags
    const allTags = [...new Set([...categoryTags, ...bookmarkTags])];
    return allTags.sort();
  }, [categories, bookmarks, activeCategory]);

  // Helper function to categorize bookmarks based on content and metadata
  const categorizeBookmark = useCallback(async (candidate: any, post: Post): Promise<string> => {
    const fullContent = `${post.content || ''} ${candidate.title} ${candidate.description} ${candidate.domain}`.toLowerCase();

    // PRIORITY 1: Check for AI-related content and if user has AI category
    if (fullContent.includes('ai') || fullContent.includes('artificial intelligence') || fullContent.includes('machine learning') || fullContent.includes('chatgpt') || fullContent.includes('openai')) {
      const aiCategory = customCategories.find(cat => 
        cat.name.toLowerCase().includes('ai') || 
        cat.tags.some(tag => tag.toLowerCase() === 'ai')
      );
      if (aiCategory) {
        return aiCategory.name;
      }
    }

    // PRIORITY 2: Check custom categories for content matches
    for (const category of customCategories) {
      // Check if category name matches content
      if (fullContent.includes(category.name.toLowerCase())) {
        return category.name;
      }
      // Check if any tags match content
      if (category.tags.some(tag => fullContent.includes(tag.toLowerCase()))) {
        return category.name;
      }
    }

    // PRIORITY 3: Only use default categories if no custom matches
    if (fullContent.includes('news') || fullContent.includes('politics') || fullContent.includes('world') || fullContent.includes('economy')) {
      return 'Current Affairs';
    }
    if (fullContent.includes('tool') || fullContent.includes('business') || fullContent.includes('education') || fullContent.includes('technology') || fullContent.includes('software') || fullContent.includes('app')) {
      return 'Work & Knowledge';
    }
    if (fullContent.includes('platform') || fullContent.includes('social') || fullContent.includes('entertainment') || fullContent.includes('culture')) {
      return 'Social Trends';
    }

    // Default fallback
    return 'Lifestyle & Opinions';
  }, [customCategories]);

  // Helper function to generate tags for bookmarks
  const generateBookmarkTags = useCallback((candidate: any, post: Post): string[] => {
    const fullContent = `${post.content || ''} ${candidate.title} ${candidate.description} ${candidate.domain}`.toLowerCase();
    const tags: string[] = [];

    // Priority: Check if content matches user's custom category tags
    customCategories.forEach(category => {
      category.tags.forEach(tag => {
        if (fullContent.includes(tag.toLowerCase()) && !tags.includes(tag)) {
          tags.push(tag);
        }
      });
    });

    // Add specific AI-related tags if detected
    if (fullContent.includes('ai') || fullContent.includes('artificial intelligence') || fullContent.includes('machine learning')) {
      if (!tags.some(tag => tag.toLowerCase() === 'ai')) {
        tags.push('AI');
      }
    }

    // Technology-related tags (only if not already covered by custom categories)
    if (!tags.length || tags.length < 3) {
      if (fullContent.includes('tool') || fullContent.includes('software')) tags.push('Tools');
      if (fullContent.includes('technology') || fullContent.includes('tech')) tags.push('Technology');
      if (fullContent.includes('business')) tags.push('Business');
      if (fullContent.includes('education') || fullContent.includes('learning')) tags.push('Education');
      if (fullContent.includes('news')) tags.push('News');
      if (fullContent.includes('platform')) tags.push('Platforms');
      if (fullContent.includes('entertainment')) tags.push('Entertainment');

      // Domain-based tags
      const domain = candidate.domain.toLowerCase();
      if (domain.includes('github')) tags.push('Development');
      if (domain.includes('youtube')) tags.push('Video');
      if (domain.includes('medium') || domain.includes('blog')) tags.push('Articles');
    }

    // Ensure we have at least one tag
    if (tags.length === 0) {
      tags.push('Website');
    }

    // Remove duplicates and limit to 4 tags max
    const uniqueTags = [...new Set(tags)];
    return uniqueTags.slice(0, 4);
  }, [customCategories]);

  console.log('[SavedBookmarksWidget] Rendering widget with', filteredBookmarks.length, 'filtered bookmarks');

  return (
    <div className="h-[450px] flex flex-col px-6 py-4">
        {/* Actions and Status */}
        <div className="flex items-center justify-end space-x-2 mb-4">
            {/* Add Bookmark Button */}
            <button
              onClick={() => {
                console.log('[SavedBookmarksWidget] Opening add bookmark modal');
                console.log('[SavedBookmarksWidget] Available categories at modal open:', categories.map(c => `${c.emoji} ${c.name}`));
                console.log('[SavedBookmarksWidget] Custom categories at modal open:', customCategories);
                setShowAddBookmarkModal(true);
                // Reset form errors when opening modal
                setFormErrors({ url: '', title: '' });
              }}
              className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-4 py-2 text-sm font-medium hover:from-indigo-600 hover:to-purple-600 hover:scale-105 shadow-notely-sm hover:shadow-notely-md transition-all border border-purple-400/30 dark:border-purple-500/50"
              title={t('widgets.addBookmark')}
            >
              + {t('widgets.addBookmark')}
            </button>

            {/* Edit Categories Button */}
            <button
              onClick={() => setShowEditCategoriesModal(true)}
              className="text-sm font-medium rounded-full bg-notely-surface text-notely-text-secondary border border-notely-border/10 dark:border-notely-border-dark/20 px-4 py-2 hover:bg-notely-dark-hover hover:text-notely-text-primary hover:shadow-notely-md notely-filter-transition hover:scale-105"
              title={t('widgets.editCategories')}
            >
              {t('widgets.editCategories')}
            </button>

            {/* Status and AI Processing */}
            {newPostsCount > 0 && (
              <>
                <span className="uppercase text-xs text-notely-text-muted tracking-wide bg-notely-surface px-3 py-1.5 rounded-full border border-notely-border/10 dark:border-notely-border-dark/20 shadow-notely-xs">
                  {newPostsCount}/{NEW_POSTS_THRESHOLD} new
                </span>
                {newPostsCount >= 3 && ( // Allow manual trigger when we have at least 3 posts
                  <button
                    onClick={() => {
                      const newPosts = posts.filter(post =>
                        post.id &&
                        post.content &&
                        post.content.length >= 20 &&
                        !processedPostIds.has(post.id)
                      );
                      if (newPosts.length > 0) {
                        processNewPostsForBookmarks(newPosts);
                      }
                    }}
                    disabled={isLoadingBookmarks}
                    className="notely-btn-primary text-sm font-medium rounded-full px-3 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed shadow-notely-sm hover:shadow-notely-md notely-filter-transition hover:scale-105 border border-notely-border/5 dark:border-notely-border-dark/20"
                    title={t('widgets.processNow')}
                  >
                    {t('widgets.processNow')}
                  </button>
                )}
              </>
            )}
        </div>

        {/* Category Pills */}
        <div className="flex flex-wrap gap-2 mb-4">
          {categories.map((category) => {
            // Define colors based on category
            let bgColor = 'bg-notely-surface';
            let textColor = 'text-notely-text-muted';
            let hoverBg = 'hover:bg-notely-card/80';
            let borderColor = 'border-notely-border/10 dark:border-notely-border-dark/20';
            let shadow = 'hover:shadow-notely-sm';
            
            if (activeCategory === category.name) {
              switch(category.name) {
                case 'Current Affairs':
                  bgColor = 'bg-blue-500/10 dark:bg-blue-600/20';
                  textColor = 'text-blue-600 dark:text-blue-400';
                  borderColor = 'border-blue-200/50 dark:border-blue-500/30';
                  shadow = 'shadow-notely-sm';
                  break;
                case 'Work & Knowledge':
                  bgColor = 'bg-green-500/10 dark:bg-green-600/20';
                  textColor = 'text-green-600 dark:text-green-400';
                  borderColor = 'border-green-200/50 dark:border-green-500/30';
                  shadow = 'shadow-notely-sm';
                  break;
                case 'Social Trends':
                  bgColor = 'bg-purple-500/10 dark:bg-purple-600/20';
                  textColor = 'text-purple-600 dark:text-purple-400';
                  borderColor = 'border-purple-200/50 dark:border-purple-500/30';
                  shadow = 'shadow-notely-sm';
                  break;
                case 'Lifestyle & Opinions':
                  bgColor = 'bg-pink-500/10 dark:bg-pink-600/20';
                  textColor = 'text-pink-600 dark:text-pink-400';
                  borderColor = 'border-pink-200/50 dark:border-pink-500/30';
                  shadow = 'shadow-notely-sm';
                  break;
                default:
                  bgColor = 'bg-notely-accent/10';
                  textColor = 'text-notely-accent dark:text-notely-accent/90';
                  borderColor = 'border-notely-accent/20 dark:border-notely-accent/30';
                  shadow = 'shadow-notely-sm';
              }
            }
            
            return (
              <button
                key={category.name}
                onClick={() => {
                  setActiveCategory(category.name);
                  setActiveTag('All');
                }}
                className={`flex items-center text-sm font-medium rounded-lg px-4 py-2 whitespace-nowrap transition-all duration-200 border ${bgColor} ${textColor} ${borderColor} ${shadow} hover:shadow-notely-md hover:-translate-y-0.5`}
              >
                <span className="mr-2 text-base">{category.emoji}</span>
                <span className="font-medium">{category.name}</span>
              </button>
            );
          })}
        </div>

        {/* Tag Filters - Enhanced Design */}
        <div className="flex flex-wrap gap-2 mb-6">
          <button
            onClick={() => setActiveTag('All')}
            className={`group text-xs font-medium rounded-full px-3 py-1.5 whitespace-nowrap transition-all duration-200 hover:scale-105 border ${
              activeTag === 'All'
                ? 'bg-gradient-to-r from-notely-sky to-notely-lavender text-white border-transparent shadow-notely-sm hover:shadow-notely-md'
                : 'bg-notely-surface/60 text-notely-text-muted border-notely-border/30 hover:bg-notely-card hover:text-notely-text-secondary hover:border-notely-sky/30 hover:shadow-notely-xs'
            }`}
          >
            <span className="group-hover:scale-105 transition-transform inline-block">{t('widgets.all')}</span>
          </button>
          {activeTagOptions.slice(0, 8).map((tag, index) => (
            <button
              key={tag}
              onClick={() => setActiveTag(tag)}
              className={`group text-xs font-medium rounded-full px-3 py-1.5 whitespace-nowrap transition-all duration-200 hover:scale-105 border ${
                activeTag === tag
                  ? 'bg-gradient-to-r from-notely-sky to-notely-lavender text-white border-transparent shadow-notely-sm hover:shadow-notely-md'
                  : 'bg-notely-surface/60 text-notely-text-muted border-notely-border/30 hover:bg-notely-card hover:text-notely-text-secondary hover:border-notely-sky/30 hover:shadow-notely-xs'
              }`}
              style={{ animationDelay: `${index * 0.05}s` }}
            >
              <span className="group-hover:scale-105 transition-transform inline-block">{formatForDisplay(tag)}</span>
            </button>
          ))}
          {activeTagOptions.length > 8 && (
            <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-notely-surface/40 text-notely-text-muted border border-notely-border/20">
              +{activeTagOptions.length - 8} more
            </span>
          )}
        </div>

        {/* Bookmarks Grid */}
        <div className="flex-1 overflow-y-auto">
          {isLoadingBookmarks ? (
            <div className="flex-1 flex items-center justify-center text-center">
              <div>
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto mb-2"></div>
                <p className="text-sm text-notely-text-secondary">{t('widgets.extractingBookmarks')}</p>
                <p className="text-xs text-notely-text-tertiary mt-1">{t('widgets.processingPosts')}</p>
              </div>
            </div>
          ) : filteredBookmarks.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {filteredBookmarks.map((bookmark) => (
                <div
                  key={bookmark.id}
                  className="notely-card bg-notely-card text-notely-text-primary rounded-xl border border-notely-border shadow-notely-sm hover:shadow-notely-lg hover:border-notely-border/80 transition-all duration-300 group relative h-fit"
                >
                  {/* Card Header - Fixed Height with Consistent Padding */}
                  <div className="p-4 pb-3">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center flex-1 min-w-0">
                        {bookmark.favicon && (
                          <img
                            src={bookmark.favicon}
                            alt=""
                            className="w-4 h-4 mr-3 flex-shrink-0 opacity-70"
                            onError={(e) => { e.currentTarget.style.display = 'none'; }}
                          />
                        )}
                        <h3 className="text-base font-semibold leading-tight truncate text-notely-text-primary group-hover:text-notely-sky transition-colors">
                          {bookmark.title}
                        </h3>
                      </div>
                      
                      {/* Action buttons - Always visible on hover */}
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center space-x-1 ml-3 flex-shrink-0">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditClick(bookmark);
                          }}
                          className="p-1.5 rounded-md bg-notely-sky/10 text-notely-sky hover:bg-notely-sky/20 transition-colors duration-200 hover:scale-110"
                          title={t('widgets.edit')}
                        >
                          <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(bookmark.id);
                          }}
                          className="p-1.5 rounded-md bg-red-500/10 text-red-500 hover:bg-red-500/20 transition-colors duration-200 hover:scale-110"
                          title={t('post.delete')}
                        >
                          <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Description - Consistent Height with Clamp */}
                    <p className="text-sm text-notely-text-secondary leading-relaxed line-clamp-2 mb-3">
                      {bookmark.description}
                    </p>
                  </div>

                  {/* Card Footer - Consistent Metadata Layout */}
                  <div 
                    className="px-4 pb-4 cursor-pointer"
                    onClick={() => window.open(bookmark.url, '_blank')}
                  >
                    {/* Metadata Row - Always Same Height */}
                    <div className="flex items-center justify-between mb-3 min-h-[20px]">
                      <div className="flex items-center text-xs text-notely-text-muted space-x-2">
                        <span className="font-medium">{bookmark.domain}</span>
                        <span className="opacity-50">•</span>
                        <span>from {bookmark.post?.platform || 'Unknown'}</span>
                      </div>
                      
                      {/* Right-aligned icons and badges */}
                      <div className="flex items-center space-x-2">
                        {/* Source indicator - Always present with consistent styling */}
                        {bookmark.id.startsWith('manual-') && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-notely-mint/15 text-notely-mint border border-notely-mint/30">
                            Manual
                          </span>
                        )}
                        {bookmark.id.startsWith('auto-') && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-notely-sky/15 text-notely-sky border border-notely-sky/30">
                            Auto
                          </span>
                        )}
                        {bookmark.id.startsWith('ai-') && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-medium bg-notely-lavender/15 text-notely-lavender border border-notely-lavender/30">
                            AI
                          </span>
                        )}
                        
                        {/* External link icon - Always present and aligned */}
                        <svg className="w-4 h-4 text-notely-text-muted opacity-60 hover:opacity-100 transition-opacity flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </div>

                    {/* Tags Row - Smart Truncation */}
                    {bookmark.tags && bookmark.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1.5">
                        {bookmark.tags.slice(0, 4).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-0.5 rounded-md text-[10px] font-medium bg-notely-surface/80 text-notely-text-muted border border-notely-border/30 hover:bg-notely-border/10 transition-colors"
                          >
                            {tag}
                          </span>
                        ))}
                        {bookmark.tags.length > 4 && (
                          <span 
                            className="inline-flex items-center px-2 py-0.5 rounded-md text-[10px] font-medium bg-notely-surface/80 text-notely-text-muted border border-notely-border/30 hover:bg-notely-border/10 transition-colors cursor-help"
                            title={`+${bookmark.tags.length - 4} more: ${bookmark.tags.slice(4).join(', ')}`}
                          >
                            +{bookmark.tags.length - 4}
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Hover overlay for better feedback */}
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-notely-sky/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center text-center">
              <div>
                <span className="text-4xl mb-2 block">🔖</span>
                {newPostsCount > 0 ? (
                  <>
                    <p className="text-sm text-notely-text-muted leading-relaxed">
                      {newPostsCount} new posts ready for processing
                    </p>
                    <p className="text-sm text-notely-text-muted leading-relaxed mt-1">
                      AI bookmark extraction will run when you have {NEW_POSTS_THRESHOLD} new posts
                    </p>
                    <p className="text-sm text-notely-text-muted leading-relaxed">
                      ({NEW_POSTS_THRESHOLD - newPostsCount} more posts needed)
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-sm text-notely-text-muted leading-relaxed">{t('widgets.noBookmarksCategory')}</p>
                    <p className="text-sm text-notely-text-muted leading-relaxed mt-1">
                      {t('widgets.savePostsForBookmarks')}
                    </p>
                    <p className="text-sm text-notely-text-muted leading-relaxed">
                      {t('widgets.aiProcessingInfo').replace('{threshold}', NEW_POSTS_THRESHOLD.toString())}
                    </p>
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Add Bookmark Modal */}
      {showAddBookmarkModal && createPortal(
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowAddBookmarkModal(false);
              setFormErrors({ url: '', title: '' });
            }
          }}
        >
          <div
            className="notely-card rounded-xl px-8 py-6 w-[480px] max-w-[90vw] max-h-[90vh] overflow-y-auto shadow-2xl border border-notely-border/10"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-semibold notely-heading">{t('widgets.addBookmark')}</h3>
              <button
                onClick={() => setShowAddBookmarkModal(false)}
                className="text-notely-text-muted hover:text-notely-text-primary transition-colors p-1"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={async (e) => {
              e.preventDefault();

              // Validate form
              const urlValidation = validateUrl(newBookmarkForm.url);
              if (!urlValidation.isValid) {
                setFormErrors(prev => ({ ...prev, url: urlValidation.error }));
                return;
              }

              if (!newBookmarkForm.title.trim()) {
                setFormErrors(prev => ({ ...prev, title: 'Title is required' }));
                return;
              }

              try {
                await addManualBookmark({
                  ...newBookmarkForm,
                  url: urlValidation.normalizedUrl // Use normalized URL
                });

                // Reset form and close modal
                setNewBookmarkForm({
                  url: '',
                  title: '',
                  description: '',
                  category: 'Work & Knowledge',
                  tags: []
                });
                setFormErrors({ url: '', title: '' });
                setShowAddBookmarkModal(false);
              } catch (error) {
                // Handle error (could show error message to user)
                console.error('Failed to add bookmark:', error);
                setFormErrors(prev => ({ ...prev, url: 'Failed to add bookmark. Please check the URL and try again.' }));
              }
            }}>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-notely-text-primary mb-2">{t('widgets.url')} *</label>
                  <input
                    type="text"
                    value={newBookmarkForm.url}
                    onChange={(e) => handleUrlChange(e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 bg-notely-surface text-notely-text-primary transition-all ${
                      formErrors.url
                        ? 'border-red-500/50 focus:ring-red-500/30'
                        : 'border-notely-border/30 focus:ring-notely-sky/30 hover:border-notely-border/50'
                    }`}
                    placeholder="google.com, www.github.com, or https://example.com"
                    required
                  />
                  {formErrors.url && (
                    <p className="text-red-500 text-sm mt-2 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.url}
                    </p>
                  )}
                  {newBookmarkForm.url && !formErrors.url && (
                    <p className="text-green-600 text-sm mt-2 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Will be saved as: {validateUrl(newBookmarkForm.url).normalizedUrl}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-notely-text-primary mb-2">{t('widgets.title')} *</label>
                  <input
                    type="text"
                    value={newBookmarkForm.title}
                    onChange={(e) => {
                      setNewBookmarkForm(prev => ({ ...prev, title: e.target.value }));
                      // Clear title error when user starts typing
                      if (formErrors.title) {
                        setFormErrors(prev => ({ ...prev, title: '' }));
                      }
                    }}
                    className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 bg-notely-surface text-notely-text-primary transition-all ${
                      formErrors.title
                        ? 'border-red-500/50 focus:ring-red-500/30'
                        : 'border-notely-border/30 focus:ring-notely-sky/30 hover:border-notely-border/50'
                    }`}
                    placeholder="Enter bookmark title"
                    required
                  />
                  {formErrors.title && (
                    <p className="text-red-500 text-sm mt-2 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.title}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-notely-text-primary mb-2">{t('widgets.description')}</label>
                  <textarea
                    value={newBookmarkForm.description}
                    onChange={(e) => setNewBookmarkForm(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-4 py-3 border border-notely-border/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-notely-sky/30 bg-notely-surface text-notely-text-primary hover:border-notely-border/50 transition-all resize-none"
                    placeholder="Brief description (optional)"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-notely-text-primary mb-2">Category</label>
                  <div className="relative">
                    <select
                      key={`category-select-${categories.length}-${categories.map(c => c.name).join('-')}`}
                      value={newBookmarkForm.category}
                      onChange={(e) => {
                        console.log('[SavedBookmarksWidget] Category selected:', e.target.value);
                        setNewBookmarkForm(prev => ({ ...prev, category: e.target.value, tags: [] })); // Reset subcategories when category changes
                      }}
                      className="w-full px-4 py-3 border border-notely-border/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-notely-sky/30 bg-notely-surface text-notely-text-primary appearance-none hover:border-notely-border/50 transition-all"
                    >
                      {categories.map(cat => {
                        console.log('[SavedBookmarksWidget] Rendering dropdown option:', cat.emoji, cat.name);
                        return (
                          <option key={cat.name} value={cat.name}>{cat.emoji} {cat.name}</option>
                        );
                      })}
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-notely-text-muted">
                      <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                      </svg>
                    </div>
                  </div>
                </div>


              </div>

              <div className="flex justify-end space-x-4 mt-8 pt-6 border-t border-notely-border/20">
                <button
                  type="button"
                  onClick={() => setShowAddBookmarkModal(false)}
                  className="px-6 py-3 text-notely-text-secondary border border-notely-border/30 rounded-lg hover:bg-notely-surface hover:text-notely-text-primary transition-all duration-200 font-medium"
                >
                  {t('widgets.cancel')}
                </button>
                <button
                  type="submit"
                  className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 font-medium shadow-notely-sm hover:shadow-notely-md hover:scale-105"
                >
                  {t('widgets.addBookmark')}
                </button>
              </div>
            </form>
          </div>
        </div>,
        document.body
      )}

      {/* Edit Categories Modal */}
      {showEditCategoriesModal && createPortal(
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowEditCategoriesModal(false);
            }
          }}
        >
          <div
            className="notely-card rounded-lg p-6 w-[500px] max-w-[90vw] max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold mb-4 notely-heading">Manage Categories</h3>

            {/* Custom Categories List */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Custom Categories</h4>
              {customCategories.length > 0 ? (
                <div className="space-y-3">
                  {customCategories.map(category => (
                    <div key={category.name} className="border border-notely-border/20 rounded-lg p-3 bg-notely-surface">
                      {editingCategory === category.name ? (
                        /* Edit Mode */
                        <div className="space-y-3">
                          <div className="flex space-x-2">
                            <input
                              type="text"
                              value={editCategoryForm.name}
                              onChange={(e) => setEditCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                              className="flex-1 px-2 py-1 border border-notely-border/20 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500/30 bg-notely-card text-notely-text-primary"
                              placeholder="Category name"
                            />
                            <EmojiPicker
                              selectedEmoji={editCategoryForm.emoji}
                              onEmojiSelect={(emoji) => setEditCategoryForm(prev => ({ ...prev, emoji }))}
                              compact={true}
                            />
                          </div>

                          {/* Tags Management */}
                          <div>
                            <div className="flex flex-wrap gap-1 mb-2">
                              {editCategoryForm.tags.map(tag => (
                                <span
                                  key={tag}
                                  className="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                                >
                                  {tag}
                                  <button
                                    onClick={() => removeTagFromEditingCategory(tag)}
                                    className="ml-1 text-purple-600 hover:text-purple-800"
                                  >
                                    ×
                                  </button>
                                </span>
                              ))}
                            </div>
                            <div className="flex space-x-2">
                              <input
                                type="text"
                                value={newTagInput}
                                onChange={(e) => setNewTagInput(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTagToEditingCategory())}
                                className="flex-1 px-2 py-1 border border-notely-border/20 rounded text-xs focus:outline-none focus:ring-2 focus:ring-purple-500/30 bg-notely-card text-notely-text-primary"
                                placeholder="Add new tag"
                              />
                              <button
                                onClick={addTagToEditingCategory}
                                className="px-2 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600"
                              >
                                Add
                              </button>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={cancelEditingCategory}
                              className="px-3 py-1 text-gray-600 dark:text-gray-300 border border-gray-300/10 dark:border-gray-600/10 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={saveEditingCategory}
                              className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                            >
                              Save
                            </button>
                          </div>
                        </div>
                      ) : (
                        /* View Mode */
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-1">
                              <span className="text-lg mr-2">{category.emoji}</span>
                              <span className="font-medium text-sm text-gray-900 dark:text-gray-100">{category.name}</span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {category.tags.map(tag => (
                                <span
                                  key={tag}
                                  className="inline-block px-2 py-0.5 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                          <div className="flex space-x-2 ml-3">
                            <button
                              onClick={() => startEditingCategory(category)}
                              className="text-blue-500 hover:text-blue-700 text-sm"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => removeCustomCategory(category.name)}
                              className="text-red-500 hover:text-red-700 text-sm"
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">No custom categories yet</p>
              )}
            </div>

            {/* Add New Category Form */}
            <form onSubmit={handleNewCategorySubmit}>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Add New Category</h4>
              <div className="space-y-3">
                <input
                  type="text"
                  value={newCategoryForm.name}
                  onChange={(e) => setNewCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Category name"
                  className="w-full px-3 py-2 border border-notely-border/20 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500/30 text-sm bg-notely-surface text-notely-text-primary"
                  required
                />
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap">Emoji:</span>
                  <EmojiPicker
                    selectedEmoji={newCategoryForm.emoji}
                    onEmojiSelect={(emoji) => setNewCategoryForm(prev => ({ ...prev, emoji }))}
                    className="flex-shrink-0"
                    compact={true}
                  />
                </div>
                <input
                  type="text"
                  value={newCategoryForm.tags}
                  onChange={(e) => setNewCategoryForm(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="Tags (comma-separated, e.g., Design, Art, Creative)"
                  className="w-full px-3 py-2 border border-notely-border/20 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500/30 text-sm bg-notely-surface text-notely-text-primary"
                  required
                />
                <button
                  type="submit"
                  className="w-full px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 text-sm"
                >
                  Add Category
                </button>
              </div>
            </form>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowEditCategoriesModal(false);
                  cancelEditingCategory(); // Reset editing state when closing modal
                  setNewCategoryForm({ name: '', emoji: '📚', tags: '' }); // Reset new category form
                }}
                className="px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300/10 dark:border-gray-600/10 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmModal && createPortal(
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              cancelDelete();
            }
          }}
        >
          <div
            className="notely-card rounded-lg px-6 py-4 w-96 max-w-[90vw]"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-xl font-semibold leading-tight mb-4 notely-heading">{t('post.delete')} {t('widgets.bookmarks')}</h3>
            <p className="text-sm text-notely-text-secondary mb-6">
              {t('dashboard.deleteConfirmation')}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="px-4 py-2 text-notely-text-secondary border border-notely-border rounded-md hover:bg-notely-surface transition-colors"
              >
                {t('widgets.cancel')}
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
              >
                {t('post.delete')}
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* Edit Bookmark Modal */}
      {showEditBookmarkModal && bookmarkToEdit && createPortal(
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              cancelEdit();
            }
          }}
        >
          <div
            className="notely-card rounded-lg px-6 py-4 w-96 max-w-[90vw] max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-xl font-semibold leading-tight mb-4 notely-heading">{t('widgets.edit')} {t('widgets.bookmarks')}</h3>

            <form onSubmit={async (e) => {
              e.preventDefault();

              // Validate form
              const urlValidation = validateUrl(editBookmarkForm.url);
              if (!urlValidation.isValid) {
                setFormErrors(prev => ({ ...prev, url: urlValidation.error }));
                return;
              }

              if (!editBookmarkForm.title.trim()) {
                setFormErrors(prev => ({ ...prev, title: 'Title is required' }));
                return;
              }

              try {
                await updateBookmark({
                  ...editBookmarkForm,
                  url: urlValidation.normalizedUrl // Use normalized URL
                });

                // Reset form and close modal
                cancelEdit();
                setFormErrors({ url: '', title: '' });
              } catch (error) {
                // Handle error (could show error message to user)
                console.error('Failed to update bookmark:', error);
                setFormErrors(prev => ({ ...prev, url: 'Failed to update bookmark. Please check the URL and try again.' }));
              }
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block uppercase text-xs text-notely-text-muted tracking-wide mb-1">{t('widgets.url')} *</label>
                  <input
                    type="text"
                    value={editBookmarkForm.url}
                    onChange={(e) => {
                      setEditBookmarkForm(prev => ({ ...prev, url: e.target.value }));
                      setFormErrors(prev => ({ ...prev, url: '' }));
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 bg-notely-surface text-notely-text-primary ${
                      formErrors.url
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-notely-border focus:ring-notely-accent'
                    }`}
                    placeholder="google.com, www.github.com, or https://example.com"
                    required
                  />
                  {formErrors.url && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.url}</p>
                  )}
                </div>

                <div>
                  <label className="block uppercase text-xs text-notely-text-muted tracking-wide mb-1">{t('widgets.title')} *</label>
                  <input
                    type="text"
                    value={editBookmarkForm.title}
                    onChange={(e) => {
                      setEditBookmarkForm(prev => ({ ...prev, title: e.target.value }));
                      if (formErrors.title) {
                        setFormErrors(prev => ({ ...prev, title: '' }));
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 bg-notely-surface text-notely-text-primary ${
                      formErrors.title
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-notely-border focus:ring-notely-accent'
                    }`}
                    placeholder="Bookmark title"
                    required
                  />
                  {formErrors.title && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.title}</p>
                  )}
                </div>

                <div>
                  <label className="block uppercase text-xs text-notely-text-muted tracking-wide mb-1">{t('widgets.description')}</label>
                  <textarea
                    value={editBookmarkForm.description}
                    onChange={(e) => setEditBookmarkForm(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-notely-border rounded-md focus:outline-none focus:ring-2 focus:ring-notely-accent bg-notely-surface text-notely-text-primary"
                    placeholder="Brief description"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block uppercase text-xs text-notely-text-muted tracking-wide mb-1">{t('categories.title')}</label>
                  <select
                    value={editBookmarkForm.category}
                    onChange={(e) => setEditBookmarkForm(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 border border-notely-border rounded-md focus:outline-none focus:ring-2 focus:ring-notely-accent bg-notely-surface text-notely-text-primary"
                  >
                    {categories.map(cat => (
                      <option key={cat.name} value={cat.name}>{cat.emoji} {cat.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={cancelEdit}
                  className="px-4 py-2 text-notely-text-secondary border border-notely-border rounded-md hover:bg-notely-surface transition-colors"
                >
                  {t('widgets.cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-notely-accent text-white rounded-md hover:bg-notely-accent/90 transition-colors"
                >
                  {t('widgets.save')}
                </button>
              </div>
            </form>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default SavedBookmarksWidget;
