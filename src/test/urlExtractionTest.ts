/**
 * Test file for URL extraction functionality
 * Run this in the browser console to test the URL extraction service
 */

import { extractUrlsFromContent, extractBookmarkCandidates, normalizeUrl } from '../services/urlExtractionService';

// Test data - sample social media post content with URLs
const testPosts = [
  {
    content: "Just discovered this amazing tool for developers: https://bolt.new - it's like having an AI pair programmer! Also check out github.com for code repositories.",
    expectedUrls: ['https://bolt.new', 'github.com']
  },
  {
    content: "Reading this great article about AI: https://www.openai.com/blog/chatgpt and also found some useful resources on medium.com/@author/article-name",
    expectedUrls: ['https://www.openai.com/blog/chatgpt', 'medium.com/@author/article-name']
  },
  {
    content: "Check out this Twitter thread: https://twitter.com/user/status/123 and this LinkedIn post: https://linkedin.com/posts/user-123. Also visit google.com for search.",
    expectedUrls: ['google.com'] // Social media URLs should be filtered out
  },
  {
    content: "Visit our website at www.example.com or contact us at https://contact.example.com/form",
    expectedUrls: ['www.example.com', 'https://contact.example.com/form']
  },
  {
    content: "No URLs in this post, just plain text content about various topics.",
    expectedUrls: []
  }
];

/**
 * Test URL extraction from content
 */
export function testUrlExtraction() {
  console.log('🧪 Testing URL Extraction Service...\n');
  
  testPosts.forEach((testCase, index) => {
    console.log(`Test ${index + 1}:`);
    console.log(`Content: "${testCase.content}"`);
    
    const extractedUrls = extractUrlsFromContent(testCase.content);
    const extractedUrlStrings = extractedUrls.map(u => u.url);
    
    console.log(`Expected: [${testCase.expectedUrls.join(', ')}]`);
    console.log(`Extracted: [${extractedUrlStrings.join(', ')}]`);
    
    // Check if extraction matches expectations
    const matches = testCase.expectedUrls.every(expected => 
      extractedUrlStrings.some(extracted => 
        extracted.includes(expected.replace('https://', '').replace('www.', ''))
      )
    );
    
    console.log(`✅ Result: ${matches ? 'PASS' : 'FAIL'}\n`);
  });
}

/**
 * Test URL normalization
 */
export function testUrlNormalization() {
  console.log('🔧 Testing URL Normalization...\n');
  
  const testCases = [
    { input: 'google.com', expected: 'https://google.com' },
    { input: 'www.github.com', expected: 'https://www.github.com' },
    { input: 'https://example.com?utm_source=test&ref=social', expected: 'https://example.com' },
    { input: 'http://site.com#section', expected: 'http://site.com' },
    { input: 'https://www.site.com/', expected: 'https://www.site.com/' }
  ];
  
  testCases.forEach((testCase, index) => {
    const normalized = normalizeUrl(testCase.input);
    const matches = normalized === testCase.expected;
    
    console.log(`Test ${index + 1}:`);
    console.log(`Input: "${testCase.input}"`);
    console.log(`Expected: "${testCase.expected}"`);
    console.log(`Normalized: "${normalized}"`);
    console.log(`✅ Result: ${matches ? 'PASS' : 'FAIL'}\n`);
  });
}

/**
 * Test bookmark candidate extraction (async)
 */
export async function testBookmarkExtraction() {
  console.log('📚 Testing Bookmark Candidate Extraction...\n');
  
  const testContent = "Check out this amazing AI tool: https://bolt.new and also visit github.com for code repositories. Here's a useful article: https://www.example.com/article";
  
  try {
    console.log(`Testing content: "${testContent}"`);
    console.log('Extracting bookmark candidates...');
    
    const candidates = await extractBookmarkCandidates(testContent);
    
    console.log(`Found ${candidates.length} bookmark candidates:`);
    candidates.forEach((candidate, index) => {
      console.log(`${index + 1}. ${candidate.title}`);
      console.log(`   URL: ${candidate.normalizedUrl}`);
      console.log(`   Domain: ${candidate.domain}`);
      console.log(`   Description: ${candidate.description}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Error during bookmark extraction:', error);
  }
}

/**
 * Run all tests
 */
export function runAllTests() {
  console.log('🚀 Running URL Extraction Service Tests\n');
  console.log('=' .repeat(50));
  
  testUrlExtraction();
  console.log('=' .repeat(50));
  
  testUrlNormalization();
  console.log('=' .repeat(50));
  
  // Note: Bookmark extraction test requires async execution
  console.log('📚 To test bookmark extraction, run: testBookmarkExtraction()');
  console.log('=' .repeat(50));
}

// Auto-run tests if this file is imported
if (typeof window !== 'undefined') {
  console.log('URL Extraction Service Test Suite loaded!');
  console.log('Run runAllTests() to execute all tests');
  console.log('Run testBookmarkExtraction() to test async bookmark extraction');
}
