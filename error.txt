[Background] Received message - Action: "GET_AUTH_TOKEN", Type: "undefined"
background.ts:551 [Background] Message keys: ['action']
background.ts:625 [Background] Processing GET_AUTH_TOKEN request
background.ts:629 [Background] Auth token retrieved: present
background.ts:550 [Background] Received message - Action: "SAVE_POST_REQUEST", Type: "undefined"
background.ts:551 [Background] Message keys: (2) ['action', 'data']
background.ts:556 [Background] Processing SAVE_POST_REQUEST for post ID: 1929478895725679051
background.ts:557 [Background] Raw post data received: {id: '1929478895725679051', platform: 'X/Twitter', permalink: 'https://x.com/rshereme/status/1929478895725679051', author: 'Roman Sheremeta', content: 'Why did Ukraine publicly disclose so much info abo', …}
background.ts:188 [Background] Starting complete processing (AI first) for post ID: 1929478895725679051
background.ts:206 [Background] User logged in, proceeding with AI-first processing for post ID: 1929478895725679051
background.ts:403 [Background] AI Analysis Pipeline initiated for post: 1929478895725679051
background.ts:404 [Background] Raw post data: {id: '1929478895725679051', platform: 'X/Twitter', author: 'Roman Sheremeta', authorName: 'Roman Sheremeta', authorHandle: 'rshereme', …}
background.ts:408 [Background] Text content length: 191, preview: "Why did Ukraine publicly disclose so much info abo"
background.ts:427 [Background] Calling GPT-4o Vision for analysis...
analysisService.ts:183 [analysisService] Calling GPT-4o. System Prompt Snippet: You are an expert content analyzer for a social media saving tool. Your task is to analyze the provided text (if any) and image (if any).
Respond ONLY with a valid JSON object with the following struc...
analysisService.ts:212 [analysisService] GPT-4o validated content: {snapNote: "Ukraine's Operation 'Spiderweb' highlights modern warfare's strategic transparency.", categories: Array(2), tags: Array(3), rawResponse: {…}}
background.ts:430 [Background] GPT-4o Vision response: {snapNote: "Ukraine's Operation 'Spiderweb' highlights modern warfare's strategic transparency.", categories: Array(2), tags: Array(3), rawResponse: {…}}
background.ts:437 [Background] Generating text embedding for: Why did Ukraine publicly disclose so much info about Operation “Spiderweb”?

That’s a very good ques...
background.ts:440 [Background] Embedding generated, dimensions: 1536
background.ts:446 [Background] Adjusting categories with user bias...
categorizer.ts:36 [categorizer] biasAdjustCategories called with: (2) ['world', 'politics'] {ai_data: 0.05555555555555555, arts_design: 0.027777777777777776, books: 0.05555555555555555, business: 0.1111111111111111, career_productivity: 0.1111111111111111, …} 36
categorizer.ts:58 [categorizer] Added GPT suggested category: world
categorizer.ts:58 [categorizer] Added GPT suggested category: politics
categorizer.ts:63 [categorizer] Final categories after bias adjustment: (2) ['world', 'politics']
background.ts:461 [Background] Final categories after bias adjustment: (2) ['world', 'politics']
background.ts:465 [Background] Final tags: (3) ['modern warfare', 'strategic transparency', 'ukraine']
background.ts:479 [Background] Text for additional analysis - Length: 191, Preview: "Why did Ukraine publicly disclose so much info abo..."
background.ts:484 [Background] Generating AI insight for post ID: 1929478895725679051
background.ts:486 [Background] AI insight generated successfully for post ID: 1929478895725679051 {sentiment: 'neutral', themes: Array(3), tone: 'inquisitive', engagement_score: 7, audience_insights: 'Individuals interested in military strategy, geopo…nalysts, and those following the Ukraine conflict', …}
background.ts:493 [Background] Generating AI fast take for post ID: 1929478895725679051
background.ts:495 [Background] AI fast take generated successfully for post ID: 1929478895725679051 Ukraine's disclosure of Operation 'Spiderweb' highlights the strategic shift in modern warfare, where information plays a critical role both on and off the battlefield.
background.ts:502 [Background] Generating AI content ideas for post ID: 1929478895725679051
background.ts:504 [Background] AI content ideas generated successfully for post ID: 1929478895725679051 (3) ['Explore the strategic advantages and risks of tran…e other countries used this approach in the past?', "Invite your audience to discuss how social media i…ons: Do you think it's beneficial or detrimental?", 'Create an interactive timeline showcasing key mome…lowers to share their thoughts on each milestone.']
background.ts:536 [Background] AI Analysis Pipeline complete for post: 1929478895725679051
background.ts:537 [Background] Analyzed post data keys: (24) ['id', 'platform', 'author', 'authorName', 'authorHandle', 'authorUrl', 'authorAvatar', 'content', 'textContent', 'title', 'createdAt', 'savedAt', 'permalink', 'media', 'stats', 'categories', 'tags', 'originalPostId', 'snapNote', 'embeddingVector', 'analyzedAt', 'inSight', 'fastTake', 'contentIdeas']
background.ts:538 [Background] Analyzed post permalink: https://x.com/rshereme/status/1929478895725679051
background.ts:219 [Background] About to save complete post: {id: '1929478895725679051', platform: 'X/Twitter', permalink: 'https://x.com/rshereme/status/1929478895725679051', hasPermalink: true, allKeys: Array(26)}
storage.ts:68 [storage.ts] Attempting to save post: {id: '1929478895725679051', platform: 'X/Twitter'}
storage.ts:99 [storage.ts] Saving 1 media items to IndexedDB for post 1929478895725679051
storage.ts:362 [storage.ts] Saving image to IndexedDB for post 1929478895725679051: https://pbs.twimg.com/media/Gsbi08SXoAEoc2w?format...
storage.ts:545 [storage.ts] Error in saveImageToIndexedDB for https://pbs.twimg.com/media/Gsbi08SXoAEoc2w?format=jpg&name=orig: NotFoundError: Failed to execute 'transaction' on 'IDBDatabase': One of the specified object stores was not found.
    at saveImageToIndexedDB (chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:403:34)
    at async chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:99:19
    at async Promise.all (index 0)
    at async savePost (chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:108:34)
    at async processPostWithAIAndSync (chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:2282:7)
saveImageToIndexedDB @ storage.ts:545
await in saveImageToIndexedDB
(anonymous) @ storage.ts:107
savePost @ storage.ts:102
await in savePost
processPostWithAIAndSync @ background.ts:228
await in processPostWithAIAndSync
(anonymous) @ background.ts:576
(anonymous) @ background.ts:587Understand this error
storage.ts:120 [storage.ts] Saved 1 of 1 media items to IndexedDB
storage.ts:146 [storage.ts] Conflict saving post to cloud (409). Response data: {message: 'This post has already been saved.', details: {…}}
savePost @ storage.ts:146
await in savePost
processPostWithAIAndSync @ background.ts:228
await in processPostWithAIAndSync
(anonymous) @ background.ts:576
(anonymous) @ background.ts:587Understand this warning
background.ts:229 [Background] Complete post with AI data saved locally for ID: 1929478895725679051
background.ts:172 [Background] User frequency data updated: {categoryCounts: {…}, totalAnalyzedPosts: 37}
background.ts:235 [Background] Syncing complete post to cloud for ID: 1929478895725679051
cloudSyncService.ts:92 [CloudSync] Starting cloud upload for post: 1929478895725679051 (X/Twitter)
cloudSyncService.ts:109 [CloudSync] Post object received by syncToCloud: {
  "id": "1929478895725679051",
  "platform": "X/Twitter",
  "author": "Roman Sheremeta",
  "authorName": "Roman Sheremeta",
  "authorHandle": "rshereme",
  "authorUrl": "https://twitter.com/rshereme",
  "authorAvatar": "https://pbs.twimg.com/profile_images/1796314780711473152/2vIkQO0F_normal.jpg",
  "content": "Why did Ukraine publicly disclose so much info about Operation “Spiderweb”?\n\nThat’s a very good question — and the answer reveals how modern warfare now goes far beyond the battlefield. \n\n1/n",
  "textContent": "Why did Ukraine publicly disclose so much info about Operation “Spiderweb”?\n\nThat’s a very good question — and the answer reveals how modern warfare now goes far beyond the battlefield. \n\n1/n",
  "title": "Why did Ukraine publicly disclose so much info about Operation “Spiderweb”?\n\nThat’s a very good ques",
  "createdAt": "2025-06-02T10:03:11.000Z",
  "savedAt": "2025-06-03T08:24:01.985Z",
  "permalink": "https://x.com/rshereme/status/1929478895725679051",
  "media": [
    {
      "type": "image",
      "url": "https://pbs.twimg.com/media/Gsbi08SXoAEoc2w?format=jpg&name=orig",
      "alt": "Image"
    }
  ],
  "stats": {
    "likes": 25000,
    "comments": 343,
    "shares": 3900,
    "views": 343
  },
  "categories": [
    "world",
    "politics"
  ],
  "tags": [
    "modern warfare",
    "strategic transparency",
    "ukraine"
  ],
  "originalPostId": "1929478895725679051",
  "snapNote": "Ukraine's Operation 'Spiderweb' highlights modern warfare's strategic transparency.",
  "embeddingVector": [
    -0.024399552,
    0.00052677013,
    -0.00026359627,
    0.029333534,
    0.03690348,
    -0.035038028,
    0.026629983,
    0.028928002,
    -0.0055929725,
    0.0272518,
    0.006941369,
    -0.030306812,
    0.017018856,
    -0.0260352,
    0.009448913,
    -0.016870162,
    -0.04877207,
    0.008266109,
    -0.053043682,
    -0.041796908,
    0.03298333,
    0.0136394175,
    -0.03828229,
    0.013821907,
    -0.03176673,
    -0.037065692,
    -0.028306184,
    0.008948755,
    -0.023466827,
    -0.0016829609,
    0.046933655,
    -0.022115052,
    -0.028441362,
    -0.00002795958,
    -0.021358058,
    -0.02936057,
    -0.012024046,
    -0.035524666,
    0.08856835,
    0.021371575,
    0.0132947145,
    -0.024521213,
    -0.019276323,
    -0.00566732,
    0.03912039,
    -0.032821115,
    -0.052719254,
    -0.022439478,
    0.0022811217,
    0.01639704,
    -0.02038478,
    0.0028674544,
    -0.019316876,
    0.05396289,
    -0.0037106245,
    0.024507696,
    -0.0022794318,
    0.0326589,
    -0.047149938,
    -0.01874913,
    0.0147208385,
    0.0002935888,
    -0.003744419,
    0.006059335,
    0.01814083,
    0.01242282,
    0.013091949,
    0.042580936,
    0.014153092,
    0.030117564,
    0.048988353,
    0.0129500115,
    -0.023480345,
    -0.013990879,
    0.041878015,
    0.035227276,
    -0.016180756,
    0.022128569,
    -0.00639052,
    0.022831492,
    -0.01700534,
    -0.028143972,
    -0.022412442,
    -0.0041871252,
    0.002127357,
    -0.009753062,
    -0.026251486,
    0.010483021,
    0.0064006583,
    0.023642559,
    -0.068075426,
    0.054233246,
    -0.00970575,
    -0.021303987,
    -0.015234513,
    0.03009053,
    -0.023358686,
    0.008218797,
    -0.021655448,
    -0.03117195,
    0.010165354,
    -0.042364653,
    -0.009584091,
    0.012443096,
    -0.0077456753,
    -0.019330394,
    0.004129675,
    0.0024027815,
    0.059045564,
    -0.015477832,
    -0.09765228,
    -0.023953468,
    -0.023980502,
    0.030225707,
    0.04358125,
    -0.018303044,
    -0.01899245,
    -0.009306977,
    0.012348472,
    0.010273496,
    -0.02779251,
    -0.019762961,
    -0.010307291,
    -0.033010364,
    0.025075441,
    0.011577959,
    -0.038011935,
    -0.0059038806,
    0.0076375334,
    0.013794872,
    0.06131655,
    0.0069346097,
    0.04563595,
    -0.068345785,
    0.019681856,
    0.016005024,
    -0.011530647,
    0.012402543,
    0.0008026169,
    0.015342655,
    -0.014977676,
    0.02502137,
    0.02079031,
    0.03901225,
    -0.07975477,
    -0.0026782057,
    -0.028549504,
    0.03666016,
    0.040093668,
    0.0064513497,
    0.014761391,
    0.0033118506,
    0.027265318,
    0.0018350356,
    0.0033659218,
    0.005437518,
    0.04647405,
    0.04896132,
    0.014464001,
    -0.008272868,
    0.027900653,
    0.05601759,
    -0.022980189,
    -0.003937047,
    -0.03019867,
    0.011801003,
    0.01791103,
    -0.046203695,
    -0.04950203,
    -0.029252429,
    -0.038444504,
    -0.015518386,
    -0.00952326,
    0.017491978,
    -0.016505182,
    0.023899395,
    -0.0075429087,
    -0.005782221,
    0.0074753202,
    -0.07683493,
    0.009861205,
    0.0035788263,
    -0.00012334954,
    -0.02477805,
    -0.040201813,
    -0.044987097,
    -0.029576855,
    -0.014464001,
    -0.04249983,
    0.09749007,
    -0.037606403,
    0.032415584,
    0.015423762,
    -0.01603206,
    0.032280404,
    -0.003882976,
    -0.0061640977,
    0.0135921
cloudSyncService.ts:130 [CloudSync] Uploading X/Twitter post 1929478895725679051 to https://api.notely.social/api/posts with data: {id: '1929478895725679051', platform: 'X/Twitter', author: 'Roman Sheremeta', authorName: 'Roman Sheremeta', authorHandle: 'rshereme', …}
cloudSyncService.ts:136 [CloudSync] Payload to be sent (postToUpload): {
  "id": "1929478895725679051",
  "platform": "X/Twitter",
  "author": "Roman Sheremeta",
  "authorName": "Roman Sheremeta",
  "authorHandle": "rshereme",
  "authorUrl": "https://twitter.com/rshereme",
  "authorAvatar": "https://pbs.twimg.com/profile_images/1796314780711473152/2vIkQO0F_normal.jpg",
  "content": "Why did Ukraine publicly disclose so much info about Operation “Spiderweb”?\n\nThat’s a very good question — and the answer reveals how modern warfare now goes far beyond the battlefield. \n\n1/n",
  "title": "Why did Ukraine publicly disclose so much info about Operation “Spiderweb”?\n\nThat’s a very good ques",
  "createdAt": "2025-06-02T10:03:11.000Z",
  "savedAt": "2025-06-03T08:24:03.025Z",
  "permalink": "https://x.com/rshereme/status/1929478895725679051",
  "media": [
    {
      "type": "image",
      "url": "https://pbs.twimg.com/media/Gsbi08SXoAEoc2w?format=jpg&name=orig",
      "alt": "Image"
    }
  ],
  "categories": [
    "world",
    "politics"
  ],
  "tags": [
    "modern warfare",
    "strategic transparency",
    "ukraine"
  ],
  "originalPostId": "1929478895725679051",
  "snapNote": "Ukraine's Operation 'Spiderweb' highlights modern warfare's strategic transparency.",
  "embeddingVector": [
    -0.024399552,
    0.00052677013,
    -0.00026359627,
    0.029333534,
    0.03690348,
    -0.035038028,
    0.026629983,
    0.028928002,
    -0.0055929725,
    0.0272518,
    0.006941369,
    -0.030306812,
    0.017018856,
    -0.0260352,
    0.009448913,
    -0.016870162,
    -0.04877207,
    0.008266109,
    -0.053043682,
    -0.041796908,
    0.03298333,
    0.0136394175,
    -0.03828229,
    0.013821907,
    -0.03176673,
    -0.037065692,
    -0.028306184,
    0.008948755,
    -0.023466827,
    -0.0016829609,
    0.046933655,
    -0.022115052,
    -0.028441362,
    -0.00002795958,
    -0.021358058,
    -0.02936057,
    -0.012024046,
    -0.035524666,
    0.08856835,
    0.021371575,
    0.0132947145,
    -0.024521213,
    -0.019276323,
    -0.00566732,
    0.03912039,
    -0.032821115,
    -0.052719254,
    -0.022439478,
    0.0022811217,
    0.01639704,
    -0.02038478,
    0.0028674544,
    -0.019316876,
    0.05396289,
    -0.0037106245,
    0.024507696,
    -0.0022794318,
    0.0326589,
    -0.047149938,
    -0.01874913,
    0.0147208385,
    0.0002935888,
    -0.003744419,
    0.006059335,
    0.01814083,
    0.01242282,
    0.013091949,
    0.042580936,
    0.014153092,
    0.030117564,
    0.048988353,
    0.0129500115,
    -0.023480345,
    -0.013990879,
    0.041878015,
    0.035227276,
    -0.016180756,
    0.022128569,
    -0.00639052,
    0.022831492,
    -0.01700534,
    -0.028143972,
    -0.022412442,
    -0.0041871252,
    0.002127357,
    -0.009753062,
    -0.026251486,
    0.010483021,
    0.0064006583,
    0.023642559,
    -0.068075426,
    0.054233246,
    -0.00970575,
    -0.021303987,
    -0.015234513,
    0.03009053,
    -0.023358686,
    0.008218797,
    -0.021655448,
    -0.03117195,
    0.010165354,
    -0.042364653,
    -0.009584091,
    0.012443096,
    -0.0077456753,
    -0.019330394,
    0.004129675,
    0.0024027815,
    0.059045564,
    -0.015477832,
    -0.09765228,
    -0.023953468,
    -0.023980502,
    0.030225707,
    0.04358125,
    -0.018303044,
    -0.01899245,
    -0.009306977,
    0.012348472,
    0.010273496,
    -0.02779251,
    -0.019762961,
    -0.010307291,
    -0.033010364,
    0.025075441,
    0.011577959,
    -0.038011935,
    -0.0059038806,
    0.0076375334,
    0.013794872,
    0.06131655,
    0.0069346097,
    0.04563595,
    -0.068345785,
    0.019681856,
    0.016005024,
    -0.011530647,
    0.012402543,
    0.0008026169,
    0.015342655,
    -0.014977676,
    0.02502137,
    0.02079031,
    0.03901225,
    -0.07975477,
    -0.0026782057,
    -0.028549504,
    0.03666016,
    0.040093668,
    0.0064513497,
    0.014761391,
    0.0033118506,
    0.027265318,
    0.0018350356,
    0.0033659218,
    0.005437518,
    0.04647405,
    0.04896132,
    0.014464001,
    -0.008272868,
    0.027900653,
    0.05601759,
    -0.022980189,
    -0.003937047,
    -0.03019867,
    0.011801003,
    0.01791103,
    -0.046203695,
    -0.04950203,
    -0.029252429,
    -0.038444504,
    -0.015518386,
    -0.00952326,
    0.017491978,
    -0.016505182,
    0.023899395,
    -0.0075429087,
    -0.005782221,
    0.0074753202,
    -0.07683493,
    0.009861205,
    0.0035788263,
    -0.00012334954,
    -0.02477805,
    -0.040201813,
    -0.044987097,
    -0.029576855,
    -0.014464001,
    -0.04249983,
    0.09749007,
    -0.037606403,
    0.032415584,
    0.015423762,
    -0.01603206,
    0.032280404,
    -0.003882976,
    -0.0061640977,
    0.013592105,
    0.011956457,
    0.0012402543,
    -0.0350921,
    0.026062237,
    0.034929886,
    -0.024250858,
    -0.00068011216,
    0.00046256077,
    0.017464943,
    0.03628166,
    -0.020655135,
    0.04906946,
    0.023953468,
    -0.029712033,
    0.01970889,
    -0.00367683,
    -0.009894999,
    -0.062289
cloudSyncService.ts:181 [CloudSync] 409 Conflict for post 1929478895725679051. Response data: {message: 'This post has already been saved.', details: {…}}
cloudSyncService.ts:261 [CloudSync] 409 Conflict for post 1929478895725679051, but NO existing cloud ID found in response. Data: {message: 'This post has already been saved.', details: {…}}
syncToCloud @ cloudSyncService.ts:261
await in syncToCloud
processPostWithAIAndSync @ background.ts:237
await in processPostWithAIAndSync
(anonymous) @ background.ts:576
(anonymous) @ background.ts:587Understand this warning
background.ts:251 [Background] Failed to sync complete post 1929478895725679051 to cloud: Post already exists (409), but existing cloud ID not provided in response.
processPostWithAIAndSync @ background.ts:251
await in processPostWithAIAndSync
(anonymous) @ background.ts:576
(anonymous) @ background.ts:587Understand this error
storage.ts:68 [storage.ts] Attempting to save post: {id: '1929478895725679051', platform: 'X/Twitter'}
storage.ts:99 [storage.ts] Saving 1 media items to IndexedDB for post 1929478895725679051
storage.ts:362 [storage.ts] Saving image to IndexedDB for post 1929478895725679051: https://pbs.twimg.com/media/Gsbi08SXoAEoc2w?format...
storage.ts:545 [storage.ts] Error in saveImageToIndexedDB for https://pbs.twimg.com/media/Gsbi08SXoAEoc2w?format=jpg&name=orig: NotFoundError: Failed to execute 'transaction' on 'IDBDatabase': One of the specified object stores was not found.
    at saveImageToIndexedDB (chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:403:34)
    at async chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:99:19
    at async Promise.all (index 0)
    at async savePost (chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:108:34)
    at async processPostWithAIAndSync (chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae/background.js:2303:9)
saveImageToIndexedDB @ storage.ts:545
await in saveImageToIndexedDB
(anonymous) @ storage.ts:107
savePost @ storage.ts:102
await in savePost
processPostWithAIAndSync @ background.ts:259
await in processPostWithAIAndSync
(anonymous) @ background.ts:576
(anonymous) @ background.ts:587Understand this error
storage.ts:120 [storage.ts] Saved 1 of 1 media items to IndexedDB
storage.ts:146 [storage.ts] Conflict saving post to cloud (409). Response data: {message: 'This post has already been saved.', details: {…}}
savePost @ storage.ts:146
await in savePost
processPostWithAIAndSync @ background.ts:259
await in processPostWithAIAndSync
(anonymous) @ background.ts:576
(anonymous) @ background.ts:587Understand this warning
background.ts:267 [Background] REFRESH_DASHBOARD_FROM_CLOUD message sent. Response: {status: 'ok', message: 'Dashboard refresh triggered by REFRESH_DASHBOARD_FROM_CLOUD.'}