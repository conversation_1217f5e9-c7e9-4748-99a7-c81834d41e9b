/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./popup.html",
    "./dashboard.html",
    "./settings.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: ['class', "class"],
  theme: {
  	extend: {
  		colors: {
  			notely: {
  				dark: {
  					bg: '#0B0B0B', // Updated to match user preference
  					surface: '#1a1a1a',
  					card: '#252525',
  					border: '#333333',
  					'border-dark': '#404040',
  					text: {
  						primary: '#ffffff',
  						secondary: '#b3b3b3',
  						muted: '#666666'
  					}
  				},
  				light: {
  					bg: '#ffffff',
  					surface: '#f8f9fa',
  					card: '#ffffff',
  					border: '#e5e7eb',
  					text: {
  						primary: '#1f2937',
  						secondary: '#6b7280',
  						muted: '#9ca3af'
  					}
  				},
  				// Common background colors for both themes
  				background: 'rgba(255, 255, 255, 0.75)', // Light mode background with transparency
  				'background-dark': 'rgba(26, 26, 26, 0.90)', // Dark mode background with transparency
  				coral: '#ff6b6b',
  				mint: '#51cf66',
  				sky: '#74c0fc',
  				lavender: '#b197fc',
  				peach: '#ffa8a8',
  				sage: '#8ce99a',
  				// New gradient colors matching notely.social
  				pink: '#FF43C8', // User's brand pink
  				purple: '#7A5CFF', // User's brand purple
  				gradient: {
  					from: '#7A5CFF', // Purple start
  					via: '#FF43C8', // Pink middle
  					to: '#6366f1', // Indigo end
  					purple: {
  						from: '#8b5cf6',
  						to: '#6366f1'
  					},
  					pink: {
  						from: '#ec4899',
  						to: '#f97316'
  					},
  					indigo: {
  						from: '#6366f1',
  						to: '#3b82f6'
  					}
  				}
  			},
  			notion: {
  				bg: '#ffffff',
  				gray: {
  					'100': '#f7f7f7',
  					'200': '#e9e9e9',
  					'300': '#d3d3d3',
  					'400': '#b3b3b3',
  					'500': '#808080',
  					'600': '#666666',
  					'700': '#4d4d4d',
  					'800': '#333333',
  					'900': '#1a1a1a'
  				},
  				border: 'rgba(0, 0, 0, 0.1)',
  				divider: 'rgba(0, 0, 0, 0.2)'
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		fontFamily: {
  			heading: [
  				'Eudoxus Sans',
  				'system-ui',
  				'sans-serif'
  			],
  			body: [
  				'DM Sans',
  				'system-ui',
  				'sans-serif'
  			],
  			quote: [
  				'Georgia',
  				'serif'
  			],
  			sans: [
  				'DM Sans',
  				'system-ui',
  				'sans-serif'
  			]
  		},
  		fontSize: {
  			notion: {
  				body: [
  					'14px',
  					{
  						lineHeight: '1.5'
  					}
  				],
  				heading: [
  					'18px',
  					{
  						lineHeight: '1.3',
  						fontWeight: '600'
  					}
  				],
  				title: [
  					'24px',
  					{
  						lineHeight: '1.2',
  						fontWeight: '700'
  					}
  				]
  			}
  		},
  		spacing: {
  			notion: {
  				block: '24px',
  				container: '32px'
  			},
  			notely: {
  				xs: '8px',
  				sm: '12px',
  				md: '16px',
  				lg: '24px',
  				xl: '32px',
  				'2xl': '48px'
  			}
  		},
  		borderRadius: {
  			notion: '3px',
  			notely: {
  				sm: '8px',
  				md: '12px',
  				lg: '16px',
  				xl: '20px',
  				'2xl': '24px'
  			},
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		borderWidth: {
  			notion: '1px'
  		},
  		// Glassmorphism and backdrop effects
  		backdropBlur: {
  			'xs': '2px',
  			'sm': '4px',
  			'md': '8px',
  			'lg': '12px',
  			'xl': '16px',
  			'2xl': '24px',
  			'3xl': '40px'
  		},
  		animation: {
  			'gradient': 'gradient 6s ease infinite',
  			'float': 'float 6s ease-in-out infinite',
  			'glow': 'glow 2s ease-in-out infinite alternate',
  			'shimmer': 'shimmer 2s linear infinite'
  		},
  		keyframes: {
  			gradient: {
  				'0%, 100%': {
  					'background-size': '200% 200%',
  					'background-position': 'left center'
  				},
  				'50%': {
  					'background-size': '200% 200%',
  					'background-position': 'right center'
  				}
  			},
  			float: {
  				'0%, 100%': { transform: 'translateY(0px)' },
  				'50%': { transform: 'translateY(-10px)' }
  			},
  			glow: {
  				'from': { 'box-shadow': '0 0 20px rgba(123, 92, 255, 0.3)' },
  				'to': { 'box-shadow': '0 0 30px rgba(255, 67, 200, 0.4)' }
  			},
  			shimmer: {
  				'0%': { transform: 'translateX(-100%)' },
  				'100%': { transform: 'translateX(100%)' }
  			}
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
  future: {
    hoverOnlyWhenSupported: true,
  },
  safelist: [
    'notion-container',
    'notion-block',
    'notion-heading',
    'notion-title',
    'notion-divider',
    'notion-button',
    'notion-input',
    'notion-card',
    'text-notion-gray-400',
    'text-notion-gray-500',
    'text-notion-gray-600',
    'text-notion-gray-700',
    'bg-notion-bg',
    'bg-notion-gray-100',
    'bg-notion-gray-200',
    'bg-notion-gray-300',
    'border-notion-border',
    'border-notion-divider',
    'rounded-notion',
    'mb-notion-block',
    'py-notion-block',
    'px-notion-block',
    'w-12', 'h-7', 'w-5', 'h-5', 'translate-x-5',
    // New gradient and glassmorphism classes
    'bg-gradient-to-r', 'bg-gradient-to-br', 'bg-gradient-to-tr',
    'from-notely-purple', 'via-notely-pink', 'to-indigo-600',
    'from-purple-600', 'to-indigo-600', 'from-purple-400', 'to-indigo-400',
    'backdrop-blur-sm', 'backdrop-blur-md', 'backdrop-blur-lg', 'backdrop-blur-xl',
    'bg-white/5', 'bg-white/10', 'bg-white/20', 'bg-black/20', 'bg-black/40',
    'border-purple-500/20', 'border-purple-500/30', 'border-white/10', 'border-white/20',
    'animate-gradient', 'animate-float', 'animate-glow', 'animate-shimmer',
    'text-transparent', 'bg-clip-text', 'shadow-purple-500/25', 'shadow-pink-500/25',
    'hover:scale-105', 'hover:shadow-2xl', 'group-hover:opacity-50',
  ],
};