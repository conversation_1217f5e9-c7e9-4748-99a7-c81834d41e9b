# 🔄 Migration Guide - Refactored Architecture

This guide helps you understand how to use the new refactored architecture and migrate any existing code.

## 📋 **Overview of Changes**

### **Phase 1: Background Script Unification**
- **Before**: 4 overlapping background scripts with duplicated logic
- **After**: 1 unified background script with modular handlers

### **Phase 2: Content Script Organization** 
- **Before**: Duplicated button styling, save logic, and mutation observers
- **After**: Shared components with platform-specific implementations

### **Phase 3: Service Organization**
- **Before**: Duplicate storage services and inconsistent error handling
- **After**: Unified service layer with consistent interfaces

---

## 🚀 **How to Use the New Architecture**

### **1. Using the Service Factory**

The new `ServiceFactory` provides a single entry point for all services:

```typescript
import { serviceFactory, getStorageService, getPlatformService } from './services/ServiceFactory';

// Get storage service
const storageService = getStorageService();
const result = await storageService.savePost(post);

// Get platform service
const twitterService = getPlatformService('X/Twitter');
const extractionResult = await twitterService.extractPostData(element);

// Get all services
await serviceFactory.initialize();
```

### **2. Using the Unified Storage Service**

```typescript
import { getStorageService } from './services/ServiceFactory';

const storageService = getStorageService();

// Save a post
const saveResult = await storageService.savePost(post, {
  useLocal: true,
  useSync: true,
  useCloud: false
});

// Get all posts
const postsResult = await storageService.getAllPosts();
if (postsResult.success) {
  const posts = postsResult.data;
}

// Get storage usage
const usageResult = await storageService.getStorageUsage();
```

### **3. Using Platform Services**

```typescript
import { getPlatformService } from './services/ServiceFactory';

const instagramService = getPlatformService('Instagram');

// Extract post data
const result = await instagramService.extractPostData(element, {
  includeImages: true,
  useCloudService: true
});

if (result.success && result.data) {
  const post = result.data;
  console.log('Extracted post:', post);
}

// Validate post
const validatedPost = instagramService.validatePost(partialPost);
```

### **4. Using Content Script Components**

```typescript
import { TwitterContentScript } from './content/platforms/TwitterContentScript';

// Initialize Twitter content script
const twitterScript = new TwitterContentScript();
twitterScript.initialize();

// Or use the base class for custom platforms
import { BaseContentScript } from './content/shared/BaseContentScript';

class CustomPlatformScript extends BaseContentScript {
  constructor() {
    super({
      platform: 'CustomPlatform',
      postSelectors: ['.post', '.article'],
      buttonConfig: { position: 'inline' }
    });
  }
  
  protected validatePost(post: HTMLElement): boolean {
    // Custom validation logic
    return post.textContent && post.textContent.length > 10;
  }
}
```

---

## 🔄 **Migration Patterns**

### **Migrating Storage Code**

**Before:**
```typescript
import { savePost, getSavedPosts } from './storage';

const result = await savePost(post);
const posts = await getSavedPosts();
```

**After (Option 1 - New API):**
```typescript
import { getStorageService } from './services/ServiceFactory';

const storageService = getStorageService();
const result = await storageService.savePost(post);
const postsResult = await storageService.getAllPosts();
const posts = postsResult.data || [];
```

**After (Option 2 - Compatibility Layer):**
```typescript
import { savePost, getAllPosts } from './storage/StorageCompatibilityLayer';

const result = await savePost(post); // Same API as before
const posts = await getAllPosts(); // Enhanced with unified service
```

### **Migrating Platform Services**

**Before:**
```typescript
import { InstagramService } from './platforms/instagram/InstagramService';

const service = new InstagramService();
const result = await service.extractPostData(element);
```

**After:**
```typescript
import { getPlatformService } from './services/ServiceFactory';

const service = getPlatformService('Instagram');
const result = await service.extractPostData(element);
```

### **Migrating Content Scripts**

**Before:**
```typescript
// Lots of duplicated code for button creation, mutation observers, etc.
function addSaveButton(post) {
  const button = document.createElement('button');
  // ... 50+ lines of styling and event handling
}
```

**After:**
```typescript
import { TwitterContentScript } from './content/platforms/TwitterContentScript';

// All common functionality is handled by the base class
const script = new TwitterContentScript();
script.initialize();
```

---

## ⚠️ **Breaking Changes**

### **1. Service Interfaces**
- All services now return `ServiceResult<T>` objects instead of direct values
- Check `result.success` before accessing `result.data`

### **2. Error Handling**
- Errors are now standardized with `ServiceError` classes
- Use `result.error` and `result.retryable` for error handling

### **3. Platform Service Instantiation**
- Use `getPlatformService(platform)` instead of `new PlatformService()`
- Services are now singletons managed by the factory

---

## 🛠️ **Backward Compatibility**

### **Storage Compatibility**
The `StorageCompatibilityLayer` maintains the existing API:

```typescript
// This still works exactly as before
import { savePost, getSavedPosts, deletePost } from './storage/StorageCompatibilityLayer';
```

### **Content Script Compatibility**
Existing content scripts continue to work but are now simple entry points:

```typescript
// src/content/twitter-content.ts now just initializes the new TwitterContentScript
import { TwitterContentScript } from './platforms/TwitterContentScript';
const twitterScript = new TwitterContentScript();
```

---

## 🎯 **Best Practices**

### **1. Use the Service Factory**
Always use the service factory instead of creating service instances directly:

```typescript
// ✅ Good
const storageService = getStorageService();

// ❌ Avoid
const storageService = new UnifiedStorageService();
```

### **2. Handle Service Results Properly**
Always check the success flag before using data:

```typescript
// ✅ Good
const result = await storageService.savePost(post);
if (result.success) {
  console.log('Post saved successfully');
} else {
  console.error('Save failed:', result.error);
}

// ❌ Avoid
const result = await storageService.savePost(post);
console.log('Post saved:', result.data); // Might be undefined
```

### **3. Use TypeScript Interfaces**
The new architecture is fully typed. Use the interfaces for better development experience:

```typescript
import { IStorageService, ServiceResult } from './services/shared/ServiceInterfaces';

function handleStorage(storage: IStorageService) {
  // TypeScript will provide full autocomplete and type checking
}
```

### **4. Extend Base Classes**
For custom functionality, extend the base classes instead of starting from scratch:

```typescript
import { BaseContentScript } from './content/shared/BaseContentScript';

class MyCustomScript extends BaseContentScript {
  // Override only what you need to customize
}
```

---

## 🔍 **Testing the Migration**

### **1. Verify Services Work**
```typescript
import { serviceFactory } from './services/ServiceFactory';

// Test service initialization
await serviceFactory.initialize();
console.log('All services initialized successfully');

// Test storage
const storageService = serviceFactory.getStorageService();
const testPost = { /* test post data */ };
const result = await storageService.savePost(testPost);
console.log('Storage test:', result.success ? 'PASS' : 'FAIL');
```

### **2. Verify Content Scripts Work**
- Load the extension and visit Twitter, Instagram, LinkedIn
- Verify save buttons appear and function correctly
- Check browser console for any errors

### **3. Verify Background Script Works**
- Check extension's background page console
- Verify cloud sync and message handling work correctly

---

## 📞 **Support**

If you encounter issues during migration:

1. **Check the console** for error messages
2. **Use compatibility layers** for gradual migration
3. **Refer to the interfaces** in `ServiceInterfaces.ts` for API documentation
4. **Look at existing implementations** in the `platforms/` and `content/platforms/` directories

The new architecture is designed to be backward compatible, so existing functionality should continue to work while you gradually adopt the new patterns.
