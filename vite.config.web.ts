import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// Web-specific Vite config for deploying to https://notely.social/dashboard
export default defineConfig({
  plugins: [react()],
  css: {
    postcss: './postcss.config.cjs',
  },
  base: '/', // Base path for web deployment
  build: {
    outDir: 'dist-web',
    rollupOptions: {
      input: resolve(__dirname, 'web/index.html'),
      output: {
        format: 'es',
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      }
    },
    emptyOutDir: true,
  },
  define: {
    // Define Chrome extension APIs as undefined for web
    'chrome': 'undefined',
    'chrome.storage': 'undefined',
    'chrome.runtime': 'undefined',
    'chrome.tabs': 'undefined',
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3001,
    host: true,
  },
});
