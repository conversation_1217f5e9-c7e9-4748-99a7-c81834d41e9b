# 🌐 Domain Setup Guide for notely.social

## 📋 **Current Status**
- ✅ **Backend API**: `api.notely.social` (already deployed on Railway)
- 🚀 **Web Dashboard**: Ready to deploy to `notely.social`
- 💻 **Chrome Extension**: Working with existing backend

## 🎯 **DNS Configuration in Spaceship**

### **Step 1: Set Up DNS Records**

In your Spaceship domain manager for `notely.social`, add these DNS records:

```
Type: A
Name: @
Value: ***********
TTL: 3600

Type: A  
Name: www
Value: ***********
TTL: 3600

Type: CNAME
Name: api
Value: api.notely.social.railway.app
TTL: 3600
```

**Note**: The IP `***********` is Vercel's IP. We'll use Vercel for the main site.

### **Step 2: Deploy to Vercel**

1. **Create Vercel Account**: Go to [vercel.com](https://vercel.com)

2. **Connect GitHub**: Import your repository `tugrulfirat/social-saver-pro`

3. **Configure Build Settings**:
   - **Build Command**: `npm run build:web`
   - **Output Directory**: `dist-web`
   - **Install Command**: `npm install`

4. **Add Custom Domain**:
   - Go to Project Settings → Domains
   - Add `notely.social` and `www.notely.social`
   - Vercel will verify DNS automatically

## 🚀 **Alternative: Deploy to Netlify**

If you prefer Netlify:

1. **Connect Repository**: Go to [netlify.com](https://netlify.com)
2. **Build Settings**:
   - **Build Command**: `npm run build:web`
   - **Publish Directory**: `dist-web`
3. **Add Domain**: In Site Settings → Domain Management

## 📱 **What Users Will See**

### **Landing Page** (`https://notely.social/`)
- ✅ Beautiful hero section with value proposition
- ✅ Feature highlights and benefits
- ✅ Pricing plans (Free vs Premium)
- ✅ Premium trial offers during registration
- ✅ Chrome extension download links
- ✅ Responsive design for all devices

### **Registration Flow** (`https://notely.social/register`)
- ✅ Premium offer banner with 7-day free trial
- ✅ Email/password registration
- ✅ Google OAuth option
- ✅ Automatic premium trial signup

### **Dashboard** (`https://notely.social/dashboard`)
- ✅ User account management
- ✅ Chrome extension promotion
- ✅ Billing and subscription management
- ✅ Settings and preferences

## 💳 **Premium Subscription Flow**

1. **User visits landing page** → Sees premium benefits
2. **Clicks "Get Started"** → Goes to registration with premium offer
3. **Registers account** → Automatically starts 7-day free trial
4. **After 7 days** → Stripe charges $9.99/month
5. **User can cancel** → Anytime in settings

## 🔧 **Backend Integration**

The web dashboard connects to your existing backend:
- **API URL**: `https://api.notely.social`
- **Authentication**: Same JWT tokens as Chrome extension
- **Billing**: Existing Stripe integration
- **User Management**: Same user database

## 📊 **URL Structure**

```
https://notely.social/           → Landing page
https://notely.social/register   → Registration with premium offers
https://notely.social/login      → Login page
https://notely.social/dashboard  → User dashboard
https://notely.social/settings   → Account settings
https://api.notely.social/*      → Backend API (already working)
```

## ⚡ **Quick Deployment Steps**

1. **Set DNS records** in Spaceship (as shown above)
2. **Deploy to Vercel**:
   ```bash
   # Option 1: Use Vercel CLI
   npm i -g vercel
   vercel --prod
   
   # Option 2: Use Vercel website
   # Import GitHub repo at vercel.com
   ```
3. **Add custom domain** in Vercel dashboard
4. **Test the site** at https://notely.social

## 🎨 **Features Included**

### **Landing Page Features**:
- ✅ Hero section with email capture
- ✅ Feature showcase (6 key features)
- ✅ Pricing comparison (Free vs Premium)
- ✅ Premium trial offers
- ✅ Chrome extension promotion
- ✅ Social proof and testimonials
- ✅ Call-to-action buttons

### **Premium Registration Features**:
- ✅ Special offer banner
- ✅ 7-day free trial promotion
- ✅ Premium benefits highlight
- ✅ Seamless signup flow
- ✅ Automatic trial activation

### **Dashboard Features**:
- ✅ Account management
- ✅ Billing settings
- ✅ Chrome extension links
- ✅ Premium status display
- ✅ Subscription management

## 🔒 **Security & Performance**

- ✅ **HTTPS**: Automatic SSL certificates
- ✅ **CDN**: Global content delivery
- ✅ **Authentication**: Secure JWT tokens
- ✅ **API Integration**: Existing backend security
- ✅ **Privacy**: GDPR compliant

## 📈 **Analytics & Tracking**

You can add analytics by including tracking codes in the HTML:
- Google Analytics
- Mixpanel
- Hotjar
- Facebook Pixel

## 🎯 **Next Steps**

1. **Set up DNS** in Spaceship
2. **Deploy to Vercel** or Netlify
3. **Test the complete flow**:
   - Landing page → Registration → Premium trial
   - Login → Dashboard → Settings
   - Chrome extension integration
4. **Monitor conversions** and optimize

## 📞 **Support**

If you need help with deployment:
1. Check DNS propagation: [whatsmydns.net](https://whatsmydns.net)
2. Verify build logs in Vercel/Netlify
3. Test API connectivity
4. Check browser console for errors

Your premium landing page is ready to convert visitors into paying customers! 🚀
