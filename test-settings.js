// Test script to verify settings functionality
// Run this in the browser console on the settings page

console.log('=== Testing Settings Functionality ===');

// Helper function to wait for DOM elements
function waitForElement(selector, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// Test 1: Check if settings are loaded correctly
async function testSettingsLoad() {
  console.log('\n1. Testing settings load...');

  try {
    const localSettings = await new Promise(resolve => {
      chrome.storage.local.get(['saveMediaToCloud', 'showMindstreamTips', 'platformIntegrations'], resolve);
    });

    const syncSettings = await new Promise(resolve => {
      chrome.storage.sync.get(['locale'], resolve);
    });

    console.log('✅ Local settings loaded:', localSettings);
    console.log('✅ Sync settings loaded:', syncSettings);

    return { localSettings, syncSettings };
  } catch (error) {
    console.error('❌ Error loading settings:', error);
    return null;
  }
}

// Test UI elements are present and functional
async function testUIElements() {
  console.log('\n1.5. Testing UI elements...');

  try {
    // Wait for language selector
    const languageSelect = await waitForElement('select[aria-label*="Language"], select[value]');
    console.log('✅ Language selector found:', languageSelect);

    // Wait for platform toggles
    const toggles = await waitForElement('[role="switch"]');
    console.log('✅ Platform toggles found:', document.querySelectorAll('[role="switch"]').length, 'toggles');

    // Check if settings form is rendered
    const settingsForm = document.querySelector('.grid');
    console.log('✅ Settings form found:', !!settingsForm);

    return true;
  } catch (error) {
    console.error('❌ Error finding UI elements:', error);
    return false;
  }
}

// Test 2: Test platform toggle functionality
async function testPlatformToggle() {
  console.log('\n2. Testing platform toggle...');
  
  try {
    // Get current platform integrations
    const current = await new Promise(resolve => {
      chrome.storage.local.get(['platformIntegrations'], resolve);
    });
    
    console.log('Current platform integrations:', current.platformIntegrations);
    
    // Toggle X/Twitter
    const newIntegrations = {
      ...current.platformIntegrations,
      'X/Twitter': !current.platformIntegrations['X/Twitter']
    };
    
    // Save the change
    await new Promise(resolve => {
      chrome.storage.local.set({ platformIntegrations: newIntegrations }, resolve);
    });
    
    // Verify the change
    const updated = await new Promise(resolve => {
      chrome.storage.local.get(['platformIntegrations'], resolve);
    });
    
    console.log('✅ Platform integrations updated:', updated.platformIntegrations);
    
    // Restore original state
    await new Promise(resolve => {
      chrome.storage.local.set({ platformIntegrations: current.platformIntegrations }, resolve);
    });
    
    console.log('✅ Platform integrations restored');
    
    return true;
  } catch (error) {
    console.error('❌ Error testing platform toggle:', error);
    return false;
  }
}

// Test 3: Test language change functionality
async function testLanguageChange() {
  console.log('\n3. Testing language change...');
  
  try {
    // Get current locale
    const current = await new Promise(resolve => {
      chrome.storage.sync.get(['locale'], resolve);
    });
    
    console.log('Current locale:', current.locale);
    
    // Change to a different locale
    const newLocale = current.locale === 'en' ? 'tr' : 'en';
    
    await new Promise(resolve => {
      chrome.storage.sync.set({ locale: newLocale }, resolve);
    });
    
    // Verify the change
    const updated = await new Promise(resolve => {
      chrome.storage.sync.get(['locale'], resolve);
    });
    
    console.log('✅ Locale updated:', updated.locale);
    
    // Restore original locale
    await new Promise(resolve => {
      chrome.storage.sync.set({ locale: current.locale }, resolve);
    });
    
    console.log('✅ Locale restored');
    
    return true;
  } catch (error) {
    console.error('❌ Error testing language change:', error);
    return false;
  }
}

// Test 4: Test storage persistence
async function testStoragePersistence() {
  console.log('\n4. Testing storage persistence...');
  
  try {
    const testData = {
      saveMediaToCloud: false,
      showMindstreamTips: false,
      platformIntegrations: {
        'X/Twitter': false,
        'LinkedIn': true,
        'Reddit': false,
        'Instagram': true,
        'pinterest': false,
        'Web': true
      }
    };
    
    // Save test data
    await new Promise(resolve => {
      chrome.storage.local.set(testData, resolve);
    });
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Retrieve and verify
    const retrieved = await new Promise(resolve => {
      chrome.storage.local.get(Object.keys(testData), resolve);
    });
    
    const isMatch = JSON.stringify(testData) === JSON.stringify(retrieved);
    
    if (isMatch) {
      console.log('✅ Storage persistence test passed');
    } else {
      console.error('❌ Storage persistence test failed');
      console.log('Expected:', testData);
      console.log('Retrieved:', retrieved);
    }
    
    return isMatch;
  } catch (error) {
    console.error('❌ Error testing storage persistence:', error);
    return false;
  }
}

// Test UI interaction
async function testUIInteraction() {
  console.log('\n5. Testing UI interaction...');

  try {
    // Test language selector interaction
    const languageSelect = document.querySelector('select[aria-label*="Language"], select');
    if (languageSelect) {
      const originalValue = languageSelect.value;
      console.log('Current language:', originalValue);

      // Try to change language
      const newValue = originalValue === 'en' ? 'tr' : 'en';
      languageSelect.value = newValue;
      languageSelect.dispatchEvent(new Event('change', { bubbles: true }));

      console.log('✅ Language selector interaction test completed');

      // Wait a moment then restore
      setTimeout(() => {
        languageSelect.value = originalValue;
        languageSelect.dispatchEvent(new Event('change', { bubbles: true }));
      }, 1000);
    }

    // Test platform toggle interaction
    const firstToggle = document.querySelector('[role="switch"]');
    if (firstToggle) {
      console.log('Testing platform toggle click...');
      firstToggle.click();
      console.log('✅ Platform toggle interaction test completed');
    }

    return true;
  } catch (error) {
    console.error('❌ Error testing UI interaction:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting comprehensive settings tests...\n');

  const results = {
    settingsLoad: await testSettingsLoad(),
    uiElements: await testUIElements(),
    platformToggle: await testPlatformToggle(),
    languageChange: await testLanguageChange(),
    storagePersistence: await testStoragePersistence(),
    uiInteraction: await testUIInteraction()
  };

  console.log('\n=== Test Results ===');
  console.log('Settings Load:', results.settingsLoad ? '✅ PASS' : '❌ FAIL');
  console.log('UI Elements:', results.uiElements ? '✅ PASS' : '❌ FAIL');
  console.log('Platform Toggle:', results.platformToggle ? '✅ PASS' : '❌ FAIL');
  console.log('Language Change:', results.languageChange ? '✅ PASS' : '❌ FAIL');
  console.log('Storage Persistence:', results.storagePersistence ? '✅ PASS' : '❌ FAIL');
  console.log('UI Interaction:', results.uiInteraction ? '✅ PASS' : '❌ FAIL');

  const allPassed = Object.values(results).every(result => result);
  console.log('\nOverall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');

  return results;
}

// Make functions available globally for manual testing
window.testSettings = {
  runAllTests,
  testSettingsLoad,
  testUIElements,
  testPlatformToggle,
  testLanguageChange,
  testStoragePersistence,
  testUIInteraction,
  waitForElement
};

console.log('Test functions available at window.testSettings');
console.log('Run window.testSettings.runAllTests() to test all functionality');

// Auto-run basic tests after a short delay to let the page load
setTimeout(() => {
  console.log('\n🚀 Auto-running basic tests...');
  window.testSettings.testSettingsLoad();
  window.testSettings.testUIElements();
}, 2000);
