# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/notely-social?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51234567890abcdef...
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef...
STRIPE_PRICE_ID_MONTHLY=price_1234567890abcdef
STRIPE_PRICE_ID_YEARLY=price_1234567890abcdef

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=AKIA1234567890ABCDEF
AWS_SECRET_ACCESS_KEY=abcdef1234567890abcdef1234567890abcdef12
AWS_S3_BUCKET_NAME=notely-social-images
AWS_S3_REGION=us-east-1

# Google OAuth Configuration
GOOGLE_CLIENT_ID=123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz123456
GOOGLE_CALLBACK_URL=https://your-api-domain.com/auth/google/callback

# Email Configuration (for password reset)
EMAIL_SMTP_URL=smtp://username:<EMAIL>:587
EMAIL_FROM=<EMAIL>

# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Client URLs
CLIENT_URL=chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae
SERVER_BASE_URL=https://your-api-domain.com

# Redis Configuration (for queue processing)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# Environment
NODE_ENV=development

# Railway Configuration (for deployment)
PORT=3000

# Testing Configuration
WEBHOOK_ENDPOINT=http://localhost:3000/auth/stripe/webhook
