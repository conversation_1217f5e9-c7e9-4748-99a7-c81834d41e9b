/**
 * Simple test script to verify queue functionality
 * Run with: node test-queue.js
 */

const { addImageProcessingJob, getQueueStats } = require('./dist/services/queueService');

async function testQueue() {
  console.log('🧪 Testing queue functionality...');
  
  try {
    // Test adding a job
    console.log('📝 Adding test job to queue...');
    const job = await addImageProcessingJob(
      'test-post-id-123',
      'test-user-id-456',
      'X/Twitter',
      [
        {
          url: 'https://example.com/test-image.jpg',
          type: 'image',
          alt: 'Test image'
        }
      ],
      { test: 'data' }
    );
    
    console.log(`✅ Job added successfully with ID: ${job.id}`);
    
    // Test getting queue stats
    console.log('📊 Getting queue stats...');
    const stats = await getQueueStats();
    console.log('Queue stats:', stats);
    
    console.log('🎉 Queue test completed successfully!');
    
  } catch (error) {
    console.error('❌ Queue test failed:', error.message);
    console.error('Make sure <PERSON><PERSON> is running on localhost:6379');
  }
  
  process.exit(0);
}

testQueue();
