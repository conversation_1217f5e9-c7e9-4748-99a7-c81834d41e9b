import * as cron from 'node-cron';
import AbuseDetectionService from '../services/abuseDetectionService';

/**
 * Abuse monitoring background worker
 * Runs automated checks for suspicious user activity
 */
export class AbuseMonitorWorker {
  private static isRunning = false;
  private static scheduledTask: cron.ScheduledTask | null = null;

  /**
   * Start the abuse monitoring worker
   */
  static start(): void {
    if (this.isRunning) {
      console.log('🔍 Abuse monitor worker is already running');
      return;
    }

    // Schedule checks every 6 hours
    this.scheduledTask = cron.schedule('0 */6 * * *', async () => {
      console.log('🔍 Starting scheduled abuse detection check...');
      try {
        await AbuseDetectionService.checkAllUsers();
        console.log('✅ Scheduled abuse detection check completed');
      } catch (error) {
        console.error('❌ Scheduled abuse detection check failed:', error);
      }
    });

    this.isRunning = true;
    console.log('🚀 Abuse monitor worker started (runs every 6 hours)');

    // Optional: Run initial check after startup (with delay)
    if (process.env.NODE_ENV === 'production') {
      setTimeout(async () => {
        console.log('🔍 Running initial abuse detection check...');
        try {
          await AbuseDetectionService.checkAllUsers();
          console.log('✅ Initial abuse detection check completed');
        } catch (error) {
          console.error('❌ Initial abuse detection check failed:', error);
        }
      }, 5 * 60 * 1000); // 5 minutes after startup
    }
  }

  /**
   * Stop the abuse monitoring worker
   */
  static stop(): void {
    if (!this.isRunning) {
      console.log('🔍 Abuse monitor worker is not running');
      return;
    }

    if (this.scheduledTask) {
      this.scheduledTask.stop();
      this.scheduledTask = null;
    }

    this.isRunning = false;
    console.log('🛑 Abuse monitor worker stopped');
  }

  /**
   * Get worker status
   */
  static getStatus(): { running: boolean; nextRun?: string } {
    return {
      running: this.isRunning,
      nextRun: this.isRunning ? 'Next check in 6 hours' : undefined,
    };
  }

  /**
   * Manual trigger for abuse detection
   */
  static async triggerCheck(): Promise<void> {
    console.log('🔍 Manually triggering abuse detection check...');
    try {
      await AbuseDetectionService.checkAllUsers();
      console.log('✅ Manual abuse detection check completed');
    } catch (error) {
      console.error('❌ Manual abuse detection check failed:', error);
      throw error;
    }
  }
}

export default AbuseMonitorWorker; 