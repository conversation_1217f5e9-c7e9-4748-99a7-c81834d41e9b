// Import the User interface
import { IUser } from '../../models/User'; // Adjust path based on your project structure

declare global {
  namespace Express {
    // Augment the Express Request interface to include the 'user' property
    export interface Request {
      user?: IUser; // Define user property, potentially IUser or any based on your needs
    }
  }
}

// Adding an empty export statement turns this file into a module
// This is necessary for the global augmentation to be applied correctly.
export {}; 