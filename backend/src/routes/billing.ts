import express, { Request, Response, Router } from 'express';
import <PERSON><PERSON> from 'stripe';
import { protect } from '../middleware/authMiddleware';
import { IUser } from '../models/User';
import Coupon from '../models/Coupon';
import { asyncHandler } from '../utils/asyncHandler';

const router: Router = express.Router();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

// Create Stripe Checkout session (with optional coupon)
router.post('/create-checkout-session', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const { priceId, couponCode, successUrl, cancelUrl } = req.body;

  console.log('[Billing] Create checkout session request:', {
    userId: user._id,
    email: user.email,
    priceId,
    couponCode,
    hasStripeKey: !!process.env.STRIPE_SECRET_KEY
  });

  if (!priceId) {
    res.status(400).json({ message: 'Price ID is required' });
    return;
  }

  if (!process.env.STRIPE_SECRET_KEY) {
    console.error('[Billing] Stripe secret key not configured');
    res.status(500).json({ message: 'Stripe not configured' });
    return;
  }

  try {
    let couponId: string | undefined;
    let appliedCoupon: { code: string; discountType: string; amount: number } | null = null;

    // Validate and apply coupon if provided
    if (couponCode) {
      const coupon = await Coupon.findOne({ 
        code: couponCode.toUpperCase().trim(),
        isActive: true,
      });

      if (!coupon) {
        res.status(400).json({ message: 'Invalid coupon code' });
        return;
      }

      // Check if expired
      if (coupon.expiresAt && coupon.expiresAt < new Date()) {
        res.status(400).json({ message: 'Coupon has expired' });
        return;
      }

      // Check usage limit
      if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
        res.status(400).json({ message: 'Coupon usage limit exceeded' });
        return;
      }

      // Create or get Stripe coupon
      try {
        let stripeCoupon;
        try {
          stripeCoupon = await stripe.coupons.retrieve(coupon.code);
        } catch {
          // Create new Stripe coupon if it doesn't exist
          stripeCoupon = await stripe.coupons.create({
            id: coupon.code,
            name: `${coupon.code} - ${coupon.discountType === 'percentage' ? coupon.amount + '%' : '$' + coupon.amount} off`,
            [coupon.discountType === 'percentage' ? 'percent_off' : 'amount_off']: 
              coupon.discountType === 'percentage' ? coupon.amount : coupon.amount * 100, // Stripe expects cents for amount_off
            currency: coupon.discountType === 'fixed' ? 'usd' : undefined,
            duration: 'once',
          });
        }
        
        couponId = stripeCoupon.id;
        appliedCoupon = {
          code: coupon.code,
          discountType: coupon.discountType,
          amount: coupon.amount,
        };
      } catch (stripeError) {
        console.error('Error creating Stripe coupon:', stripeError);
        res.status(500).json({ message: 'Failed to apply coupon' });
        return;
      }
    }

    // Ensure customer exists
    let customerId = user.stripeCustomerId;
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          userId: user._id.toString(),
        },
      });
      customerId = customer.id;
      
      // Update user with Stripe customer ID
      user.stripeCustomerId = customerId;
      await user.save();
    }

    // Create checkout session
    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl || `${process.env.CLIENT_URL || 'chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae'}/dashboard.html?success=true`,
      cancel_url: cancelUrl || `${process.env.CLIENT_URL || 'chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae'}/dashboard.html?tab=settings`,
      metadata: {
        userId: user._id.toString(),
        couponCode: couponCode || '',
      },
    };

    // Add coupon if available
    if (couponId) {
      sessionParams.discounts = [{ coupon: couponId }];
    }

    const session = await stripe.checkout.sessions.create(sessionParams);

    res.json({
      sessionId: session.id,
      url: session.url,
      appliedCoupon,
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ message: 'Failed to create checkout session' });
  }
}));

// Create Stripe Customer Portal session
router.post('/create-portal-session', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  if (!user.stripeCustomerId) {
    res.status(400).json({ message: 'No Stripe customer ID found. Please contact support.' });
    return;
  }

  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: user.stripeCustomerId,
      return_url: `${process.env.CLIENT_URL || 'chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae'}/dashboard.html?tab=settings`,
    });

    res.json({ url: session.url });
  } catch (error) {
    console.error('Error creating billing portal session:', error);
    res.status(500).json({ message: 'Failed to create billing portal session' });
  }
}));

// Cancel subscription
router.post('/cancel-subscription', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  if (!user.stripeSubscriptionId) {
    res.status(400).json({ message: 'No active subscription found' });
    return;
  }

  try {
    const subscription = await stripe.subscriptions.update(user.stripeSubscriptionId, {
      cancel_at_period_end: true,
    });

    res.json({ 
      message: 'Subscription will be canceled at the end of the current billing period',
      cancelAt: subscription.cancel_at,
    });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    res.status(500).json({ message: 'Failed to cancel subscription' });
  }
}));

// Reactivate subscription
router.post('/reactivate-subscription', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  if (!user.stripeSubscriptionId) {
    res.status(400).json({ message: 'No subscription found' });
    return;
  }

  try {
    const subscription = await stripe.subscriptions.update(user.stripeSubscriptionId, {
      cancel_at_period_end: false,
    });

    res.json({ 
      message: 'Subscription reactivated successfully',
      status: subscription.status,
    });
  } catch (error) {
    console.error('Error reactivating subscription:', error);
    res.status(500).json({ message: 'Failed to reactivate subscription' });
  }
}));

// Change subscription plan (upgrade/downgrade)
router.post('/change-plan', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const { priceId } = req.body;

  if (!priceId) {
    res.status(400).json({ message: 'Price ID is required' });
    return;
  }

  if (!user.stripeSubscriptionId) {
    res.status(400).json({ message: 'No active subscription found' });
    return;
  }

  try {
    // Get current subscription
    const subscription = await stripe.subscriptions.retrieve(user.stripeSubscriptionId);

    // Update subscription with new price
    const updatedSubscription = await stripe.subscriptions.update(user.stripeSubscriptionId, {
      items: [{
        id: subscription.items.data[0].id,
        price: priceId,
      }],
      proration_behavior: 'create_prorations', // Handle prorations for plan changes
    });

    // Update user plan in database based on price ID
    const monthlyPriceId = process.env.STRIPE_PRICE_ID_MONTHLY;
    const yearlyPriceId = process.env.STRIPE_PRICE_ID_YEARLY;

    if (priceId === monthlyPriceId || priceId === yearlyPriceId) {
      user.plan = 'premium';
    } else {
      // If it's a different price ID, assume it's a downgrade to free
      user.plan = 'free';
    }

    await user.save();

    res.json({
      message: 'Subscription plan updated successfully',
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        currentPeriodStart: updatedSubscription.current_period_start,
        currentPeriodEnd: updatedSubscription.current_period_end,
      }
    });
  } catch (error) {
    console.error('Error changing subscription plan:', error);
    res.status(500).json({ message: 'Failed to change subscription plan' });
  }
}));

// Get subscription details
router.get('/subscription', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  if (!user.stripeSubscriptionId) {
    res.json({ subscription: null });
    return;
  }

  try {
    const subscription = await stripe.subscriptions.retrieve(user.stripeSubscriptionId);

    res.json({
      subscription: {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: subscription.current_period_start,
        currentPeriodEnd: subscription.current_period_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        cancelAt: subscription.cancel_at,
        trialEnd: subscription.trial_end,
        priceId: subscription.items.data[0]?.price?.id,
      },
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    res.status(500).json({ message: 'Failed to fetch subscription details' });
  }
}));

// Get available pricing plans
router.get('/plans', asyncHandler(async (req: Request, res: Response) => {
  const plans = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      interval: null,
      priceId: null,
      features: [
        'Basic AI analysis',
        '1GB cloud storage',
        'Standard support'
      ]
    },
    {
      id: 'monthly',
      name: 'Premium Monthly',
      price: 9.99,
      interval: 'month',
      priceId: process.env.STRIPE_PRICE_ID_MONTHLY,
      features: [
        'Unlimited AI analysis',
        '10GB cloud storage',
        'Advanced content insights',
        'Priority support'
      ]
    },
    {
      id: 'yearly',
      name: 'Premium Yearly',
      price: 99.99,
      interval: 'year',
      priceId: process.env.STRIPE_PRICE_ID_YEARLY,
      features: [
        'Unlimited AI analysis',
        '10GB cloud storage',
        'Advanced content insights',
        'Priority support',
        '2 months free'
      ]
    }
  ];

  res.json({ plans });
}));

export default router;
