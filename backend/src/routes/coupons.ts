import express, { Request, Response, Router } from 'express';
import Coupon, { ICoupon } from '../models/Coupon';
import { protect, isAdmin } from '../middleware/authMiddleware';
import { asyncHandler } from '../utils/asyncHandler';

const router: Router = express.Router();

// Admin Routes

// Create a new coupon (admin only)
router.post('/admin/coupons', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { code, discountType, amount, usageLimit, expiresAt } = req.body;
  const adminUser = req.user as any;

  // Validate required fields
  if (!code || !discountType || !amount) {
    res.status(400).json({ message: 'Code, discount type, and amount are required' });
    return;
  }

  // Validate discount type and amount
  if (!['percentage', 'fixed'].includes(discountType)) {
    res.status(400).json({ message: 'Discount type must be either "percentage" or "fixed"' });
    return;
  }

  if (discountType === 'percentage' && (amount <= 0 || amount > 100)) {
    res.status(400).json({ message: 'Percentage discount must be between 1 and 100' });
    return;
  }

  if (discountType === 'fixed' && amount <= 0) {
    res.status(400).json({ message: 'Fixed discount must be greater than 0' });
    return;
  }

  try {
    const coupon = new Coupon({
      code: code.toUpperCase().trim(),
      discountType,
      amount,
      usageLimit: usageLimit || null,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      createdBy: adminUser.email,
    });

    await coupon.save();

    res.status(201).json({
      message: 'Coupon created successfully',
      coupon: {
        id: coupon._id,
        code: coupon.code,
        discountType: coupon.discountType,
        amount: coupon.amount,
        usageLimit: coupon.usageLimit,
        usedCount: coupon.usedCount,
        expiresAt: coupon.expiresAt,
        isActive: coupon.isActive,
        createdBy: coupon.createdBy,
        createdAt: coupon.createdAt,
      },
    });
  } catch (error: any) {
    if (error.code === 11000) {
      res.status(400).json({ message: 'Coupon code already exists' });
      return;
    }
    throw error;
  }
}));

// Get all coupons (admin only)
router.get('/admin/coupons', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 50, status, search } = req.query;

  const filter: any = {};
  
  if (status === 'active') {
    filter.isActive = true;
    filter.$or = [
      { expiresAt: null },
      { expiresAt: { $gt: new Date() } }
    ];
  } else if (status === 'expired') {
    filter.$or = [
      { isActive: false },
      { expiresAt: { $lte: new Date() } }
    ];
  }

  if (search) {
    filter.code = { $regex: search, $options: 'i' };
  }

  try {
    const skip = (Number(page) - 1) * Number(limit);
    
    const [coupons, total] = await Promise.all([
      Coupon.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      Coupon.countDocuments(filter),
    ]);

    // Add virtual fields manually
    const couponsWithStatus = coupons.map(coupon => ({
      id: coupon._id,
      code: coupon.code,
      discountType: coupon.discountType,
      amount: coupon.amount,
      usageLimit: coupon.usageLimit,
      usedCount: coupon.usedCount,
      expiresAt: coupon.expiresAt,
      isActive: coupon.isActive,
      createdBy: coupon.createdBy,
      createdAt: coupon.createdAt,
      updatedAt: coupon.updatedAt,
      isExpired: coupon.expiresAt && coupon.expiresAt < new Date(),
      isUsable: coupon.isActive && 
                (!coupon.expiresAt || coupon.expiresAt > new Date()) && 
                (!coupon.usageLimit || coupon.usedCount < coupon.usageLimit),
    }));

    res.json({
      coupons: couponsWithStatus,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    });
  } catch (error) {
    console.error('Error fetching coupons:', error);
    res.status(500).json({ message: 'Failed to fetch coupons' });
  }
}));

// Update coupon (admin only)
router.patch('/admin/coupons/:id', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updates = req.body;

  // Prevent updating certain fields
  delete updates.code;
  delete updates.createdBy;
  delete updates.usedCount;

  try {
    const coupon = await Coupon.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!coupon) {
      res.status(404).json({ message: 'Coupon not found' });
      return;
    }

    res.json({
      message: 'Coupon updated successfully',
      coupon: {
        id: coupon._id,
        code: coupon.code,
        discountType: coupon.discountType,
        amount: coupon.amount,
        usageLimit: coupon.usageLimit,
        usedCount: coupon.usedCount,
        expiresAt: coupon.expiresAt,
        isActive: coupon.isActive,
        createdBy: coupon.createdBy,
        createdAt: coupon.createdAt,
        updatedAt: coupon.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error updating coupon:', error);
    res.status(500).json({ message: 'Failed to update coupon' });
  }
}));

// Delete coupon (admin only)
router.delete('/admin/coupons/:id', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const coupon = await Coupon.findByIdAndDelete(id);

    if (!coupon) {
      res.status(404).json({ message: 'Coupon not found' });
      return;
    }

    res.json({ message: 'Coupon deleted successfully' });
  } catch (error) {
    console.error('Error deleting coupon:', error);
    res.status(500).json({ message: 'Failed to delete coupon' });
  }
}));

// Public Routes

// Validate and apply coupon (public)
router.post('/apply-coupon', asyncHandler(async (req: Request, res: Response) => {
  const { code } = req.body;

  if (!code) {
    res.status(400).json({ message: 'Coupon code is required' });
    return;
  }

  try {
    const coupon = await Coupon.findOne({ 
      code: code.toUpperCase().trim(),
      isActive: true,
    });

    if (!coupon) {
      res.status(404).json({ message: 'Invalid coupon code' });
      return;
    }

    // Check if expired
    if (coupon.expiresAt && coupon.expiresAt < new Date()) {
      res.status(400).json({ message: 'Coupon has expired' });
      return;
    }

    // Check usage limit
    if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
      res.status(400).json({ message: 'Coupon usage limit exceeded' });
      return;
    }

    res.json({
      message: 'Coupon is valid',
      coupon: {
        id: coupon._id,
        code: coupon.code,
        discountType: coupon.discountType,
        amount: coupon.amount,
      },
      discount: {
        type: coupon.discountType,
        amount: coupon.amount,
        description: coupon.discountType === 'percentage' 
          ? `${coupon.amount}% off` 
          : `$${coupon.amount} off`,
      },
    });
  } catch (error) {
    console.error('Error validating coupon:', error);
    res.status(500).json({ message: 'Failed to validate coupon' });
  }
}));

// Get coupon stats (admin only)
router.get('/admin/coupons/stats', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  try {
    const [
      totalCoupons,
      activeCoupons,
      expiredCoupons,
      totalUsages,
    ] = await Promise.all([
      Coupon.countDocuments(),
      Coupon.countDocuments({ 
        isActive: true,
        $or: [
          { expiresAt: null },
          { expiresAt: { $gt: new Date() } }
        ]
      }),
      Coupon.countDocuments({
        $or: [
          { isActive: false },
          { expiresAt: { $lte: new Date() } }
        ]
      }),
      Coupon.aggregate([
        { $group: { _id: null, totalUsages: { $sum: '$usedCount' } } }
      ]).then(result => result[0]?.totalUsages || 0),
    ]);

    res.json({
      stats: {
        totalCoupons,
        activeCoupons,
        expiredCoupons,
        totalUsages,
      },
    });
  } catch (error) {
    console.error('Error fetching coupon stats:', error);
    res.status(500).json({ message: 'Failed to fetch coupon stats' });
  }
}));

export default router; 