import express from 'express';
import { protect } from '../middleware/authMiddleware';
import User from '../models/User';
import AIUsageLog from '../models/AIUsageLog';
import StorageUsage from '../models/StorageUsage';
import mongoose from 'mongoose';

const router = express.Router();

// Middleware to check if user is admin
const requireAdmin = (req: any, res: any, next: any): void => {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  const userEmail = req.user?.email;

  if (!userEmail || !adminEmails.includes(userEmail)) {
    res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
    return;
  }

  next();
};

// Pricing constants
const PRICING = {
  TOKENS_PER_DOLLAR: 500000, // 1000 tokens ≈ $0.002, so 500k tokens = $1
  GB_PER_DOLLAR: 40, // 1GB ≈ $0.025, so 40GB = $1
  SUSPICIOUS_THRESHOLDS: {
    TOKENS_30D: 100000,
    STORAGE_GB: 1,
    COST_DOLLAR: 1
  }
};

interface SuspiciousUser {
  _id: string;
  email: string;
  name: string;
  displayName?: string;
  plan: string;
  tokensUsed30d: number;
  storageUsedMB: number;
  estimatedCost: number;
  flaggedForAbuse: boolean;
  lastLogin: Date;
  createdAt: Date;
  suspiciousReasons: string[];
}

/**
 * GET /admin/users/suspicious - Get users with suspicious usage patterns
 */
router.get('/admin/users/suspicious', protect, requireAdmin, async (req, res): Promise<void> => {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get all users
    const allUsers = await User.find({}, {
      email: 1,
      name: 1,
      displayName: 1,
      plan: 1,
      flaggedForAbuse: 1,
      lastLogin: 1,
      createdAt: 1,
      storageUsed: 1
    });

    const suspiciousUsers: SuspiciousUser[] = [];

    // Process each user
    for (const user of allUsers) {
      try {
        // Aggregate AI token usage for last 30 days
        const tokenUsage = await AIUsageLog.aggregate([
          {
            $match: {
              userId: user._id,
              date: { $gte: thirtyDaysAgo }
            }
          },
          {
            $group: {
              _id: null,
              totalTokens: { $sum: '$tokensUsed' }
            }
          }
        ]);

        // Aggregate storage usage for last 30 days
        const storageUsage = await StorageUsage.aggregate([
          {
            $match: {
              userId: user._id,
              date: { $gte: thirtyDaysAgo }
            }
          },
          {
            $group: {
              _id: null,
              totalMB: { $sum: '$mbUsed' }
            }
          }
        ]);

        const tokensUsed30d = tokenUsage[0]?.totalTokens || 0;
        const storageUsedMB = storageUsage[0]?.totalMB || (user.storageUsed / (1024 * 1024));
        const storageUsedGB = storageUsedMB / 1024;

        // Calculate estimated cost
        const tokenCost = tokensUsed30d / PRICING.TOKENS_PER_DOLLAR;
        const storageCost = storageUsedGB / PRICING.GB_PER_DOLLAR;
        const estimatedCost = tokenCost + storageCost;

        // Check if user meets suspicious criteria
        const suspiciousReasons: string[] = [];
        let isSuspicious = false;

        if (tokensUsed30d > PRICING.SUSPICIOUS_THRESHOLDS.TOKENS_30D) {
          suspiciousReasons.push(`High token usage: ${tokensUsed30d.toLocaleString()} tokens`);
          isSuspicious = true;
        }

        if (storageUsedGB > PRICING.SUSPICIOUS_THRESHOLDS.STORAGE_GB) {
          suspiciousReasons.push(`High storage usage: ${storageUsedGB.toFixed(2)} GB`);
          isSuspicious = true;
        }

        if (estimatedCost > PRICING.SUSPICIOUS_THRESHOLDS.COST_DOLLAR) {
          suspiciousReasons.push(`High estimated cost: $${estimatedCost.toFixed(3)}`);
          isSuspicious = true;
        }

        // Auto-flag users if they meet criteria and aren't already flagged
        if (isSuspicious && !user.flaggedForAbuse) {
          await User.findByIdAndUpdate(user._id, { flaggedForAbuse: true });
        }

        // Include user if suspicious or already flagged
        if (isSuspicious || user.flaggedForAbuse || estimatedCost > 0.1) { // Show users with any significant usage
          suspiciousUsers.push({
            _id: user._id.toString(),
            email: user.email,
            name: user.name,
            displayName: user.displayName,
            plan: user.plan,
            tokensUsed30d,
            storageUsedMB: Math.round(storageUsedMB * 100) / 100,
            estimatedCost: Math.round(estimatedCost * 1000) / 1000,
            flaggedForAbuse: user.flaggedForAbuse || isSuspicious,
            lastLogin: user.lastLogin,
            createdAt: user.createdAt,
            suspiciousReasons
          });
        }
      } catch (userError) {
        console.error(`Error processing user ${user.email}:`, userError);
        // Continue with other users even if one fails
      }
    }

    // Sort by estimated cost descending
    suspiciousUsers.sort((a, b) => b.estimatedCost - a.estimatedCost);

    // Calculate summary statistics
    const totalUsers = suspiciousUsers.length;
    const flaggedUsers = suspiciousUsers.filter(u => u.flaggedForAbuse).length;
    const totalCost = suspiciousUsers.reduce((sum, u) => sum + u.estimatedCost, 0);
    const averageCost = totalUsers > 0 ? totalCost / totalUsers : 0;

    res.json({
      success: true,
      users: suspiciousUsers,
      statistics: {
        totalUsers,
        flaggedUsers,
        totalEstimatedCost: Math.round(totalCost * 1000) / 1000,
        averageEstimatedCost: Math.round(averageCost * 1000) / 1000,
        thresholds: PRICING.SUSPICIOUS_THRESHOLDS
      }
    });
  } catch (error) {
    console.error('Error fetching suspicious users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch suspicious users'
    });
  }
});

/**
 * PATCH /admin/users/:id/mark-safe - Mark a user as safe (unflag them)
 */
router.patch('/admin/users/:id/mark-safe', protect, requireAdmin, async (req, res): Promise<void> => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
      return;
    }

    const user = await User.findByIdAndUpdate(
      id,
      { flaggedForAbuse: false },
      { new: true }
    );

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      message: `User ${user.email} marked as safe`,
      user: {
        _id: user._id,
        email: user.email,
        flaggedForAbuse: user.flaggedForAbuse
      }
    });
  } catch (error) {
    console.error('Error marking user as safe:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark user as safe'
    });
  }
});

/**
 * GET /admin/cost-analysis/summary - Get overall cost analysis summary
 */
router.get('/admin/cost-analysis/summary', protect, requireAdmin, async (req, res): Promise<void> => {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get total AI usage
    const totalTokenUsage = await AIUsageLog.aggregate([
      {
        $match: {
          date: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: null,
          totalTokens: { $sum: '$tokensUsed' },
          totalRequests: { $sum: 1 }
        }
      }
    ]);

    // Get total storage usage
    const totalStorageUsage = await StorageUsage.aggregate([
      {
        $match: {
          date: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: null,
          totalMB: { $sum: '$mbUsed' },
          totalUploads: { $sum: 1 }
        }
      }
    ]);

    const tokens = totalTokenUsage[0]?.totalTokens || 0;
    const tokenRequests = totalTokenUsage[0]?.totalRequests || 0;
    const storageMB = totalStorageUsage[0]?.totalMB || 0;
    const storageUploads = totalStorageUsage[0]?.totalUploads || 0;

    const tokenCost = tokens / PRICING.TOKENS_PER_DOLLAR;
    const storageCost = (storageMB / 1024) / PRICING.GB_PER_DOLLAR;
    const totalCost = tokenCost + storageCost;

    res.json({
      success: true,
      summary: {
        period: '30 days',
        tokens: {
          total: tokens,
          requests: tokenRequests,
          cost: Math.round(tokenCost * 1000) / 1000
        },
        storage: {
          totalMB: Math.round(storageMB * 100) / 100,
          totalGB: Math.round((storageMB / 1024) * 100) / 100,
          uploads: storageUploads,
          cost: Math.round(storageCost * 1000) / 1000
        },
        total: {
          estimatedCost: Math.round(totalCost * 1000) / 1000
        },
        pricing: PRICING
      }
    });
  } catch (error) {
    console.error('Error fetching cost analysis summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cost analysis summary'
    });
  }
});

export default router; 