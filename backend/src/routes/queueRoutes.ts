import express, { Request, Response } from 'express';
import { protect } from '../middleware/authMiddleware';
import { getQueueStats } from '../services/queueService';
import CloudPost from '../models/CloudPost';
import { IUser } from '../models/User';

const router = express.Router();

// @route   GET /api/queue/stats
// @desc    Get queue statistics (admin only for now)
// @access  Private
router.get('/stats', protect, async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await getQueueStats();
    res.json(stats);
  } catch (error) {
    console.error('Error getting queue stats:', error);
    res.status(500).json({ message: 'Failed to get queue stats' });
  }
});

// @route   GET /api/queue/posts/:postId/status
// @desc    Get image processing status for a specific post
// @access  Private
router.get('/posts/:postId/status', protect, async (req: Request, res: Response): Promise<void> => {
  try {
    const user = req.user as IUser;
    const { postId } = req.params;

    const post = await CloudPost.findOne({
      _id: postId,
      userId: user.id
    }).select('imageProcessingStatus imageProcessingJobId imageProcessingError imageProcessedAt');

    if (!post) {
      res.status(404).json({ message: 'Post not found' });
      return;
    }

    res.json({
      postId,
      status: post.imageProcessingStatus,
      jobId: post.imageProcessingJobId,
      error: post.imageProcessingError,
      processedAt: post.imageProcessedAt,
    });
  } catch (error) {
    console.error('Error getting post processing status:', error);
    res.status(500).json({ message: 'Failed to get processing status' });
  }
});

// @route   GET /api/queue/posts/processing
// @desc    Get all posts currently being processed for the user
// @access  Private
router.get('/posts/processing', protect, async (req: Request, res: Response): Promise<void> => {
  try {
    const user = req.user as IUser;

    const processingPosts = await CloudPost.find({
      userId: user.id,
      imageProcessingStatus: { $in: ['pending', 'processing'] }
    }).select('_id originalPostId platform imageProcessingStatus imageProcessingJobId savedAt');

    res.json(processingPosts);
  } catch (error) {
    console.error('Error getting processing posts:', error);
    res.status(500).json({ message: 'Failed to get processing posts' });
  }
});

export default router;
