import express from 'express';
import { protect } from '../middleware/authMiddleware';
import { AIProviderConfigService, TaskType, AIProvider } from '../services/aiProviderConfigService';

const router = express.Router();

// Middleware to check if user is admin
const requireAdmin = (req: any, res: any, next: any): void => {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  const userEmail = req.user?.email;

  if (!userEmail || !adminEmails.includes(userEmail)) {
    res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
    return;
  }

  next();
};

/**
 * GET /admin/ai-config - Get all AI provider configurations
 */
router.get('/ai-config', protect, requireAdmin, async (req, res): Promise<void> => {
  try {
    const configs = await AIProviderConfigService.getAllConfigs();
    
    res.json({
      success: true,
      configs: configs.map(config => ({
        taskType: config.taskType,
        provider: config.provider,
        model: config.model,
        updatedAt: config.updatedAt
      }))
    });
  } catch (error) {
    console.error('Error fetching AI provider configs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch AI provider configurations'
    });
  }
});

/**
 * PATCH /admin/ai-config - Update AI provider configuration
 * Body: { taskType: string, provider: string, model?: string }
 */
router.patch('/ai-config', protect, requireAdmin, async (req, res): Promise<void> => {
  try {
    const { taskType, provider, model } = req.body;

    // Validate task type
    const validTaskTypes: TaskType[] = ['summarize', 'generate', 'rewrite', 'categorize', 'insight', 'content', 'search', 'embedding'];
    if (!taskType || !validTaskTypes.includes(taskType)) {
      res.status(400).json({
        success: false,
        message: 'Invalid task type. Must be one of: ' + validTaskTypes.join(', ')
      });
      return;
    }

    // Validate provider
    const validProviders: AIProvider[] = ['openai', 'deepseek'];
    if (!provider || !validProviders.includes(provider)) {
      res.status(400).json({
        success: false,
        message: 'Invalid provider. Must be one of: ' + validProviders.join(', ')
      });
      return;
    }

    const config = await AIProviderConfigService.setProviderForTask(taskType, provider, model);

    res.json({
      success: true,
      message: `AI provider for ${taskType} updated to ${provider}${model ? ` (${model})` : ''}`,
      config: {
        taskType: config.taskType,
        provider: config.provider,
        model: config.model,
        updatedAt: config.updatedAt
      }
    });
  } catch (error) {
    console.error('Error updating AI provider config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update AI provider configuration'
    });
  }
});

/**
 * POST /admin/ai-config/reset - Reset all configurations to defaults
 */
router.post('/ai-config/reset', protect, requireAdmin, async (req, res): Promise<void> => {
  try {
    await AIProviderConfigService.resetToDefaults();
    const configs = await AIProviderConfigService.getAllConfigs();

    res.json({
      success: true,
      message: 'AI provider configurations reset to defaults',
      configs: configs.map(config => ({
        taskType: config.taskType,
        provider: config.provider,
        model: config.model,
        updatedAt: config.updatedAt
      }))
    });
  } catch (error) {
    console.error('Error resetting AI provider configs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset AI provider configurations'
    });
  }
});

/**
 * GET /admin/ai-config/providers - Get available AI providers and their models
 */
router.get('/ai-config/providers', protect, requireAdmin, async (req, res): Promise<void> => {
  try {
    const providers = {
      openai: {
        name: 'OpenAI',
        models: [
          { id: 'gpt-4o', name: 'GPT-4o' },
          { id: 'gpt-4o-mini', name: 'GPT-4o Mini' },
          { id: 'text-embedding-3-small', name: 'Text Embedding 3 Small' },
          { id: 'text-embedding-3-large', name: 'Text Embedding 3 Large' }
        ]
      },
      deepseek: {
        name: 'DeepSeek',
        models: [
          { id: 'deepseek-chat', name: 'DeepSeek Chat' },
          { id: 'deepseek-coder', name: 'DeepSeek Coder' }
        ]
      }
    };

    const taskTypes = [
      { id: 'summarize', name: 'Summarize', description: 'Generate summaries of content' },
      { id: 'generate', name: 'Generate', description: 'Generate new content' },
      { id: 'rewrite', name: 'Rewrite', description: 'Rewrite and improve content' },
      { id: 'categorize', name: 'Categorize', description: 'Categorize and tag content' },
      { id: 'insight', name: 'Insight', description: 'Generate insights and analysis' },
      { id: 'content', name: 'Content', description: 'Content suggestions and ideas' },
      { id: 'search', name: 'Search', description: 'Search result summaries' },
      { id: 'embedding', name: 'Embedding', description: 'Text embeddings for search' }
    ];

    res.json({
      success: true,
      providers,
      taskTypes
    });
  } catch (error) {
    console.error('Error fetching AI provider info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch AI provider information'
    });
  }
});

export default router; 