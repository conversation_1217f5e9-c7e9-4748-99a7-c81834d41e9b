import express, { Request, Response } from 'express';
import { protect } from '../middleware/authMiddleware';
import { IUser } from '../models/User';
import UnifiedSyncData from '../models/UnifiedSyncData';

const router = express.Router();

/**
 * @route   POST /api/unified-sync
 * @desc    Upload unified data (categories, bookmarks, chat) to cloud
 * @access  Private
 */
router.post('/', protect, async (req: Request, res: Response): Promise<void> => {
  try {
    const user = req.user as IUser;
    const { version, timestamp, deviceId, data } = req.body;

    if (!data) {
      res.status(400).json({ message: 'Data is required' });
      return;
    }

    // Find existing sync data for user
    let syncData = await UnifiedSyncData.findOne({ userId: user.id });

    if (syncData) {
      // Update existing data
      syncData.version = version;
      syncData.timestamp = timestamp;
      syncData.deviceId = deviceId;
      syncData.data = data;
      syncData.updatedAt = new Date();
    } else {
      // Create new sync data
      syncData = new UnifiedSyncData({
        userId: user.id,
        version,
        timestamp,
        deviceId,
        data,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    await syncData.save();

    console.log(`[UnifiedSync] Saved data for user ${user.id}, version ${version}`);

    res.json({
      success: true,
      version: version,
      message: 'Unified data synced successfully'
    });
  } catch (error) {
    console.error('Error uploading unified sync data:', error);
    res.status(500).json({
      message: 'Failed to sync unified data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route   GET /api/unified-sync
 * @desc    Download unified data from cloud
 * @access  Private
 */
router.get('/', protect, async (req: Request, res: Response): Promise<void> => {
  try {
    const user = req.user as IUser;
    const { version, deviceId } = req.query;
    const clientVersion = parseInt(version as string) || 0;

    // Find sync data for user
    const syncData = await UnifiedSyncData.findOne({ userId: user.id });

    if (!syncData) {
      // No data found - return empty data structure
      res.json({
        version: 1,
        timestamp: Date.now(),
        deviceId: deviceId || 'unknown',
        data: {
          categories: [],
          customBookmarkCategories: [],
          bookmarks: [],
          conversations: [],
          processedPosts: [],
          userSettings: {}
        }
      });
      return;
    }

    // Check if client has latest version
    if (syncData.version <= clientVersion) {
      res.status(304).send(); // No updates available
      return;
    }

    console.log(`[UnifiedSync] Sending data to user ${user.id}, version ${syncData.version}`);

    res.json({
      version: syncData.version,
      timestamp: syncData.timestamp,
      deviceId: syncData.deviceId,
      data: syncData.data
    });
  } catch (error) {
    console.error('Error downloading unified sync data:', error);
    res.status(500).json({
      message: 'Failed to download unified data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route   DELETE /api/unified-sync
 * @desc    Clear all unified sync data for user
 * @access  Private
 */
router.delete('/', protect, async (req: Request, res: Response): Promise<void> => {
  try {
    const user = req.user as IUser;

    await UnifiedSyncData.deleteOne({ userId: user.id });

    console.log(`[UnifiedSync] Cleared data for user ${user.id}`);

    res.json({
      success: true,
      message: 'Unified sync data cleared successfully'
    });
  } catch (error) {
    console.error('Error clearing unified sync data:', error);
    res.status(500).json({
      message: 'Failed to clear unified data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route   GET /api/unified-sync/status
 * @desc    Get sync status for user
 * @access  Private
 */
router.get('/status', protect, async (req: Request, res: Response): Promise<void> => {
  try {
    const user = req.user as IUser;

    const syncData = await UnifiedSyncData.findOne({ userId: user.id });

    if (!syncData) {
      res.json({
        hasSyncData: false,
        version: 0,
        lastSync: null,
        deviceId: null
      });
      return;
    }

    res.json({
      hasSyncData: true,
      version: syncData.version,
      lastSync: syncData.updatedAt,
      deviceId: syncData.deviceId,
      dataSize: JSON.stringify(syncData.data).length
    });
  } catch (error) {
    console.error('Error getting unified sync status:', error);
    res.status(500).json({
      message: 'Failed to get sync status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
