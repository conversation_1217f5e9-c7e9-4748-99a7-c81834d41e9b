import express, { Request, Response, NextFunction } from 'express';
import { MongoError } from 'mongodb'; // For type checking Mongo errors
import { protect } from '../middleware/authMiddleware'; // Your JWT protection middleware
import CloudPost, { ICloudPost, IMediaItem } from '../models/CloudPost'; // Adjust path as necessary
import User, { IUser } from '../models/User'; // Assuming this is your user interface
import AWS from 'aws-sdk'; // <-- ADDED: AWS SDK
import axios from 'axios'; // <-- ADDED: For downloading media
import { v4 as uuidv4 } from 'uuid'; // <-- ADDED: For unique filenames
import path from 'path'; // <-- ADDED: For handling file extensions
import dotenv from 'dotenv'; // <-- ADDED: To load env vars if not already loaded globally

dotenv.config(); // Ensure environment variables are loaded

// --- AWS S3 Configuration ---
const S3_BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME;
const S3_REGION = process.env.AWS_S3_REGION;

if (!S3_BUCKET_NAME || !S3_REGION || !process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
  console.warn('[AWS Config] Missing required AWS S3 environment variables (ID, SECRET, BUCKET, REGION). S3 uploads will be skipped.');
  // Optionally, you could throw an error here if S3 is absolutely required
  // throw new Error('Missing required AWS S3 environment variables.');
}

// Import the properly configured S3 client from s3Service instead of creating a new one
import { deleteImageFromS3 } from '../services/s3Service';
import { addImageProcessingJob } from '../services/queueService';
import { generateSearchSummary } from '../services/aiSearchService';
import {
  performVectorSearch,
  performHybridSearch,
  isVectorSearchAvailable,
  getVectorSearchStats,
  VectorSearchResult
} from '../services/vectorSearchService';
// --- End AWS S3 Configuration ---

const router = express.Router();

// --- Wrapper for async route handlers (good practice) ---
const asyncHandler = <T extends express.RequestHandler>(handler: T): T => {
  return ((req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(handler(req, res, next)).catch(next);
  }) as T;
};

// --- NEW: Helper function to download and upload media to S3 ---
async function uploadMediaToS3(
  originalMediaItem: { url: string; type: 'image' | 'video'; alt?: string },
  userId: string,
  platform?: string
): Promise<{ type: 'image' | 'video'; url: string; alt?: string; filePath: string | null } | null> {
  const { url: originalUrl, type, alt } = originalMediaItem;

  // Twitter-specific logging
  if (platform === 'X/Twitter') {
    console.log(`[uploadMediaToS3] 🐦 Twitter media upload starting - User: ${userId}, URL: ${originalUrl?.substring(0, 50)}...`);
  } else {
    console.log(`[uploadMediaToS3] Starting for user ${userId}, media URL: ${originalUrl}`);
  }

  if (!originalUrl) {
    console.warn('[uploadMediaToS3] Skipped: No media URL provided.');
    return null;
  }

  // Check if AWS config is available for upload attempt
  if (!S3_BUCKET_NAME || !process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
      console.warn(`[uploadMediaToS3] AWS S3 not configured. Storing original URL for: ${originalUrl}`);
      // Return an item pointing to the original URL if S3 isn't configured
      return { type, url: originalUrl, alt, filePath: null };
  }

  let downloadResponse;
  let buffer;
  let contentType = 'application/octet-stream';

  // Check if the URL is a base64 data URL (common for Instagram posts)
  if (originalUrl.startsWith('data:')) {
    try {
      console.log(`[uploadMediaToS3] Processing base64 data URL for ${platform} - Size: ${Math.round(originalUrl.length / 1024)}KB`);
      
      // Parse the data URL to extract content type and base64 data
      const matches = originalUrl.match(/^data:([^;]+);base64,(.+)$/);
      if (!matches) {
        throw new Error('Invalid data URL format');
      }
      
      contentType = matches[1] || 'image/jpeg';
      const base64Data = matches[2];
      buffer = Buffer.from(base64Data, 'base64');
      
      console.log(`[uploadMediaToS3] Base64 decoded successfully. Content-Type: ${contentType}, Size: ${buffer.length} bytes.`);
      
      // Create a mock download response for consistency with the rest of the code
      downloadResponse = {
        headers: {
          'content-type': contentType
        }
      };
    } catch (base64Error: any) {
      console.error(`[uploadMediaToS3] FAILED to process base64 data URL. Error:`, base64Error.message || base64Error);
      return null;
    }
  } else {
    // Handle regular HTTP/HTTPS URLs
    try {
      // 1. Download the media
      console.log(`[uploadMediaToS3] Attempting to download media from ${originalUrl}`);
      downloadResponse = await axios.get(originalUrl, {
        responseType: 'arraybuffer',
        timeout: 20000, // Increased timeout for potentially larger files
         // Add headers to mimic browser request if needed (e.g., User-Agent)
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      buffer = Buffer.from(downloadResponse.data);
      console.log(`[uploadMediaToS3] Download successful. Content-Type: ${downloadResponse.headers['content-type']}, Size: ${buffer.length} bytes.`);
    } catch (downloadError: any) {
      console.error(`[uploadMediaToS3] FAILED to download media from ${originalUrl}. Error:`, downloadError.response?.status, downloadError.message || downloadError);
      // Log response data if available for debugging
      if (downloadError.response?.data) {
          try {
              // Try to decode if it's JSON or plain text
              const responseBody = Buffer.from(downloadError.response.data).toString('utf-8');
               console.error('[uploadMediaToS3] Download error response body:', responseBody.substring(0, 500)); // Log first 500 chars
          } catch (decodeError) {
              console.error('[uploadMediaToS3] Could not decode download error response body.');
          }
      }
      return null; // Stop processing this item if download fails
    }
  }

  try {
    // 2. Use enhanced S3 service with storage limits and Twitter-specific logging
    const finalContentType = downloadResponse.headers['content-type'] || contentType;

    // Import the enhanced S3 service
    const { uploadBufferToS3 } = await import('../services/s3Service');

    // Platform-specific pre-upload logging
    if (platform === 'X/Twitter') {
      console.log(`[uploadMediaToS3] 🐦 About to upload Twitter image to S3 - Size: ${Math.round(buffer.length / 1024)}KB, ContentType: ${finalContentType}`);
    } else if (platform === 'Instagram') {
      console.log(`[uploadMediaToS3] 📸 About to upload Instagram image to S3 - Size: ${Math.round(buffer.length / 1024)}KB, ContentType: ${finalContentType}`);
    }

    // Upload using the enhanced service with storage limit checking
    const uploadResult = await uploadBufferToS3(
      buffer,
      finalContentType,
      userId,
      'post',
      originalUrl,
      platform
    );

    // Platform-specific post-upload logging
    if (platform === 'X/Twitter') {
      console.log(`[uploadMediaToS3] 🐦 S3 upload completed - Key: ${uploadResult.key}, URL: ${uploadResult.url}`);
    } else if (platform === 'Instagram') {
      console.log(`[uploadMediaToS3] 📸 S3 upload completed - Key: ${uploadResult.key}, URL: ${uploadResult.url}`);
    }

    // 4. Return the processed media item data
    const processedMediaItem: { type: 'image' | 'video'; url: string; alt?: string; filePath: string } = {
      type: type,
      url: uploadResult.url, // The URL provided by S3
      alt: alt,
      filePath: uploadResult.key, // Store the S3 key for potential deletion
    };

    // Platform-specific success logging
    if (platform === 'X/Twitter') {
      console.log(`[uploadMediaToS3] 🐦✅ Twitter media successfully processed: ${originalUrl?.substring(0, 50)}...`);
    } else if (platform === 'Instagram') {
      console.log(`[uploadMediaToS3] 📸✅ Instagram media successfully processed: ${originalUrl?.substring(0, 50)}...`);
    } else {
      console.log(`[uploadMediaToS3] Successfully processed media item: ${originalUrl?.substring(0, 50)}...`);
    }

    return processedMediaItem;

  } catch (uploadError: any) {
    // Platform-specific error logging
    if (platform === 'X/Twitter') {
      console.error(`[uploadMediaToS3] 🐦❌ Twitter media upload failed for ${originalUrl?.substring(0, 50)}...: ${uploadError.message}`);
    } else if (platform === 'Instagram') {
      console.error(`[uploadMediaToS3] 📸❌ Instagram media upload failed for ${originalUrl?.substring(0, 50)}...: ${uploadError.message}`);
    } else {
      console.error(`[uploadMediaToS3] FAILED during S3 upload or processing for original URL ${originalUrl?.substring(0, 50)}.... Error:`, uploadError.message || uploadError);
    }

    if (uploadError.code) {
      console.error(`[uploadMediaToS3] AWS Error Code: ${uploadError.code}`);
    }

    // Check if it's a storage limit error
    if (uploadError.message && uploadError.message.includes('Storage limit exceeded')) {
      console.error(`[uploadMediaToS3] Storage limit exceeded for user ${userId}`);
      // Don't fallback to original URL for storage limit errors
      return null;
    }

    // --- MODIFIED: Fallback to original URL if S3 upload fails ---
    console.warn(`[uploadMediaToS3] S3 upload failed. Storing original URL as fallback: ${originalUrl}`);
    return { type, url: originalUrl, alt, filePath: null };
    // --- END MODIFICATION ---
  }
}
// --- End Helper function ---

// @route   POST /api/posts
// @desc    Create a new cloud post with fast path (save immediately, process images in background)
// @access  Private (Requires JWT via 'protect' middleware)
router.post('/', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;
  const {
    originalPostId,
    platform,
    authorName,
    authorHandle,
    authorAvatar,
    authorImage, // This might be a URL of the author's avatar/image, not the post's image
    content,
    altText,     // Alt text for the main image/media of the post
    timestamp,   // Original post timestamp
    permalink,
    media,       // Array of media items, e.g., [{ type: 'image', url: 'img_url_from_storage', alt: 'user_alt_text', filePath: 'path_in_storage'}]
    interactions
  } = req.body;

  // Basic validation
  if (!originalPostId || !platform || !permalink) {
    res.status(400).json({ message: 'Missing required fields: originalPostId, platform, permalink' });
    return;
  }

  // Validate originalPostId to ensure it's a valid string
  if (typeof originalPostId !== 'string' || originalPostId.trim() === '') {
    res.status(400).json({ message: 'originalPostId must be a non-empty string' });
    return;
  }

  // --- MEDIA PROCESSING: Queue-based or synchronous fallback ---
  let processedMediaItems: IMediaItem[] = [];
  let shouldQueueImageProcessing = false;
  let useQueueSystem = false;

  // Check if queue system is available (not mocked)
  // Force synchronous processing since Redis is disabled
  useQueueSystem = false; // Disabled until Redis is properly configured

  if (Array.isArray(media) && media.length > 0) {
    // Check if S3 is configured
    const s3Configured = S3_BUCKET_NAME && process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY;

    if (s3Configured && useQueueSystem) {
      // FAST PATH: Queue-based processing
      processedMediaItems = media.map(item => ({
        type: (item.type as 'image' | 'video') || 'image',
        url: item.url || '',
        alt: item.alt,
        width: item.width,
        height: item.height
        // No filePath initially - will be added by background worker
      })) as IMediaItem[];

      shouldQueueImageProcessing = true;
      console.log(`[POST /api/posts] 🚀 Fast path: Saving ${media.length} media items with original URLs, queuing for background processing`);
    } else if (s3Configured && !useQueueSystem) {
      // SYNCHRONOUS FALLBACK: Process images immediately when queue is disabled
      console.log(`[POST /api/posts] 🔄 Queue disabled, processing ${media.length} media items synchronously`);

      const syncProcessedItems = [];
      for (let i = 0; i < media.length; i++) {
        const mediaItem = media[i];
        try {
          console.log(`[POST /api/posts] Processing media ${i + 1}/${media.length}: ${mediaItem.url?.substring(0, 50)}...`);
          
          // Skip S3 upload for videos - play directly from source
          if (mediaItem.type === 'video') {
            console.log(`[POST /api/posts] 🎥 Video detected, preserving original URL (no S3 upload): ${mediaItem.url?.substring(0, 50)}...`);
            syncProcessedItems.push({
              type: 'video',
              url: mediaItem.url || '',
              alt: mediaItem.alt,
              width: mediaItem.width,
              height: mediaItem.height,
              filePath: null // No S3 storage for videos
            } as IMediaItem);
            continue;
          }
          
          // Only upload images to S3
          const processedItem = await uploadMediaToS3(mediaItem, user.id, platform);
          if (processedItem) {
            syncProcessedItems.push({
              type: processedItem.type,
              url: processedItem.url,
              alt: processedItem.alt,
              width: mediaItem.width,
              height: mediaItem.height,
              filePath: processedItem.filePath
            } as IMediaItem);
          }
        } catch (error) {
          console.error(`[POST /api/posts] Failed to process media ${i + 1}/${media.length}:`, error);
          // Keep original URL as fallback
          syncProcessedItems.push({
            type: (mediaItem.type as 'image' | 'video') || 'image',
            url: mediaItem.url || '',
            alt: mediaItem.alt,
            width: mediaItem.width,
            height: mediaItem.height
          } as IMediaItem);
        }
      }

      processedMediaItems = syncProcessedItems;
      console.log(`[POST /api/posts] ✅ Synchronously processed ${processedMediaItems.length} media items`);
    } else {
      // No S3 configured - preserve original URLs
      processedMediaItems = media.map(item => ({
        type: (item.type as 'image' | 'video') || 'image',
        url: item.url || '',
        alt: item.alt,
        width: item.width,
        height: item.height
      })) as IMediaItem[];

      console.warn('[POST /api/posts] S3 not configured. Preserving original media URLs only.');
    }
  }
  // --- End Media Processing ---

  // Ensure all fields from the request body are considered,
  // then override or set server-controlled fields.
  const newPostData: Partial<ICloudPost> = {
    ...req.body, // Spread all incoming fields first
    userId: user.id, // Override userId to ensure it's from the authenticated user
    media: processedMediaItems, // Override media with original URLs initially
    savedAt: new Date(), // Set by server
    // Explicitly set authorAvatar to ensure it's included
    authorAvatar: req.body.authorAvatar || '',
    // Set image processing status
    imageProcessingStatus: shouldQueueImageProcessing ? 'pending' : 'completed',
    imageProcessingJobId: null,
    imageProcessingError: null,
    imageProcessedAt: shouldQueueImageProcessing ? null : new Date(),
  };

  // Log the author avatar to verify it's being saved
  console.log(`[POST /api/posts] Author avatar: ${newPostData.authorAvatar ? 'Present' : 'Missing'}`);
  if (newPostData.authorAvatar) {
    console.log(`[POST /api/posts] Author avatar URL: ${newPostData.authorAvatar.substring(0, 50)}...`);
  }

  // Remove potentially client-sent _id to prevent conflicts if any
  delete newPostData._id;
  // Remove userId if it was in req.body to avoid confusion, we use user.id from token
  if ('userId' in req.body) {
    delete (newPostData as any).userId; // need to cast to any to delete if it was part of req.body's type
  }
  // Add userId again from authenticated user to be sure
  newPostData.userId = user.id;

  try {
    const createdPost = await CloudPost.create(newPostData);

    // Queue image processing job if needed
    if (shouldQueueImageProcessing && media && media.length > 0) {
      try {
        const job = await addImageProcessingJob(
          createdPost._id.toString(),
          user.id,
          platform,
          media,
          req.body
        );

        // Update post with job ID
        await CloudPost.findByIdAndUpdate(createdPost._id, {
          imageProcessingJobId: job.id?.toString() || null
        });

        console.log(`[POST /api/posts] ✅ Post saved and image processing job ${job.id} queued for post ${createdPost._id}`);
      } catch (queueError) {
        console.error(`[POST /api/posts] ⚠️ Post saved but failed to queue image processing:`, queueError);
        // Don't fail the request - post is saved, just log the queue error
      }
    }

    res.status(201).json(createdPost);
  } catch (error) {
    if ((error as any)?.code === 11000) {
      // Find the existing post to return its ID
      try {
        const existingPost = await CloudPost.findOne({
          userId: newPostData.userId,
          originalPostId: newPostData.originalPostId,
          platform: newPostData.platform
        });

        res.status(409).json({
          message: 'This post has already been saved.',
          details: (error as any)?.keyValue,
          existingPost: existingPost
        });
      } catch (findError) {
        console.error('Error finding existing post:', findError);
        res.status(409).json({
          message: 'This post has already been saved.',
          details: (error as any)?.keyValue
        });
      }
      return;
    }
    console.error('Error creating post:', error);
    next(error);
  }
}));

// @route   GET /api/posts
// @desc    Get all posts for the authenticated user
// @access  Private
router.get('/', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;
  try {
    const posts = await CloudPost.find({ userId: user.id }).sort({ savedAt: -1 }); // Sort by most recently saved

    // Log the first post's author avatar to verify it's being returned
    if (posts.length > 0) {
      console.log(`[GET /api/posts] First post author avatar: ${posts[0].authorAvatar ? 'Present' : 'Missing'}`);
      if (posts[0].authorAvatar) {
        console.log(`[GET /api/posts] First post author avatar URL: ${posts[0].authorAvatar.substring(0, 50)}...`);
      }
    }

    res.json(posts);
  } catch (error) {
    console.error('Error fetching posts:', error);
    next(error);
  }
}));

// @route   GET /api/posts/:postId
// @desc    Get a single post by ID for the authenticated user
// @access  Private
router.get('/:postId', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;
  const { postId } = req.params;

  try {
    const post = await CloudPost.findOne({ _id: postId, userId: user.id });
    if (!post) {
      res.status(404).json({ message: 'Post not found or access denied' });
      return;
    }

    // Log the post's author avatar to verify it's being returned
    console.log(`[GET /api/posts/${postId}] Author avatar: ${post.authorAvatar ? 'Present' : 'Missing'}`);
    if (post.authorAvatar) {
      console.log(`[GET /api/posts/${postId}] Author avatar URL: ${post.authorAvatar.substring(0, 50)}...`);
    }

    res.json(post);
  } catch (error) {
    console.error('Error fetching post by ID:', error);
    if ((error as Error).name === 'CastError') { // Check for Mongoose CastError
      res.status(400).json({ message: 'Invalid post ID format' });
      return;
    }
    next(error);
  }
}));

// @route   PUT /api/posts/:postId
// @desc    Update a post by ID for the authenticated user
// @access  Private
router.put('/:postId', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;
  const { postId } = req.params;
  const {
    authorName,
    authorHandle,
    authorAvatar,
    authorImage,
    content,
    altText,
    timestamp,   // Original post timestamp - might be updatable if initially incorrect
    permalink,   // Usually not changed, but consider if it can be
    media,       // Array of media items - could be updated if image is replaced
    interactions,
    // User Fields
    notes,
    // AI Generated Fields
    snapNote,
    categories,
    tags,
    inSight,
    fastTake,
    contentIdeas,
    embeddingVector,
    analyzedAt
  } = req.body;

  try {
    const post = await CloudPost.findOne({ _id: postId, userId: user.id });

    if (!post) {
      res.status(404).json({ message: 'Post not found or access denied' });
      return;
    }

    // Update allowed fields
    // Fields like originalPostId, platform, userId should generally not be updated.
    if (authorName !== undefined) post.authorName = authorName;
    if (authorHandle !== undefined) post.authorHandle = authorHandle;
    if (authorAvatar !== undefined) post.authorAvatar = authorAvatar;
    if (authorImage !== undefined) post.authorImage = authorImage;
    if (content !== undefined) post.content = content;
    if (altText !== undefined) post.altText = altText;
    if (timestamp !== undefined) post.timestamp = timestamp;
    if (permalink !== undefined) post.permalink = permalink; // Be cautious with permalink updates due to uniqueness
    if (media !== undefined) post.media = media as IMediaItem[];
    if (interactions !== undefined) post.interactions = interactions;

    // Update User Fields
    if (notes !== undefined) post.notes = notes;

    // Update AI Generated Fields
    if (snapNote !== undefined) post.snapNote = snapNote;
    if (categories !== undefined) post.categories = categories;
    if (tags !== undefined) post.tags = tags;
    if (inSight !== undefined) post.inSight = inSight;
    if (fastTake !== undefined) post.fastTake = fastTake;
    if (contentIdeas !== undefined) post.contentIdeas = contentIdeas;
    if (embeddingVector !== undefined) post.embeddingVector = embeddingVector;
    if (analyzedAt !== undefined) post.analyzedAt = analyzedAt;

    // The 'updatedAt' field will be automatically handled by Mongoose due to `timestamps: true` in the schema
    const updatedPost = await post.save();
    res.json(updatedPost);

  } catch (error) {
    console.error('Error updating post by ID:', error);
    if ((error as Error).name === 'CastError') {
      res.status(400).json({ message: 'Invalid post ID format' });
      return;
    }
    if ((error as any)?.code === 11000) {
      res.status(409).json({
        message: 'Update failed due to a conflict (e.g., permalink already exists for this user).',
        details: (error as any)?.keyValue
      });
      return;
    }
    next(error);
  }
}));

// @route   DELETE /api/posts/:postId
// @desc    Delete a post by ID for the authenticated user (and its associated media from Firebase Storage)
// @access  Private
router.delete('/:postId', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;
  const { postId } = req.params;

  try {
    const post = await CloudPost.findOne({ _id: postId, userId: user.id });

    if (!post) {
      res.status(404).json({ message: 'Post not found or access denied' });
      return;
    }

    // --- Delete associated media from S3 Storage ---
    if (post.media && post.media.length > 0 && S3_BUCKET_NAME) {
      console.log(`[DELETE /api/posts] Found ${post.media.length} media items associated with post ${postId}. Attempting S3 deletion.`);
      const deletePromises = post.media.map(async (mediaItem) => {
        if (mediaItem.filePath) {
          const s3Key = mediaItem.filePath; // filePath should be the S3 Key
          console.log(`Attempting to delete from S3: Key=${s3Key}`);
          try {
            const success = await deleteImageFromS3(s3Key, user.id);
            if (success) {
              console.log(`Successfully deleted from S3: ${s3Key}`);
            } else {
              console.warn(`Failed to delete from S3: ${s3Key}`);
            }
          } catch (storageError: any) {
            console.error(`Error deleting file from S3: ${s3Key}`, storageError.message || storageError);
          }
        } else {
          console.warn(`Media item missing filePath (S3 key), cannot delete from storage: ${mediaItem.url}`);
          // For posts with original URLs that were never uploaded to S3 but may have counted storage,
          // we need to clean up any phantom storage usage
          if (mediaItem.url && (mediaItem.url.includes('pbs.twimg.com') || mediaItem.url.includes('reddit'))) {
            console.log(`[DELETE] Cleaning up phantom storage for original URL: ${mediaItem.url.substring(0, 50)}...`);
            // Estimate size and decrement storage (rough estimate: 500KB per image)
            try {
              const { updateStorageUsage } = await import('../services/s3Service');
              await updateStorageUsage(user.id, -500000); // Subtract 500KB
              console.log(`[DELETE] Cleaned up phantom storage usage for user ${user.id}`);
            } catch (cleanupError) {
              console.error(`[DELETE] Error cleaning up phantom storage:`, cleanupError);
            }
          }
        }
      });
      // Wait for all delete operations to attempt completion
      await Promise.allSettled(deletePromises);
      console.log(`[DELETE /api/posts] Finished attempting S3 deletions for post ${postId}.`);
    } else if (post.media && post.media.length > 0) {
        console.warn(`[DELETE /api/posts] Post ${postId} has media items, but S3 is not configured. Skipping S3 deletion.`);
    }
    // --- End Media Deletion ---

    // --- Delete the post from MongoDB ---
    await CloudPost.deleteOne({ _id: postId, userId: user.id });

    res.json({ message: 'Post and associated media (if any) deleted successfully' });

  } catch (error) {
    console.error('Error deleting post by ID:', error);
    if ((error as Error).name === 'CastError') {
      res.status(400).json({ message: 'Invalid post ID format' });
      return;
    }
    next(error);
  }
}));

// @route   POST /api/posts/search
// @desc    Enhanced semantic search using MongoDB Atlas Vector Search
// @access  Private
router.post('/search', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;
  const { query, limit = 10, minScore = 0.7 } = req.body;

  if (!query || typeof query !== 'string' || query.trim().length === 0) {
    res.status(400).json({ message: 'Search query is required' });
    return;
  }

  try {
    // Generate embedding for semantic search
    const queryEmbedding = await generateQueryEmbedding(query.trim());

    // Check if vector search is available
    const vectorSearchAvailable = await isVectorSearchAvailable();

    let searchResults: VectorSearchResult[] = [];
    let searchType = 'vector';

    if (vectorSearchAvailable) {
      try {
        // Use MongoDB Atlas Vector Search
        const semanticResults = await performVectorSearch(user.id, queryEmbedding.embedding, {
          limit,
          minScore,
          includeMetadata: true
        });
        searchResults = semanticResults;
      } catch (vectorError) {
        console.error('[Search] Vector search failed, using cosine fallback');
        searchType = 'cosine_fallback';
        const cosineResults = await performCosineSimilaritySearch(user.id, queryEmbedding.embedding, query.trim(), limit);
        searchResults = cosineResults;
      }
    } else {
      searchType = 'cosine_fallback';
      const cosineResults = await performCosineSimilaritySearch(user.id, queryEmbedding.embedding, query.trim(), limit);
      searchResults = cosineResults;
    }

    // If no semantic results, try text search as fallback
    if (searchResults.length === 0) {
      const textSearchResults = await CloudPost.find({
        userId: user.id,
        $or: [
          { content: { $regex: query.trim(), $options: 'i' } },
          { authorName: { $regex: query.trim(), $options: 'i' } },
          { categories: { $in: [new RegExp(query.trim(), 'i')] } },
          { tags: { $in: [new RegExp(query.trim(), 'i')] } }
        ]
      })
      .select('content authorName platform savedAt categories tags permalink media')
      .sort({ savedAt: -1 })
      .limit(limit);

      if (textSearchResults.length > 0) {
        const textResults: VectorSearchResult[] = textSearchResults.map(post => ({
          post: {
            id: post._id.toString(),
            content: post.content || '',
            authorName: post.authorName || 'Unknown Author',
            platform: post.platform,
            savedAt: post.savedAt.toISOString(),
            categories: post.categories || [],
            tags: post.tags || [],
            permalink: post.permalink,
            media: post.media || []
          },
          score: 0.5 // Default score for text matches
        }));

        const summary = await generateSearchSummary(query.trim(), textResults.map(r => r.post));

        res.json({
          results: textResults,
          summary,
          totalResults: textResults.length,
          query: query.trim(),
          searchType: 'text_fallback'
        });
        return;
      }

      // No results at all
      const totalPosts = await CloudPost.countDocuments({ userId: user.id });
      const postsWithEmbeddings = await CloudPost.countDocuments({
        userId: user.id,
        embeddingVector: { $exists: true, $ne: null }
      });

      res.json({
        results: [],
        summary: {
          overview: postsWithEmbeddings === 0
            ? `You have ${totalPosts} saved posts, but they need to be processed for semantic search. This happens automatically when you save new posts.`
            : `No posts match your search for "${query.trim()}". Try different keywords or browse your saved posts.`,
          keyFindings: [],
          relatedTopics: [],
          suggestedActions: postsWithEmbeddings === 0
            ? ['Save more posts to enable AI-powered search', 'Try browsing by category or platform']
            : ['Try different search terms', 'Browse posts by category', 'Check your saved posts'],
          totalPosts: 0
        },
        totalResults: 0,
        query: query.trim(),
        searchType: searchType,
        stats: {
          totalPosts,
          postsWithEmbeddings,
          vectorSearchAvailable
        }
      });
      return;
    }

    // Generate AI summary of search results
    const summary = await generateSearchSummary(query.trim(), searchResults.map(r => r.post));
    
    // Calculate average score from search results
    const avgScore = searchResults.length > 0 
      ? searchResults.reduce((sum, result) => sum + result.score, 0) / searchResults.length 
      : 0;

    res.json({
      results: searchResults,
      summary,
      totalResults: searchResults.length,
      query: query.trim(),
      searchType,
      stats: {
        averageScore: avgScore,
        vectorSearchAvailable
      }
    });

  } catch (error) {
    console.error('Error in semantic search:', error);
    res.status(500).json({ message: 'Search failed', error: (error as Error).message });
  }
}));

// Helper function to generate embedding for search query
async function generateQueryEmbedding(text: string): Promise<{ embedding: number[] }> {
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;

  if (!OPENAI_API_KEY) {
    throw new Error('OpenAI API key not configured');
  }

  const response = await fetch('https://api.openai.com/v1/embeddings', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'text-embedding-3-small',
      input: text.replace(/\n/g, ' '),
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (!data.data || !data.data[0] || !data.data[0].embedding) {
    throw new Error('Invalid embedding response from OpenAI');
  }

  return { embedding: data.data[0].embedding };
}

// Helper function to perform cosine similarity search (fallback when vector search is not available)
async function performCosineSimilaritySearch(
  userId: string,
  queryEmbedding: number[],
  query: string,
  limit: number
): Promise<VectorSearchResult[]> {
  // Find posts with embedding vectors for this user
  const posts = await CloudPost.find({
    userId: userId,
    embeddingVector: { $exists: true, $ne: null }
  }).select('content authorName platform savedAt categories tags embeddingVector permalink media');

  if (posts.length === 0) {
    return [];
  }

  // Calculate cosine similarity for each post
  const searchResults = posts.map(post => {
    const similarity = calculateCosineSimilarity(queryEmbedding, post.embeddingVector);
    return {
      post: {
        id: post._id.toString(),
        content: post.content || '',
        authorName: post.authorName || 'Unknown Author',
        platform: post.platform,
        savedAt: post.savedAt.toISOString(),
        categories: post.categories || [],
        tags: post.tags || [],
        permalink: post.permalink,
        media: post.media || []
      },
      score: similarity
    };
  });

  // Filter and sort results
  const filteredResults = searchResults
    .filter(result => result.score > 0.3) // Lower threshold for fallback
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);

  return filteredResults;
}

// Helper function to calculate cosine similarity between two vectors
function calculateCosineSimilarity(vecA: number[], vecB: number[]): number {
  if (!vecA || !vecB || vecA.length !== vecB.length) {
    return 0;
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

// @route   GET /api/posts/search/suggestions
// @desc    Get search suggestions based on user's content
// @access  Private
router.get('/search/suggestions', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;

  try {
    // Get recent posts for the user
    const recentPosts = await CloudPost.find({
      userId: user.id
    })
    .sort({ savedAt: -1 })
    .limit(20)
    .select('content textContent categories tags platform');

    // Generate suggestions using AI service
    const { generateSearchSuggestions } = await import('../services/aiSearchService');
    const suggestions = await generateSearchSuggestions(recentPosts, 8);

    res.json({
      suggestions,
      totalPosts: recentPosts.length
    });

  } catch (error) {
    console.error('Error generating search suggestions:', error);

    // Fallback suggestions
    const fallbackSuggestions = [
      'AI tools and prompts',
      'productivity tips',
      'design inspiration',
      'technology trends',
      'business insights',
      'creative ideas',
      'learning resources',
      'industry news'
    ];

    res.json({
      suggestions: fallbackSuggestions,
      totalPosts: 0,
      fallback: true
    });
  }
}));

// @route   GET /api/posts/search/debug
// @desc    Debug search functionality - show actual author names
// @access  Private
router.get('/search/debug', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;

  try {
    // Get sample posts to see actual author names
    const samplePosts = await CloudPost.find({ userId: user.id })
      .select('authorName authorHandle categories tags content')
      .limit(20)
      .sort({ savedAt: -1 });

    const authorNames = [...new Set(samplePosts.map(p => p.authorName).filter(Boolean))];
    const authorHandles = [...new Set(samplePosts.map(p => p.authorHandle).filter(Boolean))];
    const categories = [...new Set(samplePosts.flatMap(p => p.categories || []))];
    const tags = [...new Set(samplePosts.flatMap(p => p.tags || []))];

    // Test exact match queries
    const testQueries = ['csaba', 'Csaba', 'Csaba Kissi', 'kissi'];
    const testResults = {};

    for (const query of testQueries) {
      const matches = await CloudPost.find({
        userId: user.id,
        $or: [
          { authorName: { $regex: `^${query.trim()}$`, $options: 'i' } },
          { authorName: { $regex: `^${query.trim()}\\s`, $options: 'i' } },
          { authorName: { $regex: `\\s${query.trim()}$`, $options: 'i' } },
          { authorHandle: { $regex: `^${query.trim()}$`, $options: 'i' } },
          { categories: { $in: [new RegExp(`^${query.trim()}$`, 'i')] } },
          { tags: { $in: [new RegExp(`^${query.trim()}$`, 'i')] } }
        ]
      }).select('authorName authorHandle categories tags').limit(5);

      testResults[query] = matches.map(m => ({
        authorName: m.authorName,
        authorHandle: m.authorHandle,
        categories: m.categories,
        tags: m.tags
      }));
    }

    res.json({
      status: 'debug',
      userId: user.id,
      totalPosts: samplePosts.length,
      uniqueAuthorNames: authorNames,
      uniqueAuthorHandles: authorHandles,
      uniqueCategories: categories.slice(0, 10),
      uniqueTags: tags.slice(0, 10),
      testResults,
      samplePosts: samplePosts.slice(0, 5).map(p => ({
        id: p._id.toString(),
        authorName: p.authorName,
        authorHandle: p.authorHandle,
        categories: p.categories,
        tags: p.tags,
        contentPreview: p.content?.substring(0, 100) + '...'
      }))
    });

  } catch (error) {
    console.error('Error in search debug:', error);
    res.status(500).json({ message: 'Debug failed', error: (error as Error).message });
  }
}));

// @route   GET /api/posts/search/test
// @desc    Test search functionality, vector search availability, and API keys
// @access  Private
router.get('/search/test', protect, asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user as IUser;

  try {
    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;

    // Get vector search stats
    const stats = await getVectorSearchStats(user.id);

    // Test vector search availability
    const vectorSearchAvailable = await isVectorSearchAvailable();

    res.json({
      status: 'ok',
      userId: user.id,
      ...stats,
      hasOpenAIKey: !!OPENAI_API_KEY,
      openAIKeyLength: OPENAI_API_KEY ? OPENAI_API_KEY.length : 0,
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      vectorSearch: {
        available: vectorSearchAvailable,
        indexName: 'vector_index',
        embeddingModel: 'text-embedding-3-small',
        dimensions: 1536
      }
    });

  } catch (error) {
    console.error('Error in search test:', error);
    res.status(500).json({ message: 'Test failed', error: (error as Error).message });
  }
}));

export default router;