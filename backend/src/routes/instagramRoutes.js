const express = require('express');
const { protect } = require('../middleware/authMiddleware');
const {
  createPost,
  getPosts,
  getPostById,
  deletePost
} = require('../controllers/instagramController');

const router = express.Router();

// Apply auth middleware to all routes
router.use(protect);

// POST /api/instagram/posts - Create a new Instagram post
router.post('/posts', createPost);

// GET /api/instagram/posts - Get all Instagram posts for the authenticated user
router.get('/posts', getPosts);

// GET /api/instagram/posts/:id - Get a single Instagram post by ID
router.get('/posts/:id', getPostById);

// DELETE /api/instagram/posts/:id - Delete an Instagram post
router.delete('/posts/:id', deletePost);

module.exports = router;
