import express from 'express';
import { Request, Response } from 'express';
import { protect } from '../middleware/authMiddleware';
import AbuseDetectionService from '../services/abuseDetectionService';
import User from '../models/User';

const router = express.Router();

// Middleware to ensure admin access
const requireAdmin = (req: Request, res: Response, next: any): void => {
  const user = (req as any).user;
  if (!user) {
    res.status(401).json({ error: 'Authentication required' });
    return;
  }

  const adminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || [];
  if (!adminEmails.includes(user.email)) {
    res.status(403).json({ error: 'Admin access required' });
    return;
  }

  next();
};

/**
 * GET /admin/users/flagged
 * Get all flagged users
 */
router.get('/admin/users/flagged', protect, requireAdmin, async (req: Request, res: Response): Promise<void> => {
  try {
    const flaggedUsers = await AbuseDetectionService.getFlaggedUsers();
    
    // Don't send sensitive data
    const sanitizedUsers = flaggedUsers.map(user => ({
      _id: user._id,
      email: user.email,
      name: user.name,
      displayName: user.displayName,
      plan: user.plan,
      subscriptionStatus: user.subscriptionStatus,
      isFlagged: user.isFlagged,
      lastFlaggedReason: user.lastFlaggedReason,
      flaggedAt: user.flaggedAt,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      storageUsed: user.storageUsed,
      storageLimit: user.storageLimit,
    }));

    res.json({
      success: true,
      users: sanitizedUsers,
      count: sanitizedUsers.length,
    });
  } catch (error: any) {
    console.error('Error fetching flagged users:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch flagged users',
      message: error.message,
    });
  }
});

/**
 * POST /admin/users/:id/flag
 * Manually flag a user
 */
router.post('/admin/users/:id/flag', protect, requireAdmin, async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const adminUser = (req as any).user;

    if (!reason || typeof reason !== 'string' || reason.trim().length === 0) {
      res.status(400).json({
        success: false,
        error: 'Reason is required and must be a non-empty string',
      });
      return;
    }

    await AbuseDetectionService.manualFlag(id, reason.trim(), adminUser.email);

    res.json({
      success: true,
      message: 'User flagged successfully',
    });
      } catch (error: any) {
      console.error('Error flagging user:', error);
      
      if (error.message.includes('Cast to ObjectId failed')) {
        res.status(400).json({
          success: false,
          error: 'Invalid user ID',
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Failed to flag user',
        message: error.message,
      });
    }
});

/**
 * POST /admin/users/:id/unflag
 * Unflag a user
 */
router.post('/admin/users/:id/unflag', protect, requireAdmin, async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const adminUser = (req as any).user;

    await AbuseDetectionService.unflagUser(id, adminUser.email);

    res.json({
      success: true,
      message: 'User unflagged successfully',
    });
      } catch (error: any) {
      console.error('Error unflagging user:', error);
      
      if (error.message.includes('Cast to ObjectId failed')) {
        res.status(400).json({
          success: false,
          error: 'Invalid user ID',
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Failed to unflag user',
        message: error.message,
      });
    }
});

/**
 * GET /admin/abuse/stats
 * Get abuse detection statistics
 */
router.get('/admin/abuse/stats', protect, requireAdmin, async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await AbuseDetectionService.getAbuseStats();
    
    res.json({
      success: true,
      stats,
    });
  } catch (error: any) {
    console.error('Error fetching abuse stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch abuse statistics',
      message: error.message,
    });
  }
});

/**
 * POST /admin/abuse/check
 * Manually trigger abuse detection check
 */
router.post('/admin/abuse/check', protect, requireAdmin, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminUser = (req as any).user;
    console.log(`🔍 Manual abuse check triggered by admin: ${adminUser.email}`);
    
    // Run the check in the background to avoid timeout
    AbuseDetectionService.checkAllUsers().catch(error => {
      console.error('Background abuse check failed:', error);
    });

    res.json({
      success: true,
      message: 'Abuse detection check initiated. Results will be processed in the background.',
    });
  } catch (error: any) {
    console.error('Error initiating abuse check:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate abuse check',
      message: error.message,
    });
  }
});

/**
 * GET /admin/users/:id/abuse-history
 * Get abuse history for a specific user
 */
router.get('/admin/users/:id/abuse-history', protect, requireAdmin, async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const user = await User.findById(id);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found',
      });
      return;
    }

    // Check current violations for this user
    const abuseCheck = await AbuseDetectionService.checkUser(user);
    
    const history = {
      userId: user._id,
      email: user.email,
      currentlyFlagged: user.isFlagged,
      lastFlaggedReason: user.lastFlaggedReason,
      flaggedAt: user.flaggedAt,
      currentViolations: abuseCheck.violations,
      shouldFlag: abuseCheck.shouldFlag,
      userStats: {
        storageUsed: user.storageUsed,
        storageLimit: user.storageLimit,
        plan: user.plan,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin,
      },
    };

    res.json({
      success: true,
      history,
    });
      } catch (error: any) {
      console.error('Error fetching user abuse history:', error);
      
      if (error.message.includes('Cast to ObjectId failed')) {
        res.status(400).json({
          success: false,
          error: 'Invalid user ID',
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Failed to fetch user abuse history',
        message: error.message,
      });
    }
});

export default router; 