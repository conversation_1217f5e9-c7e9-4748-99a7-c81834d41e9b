import express, { Request, Response, Router, NextFunction, RequestHandler } from 'express';
import User, { IUser } from '../../models/User';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();
const router: Router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error('FATAL ERROR: JWT_SECRET is not defined.');
  process.exit(1);
}

// --- Wrapper for async route handlers ---
const asyncHandler = <T extends RequestHandler>(handler: T): T => {
  return ((req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(handler(req, res, next)).catch(next);
  }) as T;
};

// Helper function to generate JWT
const generateToken = (user: IUser) => {
  return jwt.sign(
    { id: user.id, displayName: user.displayName, email: user.email, googleId: user.googleId },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
};

// POST /auth/google/token - Handle Google OAuth token from extension
router.post('/auth/google/token', asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { token, userInfo } = req.body;

    if (!token || !userInfo || !userInfo.email) {
      res.status(400).json({ message: 'Invalid request. Token and user info required.' });
      return;
    }

    // Check if user exists
    let user = await User.findOne({ email: userInfo.email.toLowerCase() });

    if (!user) {
      // Create new user
      user = new User({
        email: userInfo.email.toLowerCase(),
        displayName: userInfo.name || 'User',
        name: userInfo.name || 'User',
        googleId: userInfo.id,
        profileSettings: {
          picture: userInfo.picture || '',
        },
        plan: 'free', // Default to free plan
      });

      await user.save();
    } else {
      // Update existing user
      user.googleId = userInfo.id;
      if (userInfo.picture && (!user.profileSettings || !user.profileSettings.picture)) {
        user.profileSettings = {
          ...user.profileSettings,
          picture: userInfo.picture,
        };
      }

      await user.save();
    }

    // Generate JWT token
    const jwtToken = generateToken(user);

    // Return user and token
    res.json({
      token: jwtToken,
      user: {
        id: user._id,
        name: user.displayName || user.name,
        email: user.email,
        plan: user.plan || 'free',
        profileSettings: user.profileSettings || {},
      }
    });
  } catch (error) {
    console.error('Error in Google token authentication:', error);
    res.status(500).json({ message: 'Server error during authentication' });
  }
}));

export default router;
