import express, { Request, Response, NextFunction, Router, RequestHandler as ExpressRe<PERSON>Handler } from 'express';
import passport from 'passport';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import User, { IUser } from '../models/User'; // Adjust path as needed
import PasswordReset from '../models/PasswordReset';
import dotenv from 'dotenv';
import { protect, isAdmin } from '../middleware/authMiddleware'; // Import the protect middleware
import { verifySubscription } from '../utils/subscriptionUtils';
import { sendPasswordResetEmail } from '../utils/email/brevo';
import Stripe from 'stripe';
import Coupon from '../models/Coupon';
import { asyncHandler } from '../utils/asyncHandler'; // Import asyncHandler

dotenv.config();

const router: Router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error('FATAL ERROR: JWT_SECRET is not defined.');
  process.exit(1);
}

// Initialize Stripe without a fixed apiVersion to use account default or latest compatible.
// You can pin a version later if needed e.g. { apiVersion: '2023-10-16' }
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

// --- Helper function to generate JWT ---
const generateToken = (user: IUser) => {
  // Sign the JWT
  return jwt.sign(
    { id: user.id, displayName: user.displayName, email: user.email, googleId: user.googleId }, // Payload
    JWT_SECRET!,
    { expiresIn: '7d' } // Token expires in 7 days
  );
};

// --- Wrapper for async route handlers ---
// Note: Using imported asyncHandler instead of local definition

// --- Define handler function separately ---
const registerUser: ExpressRequestHandler = async (req: Request, res: Response, next: NextFunction) => {
  const { email, password, displayName } = req.body;

  // No try...catch needed here as asyncHandler will catch promise rejections
  // Check if user already exists
  const existingUser = await User.findOne({ email: email.toLowerCase() });
  if (existingUser) {
    // Use return to stop execution, send response
    res.status(400).json({ message: 'Email already exists' });
    return;
  }

  // Create new user (password will be hashed by pre-save hook)
  const newUser = new User({
    email: email.toLowerCase(),
    password,
    name: displayName || email.split('@')[0], // Use displayName or derive from email
    displayName: displayName,
    plan: 'free', // Default plan
    // lastLogin will be set by timestamps or on first login via /user/me
  });
  await newUser.save();

  // Generate token and send response
  const token = generateToken(newUser);
  res.status(201).json({ token });
};

// --- Local Registration Route ---
router.post('/register', asyncHandler(registerUser)); // Wrap the handler

// --- Local Login Route ---
router.post('/login', (req: Request, res: Response, next: NextFunction) => {
  passport.authenticate('local', { session: false }, (err: any, user: IUser | false, info: any) => {
    if (err) {
      return next(err);
    }
    if (!user) {
      // If user is false, send the message from the strategy (e.g., 'Invalid email or password')
      res.status(401).json({ message: info?.message || 'Login failed' });
      return;
    }

    // User authenticated successfully, generate token
    const token = generateToken(user);
    res.json({ token });

  })(req, res, next);
});

// --- Google OAuth Initiation Route (for extension/popup) ---
router.get('/google', (req: Request, res: Response, next: NextFunction) => {
  // Get prompt parameter from request query if it exists
  const prompt = req.query.prompt as string;

  // Configure auth options based on prompt parameter
  const authOptions: any = {
    scope: ['profile', 'email'],
    session: false
  };

  // Add prompt parameter if provided (force account selection)
  if (prompt) {
    authOptions.prompt = prompt;
  }

  passport.authenticate('google', authOptions)(req, res, next);
});

// --- Google OAuth Initiation Route for Web ---
router.get('/google/web', (req: Request, res: Response, next: NextFunction) => {
  // Configure auth options for web requests
  const authOptions: any = {
    scope: ['profile', 'email'],
    session: false
  };

  passport.authenticate('google-web', authOptions)(req, res, next);
});

// Helper function to atomically upsert Google OAuth user
const upsertGoogleUser = async (googleProfile: any): Promise<IUser> => {
  const adminEmails = process.env.ADMIN_EMAILS
    ?.split(",")
    .map(e => e.trim().toLowerCase()) ?? [];

  const userEmail = googleProfile.email.toLowerCase();
  
  try {
    // Atomic upsert operation - race-safe and idempotent
    const googleUser = await User.findOneAndUpdate(
      { email: userEmail }, // match on email
      {
        $setOnInsert: { 
          createdAt: new Date(),
          plan: 'free',
          role: 'user', // default role, will be updated if admin
          subscriptionStatus: 'trialing',
          emailVerified: false,
          adsDisabled: false,
          storageUsed: 0,
          storageLimit: 1073741824, // 1GB default
        },
        $set: {
          googleId: googleProfile.googleId,
          name: googleProfile.displayName || userEmail.split('@')[0],
          displayName: googleProfile.displayName,
          lastLogin: new Date(),
        }
      },
      { new: true, upsert: true }
    );

    // Handle admin role assignment after atomic operation
    if (adminEmails.includes(googleUser.email) && googleUser.role !== "admin") {
      googleUser.role = "admin";
      await googleUser.save();
      console.log(`[ADMIN ROLE] Upgraded user to admin: ${googleUser.email}`);
    }

    return googleUser;
  } catch (error: any) {
    // Handle duplicate key errors gracefully
    if (error.code === 11000) {
      console.warn(`[DUPLICATE KEY] Retrying Google OAuth for ${userEmail}:`, error.message);
      
      // Retry without $setOnInsert to fetch existing document
      const existingUser = await User.findOneAndUpdate(
        { email: userEmail },
        {
          $set: {
            googleId: googleProfile.googleId,
            name: googleProfile.displayName || userEmail.split('@')[0],
            displayName: googleProfile.displayName,
            lastLogin: new Date(),
          }
        },
        { new: true }
      );

      if (!existingUser) {
        throw new Error(`User not found after duplicate key retry: ${userEmail}`);
      }

      // Handle admin role assignment for existing user
      if (adminEmails.includes(existingUser.email) && existingUser.role !== "admin") {
        existingUser.role = "admin";
        await existingUser.save();
        console.log(`[ADMIN ROLE] Upgraded existing user to admin: ${existingUser.email}`);
      }

      return existingUser;
    }
    
    // Re-throw other errors
    throw error;
  }
};

// --- Google OAuth Callback Route (for extension/popup) ---
router.get('/google/callback', passport.authenticate('google', {
    failureRedirect: '/auth/login/failed',
    session: false
}), async (req, res, next) => {
    const googleProfileUser = req.user as IUser;
    if (!googleProfileUser || !googleProfileUser.googleId) {
      return res.redirect('/auth/login/failed?message=GoogleProfileMissing');
    }

    if (!googleProfileUser.email) {
      console.warn('Google login attempt without email from profile:', googleProfileUser.googleId);
      return res.redirect('/auth/login/failed?message=EmailRequiredFromGoogle');
    }

    try {
      // Use atomic upsert operation
      const userInDb = await upsertGoogleUser(googleProfileUser);
      const token = generateToken(userInDb);

      // For popup/extension requests, use the popup flow
      res.send(
        `<html><head><title>Authentication Success</title></head><body>
        <p>Authentication successful. Please wait...</p>
        <script>
          if (window.opener) {
            window.opener.postMessage({ type: 'AUTH_SUCCESS', token: '${token}' }, '*');
            window.close();
          } else {
            document.body.innerHTML = '<p>Authentication complete. You can close this window.</p><p>Token (for testing): ${token}</p>';
          }
        </script></body></html>`
      );
    } catch (error) {
      console.error("Error in Google OAuth callback user processing:", error);
      if (next) next(error);
      else res.redirect('/auth/login/failed?message=ErrorProcessingGoogleUser');
    }
  }
);

// --- Google OAuth Callback Route for Web ---
router.get('/google/callback/web', passport.authenticate('google-web', {
    failureRedirect: '/auth/login/failed',
    session: false
}), async (req, res, next) => {
    const googleProfileUser = req.user as IUser;
    if (!googleProfileUser || !googleProfileUser.googleId) {
      return res.redirect('/auth/login/failed?message=GoogleProfileMissing');
    }

    if (!googleProfileUser.email) {
      console.warn('Google login attempt without email from profile:', googleProfileUser.googleId);
      return res.redirect('/auth/login/failed?message=EmailRequiredFromGoogle');
    }

    try {
      // Use atomic upsert operation
      const userInDb = await upsertGoogleUser(googleProfileUser);
      const token = generateToken(userInDb);

      // For web requests, redirect to the main domain with token
      const redirectUrl = process.env.NODE_ENV === 'production'
        ? `https://notely.social/dashboard?auth_token=${token}`
        : `http://localhost:5173/dashboard?auth_token=${token}`;
      res.redirect(redirectUrl);
    } catch (error) {
      console.error("Error in Google OAuth callback user processing:", error);
      if (next) next(error);
      else res.redirect('/auth/login/failed?message=ErrorProcessingGoogleUser');
    }
  }
);

// --- Simple failure route (for redirect example) ---
router.get('/login/failed', (req: Request, res: Response) => {
  const message = req.query.message || 'Google Login Failed';
  res.status(401).json({ message });
});

// --- Example Protected Route ---
// This route requires a valid JWT in the Authorization header (Bearer token)
router.get('/me', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  try {
    const userInDb = await User.findById(user.id);
    if (!userInDb) {
      res.status(404).json({ message: "User not found." });
      return;
    }

    res.json({
      id: userInDb.id,
      email: userInDb.email,
      name: userInDb.name,
      displayName: userInDb.displayName,
      role: userInDb.role,
      plan: userInDb.plan || 'free',
      subscriptionStatus: userInDb.subscriptionStatus,
      storageUsed: userInDb.storageUsed,
      storageLimit: userInDb.storageLimit,
      adsDisabled: userInDb.adsDisabled,
      emailVerified: userInDb.emailVerified,
      googleId: userInDb.googleId // Add googleId to response
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: "Failed to fetch profile." });
  }
}));

// Update user profile
router.put('/me', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const { displayName } = req.body;

  try {
    const userInDb = await User.findById(user.id);
    if (!userInDb) {
      res.status(404).json({ message: "User not found." });
      return;
    }

    // Only update displayName - name comes from account creation and doesn't change
    if (displayName !== undefined) {
      userInDb.displayName = displayName;
    }

    await userInDb.save();

    res.json({
      id: userInDb.id,
      email: userInDb.email,
      name: userInDb.name,
      displayName: userInDb.displayName,
      role: userInDb.role,
      plan: userInDb.plan || 'free',
      subscriptionStatus: userInDb.subscriptionStatus,
      storageUsed: userInDb.storageUsed,
      storageLimit: userInDb.storageLimit,
      adsDisabled: userInDb.adsDisabled,
      emailVerified: userInDb.emailVerified,
      googleId: userInDb.googleId
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ message: "Failed to update profile." });
  }
}));

// Admin: List all users
router.get('/user/all', protect, isAdmin, asyncHandler(async (req, res, next) => {
  const users = await User.find().select('-password -__v -userId'); // Exclude Firebase userId if still in schema
  res.json(users);
}));

// Admin: Manually upgrade user to premium
router.post('/user/upgrade', protect, isAdmin, asyncHandler(async (req, res, next) => {
  // userId in body now refers to MongoDB _id
  const { userId: mongoUserId, plan } = req.body;
  if (!mongoUserId || !['free', 'premium'].includes(plan)) {
    res.status(400).json({ message: 'MongoDB userId and valid plan required' });
    return;
  }
  const userToUpdate = await User.findById(mongoUserId);
  if (!userToUpdate) {
    res.status(404).json({ message: 'User not found' });
    return;
  }
  userToUpdate.plan = plan as 'free' | 'premium';
  await userToUpdate.save();
  res.json({ message: `User ${mongoUserId} upgraded to ${plan}` });
}));

// Create Stripe Checkout session for monthly/yearly plans
router.post('/stripe/create-checkout-session', protect, asyncHandler(async (req, res, next) => {
  const { plan } = req.body;
  const authenticatedUser = req.user as IUser; // From JWT

  if (!authenticatedUser || !authenticatedUser.email) {
    res.status(401).json({ message: 'User not authenticated or email missing.' });
    return;
  }
  const userEmail = authenticatedUser.email;
  const userId = authenticatedUser.id; // MongoDB _id

  // Find user in DB to get/set stripeCustomerId
  let userInDb = await User.findById(userId);
  if (!userInDb) {
      // This should ideally not happen if user is authenticated with a valid JWT
      res.status(404).json({message: "Authenticated user not found in DB for Stripe session."});
      return;
  }

  const priceIdKey = plan === 'yearly' ? 'STRIPE_PRICE_ID_YEARLY' : 'STRIPE_PRICE_ID_MONTHLY';
  const priceId = process.env[priceIdKey];
  if (!priceId) {
    res.status(500).json({ message: `Stripe price ID for ${priceIdKey} not configured` });
    return;
  }
  let stripeCustomerId = userInDb.stripeCustomerId;
  if (!stripeCustomerId) {
    const customer = await stripe.customers.create({ email: userEmail });
    stripeCustomerId = customer.id;
    userInDb.stripeCustomerId = stripeCustomerId;
    await userInDb.save();
  }
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    mode: 'subscription',
    customer: stripeCustomerId,
    line_items: [{ price: priceId, quantity: 1 }],
    success_url: `${process.env.SERVER_BASE_URL || 'http://localhost:3000'}/auth/payment/success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.SERVER_BASE_URL || 'http://localhost:3000'}/auth/payment/cancelled?session_id={CHECKOUT_SESSION_ID}`,
    metadata: { userId: userId }, // Pass MongoDB _id as userId in metadata
  });
  res.json({ url: session.url });
}));

// Export the webhook handler separately
export const stripeWebhookHandler: ExpressRequestHandler = async (req, res, next) => {
  const sig = req.headers['stripe-signature'];
  let event;
  try {
    if (!sig) throw new Error('No stripe-signature header');
    if (!process.env.STRIPE_WEBHOOK_SECRET) throw new Error('STRIPE_WEBHOOK_SECRET not set');
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    let message = 'Unknown webhook error';
    if (err instanceof Error) message = err.message;
    console.error('Stripe webhook signature error:', message);
    res.status(400).send(`Webhook Error: ${message}`);
    return;
  }

  try {
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;
      // userId in metadata is MongoDB _id
      const mongoUserId = session.metadata?.userId;
      const subscriptionId = session.subscription as string;
      const couponCode = session.metadata?.couponCode;
      
      if (mongoUserId && subscriptionId) {
        const user = await User.findById(mongoUserId);
        if (user) {
          user.plan = 'premium';
          user.subscriptionStatus = 'active';
          user.stripeSubscriptionId = subscriptionId;
          if (session.customer) user.stripeCustomerId = session.customer as string;
          await user.save();
        }
      }

      // Increment coupon usage if coupon was used
      if (couponCode) {
        try {
          await Coupon.findOneAndUpdate(
            { code: couponCode.toUpperCase() },
            { $inc: { usedCount: 1 } }
          );
        } catch (error) {
          console.error('Error incrementing coupon usage:', error);
        }
      }
    } else if (event.type === 'customer.subscription.created') {
      const subscription = event.data.object as Stripe.Subscription;
      const stripeCustomerId = subscription.customer as string;
      const user = await User.findOne({ stripeCustomerId });
      if (user) {
        user.plan = 'premium';
        user.subscriptionStatus = subscription.status === 'trialing' ? 'trialing' : 'active';
        user.stripeSubscriptionId = subscription.id;
        await user.save();
      }
    } else if (event.type === 'customer.subscription.updated') {
      const subscription = event.data.object as Stripe.Subscription;
      const stripeCustomerId = subscription.customer as string;
      const user = await User.findOne({ stripeCustomerId });
      if (user) {
        // Map Stripe status to our enum
        let subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
        let plan: 'free' | 'premium';

        switch (subscription.status) {
          case 'active':
            subscriptionStatus = 'active';
            plan = 'premium';
            break;
          case 'trialing':
            subscriptionStatus = 'trialing';
            plan = 'premium';
            break;
          case 'past_due':
            subscriptionStatus = 'past_due';
            plan = 'free';
            break;
          case 'canceled':
          case 'incomplete':
          case 'incomplete_expired':
          case 'unpaid':
          default:
            subscriptionStatus = 'canceled';
            plan = 'free';
            break;
        }

        user.plan = plan;
        user.subscriptionStatus = subscriptionStatus;
        user.stripeSubscriptionId = subscription.id;
        await user.save();
      }
    } else if (event.type === 'customer.subscription.deleted') {
      const subscription = event.data.object as Stripe.Subscription;
      const stripeCustomerId = subscription.customer as string;
      const user = await User.findOne({ stripeCustomerId });
      if (user) {
        user.plan = 'free';
        user.subscriptionStatus = 'canceled';
        user.stripeSubscriptionId = undefined;
        await user.save();
      }
    }
    res.json({ received: true });
  } catch (dbError) {
    console.error('Error processing Stripe webhook event:', dbError);
    if (next) next(dbError);
    else res.status(500).json({ message: "Error processing webhook" });
  }
};

// --- Stripe Payment Success/Cancel Redirect Handlers ---
router.get('/payment/success', (req: Request, res: Response) => {
  const clientDashboardUrl = process.env.CLIENT_URL || 'chrome-extension://hnlopcaeidipbmokamhboiooholpecbf/dashboard.html';
  const sessionId = req.query.session_id;
  res.send(`
    <html>
      <head><title>Payment Success</title></head>
      <body>
        <h1>Payment Successful!</h1>
        <p>Your payment was processed successfully.</p>
        <p><a href="${clientDashboardUrl}?payment_status=success${sessionId ? '&checkout_session_id=' + sessionId : ''}" id="returnToApp">Click here to return to Social Saver Pro</a></p>
        <p>If you are not redirected automatically, please copy and paste this link into your address bar or click the link above:</p>
        <p><code>${clientDashboardUrl}?payment_status=success${sessionId ? '&checkout_session_id=' + sessionId : ''}</code></p>
        <script>
          // Optional: A small delay then attempt redirect. Might still be blocked but worth a try.
          // setTimeout(() => { window.location.href = "${clientDashboardUrl}?payment_status=success${sessionId ? '&checkout_session_id=' + sessionId : ''}"; }, 1000);
        </script>
      </body>
    </html>
  `);
});

router.get('/payment/cancelled', (req: Request, res: Response) => {
  const clientDashboardUrl = process.env.CLIENT_URL || 'chrome-extension://hnlopcaeidipbmokamhboiooholpecbf/dashboard.html';
  const sessionId = req.query.session_id;
  res.send(`
    <html>
      <head><title>Payment Cancelled</title></head>
      <body>
        <h1>Payment Cancelled</h1>
        <p>Your payment process was cancelled.</p>
        <p><a href="${clientDashboardUrl}?payment_status=cancelled${sessionId ? '&checkout_session_id=' + sessionId : ''}" id="returnToApp">Click here to return to Social Saver Pro</a></p>
        <p>If you are not redirected automatically, please copy and paste this link into your address bar or click the link above:</p>
        <p><code>${clientDashboardUrl}?payment_status=cancelled${sessionId ? '&checkout_session_id=' + sessionId : ''}</code></p>
      </body>
    </html>
  `);
});

// Remove the webhook route from the main router to handle it separately in server.ts
// router.post('/stripe/webhook', express.raw({ type: 'application/json' }), stripeWebhookHandler);

// @route   GET /auth/queue-status
// @desc    Check image processing queue status
// @access  Private
router.get('/queue-status', protect, asyncHandler(async (req: Request, res: Response) => {
  try {
    const { getQueueStats } = await import('../services/queueService');
    const stats = await getQueueStats();

    res.json({
      queue: stats,
      message: 'Queue status retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting queue status:', error);
    res.status(500).json({
      error: 'Failed to get queue status',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

// @route   POST /auth/migrate-existing-images
// @desc    Migrate existing posts with original URLs to S3 for logged-in user
// @access  Private
router.post('/migrate-existing-images', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  try {
    const CloudPost = (await import('../models/CloudPost')).default;

    // Find posts with original URLs that need migration
    const postsToMigrate = await CloudPost.find({
      userId: user.id,
      'media.url': {
        $regex: /(pbs\.twimg\.com|i\.redd\.it|preview\.redd\.it|external-preview\.redd\.it)/i
      },
      imageProcessingStatus: { $ne: 'completed' }
    });

    if (postsToMigrate.length === 0) {
      res.json({
        message: 'No posts found that need image migration',
        migrated: 0
      });
      return;
    }

    const { addImageProcessingJob } = await import('../services/queueService');
    let queuedJobs = 0;

    // Queue each post for image processing
    for (const post of postsToMigrate) {
      const mediaItems = post.media.filter((item: any) =>
        item.url && (
          item.url.includes('pbs.twimg.com') ||
          item.url.includes('i.redd.it') ||
          item.url.includes('preview.redd.it') ||
          item.url.includes('external-preview.redd.it')
        )
      );

      if (mediaItems.length > 0) {
        try {
          await addImageProcessingJob(
            post._id.toString(),
            user.id,
            post.platform,
            mediaItems.map((item: any) => ({
              url: item.url,
              type: item.type || 'image',
              alt: item.alt,
              width: item.width,
              height: item.height
            })),
            post.toObject()
          );

          // Update post status
          await CloudPost.findByIdAndUpdate(post._id, {
            imageProcessingStatus: 'pending',
            imageProcessingError: null
          });

          queuedJobs++;
        } catch (jobError) {
          console.error(`Failed to queue migration job for post ${post._id}:`, jobError);
        }
      }
    }

    res.json({
      message: `Queued ${queuedJobs} posts for image migration`,
      found: postsToMigrate.length,
      queued: queuedJobs
    });

  } catch (error) {
    console.error('Error migrating existing images:', error);
    res.status(500).json({
      error: 'Failed to migrate existing images',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

// @route   GET /auth/storage-usage
// @desc    Get user's storage usage and limits
// @access  Private
router.get('/storage-usage', protect, async (req: Request, res: Response) => {
  try {
    const authenticatedUser = req.user as IUser; // Type assertion after protect middleware
    const user = await User.findById(authenticatedUser.id).select('storageUsed storageLimit plan');

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // TODO: Re-enable auto-fix after testing
    // Auto-fix storage limit if incorrect for premium users
    // const expectedLimit = user.plan === 'premium' ? 10737418240 : 1073741824; // 10GB : 1GB
    // if (user.storageLimit !== expectedLimit) {
    //   user.storageLimit = expectedLimit;
    //   await user.save();
    //   console.log(`Auto-fixed storage limit for user ${user.id}: ${user.plan} plan now has ${Math.round(expectedLimit / (1024 * 1024 * 1024))}GB limit`);
    // }

    // Ensure storage values are valid (fix negative values)
    if (user.storageUsed < 0) {
      console.warn(`[STORAGE FIX] User ${user.email} had negative storageUsed: ${user.storageUsed}, fixing to 0`);
      user.storageUsed = 0;
      await user.save();
    }

    // Ensure storage limit is valid
    if (!user.storageLimit || user.storageLimit <= 0) {
      const defaultLimit = user.plan === 'premium' ? 10737418240 : 1073741824; // 10GB : 1GB
      console.warn(`[STORAGE FIX] User ${user.email} had invalid storageLimit: ${user.storageLimit}, fixing to ${defaultLimit}`);
      user.storageLimit = defaultLimit;
      await user.save();
    }

    const usagePercentage = Math.max(0, Math.round((user.storageUsed / user.storageLimit) * 100));
    const usedMB = Math.max(0, Math.round(user.storageUsed / (1024 * 1024) * 100) / 100); // Round to 2 decimal places
    const limitMB = Math.round(user.storageLimit / (1024 * 1024));

    res.json({
      storageUsed: user.storageUsed,
      storageLimit: user.storageLimit,
      usagePercentage,
      usedMB,
      limitMB,
      plan: user.plan,
      isNearLimit: usagePercentage >= 90,
      isOverLimit: usagePercentage >= 100
    });
  } catch (error) {
    console.error('Error fetching storage usage:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// --- Password Reset Routes ---

// Request password reset
router.post('/request-password-reset', asyncHandler(async (req: Request, res: Response) => {
  console.log('[PASSWORD_RESET] Request received:', {
    body: req.body,
    headers: {
      'content-type': req.headers['content-type'],
      origin: req.headers.origin,
      'user-agent': req.headers['user-agent']?.substring(0, 50) + '...'
    },
    ip: req.ip
  });

  const { email } = req.body;

  if (!email) {
    console.log('[PASSWORD_RESET] Missing email in request body');
    res.status(400).json({ message: 'Email is required' });
    return;
  }

  console.log('[PASSWORD_RESET] Processing password reset for email:', email);

  try {
    // Step 1: Find user by email
    console.log('[PASSWORD_RESET] Step 1: Searching for user with email:', email.toLowerCase());
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      console.log('[PASSWORD_RESET] Step 1 RESULT: User not found for email:', email.toLowerCase());
      // Always return success to prevent email enumeration
      res.json({ message: 'If an account with that email exists, a password reset link has been sent.' });
      return;
    }

    console.log('[PASSWORD_RESET] Step 1 RESULT: User found:', {
      userId: user._id,
      email: user.email,
      hasPassword: !!user.password,
      isGoogleUser: !!user.googleId
    });

    // Step 2: Generate reset token
    console.log('[PASSWORD_RESET] Step 2: Generating reset token...');
    const resetToken = crypto.randomBytes(32).toString('hex');
    console.log('[PASSWORD_RESET] Step 2 RESULT: Token generated, length:', resetToken.length);

    // Step 3: Save reset token to database
    console.log('[PASSWORD_RESET] Step 3: Saving token to database...');
    const passwordResetRecord = await PasswordReset.create({
      email: email.toLowerCase(),
      token: resetToken,
      expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
    });
    console.log('[PASSWORD_RESET] Step 3 RESULT: Token saved to database:', {
      recordId: passwordResetRecord._id,
      email: passwordResetRecord.email,
      expiresAt: passwordResetRecord.expiresAt
    });

    // Step 4: Send password reset email
    console.log('[PASSWORD_RESET] Step 4: Preparing to send email...');
    const resetUrl = `${process.env.CLIENT_URL || 'http://localhost:3000'}/dashboard/reset-password?token=${resetToken}`;
    console.log('[PASSWORD_RESET] Step 4: Reset URL generated:', resetUrl);

    try {
      await sendPasswordResetEmail(email, resetUrl);
      console.log('[PASSWORD_RESET] Step 4 RESULT: Email service call completed successfully');
    } catch (emailError) {
      console.error('[PASSWORD_RESET] Step 4 ERROR: Email sending failed:', emailError);
      // Continue execution - return success to user even if email fails
    }

    console.log('[PASSWORD_RESET] FLOW COMPLETE: Returning success response');
    res.json({ message: 'If an account with that email exists, a password reset link has been sent.' });
  } catch (error) {
    console.error('[PASSWORD_RESET] CRITICAL ERROR in password reset request:', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      email: email
    });
    res.status(500).json({ message: 'Internal server error' });
  }
}));

// Reset password
router.post('/reset-password', asyncHandler(async (req: Request, res: Response) => {
  const { token, newPassword } = req.body;

  if (!token || !newPassword) {
    res.status(400).json({ message: 'Token and new password are required' });
    return;
  }

  if (newPassword.length < 6) {
    res.status(400).json({ message: 'Password must be at least 6 characters long' });
    return;
  }

  try {
    // Find valid reset token
    const resetRecord = await PasswordReset.findOne({
      token,
      used: false,
      expiresAt: { $gt: new Date() },
    });

    if (!resetRecord) {
      res.status(400).json({ message: 'Invalid or expired reset token' });
      return;
    }

    // Find user
    const user = await User.findOne({ email: resetRecord.email });
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Update password
    user.password = newPassword; // Will be hashed by pre-save hook
    user.emailVerified = true; // Mark email as verified since they accessed their email
    await user.save();

    // Mark token as used
    resetRecord.used = true;
    await resetRecord.save();

    res.json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Error in password reset:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}));

// ===========================================
// TEMPORARY DEBUG ROUTE - REMOVE AFTER FIX
// ===========================================
// Fix negative <NAME_EMAIL>
router.post('/debug/fix-storage', asyncHandler(async (req: Request, res: Response) => {
  try {
    console.log('[DEBUG] Attempting to fix <NAME_EMAIL>');
    
    const user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    const previousStorageUsed = user.storageUsed;
    console.log(`[DEBUG] Current storageUsed: ${previousStorageUsed}`);

    if (user.storageUsed < 0) {
      user.storageUsed = 0;
      await user.save();
      
      console.log(`[DEBUG] Fixed storageUsed: ${previousStorageUsed} -> ${user.storageUsed}`);
      
      res.status(200).json({ 
        message: 'Fixed storageUsed for admin',
        previousValue: previousStorageUsed,
        newValue: user.storageUsed
      });
    } else {
      console.log(`[DEBUG] No fix needed, storageUsed is already valid: ${user.storageUsed}`);
      
      res.status(200).json({ 
        message: 'No fix needed, storageUsed is already valid',
        currentValue: user.storageUsed
      });
    }
  } catch (error) {
    console.error('[DEBUG] Error fixing storage:', error);
    res.status(500).json({ 
      message: 'Error fixing storage',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

// --- Update Profile Route ---
router.post('/update-profile', protect, asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user?.id;
  const { displayName } = req.body;

  if (!displayName) {
    res.status(400).json({ message: 'Display name is required' });
    return;
  }

  const user = await User.findByIdAndUpdate(
    userId,
    { displayName },
    { new: true }
  );

  if (!user) {
    res.status(404).json({ message: 'User not found' });
    return;
  }

  res.json({
    id: user.id,
    email: user.email,
    name: user.name,
    displayName: user.displayName,
    plan: user.plan,
    role: user.role,
    subscriptionStatus: user.subscriptionStatus
  });
}));

export default router;