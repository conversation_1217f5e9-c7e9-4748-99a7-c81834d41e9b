import express from 'express';
import { createPost, getPosts, getPostById, deletePost } from '../controllers/postController';
import { protect } from '../middleware/authMiddleware';

const router = express.Router();

// Apply auth middleware to all routes
router.use(protect);

// POST /api/posts - Create a new post
router.post('/', createPost as any);

// GET /api/posts - Get all posts for the authenticated user
router.get('/', getPosts as any);

// GET /api/posts/:id - Get a single post by ID
router.get('/:id', getPostById as any);

// DELETE /api/posts/:id - Delete a post
router.delete('/:id', deletePost as any);

export default router;
