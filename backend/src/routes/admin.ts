import express, { Request, Response, Router } from 'express';
import User from '../models/User';
import AIUsageLog from '../models/AIUsageLog';
import StorageUsage from '../models/StorageUsage';
import { protect, isAdmin } from '../middleware/authMiddleware';
import { getUserUsageStats } from '../utils/subscriptionUtils';
import { asyncHandler } from '../utils/asyncHandler';

const router: Router = express.Router();

// Get all users with filtering
router.get('/users', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { 
    subscriptionStatus, 
    plan, 
    page = 1, 
    limit = 50,
    search 
  } = req.query;

  const filter: any = {};
  
  if (subscriptionStatus) {
    filter.subscriptionStatus = subscriptionStatus;
  }
  
  if (plan) {
    filter.plan = plan;
  }
  
  if (search) {
    filter.$or = [
      { email: { $regex: search, $options: 'i' } },
      { name: { $regex: search, $options: 'i' } },
      { displayName: { $regex: search, $options: 'i' } },
    ];
  }

  try {
    const skip = (Number(page) - 1) * Number(limit);
    
    const [users, total] = await Promise.all([
      User.find(filter)
        .select('-password -__v')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      User.countDocuments(filter),
    ]);

    res.json({
      users,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Failed to fetch users' });
  }
}));

// Get user details with usage stats
router.get('/users/:userId', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { days = 30 } = req.query;

  try {
    const user = await User.findById(userId).select('-password -__v');
    
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    const usageStats = await getUserUsageStats(userId, Number(days));

    res.json({
      user,
      usage: usageStats,
    });
  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({ message: 'Failed to fetch user details' });
  }
}));

// Toggle ads for user
router.patch('/users/:userId/ads', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { adsDisabled } = req.body;

  if (typeof adsDisabled !== 'boolean') {
    res.status(400).json({ message: 'adsDisabled must be a boolean' });
    return;
  }

  try {
    const user = await User.findByIdAndUpdate(
      userId,
      { adsDisabled },
      { new: true }
    ).select('-password -__v');

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    res.json({
      message: `Ads ${adsDisabled ? 'disabled' : 'enabled'} for user`,
      user,
    });
  } catch (error) {
    console.error('Error updating user ads setting:', error);
    res.status(500).json({ message: 'Failed to update ads setting' });
  }
}));

// Update user plan/status
router.patch('/users/:userId/subscription', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { plan, subscriptionStatus } = req.body;

  const updates: any = {};
  
  if (plan && ['free', 'premium'].includes(plan)) {
    updates.plan = plan;
  }
  
  if (subscriptionStatus && ['trialing', 'active', 'past_due', 'canceled'].includes(subscriptionStatus)) {
    updates.subscriptionStatus = subscriptionStatus;
  }

  if (Object.keys(updates).length === 0) {
    res.status(400).json({ message: 'No valid updates provided' });
    return;
  }

  try {
    const user = await User.findByIdAndUpdate(
      userId,
      updates,
      { new: true }
    ).select('-password -__v');

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    res.json({
      message: 'User subscription updated successfully',
      user,
    });
  } catch (error) {
    console.error('Error updating user subscription:', error);
    res.status(500).json({ message: 'Failed to update user subscription' });
  }
}));

// Get usage statistics overview
router.get('/stats/usage', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { days = 30 } = req.query;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - Number(days));

  try {
    // Get user counts by plan and status
    const userStats = await User.aggregate([
      {
        $group: {
          _id: {
            plan: '$plan',
            subscriptionStatus: '$subscriptionStatus',
          },
          count: { $sum: 1 },
        },
      },
    ]);

    // Get total storage usage
    const storageStats = await User.aggregate([
      {
        $group: {
          _id: '$plan',
          totalStorageUsed: { $sum: '$storageUsed' },
          avgStorageUsed: { $avg: '$storageUsed' },
          userCount: { $sum: 1 },
        },
      },
    ]);

    res.json({
      userStats,
      storageStats,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error fetching usage statistics:', error);
    res.status(500).json({ message: 'Failed to fetch usage statistics' });
  }
}));

// Send usage warning email (placeholder)
router.post('/users/:userId/warn', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { type, message } = req.body; // type: 'storage' | 'tokens'

  try {
    const user = await User.findById(userId);
    
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // TODO: Implement email sending
    console.log(`Warning email for ${user.email}:`, { type, message });

    res.json({
      message: `Warning email sent to ${user.email}`,
      type,
    });
  } catch (error) {
    console.error('Error sending warning email:', error);
    res.status(500).json({ message: 'Failed to send warning email' });
  }
}));

// Get admin overview stats (total users, active users today, total tokens, total storage)
router.get('/overview', protect, isAdmin, asyncHandler(async (req: Request, res: Response) => {
  try {
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));

    // Get total users count
    const totalUsers = await User.countDocuments();

    // Get active users today (users who logged in today)
    const activeUsersToday = await User.countDocuments({
      lastLogin: { $gte: startOfToday }
    });

    // Get total AI token usage (last 30 days)
    const tokenUsageStats = await AIUsageLog.aggregate([
      {
        $match: {
          date: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: null,
          totalTokens: { $sum: '$tokensUsed' }
        }
      }
    ]);

    const totalTokenUsage = tokenUsageStats[0]?.totalTokens || 0;

    // Get total storage used across all users
    const storageStats = await User.aggregate([
      {
        $group: {
          _id: null,
          totalStorageUsed: { $sum: '$storageUsed' }
        }
      }
    ]);

    const totalStorageUsed = storageStats[0]?.totalStorageUsed || 0;
    const totalStorageUsedGB = Math.round((totalStorageUsed / (1024 * 1024 * 1024)) * 100) / 100;

    res.json({
      overview: {
        totalUsers,
        activeUsersToday,
        totalTokenUsage,
        totalStorageUsed: totalStorageUsed,
        totalStorageUsedGB
      }
    });
  } catch (error) {
    console.error('Error fetching admin overview:', error);
    res.status(500).json({ message: 'Failed to fetch admin overview' });
  }
}));

export default router;
