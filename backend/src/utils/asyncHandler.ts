import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Wrapper for async route handlers to catch promise rejections
 * and pass them to Express error handling middleware
 */
export const asyncHandler = <T extends RequestHandler>(handler: T): T => {
  return ((req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(handler(req, res, next)).catch(next);
  }) as T;
};
