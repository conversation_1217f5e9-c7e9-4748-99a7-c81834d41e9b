import Stripe from 'stripe';
import User, { IUser } from '../models/User';
import AIUsageLog from '../models/AIUsageLog';
import StorageUsage from '../models/StorageUsage';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

/**
 * Verify subscription status with Stripe and update database if needed
 */
export const verifySubscription = async (userId: string): Promise<{
  plan: 'free' | 'premium';
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  adsDisabled: boolean;
}> => {
  try {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // If user has no Stripe subscription, return current status
    if (!user.stripeSubscriptionId) {
      return {
        plan: user.plan,
        subscriptionStatus: user.subscriptionStatus,
        adsDisabled: user.adsDisabled,
      };
    }

    // Fetch subscription from Stripe
    const subscription = await stripe.subscriptions.retrieve(user.stripeSubscriptionId);
    
    // Map Stripe status to our enum
    let subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
    let plan: 'free' | 'premium' = 'free';

    switch (subscription.status) {
      case 'active':
        subscriptionStatus = 'active';
        plan = 'premium';
        break;
      case 'trialing':
        subscriptionStatus = 'trialing';
        plan = 'premium'; // Trial users get premium features
        break;
      case 'past_due':
        subscriptionStatus = 'past_due';
        plan = 'free'; // Downgrade to free until payment
        break;
      case 'canceled':
      case 'incomplete':
      case 'incomplete_expired':
      case 'unpaid':
      default:
        subscriptionStatus = 'canceled';
        plan = 'free';
        break;
    }

    // Update user if status changed
    if (user.subscriptionStatus !== subscriptionStatus || user.plan !== plan) {
      await User.findByIdAndUpdate(userId, {
        subscriptionStatus,
        plan,
        adsDisabled: plan === 'premium' || user.adsDisabled, // Premium users or manually disabled
      });
    }

    return {
      plan,
      subscriptionStatus,
      adsDisabled: plan === 'premium' || user.adsDisabled,
    };

  } catch (error) {
    console.error('Error verifying subscription:', error);
    // Return current user status on error
    const user = await User.findById(userId);
    return {
      plan: user?.plan || 'free',
      subscriptionStatus: user?.subscriptionStatus || 'canceled',
      adsDisabled: user?.adsDisabled || false,
    };
  }
};

/**
 * Log AI token usage
 */
export const logAIUsage = async (
  userId: string,
  tokensUsed: number,
  operation: string,
  platform?: string
): Promise<void> => {
  try {
    await AIUsageLog.create({
      userId,
      tokensUsed,
      operation,
      platform,
      date: new Date(),
    });
  } catch (error) {
    console.error('Error logging AI usage:', error);
  }
};

/**
 * Log storage usage
 */
export const logStorageUsage = async (
  userId: string,
  mbUsed: number,
  operation: string,
  platform?: string,
  fileType?: string
): Promise<void> => {
  try {
    await StorageUsage.create({
      userId,
      mbUsed,
      operation,
      platform,
      fileType,
      date: new Date(),
    });

    // Update user's total storage used
    await User.findByIdAndUpdate(userId, {
      $inc: { storageUsed: mbUsed * 1024 * 1024 }, // Convert MB to bytes
    });
  } catch (error) {
    console.error('Error logging storage usage:', error);
  }
};

/**
 * Get usage statistics for a user
 */
export const getUserUsageStats = async (
  userId: string,
  days: number = 30
): Promise<{
  aiTokens: { total: number; byOperation: Record<string, number> };
  storage: { totalMB: number; byOperation: Record<string, number> };
}> => {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  try {
    // Get AI usage
    const aiUsage = await AIUsageLog.aggregate([
      {
        $match: {
          userId: userId,
          date: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: '$operation',
          totalTokens: { $sum: '$tokensUsed' },
        },
      },
    ]);

    // Get storage usage
    const storageUsage = await StorageUsage.aggregate([
      {
        $match: {
          userId: userId,
          date: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: '$operation',
          totalMB: { $sum: '$mbUsed' },
        },
      },
    ]);

    const aiTokens = {
      total: aiUsage.reduce((sum, item) => sum + item.totalTokens, 0),
      byOperation: aiUsage.reduce((acc, item) => {
        acc[item._id] = item.totalTokens;
        return acc;
      }, {} as Record<string, number>),
    };

    const storage = {
      totalMB: storageUsage.reduce((sum, item) => sum + item.totalMB, 0),
      byOperation: storageUsage.reduce((acc, item) => {
        acc[item._id] = item.totalMB;
        return acc;
      }, {} as Record<string, number>),
    };

    return { aiTokens, storage };
  } catch (error) {
    console.error('Error getting usage stats:', error);
    return {
      aiTokens: { total: 0, byOperation: {} },
      storage: { totalMB: 0, byOperation: {} },
    };
  }
};
