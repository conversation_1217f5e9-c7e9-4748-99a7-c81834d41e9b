import { AIProviderConfigService, TaskType, AIProvider } from '../services/aiProviderConfigService';

/**
 * Helper function for AI services to get the configured provider for a task type
 * This will be used by existing AI services when they need to know which provider to use
 */
export async function getAIProviderForTask(taskType: TaskType): Promise<{
  provider: AIProvider;
  model?: string;
}> {
  return await AIProviderConfigService.getProviderForTask(taskType);
}

/**
 * Check if a specific provider should be used for a task
 * Useful for conditional logic in AI services
 */
export async function shouldUseProvider(taskType: TaskType, providerToCheck: AIProvider): Promise<boolean> {
  const config = await getAIProviderForTask(taskType);
  return config.provider === providerToCheck;
}

/**
 * Get the model to use for a specific task and provider
 * Returns the configured model or falls back to default
 */
export async function getModelForTask(taskType: TaskType, provider: AIProvider): Promise<string | undefined> {
  const config = await getAIProviderForTask(taskType);
  
  if (config.provider === provider) {
    return config.model;
  }
  
  // If the provider doesn't match, return undefined
  return undefined;
}

/**
 * Simple logging utility for AI provider usage
 * This can be extended to track which providers are being used
 */
export function logAIProviderUsage(taskType: TaskType, provider: AIProvider, model?: string): void {
  console.log(`[AI Provider] ${taskType}: Using ${provider}${model ? ` (${model})` : ''}`);
} 