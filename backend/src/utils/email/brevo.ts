import axios from 'axios';

interface BrevoEmailRequest {
  sender: {
    name: string;
    email: string;
  };
  to: Array<{
    email: string;
  }>;
  subject: string;
  htmlContent: string;
}

/**
 * Send password reset email using Brevo (Sendinblue) API
 * @param email - Recipient email address
 * @param resetUrl - Password reset URL
 */
export const sendPasswordResetEmail = async (email: string, resetUrl: string): Promise<void> => {
  console.log('[EMAIL] sendPasswordResetEmail called with:', {
    email: email,
    resetUrlLength: resetUrl.length,
    environment: process.env.NODE_ENV
  });

  const brevoApiKey = process.env.BREVO_API_KEY;

  if (!brevoApiKey) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('[EMAIL] BREVO_API_KEY not configured, skipping email send in development');
      console.log(`[EMAIL] Would send password reset to ${email}: ${resetUrl}`);
      return;
    } else {
      console.error('[EMAIL] BREVO_API_KEY environment variable is missing in production');
      throw new Error('BREVO_API_KEY environment variable is required for email functionality');
    }
  }

  console.log('[EMAIL] BREVO_API_KEY found, proceeding with email send');

  const emailData: BrevoEmailRequest = {
    sender: {
      name: 'Notely',
      email: '<EMAIL>'
    },
    to: [
      {
        email: email
      }
    ],
    subject: 'Reset your Notely password',
    htmlContent: `<p>Click the link below to reset your password:</p><p><a href='${resetUrl}'>Reset Password</a></p>`
  };

  try {
    console.log(`[EMAIL] Sending password reset email to ${email}`);
    
    const response = await axios.post(
      'https://api.brevo.com/v3/smtp/email',
      emailData,
      {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': brevoApiKey
        }
      }
    );

    console.log(`[EMAIL] Password reset email sent successfully to ${email}`, {
      messageId: response.data.messageId
    });
  } catch (error: any) {
    console.error(`[EMAIL] Failed to send password reset email to ${email}:`, {
      error: error.message,
      status: error.response?.status,
      data: error.response?.data
    });

    // Don't throw error to prevent breaking the password reset flow
    // The user will still get the success message, but we log the email failure
    if (process.env.NODE_ENV === 'production') {
      // In production, we might want to alert admins about email failures
      console.error('[EMAIL] CRITICAL: Email service failure in production');
    }
  }
};

/**
 * Send abuse alert email to admin using Brevo API
 */
export const sendAbuseAlertEmail = async (
  adminEmail: string,
  alertData: {
    userEmail: string;
    userName: string;
    userPlan: string;
    violations: string[];
    flaggedAt: string;
    dashboardUrl: string;
  }
): Promise<void> => {
  console.log('[EMAIL] sendAbuseAlertEmail called for:', {
    adminEmail,
    userEmail: alertData.userEmail,
    violations: alertData.violations.length
  });

  const brevoApiKey = process.env.BREVO_API_KEY;

  if (!brevoApiKey) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('[EMAIL] BREVO_API_KEY not configured, skipping abuse alert email in development');
      console.log(`[EMAIL] Would send abuse alert to ${adminEmail} for user ${alertData.userEmail}`);
      return;
    } else {
      console.error('[EMAIL] BREVO_API_KEY environment variable is missing in production');
      throw new Error('BREVO_API_KEY environment variable is required for email functionality');
    }
  }

  const emailData: BrevoEmailRequest = {
    sender: {
      name: 'Notely Social Admin',
      email: '<EMAIL>'
    },
    to: [{ email: adminEmail }],
    subject: `🚨 Abuse Alert: User ${alertData.userEmail} flagged`,
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px; border-radius: 10px; margin-bottom: 30px;">
          <h1 style="color: white; margin: 0; font-size: 24px;">🚨 Abuse Detection Alert</h1>
          <p style="color: #f8d7da; margin: 10px 0 0 0;">A user has been flagged for suspicious activity</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 25px;">
          <h2 style="color: #333; margin: 0 0 20px 0; font-size: 20px;">User Details</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid #e9ecef;">
              <td style="padding: 10px 0; font-weight: bold; color: #495057;">Email:</td>
              <td style="padding: 10px 0; color: #6c757d;">${alertData.userEmail}</td>
            </tr>
            <tr style="border-bottom: 1px solid #e9ecef;">
              <td style="padding: 10px 0; font-weight: bold; color: #495057;">Name:</td>
              <td style="padding: 10px 0; color: #6c757d;">${alertData.userName}</td>
            </tr>
            <tr style="border-bottom: 1px solid #e9ecef;">
              <td style="padding: 10px 0; font-weight: bold; color: #495057;">Plan:</td>
              <td style="padding: 10px 0; color: #6c757d;">
                <span style="background: ${alertData.userPlan === 'premium' ? '#28a745' : '#6c757d'}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                  ${alertData.userPlan}
                </span>
              </td>
            </tr>
            <tr>
              <td style="padding: 10px 0; font-weight: bold; color: #495057;">Flagged:</td>
              <td style="padding: 10px 0; color: #6c757d;">${new Date(alertData.flaggedAt).toLocaleString()}</td>
            </tr>
          </table>
        </div>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
          <h3 style="color: #856404; margin: 0 0 15px 0; font-size: 18px;">⚠️ Violations Detected</h3>
          <ul style="margin: 0; padding-left: 20px; color: #856404;">
            ${alertData.violations.map(violation => `<li style="margin-bottom: 8px;">${violation}</li>`).join('')}
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${alertData.dashboardUrl}" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            View Admin Dashboard
          </a>
        </div>
        
        <div style="background: #e9ecef; padding: 20px; border-radius: 10px; margin-top: 30px;">
          <h4 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">Next Steps:</h4>
          <ul style="margin: 0; padding-left: 20px; color: #6c757d; font-size: 14px;">
            <li style="margin-bottom: 5px;">Review the user's activity in the admin dashboard</li>
            <li style="margin-bottom: 5px;">Investigate the flagged violations</li>
            <li style="margin-bottom: 5px;">Take appropriate action (warn, suspend, or unflag)</li>
            <li>Monitor for continued abuse patterns</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
          <p style="color: #6c757d; font-size: 12px; margin: 0;">
            This is an automated alert from Notely Social abuse detection system.
          </p>
        </div>
      </div>
    `
  };

  try {
    const response = await axios.post(
      'https://api.brevo.com/v3/smtp/email',
      emailData,
      {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': brevoApiKey
        }
      }
    );

    console.log(`[EMAIL] Abuse alert email sent successfully to ${adminEmail}`, {
      messageId: response.data.messageId,
      userEmail: alertData.userEmail
    });
  } catch (error: any) {
    console.error(`[EMAIL] Failed to send abuse alert email to ${adminEmail}:`, {
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
      userEmail: alertData.userEmail
    });
    throw error;
  }
}; 