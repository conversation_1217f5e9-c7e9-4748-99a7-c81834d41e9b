import { Request, Response } from 'express';
import { query } from '../db';
import { processInstagramPost } from '../services/instagramService';
import { deleteImageFromS3 } from '../services/s3Service';

// Define a type alias instead of extending the interface
type AuthenticatedRequest = Request & {
  user: {
    id: string;
    [key: string]: any;
  };
}

/**
 * Create a new post
 * @route POST /api/posts
 */
export const createPost = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user.id;
    const { platform } = req.body;

    if (!platform) {
      return res.status(400).json({ message: 'Platform is required' });
    }

    // Handle different platforms
    if (platform === 'Instagram') {
      const processedPost = await processInstagramPost(req.body, userId);
      return res.status(201).json(processedPost);
    } else {
      return res.status(400).json({ message: `Platform ${platform} is not supported yet` });
    }
  } catch (error: unknown) {
    console.error('Error creating post:', error);

    // <PERSON>le duplicate post error
    if (error && typeof error === 'object' && 'code' in error && error.code === '23505') { // PostgreSQL unique constraint violation
      return res.status(409).json({
        message: 'This post has already been saved',
        details: 'detail' in error ? error.detail : 'Duplicate post'
      });
    }

    return res.status(500).json({
      message: 'Server error',
      error: error && typeof error === 'object' && 'message' in error
        ? error.message
        : 'Unknown error'
    });
  }
};

/**
 * Get all posts for a user
 * @route GET /api/posts
 */
export const getPosts = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user.id;
    const { platform, limit = 50, offset = 0 } = req.query;

    let query_text = `
      SELECT
        p.id, p.original_post_id, p.platform, p.author_name, p.author_handle,
        p.author_avatar_url, p.content, p.timestamp, p.saved_at, p.permalink,
        pi.likes, pi.replies, pi.reposts
      FROM posts p
      LEFT JOIN post_interactions pi ON p.id = pi.post_id
      WHERE p.user_id = $1
    `;

    const queryParams: any[] = [userId];

    // Add platform filter if provided
    if (platform) {
      query_text += ` AND p.platform = $2`;
      queryParams.push(platform);
    }

    // Add sorting and pagination
    query_text += ` ORDER BY p.saved_at DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
    queryParams.push(limit, offset);

    const postsResult = await query(query_text, queryParams);

    // Get media for each post
    const posts = await Promise.all(postsResult.rows.map(async (post) => {
      const mediaResult = await query(
        `SELECT id, type, url, alt_text, is_primary
         FROM media_items
         WHERE post_id = $1
         ORDER BY is_primary DESC, id ASC`,
        [post.id]
      );

      return {
        ...post,
        media: mediaResult.rows.map((item: any) => ({
          type: item.type,
          url: item.url,
          alt: item.alt_text,
          isPrimary: item.is_primary
        })),
        interactions: {
          likes: post.likes || 0,
          replies: post.replies || 0,
          reposts: post.reposts || 0
        }
      };
    }));

    res.json(posts);
  } catch (error: unknown) {
    console.error('Error getting posts:', error);
    res.status(500).json({
      message: 'Server error',
      error: error && typeof error === 'object' && 'message' in error
        ? error.message
        : 'Unknown error'
    });
  }
};

/**
 * Get a single post by ID
 * @route GET /api/posts/:id
 */
export const getPostById = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user.id;
    const postId = req.params.id;

    // Get post data
    const postResult = await query(
      `SELECT
        p.id, p.original_post_id, p.platform, p.author_name, p.author_handle,
        p.author_avatar_url, p.content, p.timestamp, p.saved_at, p.permalink,
        pi.likes, pi.replies, pi.reposts
      FROM posts p
      LEFT JOIN post_interactions pi ON p.id = pi.post_id
      WHERE p.id = $1 AND p.user_id = $2`,
      [postId, userId]
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ message: 'Post not found' });
    }

    const post = postResult.rows[0];

    // Get media items
    const mediaResult = await query(
      `SELECT id, type, url, alt_text, is_primary
       FROM media_items
       WHERE post_id = $1
       ORDER BY is_primary DESC, id ASC`,
      [postId]
    );

    // Return the complete post
    res.json({
      ...post,
      media: mediaResult.rows.map((item: any) => ({
        type: item.type,
        url: item.url,
        alt: item.alt_text,
        isPrimary: item.is_primary
      })),
      interactions: {
        likes: post.likes || 0,
        replies: post.replies || 0,
        reposts: post.reposts || 0
      }
    });
  } catch (error: unknown) {
    console.error('Error getting post by ID:', error);
    res.status(500).json({
      message: 'Server error',
      error: error && typeof error === 'object' && 'message' in error
        ? error.message
        : 'Unknown error'
    });
  }
};

/**
 * Delete a post
 * @route DELETE /api/posts/:id
 */
export const deletePost = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user.id;
    const postId = req.params.id;

    // Get S3 keys for media items and avatar before deleting
    const mediaResult = await query(
      'SELECT s3_key FROM media_items WHERE post_id = $1',
      [postId]
    );

    const postResult = await query(
      'SELECT author_avatar_s3_key FROM posts WHERE id = $1 AND user_id = $2',
      [postId, userId]
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ message: 'Post not found' });
    }

    // Delete the post (cascade will delete media_items and post_interactions)
    await query('DELETE FROM posts WHERE id = $1 AND user_id = $2', [postId, userId]);

    // Delete media files from S3
    const s3DeletePromises = mediaResult.rows
      .filter((item: any) => item.s3_key)
      .map((item: any) => deleteImageFromS3(item.s3_key));

    // Delete avatar from S3 if it exists
    const avatarS3Key = postResult.rows[0].author_avatar_s3_key;
    if (avatarS3Key) {
      s3DeletePromises.push(deleteImageFromS3(avatarS3Key));
    }

    // Wait for all S3 deletions to complete
    await Promise.allSettled(s3DeletePromises);

    res.json({ message: 'Post deleted successfully' });
  } catch (error: unknown) {
    console.error('Error deleting post:', error);
    res.status(500).json({
      message: 'Server error',
      error: error && typeof error === 'object' && 'message' in error
        ? error.message
        : 'Unknown error'
    });
  }
};
