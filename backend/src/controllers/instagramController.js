const { processInstagramPost } = require('../services/instagramService');
const CloudPost = require('../models/CloudPost');
const { deleteImageFromS3 } = require('../services/s3Service');

/**
 * Create a new Instagram post with server-side image processing
 * @route POST /api/instagram/posts
 */
exports.createPost = async (req, res) => {
  try {
    const userId = req.user.id;
    const postData = req.body;

    if (!postData.platform || postData.platform !== 'Instagram') {
      return res.status(400).json({ message: 'Invalid platform. Only Instagram is supported.' });
    }

    // Process the Instagram post (fetch images, upload to S3, save to MongoDB)
    const processedPost = await processInstagramPost(postData, userId);
    
    return res.status(201).json(processedPost);
  } catch (error) {
    console.error('Error creating Instagram post:', error);
    
    // Handle duplicate post error
    if (error.code === 11000) {
      return res.status(409).json({ 
        message: 'This post has already been saved',
        details: error.keyValue
      });
    }
    
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get all Instagram posts for a user
 * @route GET /api/instagram/posts
 */
exports.getPosts = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 50, offset = 0 } = req.query;
    
    // Find all Instagram posts for the user
    const posts = await CloudPost.find({ 
      userId, 
      platform: 'Instagram' 
    })
    .sort({ savedAt: -1 })
    .skip(Number(offset))
    .limit(Number(limit));
    
    res.json(posts);
  } catch (error) {
    console.error('Error getting Instagram posts:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get a single Instagram post by ID
 * @route GET /api/instagram/posts/:id
 */
exports.getPostById = async (req, res) => {
  try {
    const userId = req.user.id;
    const postId = req.params.id;
    
    const post = await CloudPost.findOne({ 
      _id: postId, 
      userId, 
      platform: 'Instagram' 
    });
    
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }
    
    res.json(post);
  } catch (error) {
    console.error('Error getting Instagram post by ID:', error);
    
    if (error.kind === 'ObjectId') {
      return res.status(400).json({ message: 'Invalid post ID format' });
    }
    
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Delete an Instagram post
 * @route DELETE /api/instagram/posts/:id
 */
exports.deletePost = async (req, res) => {
  try {
    const userId = req.user.id;
    const postId = req.params.id;
    
    // Find the post to get S3 keys before deleting
    const post = await CloudPost.findOne({ _id: postId, userId, platform: 'Instagram' });
    
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }
    
    // Collect S3 keys for deletion
    const s3Keys = [];
    
    // Add media S3 keys
    if (post.media && post.media.length > 0) {
      post.media.forEach(item => {
        if (item.filePath) {
          s3Keys.push(item.filePath);
        }
      });
    }
    
    // Delete the post from MongoDB
    await CloudPost.deleteOne({ _id: postId, userId });
    
    // Delete files from S3
    const s3DeletePromises = s3Keys.map(key => deleteImageFromS3(key));
    await Promise.allSettled(s3DeletePromises);
    
    res.json({ message: 'Post deleted successfully' });
  } catch (error) {
    console.error('Error deleting Instagram post:', error);
    
    if (error.kind === 'ObjectId') {
      return res.status(400).json({ message: 'Invalid post ID format' });
    }
    
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
