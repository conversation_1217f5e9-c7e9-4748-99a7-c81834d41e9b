import dotenv from 'dotenv';
import connectDB from './config/db';
import { getQueueStats, cleanQueue, closeQueue } from './services/queueService';

// Load environment variables
dotenv.config();

// Connect to MongoDB
connectDB();

// Import and start the image processing worker
import './services/imageProcessingWorker';

console.log('[Worker] Starting Notely Image Processing Worker...');

// Log queue stats periodically
setInterval(async () => {
  try {
    const stats = await getQueueStats();
    console.log('[Worker] Queue Stats:', stats);
  } catch (error) {
    console.error('[Worker] Error getting queue stats:', error);
  }
}, 60000); // Every minute

// Clean up old jobs periodically
setInterval(async () => {
  try {
    await cleanQueue();
    console.log('[Worker] Cleaned up old jobs');
  } catch (error) {
    console.error('[Worker] Error cleaning queue:', error);
  }
}, 60 * 60 * 1000); // Every hour

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('[Worker] Received SIGTERM, shutting down gracefully...');
  await closeQueue();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('[Worker] Received SIGINT, shutting down gracefully...');
  await closeQueue();
  process.exit(0);
});

console.log('[Worker] Image Processing Worker is running and ready to process jobs');
