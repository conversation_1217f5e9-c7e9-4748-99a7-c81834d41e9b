import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Create a PostgreSQL connection pool
// Disabled for now since we're not using PostgreSQL yet
let pool: Pool;

// Only create the pool if DATABASE_URL is defined and we explicitly want to use PostgreSQL
if (process.env.DATABASE_URL && process.env.USE_POSTGRES === 'true') {
  pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  // Test the database connection
  pool.query('SELECT NOW()', (err: Error | null, res: any) => {
    if (err) {
      console.error('Database connection error:', err.message);
    } else {
      console.log('Database connected successfully at:', res.rows[0].now);
    }
  });
} else {
  // Create a dummy pool that logs errors when used
  pool = {} as Pool;
  console.log('PostgreSQL is disabled. Set USE_POSTGRES=true to enable it.');
}

// Helper function for running queries
export const query = async (text: string, params?: any[]) => {
  // Check if PostgreSQL is enabled
  if (!process.env.DATABASE_URL || process.env.USE_POSTGRES !== 'true') {
    console.error('PostgreSQL is disabled. Cannot execute query:', text);
    throw new Error('PostgreSQL is disabled. Set USE_POSTGRES=true to enable it.');
  }

  try {
    if (!pool.query) {
      throw new Error('PostgreSQL pool is not properly initialized');
    }

    const start = Date.now();
    const res = await pool.query(text, params);
    const duration = Date.now() - start;

    if (process.env.NODE_ENV !== 'production') {
      console.log('Executed query:', { text, duration, rows: res.rowCount });
    }

    return res;
  } catch (error) {
    console.error('Query error:', error);
    throw error;
  }
};

// Export the pool for transaction support
export default pool;
