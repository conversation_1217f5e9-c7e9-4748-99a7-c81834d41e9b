import mongoose from 'mongoose';

const connectDB = async () => {
  const mongoUri = process.env.MONGO_URI;

  if (!mongoUri) {
    console.error('Error: MONGO_URI is not defined in the environment variables.');
    process.exit(1); // Exit process with failure
  }

  try {
    // Connect to MongoDB with options
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000, // Timeout after 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
    });
    console.log('[database]: MongoDB Connected Successfully');

    // Set up event listeners
    mongoose.connection.on('error', (err) => {
      console.error(`[database]: MongoDB connection error: ${err}`);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('[database]: MongoDB disconnected.');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('[database]: MongoDB reconnected.');
    });

  } catch (err: any) {
    console.error(`[database]: MongoDB Connection Failed: ${err.message}`);
    console.error('Please check:');
    console.error('1. Your MongoDB Atlas connection string is correct');
    console.error('2. The IP address of your deployment is whitelisted in MongoDB Atlas');
    console.error('3. Your MongoDB Atlas cluster is running and accessible');

    // In production, we might want to retry or continue with limited functionality
    if (process.env.NODE_ENV === 'production') {
      console.error('Running in production mode with limited functionality due to database connection failure');
    } else {
      // In development, exit the process
      process.exit(1);
    }
  }
};

export default connectDB;