import passport from 'passport';
import { Strategy as LocalStrategy } from 'passport-local';
import { Strategy as GoogleStrategy, Profile, VerifyCallback, GoogleCallbackParameters } from 'passport-google-oauth20';
import { Strategy as JwtStrategy, ExtractJwt, StrategyOptions } from 'passport-jwt';
import mongoose from 'mongoose';
import User, { IUser } from '../models/User'; // Adjust path as necessary
import dotenv from 'dotenv';
import { Request } from 'express'; // Import Request for typing req in callback

dotenv.config(); // Load environment variables

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  console.error('FATAL ERROR: JWT_SECRET is not defined.');
  process.exit(1);
}

// --- Local Strategy (Email/Password) ---
passport.use(
  new LocalStrategy(
    { usernameField: 'email' }, // We use email as the username field
    async (email, password, done) => {
      try {
        const user = await User.findOne({ email: email.toLowerCase() });

        if (!user) {
          return done(null, false, { message: `Email ${email} not found.` });
        }
        
        // User found, now check password
        // First, ensure the user object has the comparePassword method (it should for local auth users)
        if (typeof user.comparePassword !== 'function') {
          // This case should ideally not happen for users who registered with a password
          // but it's a safeguard. It might mean the user signed up via OAuth
          // and is trying to log in with a password they never set.
          return done(null, false, { message: 'Password login not available for this account. Try social login.' });
        }

        // Also check if user.password field exists, as comparePassword might exist but password field is empty for OAuth users
        if (!user.password) {
           return done(null, false, { message: 'Account uses social login or password not set.' });
        }
        
        const isMatch = await user.comparePassword(password);
        if (isMatch) {
          return done(null, user);
        } else {
          return done(null, false, { message: 'Invalid email or password.' });
        }
      } catch (err) {
        return done(err);
      }
    }
  )
);

// --- Google OAuth 2.0 Strategy ---
const googleClientID = process.env.GOOGLE_CLIENT_ID;
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET;
const googleCallbackURL = process.env.GOOGLE_CALLBACK_URL || '/auth/google/callback'; // Default callback URL

if (googleClientID && googleClientSecret) {
    // Main Google strategy for extension/popup
    passport.use(
      new GoogleStrategy(
        {
          clientID: googleClientID,
          clientSecret: googleClientSecret,
          callbackURL: googleCallbackURL,
          passReqToCallback: true, // Required by the types when other options are present
        },
        // Verify callback signature updated to include 'params'
        async (req: Request, accessToken: string, refreshToken: string, params: GoogleCallbackParameters, profile: Profile, done: VerifyCallback) => {
          // We may not use `req` or `params` here, but their presence satisfies the type signature.
          // console.log("Google Profile:", profile);
          // console.log("Google Params:", params);

          const googleUserPayload: Partial<IUser> = {
            googleId: profile.id,
            displayName: profile.displayName || profile.name?.givenName || 'User' + profile.id,
            name: profile.displayName || profile.name?.givenName || 'User' + profile.id,
            email: profile.emails && profile.emails[0] ? profile.emails[0].value.toLowerCase() : undefined,
          };

          // The `done` callback expects (error: any, user?: Express.User | false | null, info?: IVerifyOptions) => void;
          // Our googleUserPayload is Partial<IUser>. We need to ensure this is compatible or cast appropriately if an issue arises here.
          // For now, assume Partial<IUser> is acceptable by VerifyCallback if Express.User is augmented to IUser.
          return done(null, googleUserPayload as any); // Cast to any for now to bypass deeper type issue with done, will revisit if it causes problems.
        }
      )
    );

    // Web Google strategy with different callback URL
    passport.use('google-web',
      new GoogleStrategy(
        {
          clientID: googleClientID,
          clientSecret: googleClientSecret,
          callbackURL: '/auth/google/callback/web',
          passReqToCallback: true,
        },
        // Same verify callback as main strategy
        async (req: Request, accessToken: string, refreshToken: string, params: GoogleCallbackParameters, profile: Profile, done: VerifyCallback) => {
          const googleUserPayload: Partial<IUser> = {
            googleId: profile.id,
            displayName: profile.displayName || profile.name?.givenName || 'User' + profile.id,
            name: profile.displayName || profile.name?.givenName || 'User' + profile.id,
            email: profile.emails && profile.emails[0] ? profile.emails[0].value.toLowerCase() : undefined,
          };

          return done(null, googleUserPayload as any);
        }
      )
    );
} else {
    console.warn('[passport]: Google OAuth strategy not configured. Missing GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET environment variables.');
}

// --- JWT Strategy (For verifying tokens on protected routes) ---
const jwtOptions: StrategyOptions = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(), // Extract token from Authorization: Bearer <token>
  secretOrKey: JWT_SECRET, // Use the secret from .env
};

passport.use(
  new JwtStrategy(jwtOptions, async (payload, done) => {
    // console.log('JWT Payload:', payload);
    try {
      // Find the user specified in token
      const user = await User.findById(payload.id);

      if (user) {
        // If user is found, return the user object
        return done(null, user);
      } else {
        // If user is not found
        return done(null, false);
      }
    } catch (err) {
      return done(err, false);
    }
  })
);

// Note: We are using JWT, so explicit serialize/deserialize might not be strictly necessary
/*
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (err) {
    done(err);
  }
});
*/

export default passport; 