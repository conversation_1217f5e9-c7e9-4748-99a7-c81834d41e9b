import passport from 'passport';
import { Request, Response, NextFunction } from 'express';
import { IUser } from '../models/User'; // Adjust path as needed
// No longer need firebase-admin here as we are removing firebaseAuth middleware
// import admin from 'firebase-admin'; 
import User from '../models/User';

// Augment Express's built-in User interface from @types/passport
// This tells Passport that when it sets req.user, it should be of type IUser.
declare global {
  namespace Express {
    // eslint-disable-next-line @typescript-eslint/no-empty-interface
    interface User extends IUser {}

    interface Request {
      user?: User; // Now Express.User (which is IUser) is used by Passport
    }
  }
}

// Middleware to protect routes using JWT strategy (Passport)
export const protect = (req: Request, res: Response, next: NextFunction): void => {
  passport.authenticate('jwt', { session: false }, (err: any, user: IUser | false, info: any) => {
    if (err) {
      console.error('JWT Auth Error:', err);
      return res.status(500).json({ message: 'Internal server error during authentication' });
    }
    if (!user) {
      let message = 'Unauthorized - Invalid or expired token';
      if (info instanceof Error) {
        message = info.message; // e.g., 'jwt expired', 'invalid signature'
      } else if (info && typeof info.message === 'string') {
        message = info.message; // Other messages from passport-jwt
      }
      return res.status(401).json({ message });
    }
    // req.user is now understood by TypeScript to be IUser due to the augmentation
    req.user = user;
    next();
  })(req, res, next);
};

// Middleware to check if user is admin (by email from IUser)
export const isAdmin = (req: Request, res: Response, next: NextFunction): void => {
  const adminEmails = (process.env.ADMIN_EMAILS || '').split(',').map(e => e.trim().toLowerCase()).filter(Boolean);
  
  // req.user should be IUser from 'protect' middleware
  if (req.user && req.user.email) { // Now req.user.email should be type-safe
    const userEmail = req.user.email.toLowerCase();
    if (adminEmails.includes(userEmail)) {
      next();
      return;
    }
  }
  res.status(403).json({ message: 'Admin access required' });
};

// You can add more middleware here if needed (e.g., check for specific roles) 