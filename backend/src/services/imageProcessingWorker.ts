import { Job } from 'bull';
import { imageProcessingQueue, ImageProcessingJobData } from './queueService';
import { uploadBufferToS3, fetchImageAsBuffer } from './s3Service';
import CloudPost from '../models/CloudPost';

/**
 * Image Processing Worker
 * 
 * This worker processes queued image jobs:
 * 1. Downloads images from original URLs
 * 2. Uploads them to S3
 * 3. Updates the post record with S3 URLs
 */

// Process image processing jobs
imageProcessingQueue.process('process-images', async (job: Job<ImageProcessingJobData>) => {
  const { postId, userId, platform, mediaItems, originalPostData } = job.data;
  
  console.log(`[Worker] Starting image processing for post ${postId} (${platform}) with ${mediaItems.length} media items`);
  
  try {
    // Update post status to processing
    await CloudPost.findByIdAndUpdate(postId, {
      imageProcessingStatus: 'processing',
      imageProcessingJobId: job.id,
      imageProcessingError: null,
    });

    const processedMediaItems = [];
    const errors = [];

    // Process each media item
    for (let i = 0; i < mediaItems.length; i++) {
      const mediaItem = mediaItems[i];
      
      try {
        console.log(`[Worker] Processing media item ${i + 1}/${mediaItems.length} for post ${postId}: ${mediaItem.url}`);
        
        // Download the image
        const { buffer, contentType } = await fetchImageAsBuffer(mediaItem.url);
        
        // Upload to S3
        const uploadResult = await uploadBufferToS3(
          buffer,
          contentType,
          userId,
          'post',
          mediaItem.url,
          platform
        );
        
        // Create processed media item
        processedMediaItems.push({
          type: mediaItem.type,
          url: uploadResult.url,
          filePath: uploadResult.key,
          alt: mediaItem.alt,
          width: mediaItem.width,
          height: mediaItem.height,
        });
        
        console.log(`[Worker] Successfully processed media item ${i + 1}/${mediaItems.length} for post ${postId}: ${uploadResult.url}`);
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`[Worker] Failed to process media item ${i + 1}/${mediaItems.length} for post ${postId}:`, errorMessage);
        
        errors.push({
          index: i,
          url: mediaItem.url,
          error: errorMessage,
        });
        
        // Keep original URL as fallback
        processedMediaItems.push({
          type: mediaItem.type,
          url: mediaItem.url, // Keep original URL
          alt: mediaItem.alt,
          width: mediaItem.width,
          height: mediaItem.height,
          // No filePath since S3 upload failed
        });
      }
    }

    // Update post with processed media items
    const updateData: any = {
      media: processedMediaItems,
      imageProcessedAt: new Date(),
    };

    if (errors.length === 0) {
      // All images processed successfully
      updateData.imageProcessingStatus = 'completed';
      updateData.imageProcessingError = null;
      console.log(`[Worker] Successfully processed all ${mediaItems.length} media items for post ${postId}`);
    } else if (errors.length === mediaItems.length) {
      // All images failed
      updateData.imageProcessingStatus = 'failed';
      updateData.imageProcessingError = `All ${errors.length} media items failed to process`;
      console.error(`[Worker] Failed to process all ${errors.length} media items for post ${postId}`);
    } else {
      // Partial success
      updateData.imageProcessingStatus = 'completed';
      updateData.imageProcessingError = `${errors.length}/${mediaItems.length} media items failed to process`;
      console.warn(`[Worker] Partially processed media for post ${postId}: ${processedMediaItems.length - errors.length}/${mediaItems.length} successful`);
    }

    await CloudPost.findByIdAndUpdate(postId, updateData);

    // Return job result
    return {
      postId,
      platform,
      totalItems: mediaItems.length,
      successfulItems: processedMediaItems.length - errors.length,
      failedItems: errors.length,
      errors: errors.length > 0 ? errors : undefined,
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`[Worker] Critical error processing images for post ${postId}:`, errorMessage);
    
    // Update post status to failed
    await CloudPost.findByIdAndUpdate(postId, {
      imageProcessingStatus: 'failed',
      imageProcessingError: errorMessage,
      imageProcessedAt: new Date(),
    });
    
    throw error; // Re-throw to mark job as failed
  }
});

// Worker event handlers
imageProcessingQueue.on('completed', (job, result) => {
  console.log(`[Worker] Job ${job.id} completed:`, result);
});

imageProcessingQueue.on('failed', (job, err) => {
  console.error(`[Worker] Job ${job.id} failed:`, err.message);
});

imageProcessingQueue.on('stalled', (job) => {
  console.warn(`[Worker] Job ${job.id} stalled`);
});

console.log('[Worker] Image processing worker started and ready to process jobs');

export { imageProcessingQueue };
