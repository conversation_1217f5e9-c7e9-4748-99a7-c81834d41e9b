import { ICloudPost } from '../models/CloudPost';

const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

interface SearchResult {
  id: string;
  content?: string;
  authorName?: string;
  platform: string;
  savedAt: string;
  categories?: string[];
  tags?: string[];
  permalink: string;
  media?: any[];
}

interface SearchSummary {
  overview: string;
  keyFindings: string[];
  relatedTopics: string[];
  suggestedActions: string[];
  totalPosts: number;
}

/**
 * Generate an AI summary of search results
 */
export async function generateSearchSummary(
  query: string,
  searchResults: SearchResult[]
): Promise<SearchSummary> {
  if (!OPENAI_API_KEY) {
    console.warn('OpenAI API key not found for search summary generation');
    return {
      overview: 'Search completed successfully.',
      keyFindings: ['API key not configured for AI summaries'],
      relatedTopics: [],
      suggestedActions: ['Configure OpenAI API key to enable AI summaries'],
      totalPosts: searchResults.length
    };
  }

  if (searchResults.length === 0) {
    return {
      overview: `No relevant posts found for "${query}".`,
      keyFindings: ['No matching content in your saved posts'],
      relatedTopics: [],
      suggestedActions: ['Try a different search term', 'Save more posts to expand your searchable content'],
      totalPosts: 0
    };
  }

  try {
    // Prepare content for AI analysis
    const postsContent = searchResults.slice(0, 5).map((result, index) => {
      const content = result.content || 'No content available';
      const platform = result.platform;
      const author = result.authorName || 'Unknown author';
      const categories = result.categories?.join(', ') || 'Uncategorized';
      const tags = result.tags?.join(', ') || 'No tags';

      return `Post ${index + 1}:
Platform: ${platform}
Author: ${author}
Categories: ${categories}
Tags: ${tags}
Content: ${content.substring(0, 300)}${content.length > 300 ? '...' : ''}
---`;
    }).join('\n\n');

    const prompt = `You are analyzing search results from a user's saved social media posts. The user searched for: "${query}"

Here are the most relevant posts found:

${postsContent}

Please provide a comprehensive analysis in the following JSON format:
{
  "overview": "A 2-3 sentence summary of what the search results reveal about the user's interests related to this query",
  "keyFindings": ["2-3 specific insights or patterns from the content", "Each finding should be actionable or interesting"],
  "relatedTopics": ["3-4 related topics the user might want to explore", "Based on the content themes"],
  "suggestedActions": ["2-3 specific actions the user could take", "Based on the content and query"]
}

Focus on being helpful, specific, and actionable. Keep responses concise but insightful.`;

    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert content analyst specializing in social media content analysis and user behavior insights. Always respond with valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from OpenAI API');
    }

    // Parse the JSON response
    const aiSummary = JSON.parse(content);

    return {
      overview: aiSummary.overview || 'Analysis completed.',
      keyFindings: aiSummary.keyFindings || ['Content analysis completed'],
      relatedTopics: aiSummary.relatedTopics || [],
      suggestedActions: aiSummary.suggestedActions || ['Explore related content'],
      totalPosts: searchResults.length
    };

  } catch (error) {
    console.error('Error generating search summary:', error);

    // Fallback summary
    const platforms = [...new Set(searchResults.map(r => r.platform))];
    const categories = [...new Set(searchResults.flatMap(r => r.categories || []))];

    return {
      overview: `Found ${searchResults.length} posts related to "${query}" across ${platforms.join(', ')}.`,
      keyFindings: [
        `Content spans ${platforms.length} platform${platforms.length > 1 ? 's' : ''}`,
        categories.length > 0 ? `Main categories: ${categories.slice(0, 3).join(', ')}` : 'Various topics covered'
      ],
      relatedTopics: categories.slice(0, 4),
      suggestedActions: [
        'Review the detailed results below',
        'Refine your search with more specific terms'
      ],
      totalPosts: searchResults.length
    };
  }
}

/**
 * Generate search suggestions based on user's content
 */
export async function generateSearchSuggestions(
  userPosts: ICloudPost[],
  limit: number = 5
): Promise<string[]> {
  if (!OPENAI_API_KEY || userPosts.length === 0) {
    return [
      'AI prompts and tools',
      'productivity tips',
      'design inspiration',
      'technology trends',
      'business insights'
    ];
  }

  try {
    // Sample recent posts for analysis
    const recentPosts = userPosts
      .slice(0, 20)
      .map(post => ({
        content: (post.content || '').substring(0, 200),
        categories: post.categories,
        tags: post.tags,
        platform: post.platform
      }));

    const prompt = `Based on this user's recently saved social media posts, suggest ${limit} natural language search queries they might find useful. Focus on the main themes and topics in their content.

Recent posts sample:
${recentPosts.map((post, i) => `${i + 1}. ${post.content} (${post.platform})`).join('\n')}

Return only a JSON array of search query strings, like:
["query 1", "query 2", "query 3"]

Make queries specific and actionable, reflecting the user's actual interests.`;

    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 300,
        temperature: 0.8,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (content) {
      const suggestions = JSON.parse(content);
      return Array.isArray(suggestions) ? suggestions.slice(0, limit) : [];
    }
  } catch (error) {
    console.error('Error generating search suggestions:', error);
  }

  // Fallback suggestions
  return [
    'AI and automation tools',
    'productivity and workflow tips',
    'design and creativity',
    'business and entrepreneurship',
    'technology trends'
  ];
}
