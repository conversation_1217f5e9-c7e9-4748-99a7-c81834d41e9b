import axios from 'axios';
import { fetchAndUploadImage } from './s3Service';
import { query } from '../db';

interface InstagramPostData {
  originalPostId: string;
  authorName: string;
  authorHandle?: string;
  authorAvatar?: string;
  content?: string;
  timestamp?: string;
  permalink: string;
  media?: Array<{
    type: 'image' | 'video';
    url: string;
    alt?: string;
  }>;
  interactions?: {
    likes?: number;
    comments?: number;
  };
}

interface ProcessedPostData {
  id: number;
  originalPostId: string;
  authorName: string;
  authorHandle?: string;
  authorAvatarUrl: string;
  content?: string;
  timestamp?: string;
  permalink: string;
  media: Array<{
    type: 'image' | 'video';
    url: string;
    alt?: string;
    isPrimary: boolean;
  }>;
  interactions?: {
    likes?: number;
    replies?: number;
    reposts?: number;
  };
}

/**
 * Process and save an Instagram post
 * This function handles:
 * 1. Fetching and uploading the author avatar to S3
 * 2. Fetching and uploading post images to S3
 * 3. Saving all metadata to PostgreSQL
 */
export async function processInstagramPost(
  postData: InstagramPostData,
  userId: string
): Promise<ProcessedPostData> {
  try {
    console.log(`Processing Instagram post: ${postData.originalPostId}`);
    
    // 1. Upload author avatar to S3 if available
    let authorAvatarUrl = '';
    let authorAvatarS3Key = '';
    
    if (postData.authorAvatar) {
      try {
        console.log(`Fetching and uploading author avatar: ${postData.authorAvatar}`);
        const avatarResult = await fetchAndUploadImage(postData.authorAvatar, userId, 'avatar');
        authorAvatarUrl = avatarResult.url;
        authorAvatarS3Key = avatarResult.key;
        console.log(`Successfully uploaded author avatar to S3: ${authorAvatarUrl}`);
      } catch (avatarError) {
        console.error('Error uploading author avatar:', avatarError);
        // Continue with the process even if avatar upload fails
      }
    }
    
    // 2. Begin a database transaction
    const client = await query('BEGIN');
    
    try {
      // 3. Insert the post into the database
      const postResult = await query(
        `INSERT INTO posts (
          user_id, original_post_id, platform, author_name, author_handle, 
          author_avatar_url, author_avatar_s3_key, content, timestamp, permalink
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) 
        RETURNING id`,
        [
          userId,
          postData.originalPostId,
          'Instagram',
          postData.authorName,
          postData.authorHandle || null,
          authorAvatarUrl,
          authorAvatarS3Key,
          postData.content || null,
          postData.timestamp || null,
          postData.permalink
        ]
      );
      
      const postId = postResult.rows[0].id;
      console.log(`Created post record with ID: ${postId}`);
      
      // 4. Insert interactions if available
      if (postData.interactions) {
        await query(
          `INSERT INTO post_interactions (post_id, likes, replies) 
           VALUES ($1, $2, $3)`,
          [
            postId,
            postData.interactions.likes || 0,
            postData.interactions.comments || 0
          ]
        );
      }
      
      // 5. Process and upload media items
      const processedMedia = [];
      
      if (postData.media && postData.media.length > 0) {
        console.log(`Processing ${postData.media.length} media items`);
        
        for (let i = 0; i < postData.media.length; i++) {
          const mediaItem = postData.media[i];
          const isPrimary = i === 0; // First media item is primary
          
          try {
            // Upload the media to S3
            console.log(`Fetching and uploading media: ${mediaItem.url}`);
            const mediaResult = await fetchAndUploadImage(mediaItem.url, userId, 'post');
            
            // Insert the media item into the database
            await query(
              `INSERT INTO media_items (
                post_id, type, url, s3_key, alt_text, is_primary
              ) VALUES ($1, $2, $3, $4, $5, $6)`,
              [
                postId,
                mediaItem.type,
                mediaResult.url,
                mediaResult.key,
                mediaItem.alt || null,
                isPrimary
              ]
            );
            
            processedMedia.push({
              type: mediaItem.type,
              url: mediaResult.url,
              alt: mediaItem.alt,
              isPrimary
            });
            
            console.log(`Successfully processed media item: ${mediaResult.url}`);
          } catch (mediaError) {
            console.error(`Error processing media item ${i}:`, mediaError);
            // Continue with other media items even if one fails
          }
        }
      }
      
      // 6. Commit the transaction
      await query('COMMIT');
      
      // 7. Return the processed post data
      return {
        id: postId,
        originalPostId: postData.originalPostId,
        authorName: postData.authorName,
        authorHandle: postData.authorHandle,
        authorAvatarUrl,
        content: postData.content,
        timestamp: postData.timestamp,
        permalink: postData.permalink,
        media: processedMedia,
        interactions: {
          likes: postData.interactions?.likes,
          replies: postData.interactions?.comments,
          reposts: 0
        }
      };
    } catch (dbError) {
      // Rollback the transaction if anything fails
      await query('ROLLBACK');
      console.error('Database error during Instagram post processing:', dbError);
      throw dbError;
    }
  } catch (error) {
    console.error('Error processing Instagram post:', error);
    throw error;
  }
}
