const { fetchAndUploadImage } = require('./s3Service');
const CloudPost = require('../models/CloudPost');

/**
 * Process and save an Instagram post
 * This function handles:
 * 1. Fetching and uploading the author avatar to S3
 * 2. Fetching and uploading post images to S3
 * 3. Saving all metadata to MongoDB
 */
async function processInstagramPost(postData, userId) {
  try {
    console.log(`Processing Instagram post: ${postData.originalPostId}`);
    
    // 1. Upload author avatar to S3 if available
    let authorAvatarUrl = '';
    let authorAvatarS3Key = '';
    
    if (postData.authorAvatar) {
      try {
        console.log(`Fetching and uploading author avatar: ${postData.authorAvatar}`);
        const avatarResult = await fetchAndUploadImage(postData.authorAvatar, userId, 'avatar');
        authorAvatarUrl = avatarResult.url;
        authorAvatarS3Key = avatarResult.key;
        console.log(`Successfully uploaded author avatar to S3: ${authorAvatarUrl}`);
      } catch (avatarError) {
        console.error('Error uploading author avatar:', avatarError);
        // Continue with the process even if avatar upload fails
      }
    }
    
    // 2. Process and upload media items
    const processedMedia = [];
    
    if (postData.media && postData.media.length > 0) {
      console.log(`Processing ${postData.media.length} media items`);
      
      for (let i = 0; i < postData.media.length; i++) {
        const mediaItem = postData.media[i];
        const isPrimary = i === 0; // First media item is primary
        
        try {
          // Upload the media to S3
          console.log(`Fetching and uploading media: ${mediaItem.url}`);
          const mediaResult = await fetchAndUploadImage(mediaItem.url, userId, 'post');
          
          processedMedia.push({
            type: mediaItem.type || 'image',
            url: mediaResult.url,
            filePath: mediaResult.key,
            alt: mediaItem.alt || null,
            isPrimary
          });
          
          console.log(`Successfully processed media item: ${mediaResult.url}`);
        } catch (mediaError) {
          console.error(`Error processing media item ${i}:`, mediaError);
          // Continue with other media items even if one fails
        }
      }
    }
    
    // 3. Create the post object for MongoDB
    const newPostData = {
      userId,
      originalPostId: postData.originalPostId,
      platform: 'Instagram',
      authorName: postData.authorName,
      authorHandle: postData.authorHandle || null,
      authorAvatar: authorAvatarUrl || postData.authorAvatar, // Use S3 URL if available, otherwise original URL
      authorImage: authorAvatarUrl || postData.authorAvatar, // For backward compatibility
      content: postData.content || null,
      timestamp: postData.timestamp || null,
      permalink: postData.permalink,
      savedAt: new Date(),
      media: processedMedia,
      interactions: {
        likes: postData.interactions?.likes || 0,
        replies: postData.interactions?.comments || 0,
        reposts: 0
      }
    };
    
    // 4. Save to MongoDB
    const createdPost = await CloudPost.create(newPostData);
    console.log(`Created post record with ID: ${createdPost._id}`);
    
    // 5. Return the processed post data
    return {
      id: createdPost._id,
      originalPostId: postData.originalPostId,
      authorName: postData.authorName,
      authorHandle: postData.authorHandle,
      authorAvatar: authorAvatarUrl || postData.authorAvatar,
      content: postData.content,
      timestamp: postData.timestamp,
      permalink: postData.permalink,
      media: processedMedia.map(item => ({
        type: item.type,
        url: item.url,
        alt: item.alt,
        isPrimary: item.isPrimary
      })),
      interactions: {
        likes: postData.interactions?.likes || 0,
        replies: postData.interactions?.comments || 0,
        reposts: 0
      }
    };
  } catch (error) {
    console.error('Error processing Instagram post:', error);
    throw error;
  }
}

module.exports = {
  processInstagramPost
};
