import User, { IUser } from '../models/User';
import AIUsageLog from '../models/AIUsageLog';
import StorageUsage from '../models/StorageUsage';
import { sendAbuseAlertEmail } from '../utils/email/brevo';

interface AbuseRule {
  name: string;
  description: string;
  check: (user: IUser) => Promise<boolean>;
}

interface AbuseCheckResult {
  user: IUser;
  violations: string[];
  shouldFlag: boolean;
}

export class AbuseDetectionService {
  private static readonly RULES: AbuseRule[] = [
    {
      name: 'excessive_ai_usage',
      description: 'AI token usage > 100,000 in 24 hours',
      check: async (user: IUser) => {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        
        const tokenUsage = await AIUsageLog.aggregate([
          {
            $match: {
              userId: user._id.toString(),
              date: { $gte: oneDayAgo },
            },
          },
          {
            $group: {
              _id: null,
              totalTokens: { $sum: '$tokensUsed' },
            },
          },
        ]);

        const totalTokens = tokenUsage[0]?.totalTokens || 0;
        return totalTokens > 100000;
      },
    },
    {
      name: 'excessive_storage_increase',
      description: 'Storage usage increased by > 500MB in 24 hours',
      check: async (user: IUser) => {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        
        // Get storage usage from 24 hours ago
        const oldUsage = await StorageUsage.findOne({
          userId: user._id.toString(),
          date: { $lte: oneDayAgo },
        }).sort({ date: -1 });

        if (!oldUsage) return false;

        const currentStorageMB = user.storageUsed / (1024 * 1024); // Convert bytes to MB
        const storageIncreaseMB = currentStorageMB - oldUsage.mbUsed;
        const fiveHundredMB = 500; // 500MB
        
        return storageIncreaseMB > fiveHundredMB;
      },
    },
    {
      name: 'excessive_post_creation',
      description: 'More than 50 posts created in 1 hour',
      check: async (user: IUser) => {
        // This would require a posts collection or creation tracking
        // For now, we'll check if there's a CloudPost model available
        try {
          const { default: CloudPost } = await import('../models/CloudPost');
          const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
          
          const postCount = await CloudPost.countDocuments({
            userId: user._id.toString(),
            createdAt: { $gte: oneHourAgo },
          });

          return postCount > 50;
        } catch (error) {
          // CloudPost model might not exist or be available
          console.warn('CloudPost model not available for post creation check');
          return false;
        }
      },
    },
  ];

  /**
   * Check a single user for abuse violations
   */
  static async checkUser(user: IUser): Promise<AbuseCheckResult> {
    const violations: string[] = [];

    for (const rule of this.RULES) {
      try {
        const violates = await rule.check(user);
        if (violates) {
          violations.push(rule.description);
        }
      } catch (error) {
        console.error(`Error checking rule ${rule.name} for user ${user.email}:`, error);
      }
    }

    return {
      user,
      violations,
      shouldFlag: violations.length > 0,
    };
  }

  /**
   * Check all users for abuse violations
   */
  static async checkAllUsers(): Promise<AbuseCheckResult[]> {
    console.log('🔍 Starting abuse detection check for all users...');
    
    try {
      // Get all users that are not already flagged or were flagged more than 24 hours ago
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const users = await User.find({
        $or: [
          { isFlagged: false },
          { isFlagged: true, flaggedAt: { $lte: twentyFourHoursAgo } },
        ],
      });

      console.log(`📊 Checking ${users.length} users for abuse violations...`);

      const results: AbuseCheckResult[] = [];
      
      // Process users in batches to avoid overwhelming the database
      const batchSize = 10;
      for (let i = 0; i < users.length; i += batchSize) {
        const batch = users.slice(i, i + batchSize);
        const batchResults = await Promise.all(
          batch.map(user => this.checkUser(user))
        );
        results.push(...batchResults);
        
        // Small delay between batches
        if (i + batchSize < users.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const flaggedResults = results.filter(result => result.shouldFlag);
      
      if (flaggedResults.length > 0) {
        console.log(`🚨 Found ${flaggedResults.length} users with abuse violations`);
        
        // Flag users and send alerts
        await Promise.all(flaggedResults.map(result => this.flagUser(result)));
      } else {
        console.log('✅ No abuse violations detected');
      }

      return results;
    } catch (error) {
      console.error('❌ Error during abuse detection check:', error);
      throw error;
    }
  }

  /**
   * Flag a user based on abuse detection results
   */
  static async flagUser(result: AbuseCheckResult): Promise<void> {
    const { user, violations } = result;
    const reason = violations.join('; ');

    try {
      await User.findByIdAndUpdate(user._id, {
        isFlagged: true,
        lastFlaggedReason: reason,
        flaggedAt: new Date(),
      });

      console.log(`🚩 Flagged user ${user.email} for: ${reason}`);

      // Send alert email to admins
      await this.sendAbuseAlert(user, violations);
    } catch (error) {
      console.error(`Error flagging user ${user.email}:`, error);
    }
  }

  /**
   * Manually flag a user
   */
  static async manualFlag(userId: string, reason: string, adminEmail: string): Promise<void> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        {
          isFlagged: true,
          lastFlaggedReason: `Manual flag by ${adminEmail}: ${reason}`,
          flaggedAt: new Date(),
        },
        { new: true }
      );

      if (user) {
        console.log(`🚩 User ${user.email} manually flagged by ${adminEmail}: ${reason}`);
        await this.sendAbuseAlert(user, [`Manual flag: ${reason}`]);
      }
    } catch (error) {
      console.error(`Error manually flagging user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Unflag a user
   */
  static async unflagUser(userId: string, adminEmail: string): Promise<void> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        {
          isFlagged: false,
          lastFlaggedReason: `Unflagged by ${adminEmail} at ${new Date().toISOString()}`,
          flaggedAt: null,
        },
        { new: true }
      );

      if (user) {
        console.log(`✅ User ${user.email} unflagged by ${adminEmail}`);
      }
    } catch (error) {
      console.error(`Error unflagging user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get all flagged users
   */
  static async getFlaggedUsers(): Promise<IUser[]> {
    try {
      return await User.find({ isFlagged: true }).sort({ flaggedAt: -1 });
    } catch (error) {
      console.error('Error fetching flagged users:', error);
      throw error;
    }
  }

  /**
   * Send abuse alert email to admins
   */
  private static async sendAbuseAlert(user: IUser, violations: string[]): Promise<void> {
    try {
      const adminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || [];
      
      if (adminEmails.length === 0) {
        console.warn('No admin emails configured for abuse alerts');
        return;
      }

      const alertData = {
        userEmail: user.email,
        userName: user.displayName || user.name,
        userPlan: user.plan,
        violations: violations,
        flaggedAt: new Date().toISOString(),
        dashboardUrl: `${process.env.CLIENT_URL || 'https://your-domain.com'}/admin?tab=flagged`,
      };

      // Send email to each admin
      await Promise.all(
        adminEmails.map(adminEmail =>
          sendAbuseAlertEmail(adminEmail, alertData)
        )
      );

      console.log(`📧 Abuse alert sent to ${adminEmails.length} admin(s)`);
    } catch (error) {
      console.error('Error sending abuse alert email:', error);
    }
  }

  /**
   * Get abuse statistics
   */
  static async getAbuseStats(): Promise<{
    totalFlagged: number;
    flaggedToday: number;
    flaggedThisWeek: number;
    mostCommonReasons: Array<{ reason: string; count: number }>;
  }> {
    try {
      const now = new Date();
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const [totalFlagged, flaggedToday, flaggedThisWeek, reasonStats] = await Promise.all([
        User.countDocuments({ isFlagged: true }),
        User.countDocuments({ isFlagged: true, flaggedAt: { $gte: todayStart } }),
        User.countDocuments({ isFlagged: true, flaggedAt: { $gte: weekStart } }),
        User.aggregate([
          { $match: { isFlagged: true, lastFlaggedReason: { $exists: true } } },
          { $group: { _id: '$lastFlaggedReason', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 5 },
          { $project: { reason: '$_id', count: 1, _id: 0 } },
        ]),
      ]);

      return {
        totalFlagged,
        flaggedToday,
        flaggedThisWeek,
        mostCommonReasons: reasonStats,
      };
    } catch (error) {
      console.error('Error getting abuse stats:', error);
      throw error;
    }
  }
}

export default AbuseDetectionService; 