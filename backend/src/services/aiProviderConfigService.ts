import { AIProviderConfig, IAIProviderConfig } from '../models/AIProviderConfig';

export type AIProvider = 'openai' | 'deepseek';
export type TaskType = 'summarize' | 'generate' | 'rewrite' | 'categorize' | 'insight' | 'content' | 'search' | 'embedding';

// Default task types and their default providers
const DEFAULT_CONFIGS: Array<{ taskType: TaskType; provider: AIProvider; model?: string }> = [
  { taskType: 'summarize', provider: 'openai', model: 'gpt-4o-mini' },
  { taskType: 'generate', provider: 'openai', model: 'gpt-4o' },
  { taskType: 'rewrite', provider: 'openai', model: 'gpt-4o' },
  { taskType: 'categorize', provider: 'openai', model: 'gpt-4o' },
  { taskType: 'insight', provider: 'openai', model: 'gpt-4o' },
  { taskType: 'content', provider: 'openai', model: 'gpt-4o' },
  { taskType: 'search', provider: 'openai', model: 'gpt-4o-mini' },
  { taskType: 'embedding', provider: 'openai', model: 'text-embedding-3-small' },
];

export class AIProviderConfigService {
  /**
   * Get AI provider for a specific task type
   * Falls back to OpenAI if no config exists
   */
  static async getProviderForTask(taskType: TaskType): Promise<{
    provider: AIProvider;
    model?: string;
  }> {
    try {
      const config = await AIProviderConfig.findOne({ taskType });
      
      if (config) {
        return {
          provider: config.provider,
          model: config.model || undefined
        };
      }

      // Fallback to default config
      const defaultConfig = DEFAULT_CONFIGS.find(c => c.taskType === taskType);
      return {
        provider: defaultConfig?.provider || 'openai',
        model: defaultConfig?.model
      };
    } catch (error) {
      console.error('Error getting AI provider config:', error);
      // Fallback to OpenAI on any error
      return { provider: 'openai' };
    }
  }

  /**
   * Set AI provider for a specific task type
   */
  static async setProviderForTask(
    taskType: TaskType, 
    provider: AIProvider, 
    model?: string
  ): Promise<IAIProviderConfig> {
    try {
      const config = await AIProviderConfig.findOneAndUpdate(
        { taskType },
        { 
          taskType,
          provider,
          model,
          updatedAt: new Date()
        },
        { 
          upsert: true, 
          new: true 
        }
      );

      console.log(`AI Provider config updated: ${taskType} -> ${provider}${model ? ` (${model})` : ''}`);
      return config;
    } catch (error) {
      console.error('Error setting AI provider config:', error);
      throw error;
    }
  }

  /**
   * Get all AI provider configurations
   */
  static async getAllConfigs(): Promise<IAIProviderConfig[]> {
    try {
      const configs = await AIProviderConfig.find().sort({ taskType: 1 });
      
      // Ensure all default task types are represented
      const configMap = new Map(configs.map(c => [c.taskType, c]));
      const allConfigs: IAIProviderConfig[] = [];

      for (const defaultConfig of DEFAULT_CONFIGS) {
        const existingConfig = configMap.get(defaultConfig.taskType);
        if (existingConfig) {
          allConfigs.push(existingConfig);
        } else {
          // Add default config (not saved to DB until explicitly set)
          allConfigs.push({
            taskType: defaultConfig.taskType,
            provider: defaultConfig.provider,
            model: defaultConfig.model,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        }
      }

      return allConfigs;
    } catch (error) {
      console.error('Error getting all AI provider configs:', error);
      throw error;
    }
  }

  /**
   * Reset all configurations to defaults
   */
  static async resetToDefaults(): Promise<void> {
    try {
      await AIProviderConfig.deleteMany({});
      
      for (const defaultConfig of DEFAULT_CONFIGS) {
        await AIProviderConfig.create({
          taskType: defaultConfig.taskType,
          provider: defaultConfig.provider,
          model: defaultConfig.model
        });
      }

      console.log('AI Provider configs reset to defaults');
    } catch (error) {
      console.error('Error resetting AI provider configs:', error);
      throw error;
    }
  }

  /**
   * Initialize default configurations if they don't exist
   * Called on app startup
   */
  static async initializeDefaults(): Promise<void> {
    try {
      const existingCount = await AIProviderConfig.countDocuments();
      
      if (existingCount === 0) {
        console.log('Initializing default AI provider configurations...');
        await this.resetToDefaults();
      }
    } catch (error) {
      console.error('Error initializing default AI provider configs:', error);
    }
  }
} 