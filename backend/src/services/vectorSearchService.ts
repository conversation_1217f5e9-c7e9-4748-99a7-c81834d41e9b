import CloudPost, { ICloudPost } from '../models/CloudPost';
import mongoose from 'mongoose';

export interface VectorSearchResult {
  post: {
    id: string;
    content?: string;
    authorName?: string;
    platform: string;
    savedAt: string;
    categories?: string[];
    tags?: string[];
    permalink: string;
    media?: any[];
  };
  score: number; // MongoDB Atlas vector search score
}

export interface VectorSearchOptions {
  limit?: number;
  minScore?: number;
  includeMetadata?: boolean;
}

/**
 * Perform vector search using MongoDB Atlas Vector Search
 */
export async function performVectorSearch(
  userId: string,
  queryEmbedding: number[],
  options: VectorSearchOptions = {}
): Promise<VectorSearchResult[]> {
  const { limit = 10, minScore = 0.7, includeMetadata = true } = options;

  try {
    // MongoDB Atlas Vector Search aggregation pipeline
    const pipeline = [
      {
        $vectorSearch: {
          index: 'vector_index', // Name of the vector search index
          path: 'embeddingVector',
          queryVector: queryEmbedding,
          numCandidates: Math.max(limit * 10, 100), // Search more candidates for better results
          limit: limit,
          filter: {
            userId: new mongoose.Types.ObjectId(userId)
          }
        }
      },
      {
        $addFields: {
          score: { $meta: 'vectorSearchScore' }
        }
      },
      {
        $match: {
          score: { $gte: minScore }
        }
      },
      {
        $project: {
          _id: 1,
          content: 1,
          authorName: 1,
          platform: 1,
          savedAt: 1,
          categories: 1,
          tags: 1,
          permalink: 1,
          media: 1,
          score: 1,
          ...(includeMetadata && {
            embeddingVector: 1,
            analyzedAt: 1
          })
        }
      }
    ];

    console.log('[VectorSearch] Executing vector search with pipeline:', JSON.stringify(pipeline, null, 2));

    const results = await CloudPost.aggregate(pipeline);

    console.log(`[VectorSearch] Found ${results.length} results with scores >= ${minScore}`);

    // Transform results to match expected format
    const searchResults: VectorSearchResult[] = results.map(doc => ({
      post: {
        id: doc._id.toString(),
        content: doc.content || '',
        authorName: doc.authorName || 'Unknown Author',
        platform: doc.platform,
        savedAt: doc.savedAt.toISOString(),
        categories: doc.categories || [],
        tags: doc.tags || [],
        permalink: doc.permalink,
        media: doc.media || []
      },
      score: doc.score
    }));

    return searchResults;

  } catch (error) {
    console.error('[VectorSearch] Error performing vector search:', error);
    
    // Check if it's a vector search index error
    if (error instanceof Error && error.message.includes('vector search index')) {
      throw new Error('Vector search index not found. Please create the vector_index in MongoDB Atlas.');
    }
    
    throw error;
  }
}

/**
 * Perform hybrid search combining vector search with text search
 */
export async function performHybridSearch(
  userId: string,
  queryEmbedding: number[],
  textQuery: string,
  options: VectorSearchOptions = {}
): Promise<VectorSearchResult[]> {
  const { limit = 10 } = options;
  
  try {
    // Get vector search results
    const vectorResults = await performVectorSearch(userId, queryEmbedding, {
      ...options,
      limit: Math.ceil(limit * 0.7) // 70% from vector search
    });

    // Get text search results for remaining slots
    const remainingSlots = limit - vectorResults.length;
    let textResults: VectorSearchResult[] = [];

    if (remainingSlots > 0) {
      const textSearchPosts = await CloudPost.find({
        userId: new mongoose.Types.ObjectId(userId),
        $or: [
          { content: { $regex: textQuery, $options: 'i' } },
          { authorName: { $regex: textQuery, $options: 'i' } },
          { categories: { $in: [new RegExp(textQuery, 'i')] } },
          { tags: { $in: [new RegExp(textQuery, 'i')] } }
        ]
      })
      .select('content authorName platform savedAt categories tags permalink media')
      .sort({ savedAt: -1 })
      .limit(remainingSlots);

      textResults = textSearchPosts.map(post => ({
        post: {
          id: post._id.toString(),
          content: post.content || '',
          authorName: post.authorName || 'Unknown Author',
          platform: post.platform,
          savedAt: post.savedAt.toISOString(),
          categories: post.categories || [],
          tags: post.tags || [],
          permalink: post.permalink,
          media: post.media || []
        },
        score: 0.5 // Default score for text matches
      }));
    }

    // Combine and deduplicate results
    const combinedResults = [...vectorResults];
    const existingIds = new Set(vectorResults.map(r => r.post.id));

    for (const textResult of textResults) {
      if (!existingIds.has(textResult.post.id)) {
        combinedResults.push(textResult);
      }
    }

    // Sort by score (descending)
    combinedResults.sort((a, b) => b.score - a.score);

    return combinedResults.slice(0, limit);

  } catch (error) {
    console.error('[VectorSearch] Error performing hybrid search:', error);
    throw error;
  }
}

/**
 * Check if vector search is available (index exists)
 */
export async function isVectorSearchAvailable(): Promise<boolean> {
  try {
    // Try a simple vector search to check if index exists
    const testEmbedding = new Array(1536).fill(0.001);
    await CloudPost.aggregate([
      {
        $vectorSearch: {
          index: 'vector_index',
          path: 'embeddingVector',
          queryVector: testEmbedding,
          numCandidates: 1,
          limit: 1
        }
      },
      { $limit: 1 }
    ]);
    
    return true;
  } catch (error) {
    console.log('[VectorSearch] Vector search not available:', error instanceof Error ? error.message : 'Unknown error');
    return false;
  }
}

/**
 * Get vector search statistics
 */
export async function getVectorSearchStats(userId: string) {
  try {
    const totalPosts = await CloudPost.countDocuments({ 
      userId: new mongoose.Types.ObjectId(userId) 
    });
    
    const postsWithEmbeddings = await CloudPost.countDocuments({
      userId: new mongoose.Types.ObjectId(userId),
      embeddingVector: { $exists: true, $ne: null }
    });

    const vectorSearchAvailable = await isVectorSearchAvailable();

    return {
      totalPosts,
      postsWithEmbeddings,
      vectorSearchAvailable,
      embeddingCoverage: totalPosts > 0 ? (postsWithEmbeddings / totalPosts) * 100 : 0
    };
  } catch (error) {
    console.error('[VectorSearch] Error getting stats:', error);
    return {
      totalPosts: 0,
      postsWithEmbeddings: 0,
      vectorSearchAvailable: false,
      embeddingCoverage: 0
    };
  }
}
