// import Bull from 'bull';
// import Redis from 'ioredis';

// REDIS DISABLED FOR NOW - Using mock implementation
console.log('[Queue] Redis disabled - using mock queue service');

// Mock Redis connection
export const redis = {
  disconnect: () => Promise.resolve(),
  status: 'ready'
};

// Job data interfaces
export interface ImageProcessingJobData {
  postId: string;
  userId: string;
  platform: string;
  mediaItems: Array<{
    url: string;
    type: 'image' | 'video';
    alt?: string;
    width?: number;
    height?: number;
  }>;
  originalPostData: any; // Full post data for context
}

// Mock Bull queue for image processing
export const imageProcessingQueue = {
  add: (name: string, data: any) => {
    console.log(`[Queue] Mock: Would add job ${name} with data:`, data);
    return Promise.resolve({ id: Date.now() });
  },
  process: (name: string, handler: any) => {
    console.log(`[Queue] Mock: Would register processor for ${name}`);
  },
  on: (event: string, handler: any) => {
    console.log(`[Queue] Mock: Would register event handler for ${event}`);
  },
  close: () => Promise.resolve(),
};

// Queue monitoring and logging
imageProcessingQueue.on('completed', (job, result) => {
  console.log(`[Queue] Image processing job ${job.id} completed for post ${job.data.postId}`);
});

imageProcessingQueue.on('failed', (job, err) => {
  console.error(`[Queue] Image processing job ${job.id} failed for post ${job.data.postId}:`, err.message);
});

imageProcessingQueue.on('stalled', (job) => {
  console.warn(`[Queue] Image processing job ${job.id} stalled for post ${job.data.postId}`);
});

// Helper function to add image processing job (mock)
export async function addImageProcessingJob(
  postId: string,
  userId: string,
  platform: string,
  mediaItems: ImageProcessingJobData['mediaItems'],
  originalPostData: any,
  options?: {
    delay?: number;
    priority?: number;
  }
): Promise<any> {
  const jobData: ImageProcessingJobData = {
    postId,
    userId,
    platform,
    mediaItems,
    originalPostData,
  };

  console.log(`[Queue] Mock: Would add image processing job for post ${postId} (${platform}) with ${mediaItems.length} media items`);

  return { id: Date.now(), data: jobData };
}

// Helper function to get queue stats (mock)
export async function getQueueStats() {
  return {
    waiting: 0,
    active: 0,
    completed: 0,
    failed: 0,
  };
}

// Helper function to clean up old jobs (mock)
export async function cleanQueue() {
  console.log('[Queue] Mock: Would clean old jobs');
}

// Graceful shutdown (mock)
export async function closeQueue() {
  console.log('[Queue] Mock: Would close queue and disconnect Redis');
}
