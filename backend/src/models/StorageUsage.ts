import mongoose, { Schema, Document } from 'mongoose';

export interface IStorageUsage extends Document {
  userId: mongoose.Types.ObjectId;
  date: Date;
  mbUsed: number;
  operation: string; // e.g., 'image_upload', 'post_save'
  platform?: string;
  fileType?: string; // e.g., 'image', 'video'
  createdAt: Date;
}

const StorageUsageSchema: Schema<IStorageUsage> = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
    mbUsed: {
      type: Number,
      required: true,
      min: 0,
    },
    operation: {
      type: String,
      required: true,
      enum: ['image_upload', 'post_save', 'media_processing'],
    },
    platform: {
      type: String,
      enum: ['twitter', 'linkedin', 'reddit', 'instagram', 'pinterest'],
    },
    fileType: {
      type: String,
      enum: ['image', 'video', 'gif'],
    },
  },
  {
    timestamps: true,
  }
);

// Compound index for efficient date-range queries per user
StorageUsageSchema.index({ userId: 1, date: -1 });

// Index for cleanup of old logs (optional - keep last 90 days)
StorageUsageSchema.index({ createdAt: 1 });

const StorageUsage = mongoose.model<IStorageUsage>('StorageUsage', StorageUsageSchema);

export default StorageUsage;
