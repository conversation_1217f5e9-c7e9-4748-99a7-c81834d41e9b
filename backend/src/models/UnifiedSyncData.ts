import mongoose, { Document, Schema } from 'mongoose';

export interface IUnifiedSyncData extends Document {
  userId: string;
  version: number;
  timestamp: number;
  deviceId: string;
  data: {
    categories: any[];
    customBookmarkCategories: any[];
    bookmarks: any[];
    conversations: any[];
    processedPosts: string[];
    userSettings: any;
  };
  createdAt: Date;
  updatedAt: Date;
}

const UnifiedSyncDataSchema: Schema = new Schema({
  userId: {
    type: String,
    required: true,
    unique: true, // One sync data record per user
    index: true
  },
  version: {
    type: Number,
    required: true,
    default: 1
  },
  timestamp: {
    type: Number,
    required: true,
    default: Date.now
  },
  deviceId: {
    type: String,
    required: true
  },
  data: {
    categories: {
      type: [Schema.Types.Mixed],
      default: []
    },
    customBookmarkCategories: {
      type: [Schema.Types.Mixed],
      default: []
    },
    bookmarks: {
      type: [Schema.Types.Mixed],
      default: []
    },
    conversations: {
      type: [Schema.Types.Mixed],
      default: []
    },
    processedPosts: {
      type: [String],
      default: []
    },
    userSettings: {
      type: Schema.Types.Mixed,
      default: {}
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
UnifiedSyncDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Index for efficient queries
UnifiedSyncDataSchema.index({ userId: 1 });
UnifiedSyncDataSchema.index({ updatedAt: -1 });

export default mongoose.model<IUnifiedSyncData>('UnifiedSyncData', UnifiedSyncDataSchema);
