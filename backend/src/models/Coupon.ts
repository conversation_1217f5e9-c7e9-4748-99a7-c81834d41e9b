import mongoose, { Schema, Document } from 'mongoose';

export interface ICoupon extends Document {
  code: string;
  discountType: 'percentage' | 'fixed';
  amount: number;
  usageLimit?: number;
  usedCount: number;
  expiresAt?: Date;
  createdBy: string; // admin email
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const CouponSchema: Schema<ICoupon> = new Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
      minlength: 3,
      maxlength: 20,
      match: [/^[A-Z0-9]+$/, 'Coupon code must contain only uppercase letters and numbers'],
      index: true,
    },
    discountType: {
      type: String,
      required: true,
      enum: ['percentage', 'fixed'],
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
      validate: {
        validator: function(this: ICoupon, value: number) {
          if (this.discountType === 'percentage') {
            return value > 0 && value <= 100;
          }
          return value > 0;
        },
        message: 'Percentage discounts must be between 1-100, fixed discounts must be greater than 0',
      },
    },
    usageLimit: {
      type: Number,
      min: 1,
      default: null,
    },
    usedCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    expiresAt: {
      type: Date,
      default: null,
    },
    createdBy: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Index for efficient queries
CouponSchema.index({ isActive: 1, expiresAt: 1 });
CouponSchema.index({ createdBy: 1 });

// Virtual to check if coupon is expired
CouponSchema.virtual('isExpired').get(function(this: ICoupon) {
  return this.expiresAt && this.expiresAt < new Date();
});

// Virtual to check if coupon is usable
CouponSchema.virtual('isUsable').get(function(this: ICoupon) {
  const isNotExpired = !this.expiresAt || this.expiresAt > new Date();
  const hasUsagesLeft = !this.usageLimit || this.usedCount < this.usageLimit;
  return this.isActive && isNotExpired && hasUsagesLeft;
});

// Method to increment usage count
CouponSchema.methods.incrementUsage = function(this: ICoupon) {
  this.usedCount += 1;
  return this.save();
};

const Coupon = mongoose.model<ICoupon>('Coupon', CouponSchema);

export default Coupon; 