import mongoose, { Schema, Document, Model } from 'mongoose';
import bcrypt from 'bcrypt';

// Interface representing a document in MongoDB.
export interface IUser extends Document {
  userId?: string; // Made optional - was for Firebase Auth UID
  name: string;
  email: string;
  plan: 'free' | 'premium';
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  role: 'user' | 'admin';
  emailVerified: boolean;
  adsDisabled: boolean;
  createdAt: Date;
  lastLogin: Date;
  profileSettings?: Record<string, any>;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  // Storage usage tracking
  storageUsed: number; // in bytes
  storageLimit: number; // in bytes
  // Abuse detection and flagging
  isFlagged: boolean;
  lastFlaggedReason?: string;
  flaggedAt?: Date;
  // Cost-based abuse detection
  flaggedForAbuse: boolean;
  // ...existing fields for local/Google login (optional)
  password?: string;
  googleId?: string;
  displayName?: string;
  updatedAt?: Date;
  comparePassword?(candidatePassword: string): Promise<boolean>;
}

// Schema definition corresponding to the IUser interface.
const UserSchema: Schema<IUser> = new Schema(
  {
    userId: {
      type: String,
      required: false, // Changed from true
      unique: true,
      index: true,
      sparse: true,    // Allow multiple null/undefined if unique and not required
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      match: [/^\S+@\S+\.\S+$/, 'Please use a valid email address.'],
    },
    plan: {
      type: String,
      enum: ['free', 'premium'],
      default: 'free',
    },
    subscriptionStatus: {
      type: String,
      enum: ['trialing', 'active', 'past_due', 'canceled'],
      default: 'trialing',
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user',
    },
    emailVerified: {
      type: Boolean,
      default: false,
    },
    adsDisabled: {
      type: Boolean,
      default: false,
    },
    storageUsed: {
      type: Number,
      default: 0,
      min: 0,
    },
    storageLimit: {
      type: Number,
      default: 1073741824, // 1GB in bytes for free users
    },
    isFlagged: {
      type: Boolean,
      default: false,
    },
    lastFlaggedReason: {
      type: String,
      required: false,
    },
    flaggedAt: {
      type: Date,
      required: false,
    },
    flaggedForAbuse: {
      type: Boolean,
      default: false,
    },
    // createdAt and updatedAt are automatically managed by timestamps: true below
    // So, explicit createdAt and updatedAt in IUser and default in schema might be redundant
    // However, lastLogin is custom and needs its default.
    lastLogin: {
      type: Date,
      default: Date.now,
    },
    profileSettings: {
      type: Schema.Types.Mixed,
      default: {},
    },
    stripeCustomerId: {
      type: String,
      default: null, // Using null for explicitly not set vs undefined
    },
    stripeSubscriptionId: {
      type: String,
      default: null, // Using null for explicitly not set vs undefined
    },
    // Legacy fields for local/Google login (optional)
    password: {
      type: String,
      required: false,
      minlength: 6,
    },
    googleId: {
      type: String,
      // unique: false, // googleId should be unique if used as a primary link to Google Account
      // sparse: true,  // this allows multiple nulls, but if present, it should be unique
      // Let's make googleId unique and sparse if it's a key identifier
      unique: true,
      sparse: true,
    },
    displayName: {
      type: String,
      required: false,
      trim: true,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt
  }
);

// Pre-save hook to hash password, set storage limits, and validate storage
UserSchema.pre<IUser>('save', async function (next) {
  // Hash password if it has been modified (or is new)
  if (this.isModified('password') && this.password) {
    try {
      const salt = await bcrypt.genSalt(10); // Generate salt
      this.password = await bcrypt.hash(this.password, salt); // Hash password
    } catch (error: any) {
      return next(error); // Pass error to the next middleware
    }
  }

  // Set storage limit based on plan
  if (this.isModified('plan') || this.isNew) {
    if (this.plan === 'premium') {
      this.storageLimit = 10737418240; // 10GB in bytes
    } else {
      this.storageLimit = 1073741824; // 1GB in bytes
    }
  }

  // Prevent negative storage values
  if (this.storageUsed < 0) {
    console.warn(`[STORAGE FIX] User ${this.email} had negative storageUsed: ${this.storageUsed}, setting to 0`);
    this.storageUsed = 0;
  }

  next();
});

// Method to compare candidate password with the user's hashed password
UserSchema.methods.comparePassword = async function (
  candidatePassword: string
): Promise<boolean> {
  if (!this.password) {
    // Handle case where user logged in via OAuth and has no password
    return false;
  }
  return bcrypt.compare(candidatePassword, this.password);
};

// Create and export the User model
const User: Model<IUser> = mongoose.model<IUser>('User', UserSchema);

export default User;