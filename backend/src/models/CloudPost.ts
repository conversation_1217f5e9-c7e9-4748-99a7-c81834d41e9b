import mongoose, { Schema, Document, Model } from 'mongoose';

// Interface for individual media items (images, videos)
export interface IMediaItem extends Document {
  type: 'image' | 'video';
  url: string; // Public URL of the media
  filePath?: string; // Path of the file if stored in a service like S3/Firebase Storage
  alt?: string;
  width?: number;
  height?: number;
}

const MediaItemSchema: Schema<IMediaItem> = new Schema({
  type: { type: String, enum: ['image', 'video'], required: true },
  url: { type: String, required: true },
  filePath: String,
  alt: String,
  width: Number,
  height: Number,
}, { _id: false });

// Interface for the AI-generated Insight data
export interface IAIInSightData {
  sentiment: 'positive' | 'neutral' | 'negative';
  emoji: string;
  contextTags: string[];
}

const AIInSightDataSchema: Schema<IAIInSightData> = new Schema({
  sentiment: { type: String, enum: ['positive', 'neutral', 'negative'], default: 'neutral' },
  emoji: { type: String, default: '🤔' },
  contextTags: [{ type: String }],
}, { _id: false });

// Main interface for the CloudPost document
export interface ICloudPost extends Document {
  userId: mongoose.Schema.Types.ObjectId; // Reference to the User who saved the post
  originalPostId?: string | null; // The ID from the social media platform (e.g., tweet ID), can be null if not applicable
  platform: 'X/Twitter' | 'LinkedIn' | 'Reddit' | 'Instagram' | 'facebook' | 'pinterest' | 'Other';
  authorName?: string;
  authorHandle?: string;
  authorTitle?: string;
  authorAvatar?: string;
  authorImage?: string;
  content?: string; // Original text content of the post
  altText?: string;
  timestamp?: string; // Original post timestamp (ISO format)
  savedAt: Date;     // Timestamp when the post was saved (set by server or background script)
  permalink: string;
  media?: IMediaItem[];
  interactions?: {
    replies?: number;
    reposts?: number;
    likes?: number;
  };

  // --- Image Processing Status ---
  imageProcessingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  imageProcessingJobId?: string | null;
  imageProcessingError?: string | null;
  imageProcessedAt?: Date | null;
  // --- End Image Processing Status ---

  // --- User Fields ---
  notes?: string | null; // User-added personal notes
  // --- End User Fields ---

  // --- AI Generated Fields ---
  snapNote?: string | null;
  categories?: string[]; // Array of category slugs
  tags?: string[];       // Array of tags
  inSight?: IAIInSightData | null;
  fastTake?: string | null;
  contentIdeas?: string[] | null;
  embeddingVector?: number[]; // Optional, for similarity search etc.
  analyzedAt?: Date | null;   // Timestamp of when AI analysis was performed
  // --- End AI Generated Fields ---
}

const CloudPostSchema: Schema<ICloudPost> = new Schema(
  {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    originalPostId: { type: String, required: false, index: true, default: null },
    platform: {
      type: String,
      enum: ['X/Twitter', 'LinkedIn', 'Reddit', 'Instagram', 'facebook', 'pinterest', 'Other'],
      required: true,
    },
    authorName: String,
    authorHandle: String,
    authorTitle: String,
    authorAvatar: String,
    authorImage: String,
    content: String,
    altText: String,
    timestamp: String,
    savedAt: { type: Date, default: Date.now },
    permalink: { type: String, required: true, index: true },
    media: [MediaItemSchema],
    interactions: {
      replies: { type: Number, default: 0 },
      reposts: { type: Number, default: 0 },
      likes: { type: Number, default: 0 },
    },

    // --- Image Processing Status Schema Definition ---
    imageProcessingStatus: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    },
    imageProcessingJobId: { type: String, default: null },
    imageProcessingError: { type: String, default: null },
    imageProcessedAt: { type: Date, default: null },
    // --- End Image Processing Status Schema Definition ---

    // --- User Fields Schema Definition ---
    notes: { type: String, default: null },
    // --- End User Fields Schema Definition ---

    // --- AI Generated Fields Schema Definition ---
    snapNote: { type: String, default: null },
    categories: { type: [String], default: [] },
    tags: { type: [String], default: [] },
    inSight: { type: AIInSightDataSchema, default: null },
    fastTake: { type: String, default: null },
    contentIdeas: { type: [String], default: [] },
    embeddingVector: { type: [Number], default: undefined }, // Default to undefined if not always present
    analyzedAt: { type: Date, default: null },
    // --- End AI Generated Fields Schema Definition ---
  },
  {
    timestamps: true, // Adds createdAt and updatedAt automatically
    strict: true,     // Ensures only fields in schema are saved, can be 'throw' or false
  }
);

// Ensure that a user cannot save the exact same post (identified by permalink) multiple times.
// If originalPostId is reliably unique per platform per user, that can be used too.
CloudPostSchema.index({ userId: 1, permalink: 1 }, { unique: true });
// Optional: if originalPostId is important and should be unique per user/platform when present
CloudPostSchema.index({ userId: 1, platform: 1, originalPostId: 1 }, { unique: true, sparse: true });

// Vector search index for semantic search - this will be created via MongoDB Atlas UI or CLI
// The index should be named 'vector_index' and target the 'embeddingVector' field
// Index configuration:
// {
//   "fields": [
//     {
//       "type": "vector",
//       "path": "embeddingVector",
//       "numDimensions": 1536,
//       "similarity": "cosine"
//     },
//     {
//       "type": "filter",
//       "path": "userId"
//     }
//   ]
// }


const CloudPost: Model<ICloudPost> = mongoose.model<ICloudPost>('CloudPost', CloudPostSchema);

export default CloudPost;