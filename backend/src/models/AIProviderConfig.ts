import mongoose from 'mongoose';

export interface IAIProviderConfig {
  _id?: string;
  taskType: string; // 'summarize', 'generate', 'rewrite', 'categorize', 'insight', etc.
  provider: 'openai' | 'deepseek'; // Available AI providers
  model?: string; // Optional specific model (e.g., 'gpt-4o', 'deepseek-chat')
  createdAt: Date;
  updatedAt: Date;
}

const AIProviderConfigSchema = new mongoose.Schema<IAIProviderConfig>({
  taskType: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  provider: {
    type: String,
    required: true,
    enum: ['openai', 'deepseek'],
    default: 'openai'
  },
  model: {
    type: String,
    required: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
AIProviderConfigSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

export const AIProviderConfig = mongoose.model<IAIProviderConfig>('AIProviderConfig', AIProviderConfigSchema); 