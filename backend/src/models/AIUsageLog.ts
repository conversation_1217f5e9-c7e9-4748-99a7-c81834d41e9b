import mongoose, { Schema, Document } from 'mongoose';

export interface IAIUsageLog extends Document {
  userId: mongoose.Types.ObjectId;
  date: Date;
  tokensUsed: number;
  operation: string; // e.g., 'categorization', 'insights', 'chat'
  platform?: string; // e.g., 'twitter', 'linkedin'
  createdAt: Date;
}

const AIUsageLogSchema: Schema<IAIUsageLog> = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
    tokensUsed: {
      type: Number,
      required: true,
      min: 0,
    },
    operation: {
      type: String,
      required: true,
      enum: ['categorization', 'insights', 'chat', 'analysis', 'content_generation'],
    },
    platform: {
      type: String,
      enum: ['twitter', 'linkedin', 'reddit', 'instagram', 'pinterest'],
    },
  },
  {
    timestamps: true,
  }
);

// Compound index for efficient date-range queries per user
AIUsageLogSchema.index({ userId: 1, date: -1 });

// Index for cleanup of old logs (optional - keep last 90 days)
AIUsageLogSchema.index({ createdAt: 1 });

const AIUsageLog = mongoose.model<IAIUsageLog>('AIUsageLog', AIUsageLogSchema);

export default AIUsageLog;
