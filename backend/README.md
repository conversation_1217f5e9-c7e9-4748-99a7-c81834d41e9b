# Notely.social Backend

This is the backend for the Notely.social Chrome extension.

## Local Development

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file with the following variables:
   ```
   MONGO_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   AWS_ACCESS_KEY_ID=your_aws_access_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret_key
   AWS_S3_BUCKET_NAME=your_s3_bucket_name
   AWS_S3_REGION=your_s3_region
   ```

3. Build the application:
   ```
   npm run build
   ```

4. Start the server:
   ```
   npm start
   ```

## Deployment to Railway

1. Create a new project on Railway:
   ```
   railway init
   ```

2. Add environment variables:
   ```
   railway vars set MONGO_URI=your_mongodb_connection_string
   railway vars set JWT_SECRET=your_jwt_secret
   railway vars set AWS_ACCESS_KEY_ID=your_aws_access_key
   railway vars set AWS_SECRET_ACCESS_KEY=your_aws_secret_key
   railway vars set AWS_S3_BUCKET_NAME=your_s3_bucket_name
   railway vars set AWS_S3_REGION=your_s3_region
   railway vars set NODE_ENV=production
   ```

3. Deploy to Railway:
   ```
   railway up
   ```

4. Open the deployed application:
   ```
   railway open
   ```

## Manual Deployment

If you don't have the Railway CLI installed, you can deploy manually through the Railway dashboard:

1. Go to [Railway](https://railway.app/)
2. Create a new project
3. Connect your GitHub repository
4. Add the environment variables listed above
5. Deploy the project

## Instagram Integration

The Instagram integration uses server-side image fetching and S3 storage to avoid CORS issues. The backend:

1. Receives post data from the Chrome extension
2. Fetches images from Instagram URLs or processes base64-encoded images
3. Uploads images to S3
4. Stores metadata in MongoDB
5. Returns the processed post data to the client

## API Endpoints

- `POST /api/instagram/posts` - Create a new Instagram post
- `GET /api/instagram/posts` - Get all Instagram posts for the authenticated user
- `GET /api/instagram/posts/:id` - Get a single Instagram post by ID
- `DELETE /api/instagram/posts/:id` - Delete an Instagram post
