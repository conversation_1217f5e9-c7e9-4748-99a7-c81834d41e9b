# Railway Deployment Guide

## 🚀 Deploy Queue-Based Image Processing to Railway

### Prerequisites
1. Railway account
2. Railway CLI installed: `npm install -g @railway/cli`
3. Git repository connected to Railway

### Step 1: Create Railway Project
```bash
# Login to Railway
railway login

# Create new project (or use existing)
railway init

# Link to existing project if needed
railway link
```

### Step 2: Add Redis Service
```bash
# Add Redis plugin to your Railway project
railway add redis

# This will automatically set REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
```

### Step 3: Deploy Web Service
```bash
# Deploy the main API server
railway up

# Or deploy with specific service name
railway up --service web
```

### Step 4: Create Worker Service
```bash
# Create a new service for the worker
railway service create worker

# Deploy worker service
railway up --service worker
```

### Step 5: Set Environment Variables
Set these variables in Railway dashboard or via CLI:

```bash
# Required for both services
railway variables set MONGO_URI="your_mongodb_connection_string"
railway variables set JWT_SECRET="your_jwt_secret"

# AWS S3 Configuration
railway variables set AWS_ACCESS_KEY_ID="your_aws_access_key"
railway variables set AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
railway variables set AWS_S3_BUCKET_NAME="my-social-saver-bucket"
railway variables set AWS_S3_REGION="eu-north-1"

# Google OAuth
railway variables set GOOGLE_CLIENT_ID="your_google_client_id"
railway variables set GOOGLE_CLIENT_SECRET="your_google_client_secret"

# Stripe
railway variables set STRIPE_SECRET_KEY="your_stripe_secret_key"
railway variables set STRIPE_WEBHOOK_SECRET="your_stripe_webhook_secret"

# Redis (automatically set by Railway Redis plugin)
# REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
```

### Step 6: Configure Services

#### Web Service (API Server)
- **Start Command**: `npm start`
- **Build Command**: `npm run build`
- **Port**: Auto-detected from `process.env.PORT`

#### Worker Service (Image Processing)
- **Start Command**: `npm run worker`
- **Build Command**: `npm run build`
- **No public port needed** (internal service)

### Step 7: Verify Deployment

#### Check Web Service
```bash
curl https://your-web-service.railway.app/api/queue/stats
```

#### Check Logs
```bash
# Web service logs
railway logs --service web

# Worker service logs
railway logs --service worker
```

### Step 8: Monitor Queue Health

#### API Endpoints
- `GET /api/queue/stats` - Queue statistics
- `GET /api/queue/posts/:postId/status` - Post processing status
- `GET /api/queue/posts/processing` - All processing posts

#### Expected Logs
```
[Worker] Image Processing Worker is running and ready to process jobs
[Queue] Adding image processing job for post...
[Worker] Successfully processed media item...
```

### Troubleshooting

#### Common Issues
1. **Redis Connection Failed**
   - Ensure Redis plugin is added
   - Check REDIS_* environment variables

2. **Worker Not Processing Jobs**
   - Check worker service logs
   - Verify MongoDB connection
   - Ensure worker service is running

3. **S3 Upload Errors**
   - Verify AWS credentials
   - Check bucket policy (no ACL needed)
   - Ensure bucket exists in correct region

#### Debug Commands
```bash
# Check service status
railway status

# View environment variables
railway variables

# Restart services
railway service restart web
railway service restart worker
```

### Production Checklist
- [ ] Redis service added and connected
- [ ] Both web and worker services deployed
- [ ] All environment variables set
- [ ] MongoDB connection working
- [ ] S3 bucket configured with public policy
- [ ] Queue stats endpoint responding
- [ ] Worker processing test jobs
- [ ] Logs showing successful image processing

### Scaling
- **Web Service**: Scale based on API traffic
- **Worker Service**: Scale based on queue length
- **Redis**: Use Railway's managed Redis for reliability

Railway will automatically handle:
- Load balancing
- Health checks
- Auto-restarts
- SSL certificates
- Domain management
