# Queue-Based Image Processing Architecture

## Overview

This document describes the new decoupled architecture for handling post saves and image processing in the <PERSON>ly backend.

## Problem Solved

Previously, when users saved posts, the system would:
1. Try to download images from Twitter/X
2. Upload them to S3 synchronously
3. Save the post to MongoDB

This caused issues:
- S3 ACL errors (`AccessControlListNotSupported`)
- Timeouts during image downloads
- Failed post saves when image processing failed
- Poor user experience with slow responses

## New Architecture

### Fast Path (Immediate Response)
1. **POST /api/posts** receives a save request
2. Post is saved immediately to MongoDB with **original image URLs**
3. Response returned to extension immediately (fast!)
4. Image processing job is queued for background processing

### Background Path (Image Processing Worker)
1. **Worker process** picks up queued jobs
2. Downloads images from original URLs
3. Uploads to S3 (with fixed ACL issues)
4. Updates post record with S3 URLs
5. Updates processing status

## Components

### 1. Queue Service (`queueService.ts`)
- Uses **Bull** (Redis-based) for job queuing
- Handles job creation, monitoring, and cleanup
- Configurable retry logic with exponential backoff

### 2. Image Processing Worker (`imageProcessingWorker.ts`)
- Processes queued image jobs
- Downloads images and uploads to S3
- Updates post records with results
- Handles partial failures gracefully

### 3. Worker Entry Point (`worker.ts`)
- Main worker process
- Connects to MongoDB and Redis
- Handles graceful shutdown

### 4. Updated Post Model
New fields added to `CloudPost`:
- `imageProcessingStatus`: 'pending' | 'processing' | 'completed' | 'failed'
- `imageProcessingJobId`: Bull job ID for tracking
- `imageProcessingError`: Error message if processing failed
- `imageProcessedAt`: Timestamp when processing completed

### 5. Queue Management API (`queueRoutes.ts`)
- `GET /api/queue/stats` - Queue statistics
- `GET /api/queue/posts/:postId/status` - Processing status for specific post
- `GET /api/queue/posts/processing` - All posts currently being processed

## S3 Configuration Changes

### Fixed ACL Issues
- Removed `ACL: 'public-read'` parameter from S3 uploads
- Bucket policy should handle public access instead
- Prevents `AccessControlListNotSupported` errors

### Example Bucket Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::my-social-saver-bucket/*"
    }
  ]
}
```

## Railway Deployment

### Multi-Service Configuration (`railway.toml`)
```toml
# Web service (main API server)
[[services]]
name = "web"
startCommand = "npm start"

# Worker service (image processing)
[[services]]
name = "worker"
startCommand = "npm run worker"
```

### Required Environment Variables
- `REDIS_HOST` - Redis server host
- `REDIS_PORT` - Redis server port (default: 6379)
- `REDIS_PASSWORD` - Redis password (if required)
- All existing AWS S3 variables

## Benefits

1. **Fast Response Times**: Posts save immediately, no waiting for image processing
2. **Reliability**: Image processing failures don't affect post saving
3. **Scalability**: Worker processes can be scaled independently
4. **Monitoring**: Queue stats and processing status available via API
5. **Retry Logic**: Failed image processing jobs are automatically retried
6. **Graceful Degradation**: Original URLs preserved if S3 processing fails

## Usage Flow

### For Users
1. Click "Save" on a Twitter post
2. Get immediate success response
3. Post appears in their collection with original images
4. Images are gradually replaced with S3 URLs in the background

### For Developers
1. Monitor queue health via `/api/queue/stats`
2. Check specific post processing via `/api/queue/posts/:postId/status`
3. View all processing posts via `/api/queue/posts/processing`

## Development

### Local Development
```bash
# Terminal 1: Start main server
npm run dev

# Terminal 2: Start worker
npm run dev:worker

# Make sure Redis is running locally
redis-server
```

### Production Deployment
1. Deploy both web and worker services to Railway
2. Ensure Redis is available (Railway Redis plugin)
3. Configure environment variables
4. Monitor queue stats and processing status

## Monitoring

### Queue Health
- Check `/api/queue/stats` for queue metrics
- Monitor failed job counts
- Watch for stalled jobs

### Post Processing Status
- `pending`: Job queued but not started
- `processing`: Worker is currently processing images
- `completed`: All images processed successfully
- `failed`: Processing failed (original URLs preserved)

### Error Handling
- Partial failures: Some images processed, some failed
- Complete failures: All images failed, original URLs preserved
- Storage limits: Respected during background processing
- Network errors: Automatic retry with exponential backoff
