/**
 * Test LinkedIn Queue Processing
 * 
 * This script tests the queue-based image processing system for LinkedIn posts
 * to ensure it works the same way as Twitter.
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:3000/api';
const TEST_USER_TOKEN = 'your-test-jwt-token-here'; // Replace with actual token

// Sample LinkedIn post data (similar to what the extension would send)
const sampleLinkedInPost = {
  originalPostId: 'linkedin-test-' + Date.now(),
  platform: 'LinkedIn',
  authorName: '<PERSON>',
  authorHandle: 'johndoe',
  authorAvatar: 'https://media.licdn.com/dms/image/sample-avatar.jpg',
  content: 'This is a test LinkedIn post with images to verify queue processing works correctly.',
  timestamp: new Date().toISOString(),
  permalink: 'https://www.linkedin.com/posts/johndoe_test-post-' + Date.now(),
  media: [
    {
      type: 'image',
      url: 'https://media.licdn.com/dms/image/sample-image-1.jpg',
      alt: 'Sample LinkedIn image 1',
      width: 800,
      height: 600
    },
    {
      type: 'image', 
      url: 'https://media.licdn.com/dms/image/sample-image-2.jpg',
      alt: 'Sample LinkedIn image 2',
      width: 1200,
      height: 800
    }
  ],
  interactions: {
    likes: 42,
    comments: 5,
    shares: 3
  }
};

async function testLinkedInQueueProcessing() {
  console.log('🧪 Testing LinkedIn Queue Processing System...\n');

  try {
    // Step 1: Create LinkedIn post (should trigger queue processing)
    console.log('📝 Step 1: Creating LinkedIn post...');
    const createResponse = await axios.post(`${API_BASE_URL}/posts`, sampleLinkedInPost, {
      headers: {
        'Authorization': `Bearer ${TEST_USER_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (createResponse.status === 201) {
      console.log('✅ LinkedIn post created successfully!');
      console.log(`   Post ID: ${createResponse.data._id}`);
      console.log(`   Image Processing Status: ${createResponse.data.imageProcessingStatus}`);
      console.log(`   Job ID: ${createResponse.data.imageProcessingJobId || 'None'}`);
      
      const postId = createResponse.data._id;

      // Step 2: Check initial status
      console.log('\n🔍 Step 2: Checking initial processing status...');
      await checkProcessingStatus(postId);

      // Step 3: Wait and check status again
      console.log('\n⏳ Step 3: Waiting 10 seconds for background processing...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      console.log('🔍 Step 4: Checking processing status after wait...');
      await checkProcessingStatus(postId);

      // Step 5: Get final post data
      console.log('\n📄 Step 5: Getting final post data...');
      const finalResponse = await axios.get(`${API_BASE_URL}/posts/${postId}`, {
        headers: {
          'Authorization': `Bearer ${TEST_USER_TOKEN}`
        }
      });

      if (finalResponse.status === 200) {
        const post = finalResponse.data;
        console.log('✅ Final post data retrieved:');
        console.log(`   Platform: ${post.platform}`);
        console.log(`   Author: ${post.authorName}`);
        console.log(`   Media Items: ${post.media?.length || 0}`);
        console.log(`   Processing Status: ${post.imageProcessingStatus}`);
        
        if (post.media && post.media.length > 0) {
          console.log('   Media URLs:');
          post.media.forEach((item, index) => {
            console.log(`     ${index + 1}. ${item.url.substring(0, 80)}...`);
            console.log(`        S3 Path: ${item.filePath || 'Not processed'}`);
          });
        }
      }

    } else {
      console.error('❌ Failed to create LinkedIn post:', createResponse.status);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

async function checkProcessingStatus(postId) {
  try {
    const statusResponse = await axios.get(`${API_BASE_URL}/queue/posts/${postId}/status`, {
      headers: {
        'Authorization': `Bearer ${TEST_USER_TOKEN}`
      }
    });

    if (statusResponse.status === 200) {
      const status = statusResponse.data;
      console.log(`   Status: ${status.status}`);
      console.log(`   Job ID: ${status.jobId || 'None'}`);
      console.log(`   Error: ${status.error || 'None'}`);
      console.log(`   Processed At: ${status.processedAt || 'Not yet'}`);
    }
  } catch (error) {
    console.error('   ❌ Failed to get status:', error.message);
  }
}

// Instructions for running the test
if (require.main === module) {
  console.log('🚀 LinkedIn Queue Processing Test');
  console.log('=====================================\n');
  console.log('⚠️  SETUP REQUIRED:');
  console.log('1. Make sure the backend server is running (npm run dev)');
  console.log('2. Make sure Redis is running');
  console.log('3. Make sure the worker is running (npm run worker)');
  console.log('4. Replace TEST_USER_TOKEN with a valid JWT token');
  console.log('5. Update API_BASE_URL if needed\n');
  
  if (TEST_USER_TOKEN === 'your-test-jwt-token-here') {
    console.log('❌ Please update TEST_USER_TOKEN before running the test!');
    process.exit(1);
  }
  
  testLinkedInQueueProcessing();
}

module.exports = { testLinkedInQueueProcessing };
