/**
 * MongoDB Atlas Vector Search Index Setup Script
 * 
 * This script helps you set up the vector search index for semantic search functionality.
 * 
 * IMPORTANT: This script provides the configuration and instructions.
 * The actual index creation must be done through MongoDB Atlas UI or Atlas CLI.
 */

const vectorSearchIndexConfig = {
  "name": "vector_index",
  "type": "vectorSearch",
  "definition": {
    "fields": [
      {
        "type": "vector",
        "path": "embeddingVector",
        "numDimensions": 1536,
        "similarity": "cosine"
      },
      {
        "type": "filter",
        "path": "userId"
      },
      {
        "type": "filter", 
        "path": "platform"
      },
      {
        "type": "filter",
        "path": "categories"
      },
      {
        "type": "filter",
        "path": "tags"
      }
    ]
  }
};

console.log('='.repeat(80));
console.log('MongoDB Atlas Vector Search Index Setup');
console.log('='.repeat(80));
console.log();

console.log('📋 VECTOR SEARCH INDEX CONFIGURATION:');
console.log();
console.log(JSON.stringify(vectorSearchIndexConfig, null, 2));
console.log();

console.log('🚀 SETUP INSTRUCTIONS:');
console.log();
console.log('1. Log in to MongoDB Atlas (https://cloud.mongodb.com)');
console.log('2. Navigate to your cluster: social-post-saver-cluster');
console.log('3. Go to "Search" tab in the cluster view');
console.log('4. Click "Create Search Index"');
console.log('5. Choose "JSON Editor" option');
console.log('6. Select your database and collection:');
console.log('   - Database: (your database name)');
console.log('   - Collection: cloudposts');
console.log('7. Paste the configuration above into the JSON editor');
console.log('8. Click "Next" and then "Create Search Index"');
console.log();

console.log('⚡ ALTERNATIVE: Using Atlas CLI');
console.log();
console.log('If you have Atlas CLI installed, you can create the index with:');
console.log();
console.log('atlas clusters search indexes create \\');
console.log('  --clusterName social-post-saver-cluster \\');
console.log('  --db YOUR_DATABASE_NAME \\');
console.log('  --collection cloudposts \\');
console.log('  --file vector-search-index.json');
console.log();

console.log('📝 VERIFICATION:');
console.log();
console.log('After creating the index:');
console.log('1. Test the search endpoint: GET /api/posts/search/test');
console.log('2. Check that vectorSearchAvailable is true');
console.log('3. Try a semantic search: POST /api/posts/search');
console.log();

console.log('🔧 TROUBLESHOOTING:');
console.log();
console.log('- Index creation can take 5-10 minutes for large collections');
console.log('- Ensure your posts have embeddingVector fields populated');
console.log('- Check MongoDB Atlas logs if the index creation fails');
console.log('- Verify your cluster tier supports vector search (M10+)');
console.log();

console.log('💡 PERFORMANCE TIPS:');
console.log();
console.log('- Vector search works best with 100+ posts with embeddings');
console.log('- Consider using hybrid search for better coverage');
console.log('- Monitor search performance in Atlas metrics');
console.log('- Adjust similarity thresholds based on your data');
console.log();

// Save the configuration to a file for Atlas CLI usage
const fs = require('fs');
const path = require('path');

const configPath = path.join(__dirname, 'vector-search-index.json');
fs.writeFileSync(configPath, JSON.stringify(vectorSearchIndexConfig, null, 2));

console.log(`📁 Configuration saved to: ${configPath}`);
console.log('   Use this file with Atlas CLI for automated setup');
console.log();
console.log('='.repeat(80));
