/**
 * Fix Semantic Search - Complete Setup Guide
 * 
 * This script identifies what's needed to fix semantic search and provides instructions
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the CloudPost model (simplified)
const CloudPostSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  embeddingVector: { type: [Number], default: undefined },
}, { timestamps: true });

const CloudPost = mongoose.model('CloudPost', CloudPostSchema);

async function connectDB() {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI;
    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables');
    }
    
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    return false;
  }
}

async function checkVectorSearchIndex() {
  try {
    // Try a simple vector search to check if index exists
    const testEmbedding = new Array(1536).fill(0.001);
    await CloudPost.aggregate([
      {
        $vectorSearch: {
          index: 'vector_index',
          path: 'embeddingVector',
          queryVector: testEmbedding,
          numCandidates: 1,
          limit: 1
        }
      },
      { $limit: 1 }
    ]);
    
    return true;
  } catch (error) {
    return false;
  }
}

async function checkEmbeddings() {
  const totalPosts = await CloudPost.countDocuments({});
  const postsWithEmbeddings = await CloudPost.countDocuments({
    embeddingVector: { $exists: true, $ne: null, $not: { $size: 0 } }
  });
  
  return { totalPosts, postsWithEmbeddings };
}

async function checkOpenAIKey() {
  const key = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;
  return !!key;
}

async function main() {
  console.log('🔍 SEMANTIC SEARCH DIAGNOSTIC\n');
  console.log('='.repeat(50));
  
  // Check MongoDB connection
  const connected = await connectDB();
  if (!connected) {
    console.log('❌ Cannot proceed without MongoDB connection');
    return;
  }
  
  // Check OpenAI API key
  const hasOpenAIKey = await checkOpenAIKey();
  console.log(`🔑 OpenAI API Key: ${hasOpenAIKey ? '✅ Found' : '❌ Missing'}`);
  
  // Check vector search index
  const hasVectorIndex = await checkVectorSearchIndex();
  console.log(`📊 Vector Search Index: ${hasVectorIndex ? '✅ Available' : '❌ Missing'}`);
  
  // Check embeddings
  const { totalPosts, postsWithEmbeddings } = await checkEmbeddings();
  const embeddingCoverage = totalPosts > 0 ? (postsWithEmbeddings / totalPosts * 100).toFixed(1) : 0;
  console.log(`📝 Posts with embeddings: ${postsWithEmbeddings}/${totalPosts} (${embeddingCoverage}%)`);
  
  console.log('\n' + '='.repeat(50));
  console.log('🎯 REQUIRED ACTIONS:\n');
  
  let actionCount = 1;
  
  if (!hasOpenAIKey) {
    console.log(`${actionCount}. ❌ ADD OPENAI API KEY`);
    console.log('   Add OPENAI_API_KEY to your .env file');
    console.log('   Get key from: https://platform.openai.com/api-keys\n');
    actionCount++;
  }
  
  if (!hasVectorIndex) {
    console.log(`${actionCount}. ❌ CREATE VECTOR SEARCH INDEX`);
    console.log('   Go to MongoDB Atlas → Your Cluster → Search → Create Search Index');
    console.log('   Use JSON Editor with this config:');
    console.log('   {');
    console.log('     "name": "vector_index",');
    console.log('     "type": "vectorSearch",');
    console.log('     "definition": {');
    console.log('       "fields": [');
    console.log('         {');
    console.log('           "type": "vector",');
    console.log('           "path": "embeddingVector",');
    console.log('           "numDimensions": 1536,');
    console.log('           "similarity": "cosine"');
    console.log('         },');
    console.log('         {');
    console.log('           "type": "filter",');
    console.log('           "path": "userId"');
    console.log('         }');
    console.log('       ]');
    console.log('     }');
    console.log('   }\n');
    actionCount++;
  }
  
  if (postsWithEmbeddings === 0 && totalPosts > 0) {
    console.log(`${actionCount}. ❌ GENERATE EMBEDDINGS FOR EXISTING POSTS`);
    console.log('   Run: node backend/scripts/generate-embeddings.js');
    console.log('   This will add embeddings to your existing posts\n');
    actionCount++;
  } else if (postsWithEmbeddings < totalPosts) {
    console.log(`${actionCount}. ⚠️  SOME POSTS MISSING EMBEDDINGS`);
    console.log('   Run: node backend/scripts/generate-embeddings.js');
    console.log('   This will add embeddings to posts that don\'t have them\n');
    actionCount++;
  }
  
  if (hasOpenAIKey && hasVectorIndex && postsWithEmbeddings > 0) {
    console.log('🎉 SEMANTIC SEARCH SHOULD BE WORKING!');
    console.log('   Test it by searching for concepts in your posts');
    console.log('   Example: "AI tools", "productivity tips", "design inspiration"');
  } else {
    console.log('⏳ Complete the actions above to enable semantic search');
  }
  
  console.log('\n' + '='.repeat(50));
  
  await mongoose.disconnect();
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
