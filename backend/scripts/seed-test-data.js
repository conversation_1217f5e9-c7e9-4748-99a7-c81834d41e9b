#!/usr/bin/env node

/**
 * Seed script for creating test data
 * Run with: node scripts/seed-test-data.js
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models - handle both CommonJS and ES modules
let User, AIUsageLog, StorageUsage;

try {
  User = require('../dist/models/User').default || require('../dist/models/User');
  AIUsageLog = require('../dist/models/AIUsageLog').default || require('../dist/models/AIUsageLog');
  StorageUsage = require('../dist/models/StorageUsage').default || require('../dist/models/StorageUsage');
} catch (error) {
  console.error('Error importing models:', error.message);
  console.log('Trying alternative import paths...');
  try {
    User = require('../src/models/User').default;
    AIUsageLog = require('../src/models/AIUsageLog').default;
    StorageUsage = require('../src/models/StorageUsage').default;
  } catch (srcError) {
    console.error('Failed to import from src as well:', srcError.message);
    process.exit(1);
  }
}

async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
}

async function createTestUsers() {
  console.log('👥 Creating test users...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Admin User',
      displayName: 'Admin',
      role: 'admin',
      plan: 'premium',
      subscriptionStatus: 'active',
      emailVerified: true,
      adsDisabled: true,
    },
    {
      email: '<EMAIL>',
      password: 'premium123',
      name: 'Premium User',
      displayName: 'Premium',
      role: 'user',
      plan: 'premium',
      subscriptionStatus: 'active',
      emailVerified: true,
      adsDisabled: false,
      stripeCustomerId: 'cus_test_premium',
      stripeSubscriptionId: 'sub_test_premium',
    },
    {
      email: '<EMAIL>',
      password: 'trial123',
      name: 'Trial User',
      displayName: 'Trial',
      role: 'user',
      plan: 'premium',
      subscriptionStatus: 'trialing',
      emailVerified: true,
      adsDisabled: false,
      stripeCustomerId: 'cus_test_trial',
      stripeSubscriptionId: 'sub_test_trial',
    },
    {
      email: '<EMAIL>',
      password: 'free123',
      name: 'Free User',
      displayName: 'Free',
      role: 'user',
      plan: 'free',
      subscriptionStatus: 'canceled',
      emailVerified: true,
      adsDisabled: false,
    },
    {
      email: '<EMAIL>',
      password: 'pastdue123',
      name: 'Past Due User',
      displayName: 'Past Due',
      role: 'user',
      plan: 'free',
      subscriptionStatus: 'past_due',
      emailVerified: true,
      adsDisabled: false,
      stripeCustomerId: 'cus_test_pastdue',
      stripeSubscriptionId: 'sub_test_pastdue',
    },
  ];

  const createdUsers = [];

  for (const userData of testUsers) {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        console.log(`⚠️  User ${userData.email} already exists, skipping...`);
        createdUsers.push(existingUser);
        continue;
      }

      // Create new user
      const user = new User(userData);
      await user.save();
      createdUsers.push(user);
      console.log(`✅ Created user: ${userData.email} (${userData.role})`);
    } catch (error) {
      console.error(`❌ Failed to create user ${userData.email}:`, error.message);
    }
  }

  return createdUsers;
}

async function createTestUsageLogs(users) {
  console.log('📊 Creating test usage logs...');

  const operations = ['categorization', 'insights', 'chat', 'analysis', 'content_generation'];
  const platforms = ['twitter', 'linkedin', 'reddit', 'instagram'];

  for (const user of users) {
    // Create AI usage logs for the last 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // Random number of operations per day (0-5)
      const operationsCount = Math.floor(Math.random() * 6);

      for (let j = 0; j < operationsCount; j++) {
        try {
          await AIUsageLog.create({
            userId: user._id,
            date: date,
            tokensUsed: Math.floor(Math.random() * 1000) + 100, // 100-1100 tokens
            operation: operations[Math.floor(Math.random() * operations.length)],
            platform: platforms[Math.floor(Math.random() * platforms.length)],
          });
        } catch (error) {
          // Ignore duplicate key errors
          if (!error.message.includes('duplicate key')) {
            console.error('Error creating AI usage log:', error.message);
          }
        }
      }

      // Create storage usage logs
      if (Math.random() > 0.7) { // 30% chance of storage usage per day
        try {
          await StorageUsage.create({
            userId: user._id,
            date: date,
            mbUsed: Math.random() * 10 + 1, // 1-11 MB
            operation: 'image_upload',
            platform: platforms[Math.floor(Math.random() * platforms.length)],
            fileType: 'image',
          });
        } catch (error) {
          if (!error.message.includes('duplicate key')) {
            console.error('Error creating storage usage log:', error.message);
          }
        }
      }
    }

    console.log(`✅ Created usage logs for ${user.email}`);
  }
}

async function updateUserStorageUsed(users) {
  console.log('💾 Updating user storage usage...');

  for (const user of users) {
    // Calculate total storage used from logs
    const totalStorageUsage = await StorageUsage.aggregate([
      { $match: { userId: user._id } },
      { $group: { _id: null, totalMB: { $sum: '$mbUsed' } } }
    ]);

    const totalMB = totalStorageUsage[0]?.totalMB || 0;
    const totalBytes = Math.floor(totalMB * 1024 * 1024);

    await User.findByIdAndUpdate(user._id, {
      storageUsed: totalBytes,
    });

    console.log(`✅ Updated storage for ${user.email}: ${totalMB.toFixed(2)} MB`);
  }
}

async function printSummary(users) {
  console.log('\n📋 Test Data Summary:');
  console.log('='.repeat(50));

  for (const user of users) {
    const aiUsage = await AIUsageLog.aggregate([
      { $match: { userId: user._id } },
      { $group: { _id: null, totalTokens: { $sum: '$tokensUsed' } } }
    ]);

    const storageUsage = await StorageUsage.aggregate([
      { $match: { userId: user._id } },
      { $group: { _id: null, totalMB: { $sum: '$mbUsed' } } }
    ]);

    console.log(`\n👤 ${user.email}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Plan: ${user.plan} (${user.subscriptionStatus})`);
    console.log(`   AI Tokens: ${aiUsage[0]?.totalTokens || 0}`);
    console.log(`   Storage: ${(storageUsage[0]?.totalMB || 0).toFixed(2)} MB`);
    console.log(`   Ads: ${user.adsDisabled ? 'Disabled' : 'Enabled'}`);
  }

  console.log('\n🔑 Login Credentials:');
  console.log('   Admin: <EMAIL> / admin123');
  console.log('   Premium: <EMAIL> / premium123');
  console.log('   Trial: <EMAIL> / trial123');
  console.log('   Free: <EMAIL> / free123');
  console.log('   Past Due: <EMAIL> / pastdue123');
}

async function main() {
  console.log('🌱 Starting seed script...\n');

  try {
    await connectDB();

    const users = await createTestUsers();
    await createTestUsageLogs(users);
    await updateUserStorageUsed(users);
    await printSummary(users);

    console.log('\n✅ Seed script completed successfully!');
  } catch (error) {
    console.error('\n❌ Seed script failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
