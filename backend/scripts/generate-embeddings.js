/**
 * Generate embeddings for existing posts that don't have them
 * This script will process all posts without embeddingVector and generate embeddings
 */

const mongoose = require('mongoose');
const fetch = require('node-fetch');
require('dotenv').config();

// Import the CloudPost model
const CloudPostSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  platform: { type: String, required: true },
  content: { type: String, default: '' },
  textContent: { type: String, default: '' },
  authorName: { type: String, default: '' },
  authorHandle: { type: String, default: '' },
  categories: { type: [String], default: [] },
  tags: { type: [String], default: [] },
  snapNote: { type: String, default: null },
  embeddingVector: { type: [Number], default: undefined },
  analyzedAt: { type: Date, default: null },
  savedAt: { type: Date, default: Date.now },
  permalink: { type: String, required: true }
}, { timestamps: true });

const CloudPost = mongoose.model('CloudPost', CloudPostSchema);

// Connect to MongoDB
async function connectDB() {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI;
    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables');
    }
    
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

// Generate embedding using OpenAI API
async function generateEmbedding(text) {
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;
  
  if (!OPENAI_API_KEY) {
    throw new Error('OpenAI API key not configured');
  }

  const response = await fetch('https://api.openai.com/v1/embeddings', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'text-embedding-3-small',
      input: text.replace(/\n/g, ' ').trim(),
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  if (!data.data || !data.data[0] || !data.data[0].embedding) {
    throw new Error('Invalid embedding response from OpenAI');
  }

  return data.data[0].embedding;
}

// Process posts in batches
async function generateEmbeddingsForPosts() {
  console.log('🔍 Finding posts without embeddings...');
  
  // Find posts without embeddings
  const postsWithoutEmbeddings = await CloudPost.find({
    $or: [
      { embeddingVector: { $exists: false } },
      { embeddingVector: null },
      { embeddingVector: [] }
    ]
  }).select('_id content textContent snapNote tags categories authorName');

  console.log(`📊 Found ${postsWithoutEmbeddings.length} posts without embeddings`);

  if (postsWithoutEmbeddings.length === 0) {
    console.log('✅ All posts already have embeddings!');
    return;
  }

  let processed = 0;
  let errors = 0;
  const batchSize = 10; // Process in small batches to avoid rate limits

  for (let i = 0; i < postsWithoutEmbeddings.length; i += batchSize) {
    const batch = postsWithoutEmbeddings.slice(i, i + batchSize);
    
    console.log(`\n📦 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(postsWithoutEmbeddings.length/batchSize)}`);
    
    for (const post of batch) {
      try {
        // Combine text content for embedding
        const textToEmbed = [
          post.content || post.textContent || '',
          post.snapNote || '',
          ...(post.tags || []),
          ...(post.categories || []),
          post.authorName || ''
        ].filter(Boolean).join(' | ');

        if (textToEmbed.trim().length === 0) {
          console.log(`⚠️  Skipping post ${post._id} - no text content`);
          continue;
        }

        console.log(`🔄 Generating embedding for post ${post._id}...`);
        
        // Generate embedding
        const embedding = await generateEmbedding(textToEmbed);
        
        // Update post with embedding
        await CloudPost.findByIdAndUpdate(post._id, {
          embeddingVector: embedding,
          analyzedAt: new Date()
        });

        processed++;
        console.log(`✅ Post ${post._id} updated with ${embedding.length}D embedding`);
        
        // Small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        errors++;
        console.error(`❌ Error processing post ${post._id}:`, error.message);
      }
    }
    
    // Longer delay between batches
    if (i + batchSize < postsWithoutEmbeddings.length) {
      console.log('⏳ Waiting 2 seconds before next batch...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  console.log('\n📋 Summary:');
  console.log(`✅ Successfully processed: ${processed} posts`);
  console.log(`❌ Errors: ${errors} posts`);
  console.log(`📊 Total found: ${postsWithoutEmbeddings.length} posts`);
}

// Main function
async function main() {
  console.log('🚀 Starting embedding generation script...\n');
  
  try {
    await connectDB();
    await generateEmbeddingsForPosts();
    console.log('\n✅ Embedding generation completed!');
  } catch (error) {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
