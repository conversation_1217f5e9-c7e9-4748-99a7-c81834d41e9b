#!/usr/bin/env node

/**
 * Test script for Stripe webhook functionality
 * Run with: npm run webhook:test
 */

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const crypto = require('crypto');
const axios = require('axios');

const WEBHOOK_ENDPOINT = process.env.WEBHOOK_ENDPOINT || 'http://localhost:3000/auth/stripe/webhook';
const WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;

if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY environment variable is required');
  process.exit(1);
}

if (!WEBHOOK_SECRET) {
  console.error('❌ STRIPE_WEBHOOK_SECRET environment variable is required');
  process.exit(1);
}

/**
 * Create a test webhook event
 */
function createTestEvent(type, data) {
  return {
    id: `evt_test_${Date.now()}`,
    object: 'event',
    api_version: '2023-10-16',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: data
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: null,
      idempotency_key: null
    },
    type: type
  };
}

/**
 * Generate webhook signature
 */
function generateSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const signedPayload = `${timestamp}.${payload}`;
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

/**
 * Send test webhook
 */
async function sendTestWebhook(event) {
  const payload = JSON.stringify(event);
  const signature = generateSignature(payload, WEBHOOK_SECRET);

  try {
    console.log(`📤 Sending ${event.type} webhook...`);
    
    const response = await axios.post(WEBHOOK_ENDPOINT, payload, {
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': signature,
      },
      timeout: 10000,
    });

    console.log(`✅ Webhook sent successfully: ${response.status}`);
    return response.data;
  } catch (error) {
    console.error(`❌ Webhook failed:`, error.response?.data || error.message);
    throw error;
  }
}

/**
 * Test subscription created event
 */
async function testSubscriptionCreated() {
  const subscription = {
    id: 'sub_test_123',
    object: 'subscription',
    customer: 'cus_test_123',
    status: 'active',
    current_period_start: Math.floor(Date.now() / 1000),
    current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
    trial_end: null,
  };

  const event = createTestEvent('customer.subscription.created', subscription);
  await sendTestWebhook(event);
}

/**
 * Test subscription updated event
 */
async function testSubscriptionUpdated() {
  const subscription = {
    id: 'sub_test_123',
    object: 'subscription',
    customer: 'cus_test_123',
    status: 'past_due',
    current_period_start: Math.floor(Date.now() / 1000),
    current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
    trial_end: null,
  };

  const event = createTestEvent('customer.subscription.updated', subscription);
  await sendTestWebhook(event);
}

/**
 * Test subscription deleted event
 */
async function testSubscriptionDeleted() {
  const subscription = {
    id: 'sub_test_123',
    object: 'subscription',
    customer: 'cus_test_123',
    status: 'canceled',
    current_period_start: Math.floor(Date.now() / 1000),
    current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
    trial_end: null,
  };

  const event = createTestEvent('customer.subscription.deleted', subscription);
  await sendTestWebhook(event);
}

/**
 * Test checkout session completed event
 */
async function testCheckoutCompleted() {
  const session = {
    id: 'cs_test_123',
    object: 'checkout.session',
    customer: 'cus_test_123',
    subscription: 'sub_test_123',
    metadata: {
      userId: '507f1f77bcf86cd799439011' // Test MongoDB ObjectId
    },
    payment_status: 'paid',
    status: 'complete',
  };

  const event = createTestEvent('checkout.session.completed', session);
  await sendTestWebhook(event);
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🧪 Starting Stripe webhook tests...\n');
  console.log(`📍 Webhook endpoint: ${WEBHOOK_ENDPOINT}`);
  console.log(`🔐 Using webhook secret: ${WEBHOOK_SECRET.substring(0, 10)}...\n`);

  const tests = [
    { name: 'Checkout Session Completed', fn: testCheckoutCompleted },
    { name: 'Subscription Created', fn: testSubscriptionCreated },
    { name: 'Subscription Updated', fn: testSubscriptionUpdated },
    { name: 'Subscription Deleted', fn: testSubscriptionDeleted },
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`\n🔄 Running: ${test.name}`);
      await test.fn();
      console.log(`✅ ${test.name} passed`);
      passed++;
    } catch (error) {
      console.error(`❌ ${test.name} failed`);
      failed++;
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed > 0) {
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  sendTestWebhook,
  createTestEvent,
};
