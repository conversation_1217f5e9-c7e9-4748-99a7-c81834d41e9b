const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// User schema (simplified for this script)
const userSchema = new mongoose.Schema({
  plan: { type: String, enum: ['free', 'premium'], default: 'free' },
  storageLimit: { type: Number, default: 1073741824 }, // 1GB default
  storageUsed: { type: Number, default: 0 },
  email: String,
  displayName: String,
});

const User = mongoose.model('User', userSchema);

async function fixPremiumStorageLimits() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');

    // Find all premium users with incorrect storage limits
    const premiumUsers = await User.find({
      plan: 'premium',
      storageLimit: { $ne: 10737418240 } // Not equal to 10GB
    });

    console.log(`Found ${premiumUsers.length} premium users with incorrect storage limits`);

    if (premiumUsers.length === 0) {
      console.log('All premium users already have correct storage limits');
      return;
    }

    // Update storage limits for premium users
    const result = await User.updateMany(
      { 
        plan: 'premium',
        storageLimit: { $ne: 10737418240 }
      },
      { 
        $set: { storageLimit: 10737418240 } // 10GB in bytes
      }
    );

    console.log(`Updated ${result.modifiedCount} premium users to have 10GB storage limit`);

    // Verify the changes
    const updatedUsers = await User.find({ plan: 'premium' }).select('email displayName storageLimit storageUsed');
    console.log('\nPremium users after update:');
    updatedUsers.forEach(user => {
      const limitGB = Math.round(user.storageLimit / (1024 * 1024 * 1024) * 100) / 100;
      const usedMB = Math.round(user.storageUsed / (1024 * 1024) * 100) / 100;
      console.log(`- ${user.email || user.displayName}: ${limitGB}GB limit, ${usedMB}MB used`);
    });

  } catch (error) {
    console.error('Error fixing premium storage limits:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
fixPremiumStorageLimits();
