#!/bin/bash

# Build the application
echo "Building the application..."
npm run build

# Create a temporary .env file for Railway
echo "Creating temporary .env file for Railway..."
cat > .env.railway << EOL
MONGO_URI=${MONGO_URI}
JWT_SECRET=${JWT_SECRET}
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
AWS_S3_REGION=${AWS_S3_REGION}
NODE_ENV=production
EOL

# Deploy to Railway
echo "Deploying to Railway..."
railway up

# Clean up
echo "Cleaning up..."
rm .env.railway

echo "Deployment complete!"
