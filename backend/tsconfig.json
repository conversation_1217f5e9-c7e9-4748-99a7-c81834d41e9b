{
  "compilerOptions": {
    "target": "ES2016",                     // Specify ECMAScript target version
    "module": "CommonJS",                   // Specify module code generation
    "outDir": "./dist",                     // Redirect output structure to the directory
    "rootDir": "./src",                     // Specify the root directory of input files
    "strict": true,                         // Enable all strict type-checking options
    "esModuleInterop": true,                // Enables emit interoperability between CommonJS and ES Modules
    "skipLibCheck": true,                   // Skip type checking of declaration files
    "forceConsistentCasingInFileNames": true, // Disallow inconsistently-cased references to the same file
    "resolveJsonModule": true,              // Include modules imported with .json extension
    "noImplicitAny": false,                 // Allow implicit any types
    "strictNullChecks": false               // Disable strict null checks
  },
  "include": ["src/**/*"],                  // Include all files in the src directory
  "exclude": ["node_modules", "**/*.spec.ts"] // Exclude node_modules and test files
}