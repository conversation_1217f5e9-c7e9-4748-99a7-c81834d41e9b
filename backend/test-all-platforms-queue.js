/**
 * Test All Platforms Queue Processing
 * 
 * This script tests the queue-based image processing system for all platforms
 * to ensure they all work consistently.
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:3000/api';
const TEST_USER_TOKEN = 'your-test-jwt-token-here'; // Replace with actual token

// Sample post data for each platform
const samplePosts = {
  'X/Twitter': {
    originalPostId: 'twitter-test-' + Date.now(),
    platform: 'X/Twitter',
    authorName: '<PERSON>',
    authorHandle: '@johndoe',
    authorAvatar: 'https://pbs.twimg.com/profile_images/sample-avatar.jpg',
    content: 'This is a test Twitter post with images to verify queue processing works correctly.',
    timestamp: new Date().toISOString(),
    permalink: 'https://twitter.com/johndoe/status/' + Date.now(),
    media: [
      {
        type: 'image',
        url: 'https://pbs.twimg.com/media/sample-image-1.jpg',
        alt: 'Sample Twitter image 1',
        width: 800,
        height: 600
      }
    ],
    interactions: { likes: 42, replies: 5, reposts: 3 }
  },

  LinkedIn: {
    originalPostId: 'linkedin-test-' + Date.now(),
    platform: 'LinkedIn',
    authorName: 'Jane Smith',
    authorHandle: 'janesmith',
    authorAvatar: 'https://media.licdn.com/dms/image/sample-avatar.jpg',
    content: 'This is a test LinkedIn post with images to verify queue processing works correctly.',
    timestamp: new Date().toISOString(),
    permalink: 'https://www.linkedin.com/posts/janesmith_test-post-' + Date.now(),
    media: [
      {
        type: 'image',
        url: 'https://media.licdn.com/dms/image/sample-image-1.jpg',
        alt: 'Sample LinkedIn image 1',
        width: 1200,
        height: 800
      }
    ],
    interactions: { likes: 25, comments: 8, shares: 2 }
  },

  Instagram: {
    originalPostId: 'instagram-test-' + Date.now(),
    platform: 'Instagram',
    authorName: 'Mike Johnson',
    authorHandle: 'mikejohnson',
    authorAvatar: 'https://instagram.com/static/images/sample-avatar.jpg',
    content: 'This is a test Instagram post with images to verify queue processing works correctly.',
    timestamp: new Date().toISOString(),
    permalink: 'https://www.instagram.com/p/test-post-' + Date.now(),
    media: [
      {
        type: 'image',
        url: 'https://instagram.com/static/images/sample-image-1.jpg',
        alt: 'Sample Instagram image 1',
        width: 1080,
        height: 1080
      }
    ],
    interactions: { likes: 156, comments: 23 }
  },

  Reddit: {
    originalPostId: 'reddit-test-' + Date.now(),
    platform: 'Reddit',
    authorName: 'reddituser123',
    authorHandle: 'reddituser123',
    authorAvatar: 'https://www.redditstatic.com/avatars/sample-avatar.png',
    content: 'This is a test Reddit post with images to verify queue processing works correctly.',
    timestamp: new Date().toISOString(),
    permalink: 'https://www.reddit.com/r/test/comments/test-post-' + Date.now(),
    media: [
      {
        type: 'image',
        url: 'https://i.redd.it/sample-image-1.jpg',
        alt: 'Sample Reddit image 1',
        width: 800,
        height: 600
      }
    ],
    interactions: { likes: 89, comments: 34 }
  },

  Pinterest: {
    originalPostId: 'pinterest-test-' + Date.now(),
    platform: 'Pinterest',
    authorName: 'Sarah Wilson',
    authorHandle: 'sarahwilson',
    authorAvatar: 'https://i.pinimg.com/avatars/sample-avatar.jpg',
    content: 'This is a test Pinterest pin with images to verify queue processing works correctly.',
    timestamp: new Date().toISOString(),
    permalink: 'https://www.pinterest.com/pin/test-pin-' + Date.now(),
    media: [
      {
        type: 'image',
        url: 'https://i.pinimg.com/originals/sample-image-1.jpg',
        alt: 'Sample Pinterest image 1',
        width: 600,
        height: 900
      }
    ],
    interactions: { likes: 67, comments: 12 }
  }
};

async function testPlatformQueueProcessing(platform, postData) {
  console.log(`\n🧪 Testing ${platform} Queue Processing...`);

  try {
    // Create post (should trigger queue processing)
    console.log(`📝 Creating ${platform} post...`);
    const createResponse = await axios.post(`${API_BASE_URL}/posts`, postData, {
      headers: {
        'Authorization': `Bearer ${TEST_USER_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (createResponse.status === 201) {
      console.log(`✅ ${platform} post created successfully!`);
      console.log(`   Post ID: ${createResponse.data._id}`);
      console.log(`   Image Processing Status: ${createResponse.data.imageProcessingStatus}`);
      console.log(`   Job ID: ${createResponse.data.imageProcessingJobId || 'None'}`);
      
      return {
        platform,
        success: true,
        postId: createResponse.data._id,
        status: createResponse.data.imageProcessingStatus,
        jobId: createResponse.data.imageProcessingJobId
      };
    } else {
      console.error(`❌ Failed to create ${platform} post:`, createResponse.status);
      return { platform, success: false, error: `HTTP ${createResponse.status}` };
    }

  } catch (error) {
    console.error(`❌ ${platform} test failed:`, error.message);
    return { platform, success: false, error: error.message };
  }
}

async function checkAllProcessingStatus(results) {
  console.log('\n🔍 Checking processing status for all platforms...');
  
  for (const result of results) {
    if (result.success && result.postId) {
      try {
        const statusResponse = await axios.get(`${API_BASE_URL}/queue/posts/${result.postId}/status`, {
          headers: {
            'Authorization': `Bearer ${TEST_USER_TOKEN}`
          }
        });

        if (statusResponse.status === 200) {
          const status = statusResponse.data;
          console.log(`   ${result.platform}: ${status.status} (Job: ${status.jobId || 'None'})`);
        }
      } catch (error) {
        console.error(`   ${result.platform}: Failed to get status - ${error.message}`);
      }
    }
  }
}

async function testAllPlatforms() {
  console.log('🚀 All Platforms Queue Processing Test');
  console.log('==========================================\n');
  console.log('⚠️  SETUP REQUIRED:');
  console.log('1. Make sure the backend server is running (npm run dev)');
  console.log('2. Make sure Redis is running');
  console.log('3. Make sure the worker is running (npm run worker)');
  console.log('4. Replace TEST_USER_TOKEN with a valid JWT token');
  console.log('5. Update API_BASE_URL if needed\n');
  
  if (TEST_USER_TOKEN === 'your-test-jwt-token-here') {
    console.log('❌ Please update TEST_USER_TOKEN before running the test!');
    process.exit(1);
  }

  const results = [];

  // Test each platform
  for (const [platform, postData] of Object.entries(samplePosts)) {
    const result = await testPlatformQueueProcessing(platform, postData);
    results.push(result);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  successful.forEach(r => console.log(`   - ${r.platform}`));
  
  if (failed.length > 0) {
    console.log(`❌ Failed: ${failed.length}/${results.length}`);
    failed.forEach(r => console.log(`   - ${r.platform}: ${r.error}`));
  }

  // Check processing status after a delay
  console.log('\n⏳ Waiting 10 seconds for background processing...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  await checkAllProcessingStatus(results);

  console.log('\n🎉 All platform tests completed!');
  console.log('Check the worker logs to see image processing progress.');
}

// Run the test
if (require.main === module) {
  testAllPlatforms();
}

module.exports = { testAllPlatforms };
