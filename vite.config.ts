import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { viteStaticCopy } from 'vite-plugin-static-copy';

// https://vitejs.dev/config/
export default defineConfig({
  base: '',
  plugins: [
    react(),
    viteStaticCopy({
      targets: [
        // Remove the instagram entries as they were based on incorrect assumption
      ]
    })
  ],
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  css: {
    postcss: './postcss.config.cjs',
  },
  build: {
    outDir: 'dist',
    base: './',
    rollupOptions: {
      input: {
        // HTML pages
        dashboard: resolve(__dirname, 'dashboard.html'),
        settings: resolve(__dirname, 'settings.html'),
        postDetail: resolve(__dirname, 'post-detail.html'),
        // Background script and content scripts are built separately with esbuild
        // See scripts/build.sh for details
      },
      output: {
        format: 'es',
        entryFileNames: (chunkInfo) => {
          // Special handling for detail page
          if (chunkInfo.name === 'main') {
            return 'assets/detailMain.js';
          }
          return 'assets/[name].js';
        },
        chunkFileNames: 'assets/[name]-chunk.js',
        assetFileNames: 'assets/[name].[ext]',
      }
    },
    emptyOutDir: false, // Set to false to prevent Vite from deleting esbuild output
  }
});
