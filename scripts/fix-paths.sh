#!/bin/bash

# This script is crucial for Chrome Extension builds.
# Vite builds with an absolute path `/` which works for web servers,
# but fails for extensions loaded via `file://`.
# This script converts all absolute asset paths to relative paths.

# Directory containing the production build
BUILD_DIR="dist"

# Check if the build directory exists
if [ ! -d "$BUILD_DIR" ]; then
  echo "Build directory '$BUILD_DIR' not found. Run the build first."
  exit 1
fi

echo "Fixing relative paths in HTML files..."

# For macOS, we need to pass an empty string to sed -i
if [[ "$OSTYPE" == "darwin"* ]]; then
  find "$BUILD_DIR" -name "*.html" -print0 | xargs -0 sed -i '' 's|src="/assets/|src="./assets/|g'
  find "$BUILD_DIR" -name "*.html" -print0 | xargs -0 sed -i '' 's|href="/assets/|href="./assets/|g'
  find "$BUILD_DIR" -name "*.html" -print0 | xargs -0 sed -i '' 's|href="/|href="./|g'
else
  # For Linux
  find "$BUILD_DIR" -name "*.html" -print0 | xargs -0 sed -i 's|src="/assets/|src="./assets/|g'
  find "$BUILD_DIR" -name "*.html" -print0 | xargs -0 sed -i 's|href="/assets/|href="./assets/|g'
  find "$BUILD_DIR" -name "*.html" -print0 | xargs -0 sed -i 's|href="/|href="./|g'
fi

echo "Paths fixed successfully!" 