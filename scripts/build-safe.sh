#!/bin/bash
set -e

# Alternative build script that creates manifest.json first
# Use this if you're having issues with the regular build script

echo "🚀 Starting safe build process..."

# Clean up
echo "🧹 Cleaning up..."
rm -rf dist/
mkdir -p dist/assets/content

# Validate the source manifest.json first
echo "📋 Validating source manifest.json..."
if ! python3 -m json.tool public/manifest.json > /dev/null 2>&1; then
  echo "❌ Error: public/manifest.json is not valid JSON!"
  exit 1
fi
echo "✅ Source manifest is valid"

# Build content scripts
echo "🔧 Building content scripts..."
esbuild src/content/twitter-content.ts --bundle --format=iife --outfile=dist/twitter-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/platforms/LinkedInContentScript.ts --bundle --format=iife --outfile=dist/linkedin-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/instagram-content.ts --bundle --format=iife --outfile=dist/instagram-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/reddit-content.ts --bundle --format=iife --outfile=dist/reddit-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/pinterest-content.ts --bundle --format=iife --outfile=dist/pinterest-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/web-content.ts --bundle --format=iife --outfile=dist/assets/content/web.js --target=esnext --sourcemap --allow-overwrite

# Build background script
echo "🔧 Building background script..."
esbuild src/background/background.ts --bundle --format=iife --outfile=dist/background.js --target=esnext --sourcemap --allow-overwrite --define:import.meta.env.VITE_OPENAI_API_KEY="\"$VITE_OPENAI_API_KEY\"" --define:process.env.OPENAI_API_KEY="\"$VITE_OPENAI_API_KEY\""

# Copy static assets
echo "📁 Copying static assets..."
cp -r public/icons dist/
cp -r public/extension-icons dist/
cp public/popup.html dist/
mkdir -p dist/assets
cp public/assets/popup.js dist/assets/

# Build the rest with Vite
echo "⚡ Building with Vite..."
vite build

# Fix relative paths for extension compatibility
echo "🔧 Fixing relative paths..."
bash scripts/fix-paths.sh

# Force copy the correct manifest.json AFTER all other operations
echo "📋 Ensuring correct manifest.json..."
cp public/manifest.json dist/manifest.json
sync
sleep 0.2

# Final validation
echo "🔍 Final validation..."
if [ ! -f "dist/manifest.json" ]; then
  echo "❌ Error: manifest.json is missing!"
  exit 1
fi

if ! python3 -m json.tool dist/manifest.json > /dev/null 2>&1; then
  echo "❌ Error: manifest.json is corrupted!"
  echo "Content:"
  cat dist/manifest.json
  exit 1
fi

# Ensure all files are synced
sync

echo "✅ Safe build complete!"
echo "📦 Extension ready in dist/ folder"
