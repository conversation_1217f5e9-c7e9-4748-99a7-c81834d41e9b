#!/usr/bin/env node

/**
 * Stripe Setup Script
 * 
 * This script helps you set up Stripe products and prices for Notely.
 * Run with: node scripts/setup-stripe.js
 * 
 * Make sure to set your STRIPE_SECRET_KEY environment variable first:
 * export STRIPE_SECRET_KEY=sk_test_...
 */

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

async function setupStripeProducts() {
  if (!process.env.STRIPE_SECRET_KEY) {
    console.error('❌ Please set STRIPE_SECRET_KEY environment variable');
    console.log('   export STRIPE_SECRET_KEY=sk_test_...');
    process.exit(1);
  }

  console.log('🚀 Setting up Stripe products and prices for Notely...\n');

  try {
    // Create product
    console.log('📦 Creating product...');
    const product = await stripe.products.create({
      name: 'Notely Premium',
      description: 'Premium subscription for Notely - unlimited AI analysis and advanced features',
      metadata: {
        app: 'notely',
        version: '1.0'
      }
    });
    console.log(`✅ Product created: ${product.id}`);

    // Create monthly price
    console.log('💰 Creating monthly price...');
    const monthlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: 999, // $9.99 in cents
      currency: 'usd',
      recurring: {
        interval: 'month'
      },
      metadata: {
        plan: 'monthly'
      }
    });
    console.log(`✅ Monthly price created: ${monthlyPrice.id}`);

    // Create yearly price
    console.log('💰 Creating yearly price...');
    const yearlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: 9999, // $99.99 in cents
      currency: 'usd',
      recurring: {
        interval: 'year'
      },
      metadata: {
        plan: 'yearly'
      }
    });
    console.log(`✅ Yearly price created: ${yearlyPrice.id}`);

    console.log('\n🎉 Setup complete! Here are your price IDs:\n');
    console.log('📋 Copy these to your configuration:');
    console.log('─'.repeat(50));
    console.log(`Monthly Price ID: ${monthlyPrice.id}`);
    console.log(`Yearly Price ID:  ${yearlyPrice.id}`);
    console.log('─'.repeat(50));
    
    console.log('\n📝 Next steps:');
    console.log('1. Update src/config/stripe.ts with these price IDs');
    console.log('2. Set environment variables in your backend:');
    console.log(`   STRIPE_PRICE_ID_MONTHLY=${monthlyPrice.id}`);
    console.log(`   STRIPE_PRICE_ID_YEARLY=${yearlyPrice.id}`);
    console.log('3. Update Railway environment variables');
    console.log('4. Test the subscription flow');

  } catch (error) {
    console.error('❌ Error setting up Stripe:', error.message);
    
    if (error.type === 'StripeAuthenticationError') {
      console.log('\n💡 Make sure your STRIPE_SECRET_KEY is correct and has the right permissions');
    }
    
    process.exit(1);
  }
}

// Run the setup
setupStripeProducts();
