#!/bin/bash
set -e

# Clean up
echo "Cleaning up..."
rm -rf dist/
mkdir -p dist/assets/content

# Add a small delay to ensure file system is ready
sleep 0.5

# Build content scripts
echo "Building content scripts..."
esbuild src/content/twitter-content.ts --bundle --format=iife --outfile=dist/twitter-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/platforms/LinkedInContentScript.ts --bundle --format=iife --outfile=dist/linkedin-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/instagram-content.ts --bundle --format=iife --outfile=dist/instagram-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/reddit-content.ts --bundle --format=iife --outfile=dist/reddit-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/pinterest-content.ts --bundle --format=iife --outfile=dist/pinterest-content.js --target=esnext --sourcemap --allow-overwrite
esbuild src/content/web-content.ts --bundle --format=iife --outfile=dist/assets/content/web.js --target=esnext --sourcemap --allow-overwrite

# Build background script
echo "Building background script..."
esbuild src/background/background.ts --bundle --format=iife --outfile=dist/background.js --target=esnext --sourcemap --allow-overwrite --define:import.meta.env.VITE_OPENAI_API_KEY="\"$VITE_OPENAI_API_KEY\"" --define:process.env.OPENAI_API_KEY="\"$VITE_OPENAI_API_KEY\""

# Copy static assets
echo "Copying static assets..."
cp -r public/icons dist/
cp -r public/extension-icons dist/
cp public/popup.html dist/
mkdir -p dist/assets
cp public/assets/popup.js dist/assets/

# Build the rest with Vite
echo "Building with Vite..."
vite build

# Force copy the correct manifest.json (Vite might not copy it properly)
echo "Ensuring correct manifest.json..."
cp public/manifest.json dist/manifest.json

# Fix relative paths for extension compatibility
bash scripts/fix-paths.sh

# Ensure manifest.json is properly written and synced
sync
sleep 0.5

# Validate the manifest.json was created correctly
if [ ! -f "dist/manifest.json" ]; then
  echo "❌ Error: manifest.json was not created!"
  exit 1
fi

# Validate JSON syntax
if ! python3 -m json.tool dist/manifest.json > /dev/null 2>&1; then
  echo "❌ Error: manifest.json is not valid JSON!"
  echo "Content of manifest.json:"
  cat dist/manifest.json
  exit 1
fi

echo "✅ Manifest validation passed!"
echo "Build complete!"
