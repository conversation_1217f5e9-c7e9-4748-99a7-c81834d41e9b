#!/bin/bash

echo "Building web version..."

# Clean previous build
rm -rf dist-web

# Build with Vite
vite build --config vite.config.web.ts

# Move web files to root of dist-web
if [ -d "dist-web/web" ]; then
  mv dist-web/web/index.html dist-web/
  rmdir dist-web/web
fi

# Copy necessary static assets
cp public/notely.svg dist-web/
cp public/notely-dark.svg dist-web/
cp -r public/icons dist-web/

echo "Web build complete! Files are in dist-web/"
echo "You can now deploy the dist-web folder to your hosting service."
