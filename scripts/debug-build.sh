#!/bin/bash

# Debug script to help diagnose build issues
echo "🔍 Diagnosing build environment..."

# Check system info
echo "📊 System Information:"
echo "OS: $(uname -s)"
echo "Architecture: $(uname -m)"
echo "Shell: $SHELL"
echo "Node version: $(node --version)"
echo "NPM version: $(npm --version)"

# Check file system
echo ""
echo "📁 File System Check:"
echo "Current directory: $(pwd)"
echo "Disk space: $(df -h . | tail -1)"

# Check if dist directory exists and its permissions
if [ -d "dist" ]; then
  echo "dist/ directory exists"
  echo "dist/ permissions: $(ls -ld dist)"
  echo "dist/ contents: $(ls -la dist | wc -l) items"
else
  echo "dist/ directory does not exist"
fi

# Check for file locking issues
echo ""
echo "🔒 File Locking Check:"
if command -v lsof >/dev/null 2>&1; then
  echo "Checking for locked files in dist/..."
  lsof +D dist/ 2>/dev/null || echo "No locked files found"
else
  echo "lsof not available, skipping file lock check"
fi

# Check Python availability for JSON validation
echo ""
echo "🐍 Python Check:"
if command -v python3 >/dev/null 2>&1; then
  echo "Python3 available: $(python3 --version)"
  echo "Testing JSON validation..."
  echo '{"test": true}' | python3 -m json.tool > /dev/null && echo "JSON validation works" || echo "JSON validation failed"
else
  echo "Python3 not available - JSON validation will be skipped"
fi

# Check build tools
echo ""
echo "🛠️ Build Tools Check:"
if command -v esbuild >/dev/null 2>&1; then
  echo "esbuild available: $(esbuild --version)"
else
  echo "esbuild not found"
fi

if command -v vite >/dev/null 2>&1; then
  echo "vite available: $(vite --version)"
else
  echo "vite not found"
fi

# Test file creation and deletion
echo ""
echo "📝 File System Test:"
TEST_FILE="test_file_$$"
echo "test" > "$TEST_FILE" && echo "File creation: OK" || echo "File creation: FAILED"
if [ -f "$TEST_FILE" ]; then
  rm "$TEST_FILE" && echo "File deletion: OK" || echo "File deletion: FAILED"
fi

# Check for any Chrome processes that might be locking files
echo ""
echo "🌐 Chrome Process Check:"
if command -v pgrep >/dev/null 2>&1; then
  CHROME_PROCS=$(pgrep -f "Chrome" | wc -l)
  echo "Chrome processes running: $CHROME_PROCS"
  if [ "$CHROME_PROCS" -gt 0 ]; then
    echo "💡 Tip: Close Chrome completely before building to avoid file locking issues"
  fi
else
  echo "pgrep not available, skipping Chrome process check"
fi

echo ""
echo "✅ Diagnosis complete!"
echo ""
echo "🔧 Recommended solutions for manifest issues:"
echo "1. Use 'npm run build:safe' instead of 'npm run build'"
echo "2. Close Chrome completely before building"
echo "3. Run 'npm run clean' before building"
echo "4. If issue persists, try building in a different terminal"
