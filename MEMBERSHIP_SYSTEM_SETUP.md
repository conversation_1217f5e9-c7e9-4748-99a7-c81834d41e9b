# Membership System Setup

This document outlines the setup and configuration of the Notely Social membership system.

## Overview

The membership system provides:
- Dual authentication (Google OAuth + Email/Password)
- Subscription management with Stripe
- Role-based access control
- Usage tracking (AI tokens and storage)
- Admin interface for user management

## Architecture

- **Backend**: Express.js with MongoDB (Mongoose)
- **Authentication**: Passport.js with JWT
- **Payments**: Stripe with webhooks
- **Deployment**: Railway

## Environment Variables

Add these variables to your `.env` file:

```env
# Existing variables
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret_key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRICE_ID_MONTHLY=price_...
STRIPE_PRICE_ID_YEARLY=price_...

# Email Configuration (for password reset)
EMAIL_SMTP_URL=smtp://user:<EMAIL>:587
EMAIL_FROM=<EMAIL>

# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Client URLs
CLIENT_URL=chrome-extension://nmgooacbgjndjpajpiifiidbmchlpeae
SERVER_BASE_URL=https://your-api-domain.com
```

## Database Schema Updates

The system adds new fields to the existing User model:

```typescript
interface IUser {
  // Existing fields...
  
  // New membership fields
  subscriptionStatus: 'trialing' | 'active' | 'past_due' | 'canceled';
  role: 'user' | 'admin';
  emailVerified: boolean;
  adsDisabled: boolean;
}
```

New models:
- `PasswordReset`: For password reset tokens
- `AIUsageLog`: For tracking AI token usage
- `StorageUsage`: For tracking storage consumption

## API Endpoints

### Authentication
- `POST /auth/register` - Email/password registration
- `POST /auth/login` - Email/password login
- `GET /auth/me` - Get user profile with subscription status
- `POST /auth/request-password-reset` - Request password reset
- `POST /auth/reset-password` - Reset password with token

### Billing
- `POST /billing/create-portal-session` - Create Stripe customer portal
- `POST /billing/cancel-subscription` - Cancel subscription
- `POST /billing/reactivate-subscription` - Reactivate subscription
- `GET /billing/subscription` - Get subscription details

### Admin
- `GET /admin/users` - List users with filtering
- `GET /admin/users/:userId` - Get user details with usage stats
- `PATCH /admin/users/:userId/ads` - Toggle ads for user
- `PATCH /admin/users/:userId/subscription` - Update user subscription
- `GET /admin/stats/usage` - Get usage statistics overview
- `POST /admin/users/:userId/warn` - Send usage warning email

### Stripe Webhooks
- `POST /auth/stripe/webhook` - Handle subscription events

## Stripe Configuration

1. **Create Products and Prices** in Stripe Dashboard:
   - Monthly Premium Plan
   - Yearly Premium Plan

2. **Configure Webhook Endpoint**:
   - URL: `https://your-api-domain.com/auth/stripe/webhook`
   - Events: `customer.subscription.created`, `customer.subscription.updated`, `customer.subscription.deleted`, `checkout.session.completed`

3. **Test with Stripe CLI**:
   ```bash
   stripe listen --forward-to localhost:3000/auth/stripe/webhook
   ```

## Usage Tracking

The system tracks:
- **AI Usage**: Token consumption per operation
- **Storage Usage**: File storage in MB

Usage is logged automatically when:
- AI operations are performed
- Images are uploaded/processed

## Admin Interface

Admins can:
- View all users with filtering by plan/status
- Toggle ads for individual users
- View usage statistics
- Send warning emails for excessive usage
- Manually update user subscriptions

## Testing

### Seed Data
```javascript
// Create test users
const testUser = await User.create({
  email: '<EMAIL>',
  password: 'password123',
  name: 'Test User',
  plan: 'free',
  subscriptionStatus: 'trialing'
});
```

### Test Stripe Webhooks
```bash
npm run webhook:test
```

## Deployment

1. **Update Railway Environment Variables**:
   ```bash
   railway variables set STRIPE_SECRET_KEY=sk_live_...
   railway variables set STRIPE_WEBHOOK_SECRET=whsec_...
   railway variables set ADMIN_EMAILS=<EMAIL>
   ```

2. **Deploy**:
   ```bash
   railway up
   ```

## Security Considerations

- JWT tokens expire in 7 days
- Password reset tokens expire in 30 minutes
- Admin access is email-based
- Stripe webhooks are signature-verified
- Sensitive data is excluded from API responses

## Frontend Integration

### Using PremiumBadge Component
```tsx
import { PremiumBadge } from '../components/PremiumBadge';

<PremiumBadge 
  plan={user.plan} 
  subscriptionStatus={user.subscriptionStatus}
  size="md" 
/>
```

### Using BillingSettings Component
```tsx
import { BillingSettings } from '../components/BillingSettings';

<BillingSettings />
```

### Ad Display Logic
```tsx
const shouldShowAds = user.plan === 'free' || 
                     user.subscriptionStatus === 'trialing' ||
                     !user.adsDisabled;
```

## Troubleshooting

### Common Issues

1. **Webhook Signature Verification Fails**
   - Check `STRIPE_WEBHOOK_SECRET` is correct
   - Ensure raw body is passed to webhook handler

2. **Subscription Status Not Updating**
   - Verify webhook events are being received
   - Check Stripe webhook logs

3. **Admin Access Denied**
   - Verify email is in `ADMIN_EMAILS` environment variable
   - Check email case sensitivity

### Logs
Monitor these logs for issues:
- Stripe webhook events
- Subscription verification errors
- Authentication failures
- Usage tracking errors

## Support

For issues with the membership system:
1. Check Railway logs
2. Verify Stripe webhook delivery
3. Test with Stripe test mode
4. Review environment variables
