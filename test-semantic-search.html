<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Semantic Search - Notely Social</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0f0f0f;
            color: #ffffff;
        }
        .container {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #333;
        }
        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #252525;
            color: #fff;
            font-size: 16px;
            margin-bottom: 16px;
        }
        .search-input:focus {
            outline: none;
            border-color: #74c0fc;
            box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
        }
        .btn {
            background: #74c0fc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        .btn:hover {
            background: #5ba7f7;
        }
        .btn-secondary {
            background: #333;
            color: #fff;
        }
        .btn-secondary:hover {
            background: #444;
        }
        .results {
            margin-top: 24px;
            padding: 16px;
            background: #252525;
            border-radius: 8px;
            border: 1px solid #333;
        }
        .loading {
            text-align: center;
            color: #666;
            padding: 20px;
        }
        .error {
            color: #ff6b6b;
            padding: 16px;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 107, 107, 0.2);
        }
        .success {
            color: #51cf66;
            padding: 16px;
            background: rgba(81, 207, 102, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(81, 207, 102, 0.2);
        }
        .result-item {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }
        .platform {
            background: #333;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .similarity {
            color: #74c0fc;
            font-size: 12px;
        }
        .content {
            color: #b3b3b3;
            line-height: 1.5;
        }
        .summary {
            background: #1a1a1a;
            border: 1px solid #51cf66;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .summary h3 {
            color: #51cf66;
            margin-top: 0;
        }
        .key-findings {
            margin: 12px 0;
        }
        .key-findings li {
            margin-bottom: 4px;
        }
        .suggestions {
            margin-top: 16px;
        }
        .suggestion-btn {
            background: #333;
            color: #fff;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        .suggestion-btn:hover {
            background: #444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Semantic Search</h1>
        <p>Test the "Ask My Bookmarks" semantic search functionality for Notely Social.</p>

        <div>
            <input
                type="text"
                id="searchInput"
                class="search-input"
                placeholder="Ask My Bookmarks… (e.g., 'AI tools for productivity')"
            >
            <button onclick="performSearch()" class="btn">Search</button>
            <button onclick="testConnection()" class="btn btn-secondary">Test Connection</button>
            <button onclick="loadSuggestions()" class="btn btn-secondary">Load Suggestions</button>
        </div>

        <div class="suggestions">
            <h3>Quick Test Queries:</h3>
            <button onclick="setQuery('AI tools and automation')" class="suggestion-btn">AI tools and automation</button>
            <button onclick="setQuery('productivity tips')" class="suggestion-btn">productivity tips</button>
            <button onclick="setQuery('design inspiration')" class="suggestion-btn">design inspiration</button>
            <button onclick="setQuery('business insights')" class="suggestion-btn">business insights</button>
            <button onclick="setQuery('technology trends')" class="suggestion-btn">technology trends</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_URL = 'https://api.notely.social';

        function setQuery(query) {
            document.getElementById('searchInput').value = query;
        }

        async function getAuthToken() {
            return new Promise((resolve) => {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage(
                        { action: 'GET_AUTH_TOKEN' },
                        (response) => {
                            if (chrome.runtime.lastError) {
                                console.error('Error getting auth token:', chrome.runtime.lastError);
                                resolve(null);
                                return;
                            }
                            resolve(response?.token || null);
                        }
                    );
                } else {
                    // For testing outside extension context
                    const token = localStorage.getItem('test_auth_token') || prompt('Enter your JWT token for testing:');
                    if (token) {
                        localStorage.setItem('test_auth_token', token);
                    }
                    resolve(token);
                }
            });
        }

        async function testConnection() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">Testing connection...</div>';

            try {
                const token = await getAuthToken();
                if (!token) {
                    resultsDiv.innerHTML = '<div class="error">❌ No authentication token found. Please log in to the extension first.</div>';
                    return;
                }

                const response = await fetch(`${API_URL}/health`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ Connection successful!<br>
                            Status: ${data.status}<br>
                            Environment: ${data.environment}<br>
                            Timestamp: ${data.timestamp}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Connection failed: ${response.status} ${response.statusText}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Connection error: ${error.message}</div>`;
            }
        }

        async function loadSuggestions() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">Loading search suggestions...</div>';

            try {
                const token = await getAuthToken();
                if (!token) {
                    resultsDiv.innerHTML = '<div class="error">❌ No authentication token found. Please log in to the extension first.</div>';
                    return;
                }

                const response = await fetch(`${API_URL}/api/posts/search/suggestions`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    const suggestionsHtml = data.suggestions.map(suggestion =>
                        `<button onclick="setQuery('${suggestion}')" class="suggestion-btn">${suggestion}</button>`
                    ).join('');

                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ Suggestions loaded! (${data.totalPosts} posts analyzed)<br>
                            <div style="margin-top: 12px;">
                                ${suggestionsHtml}
                            </div>
                        </div>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    resultsDiv.innerHTML = `<div class="error">❌ Failed to load suggestions: ${errorData.message || response.statusText}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error loading suggestions: ${error.message}</div>`;
            }
        }

        async function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            const resultsDiv = document.getElementById('results');

            if (!query) {
                resultsDiv.innerHTML = '<div class="error">❌ Please enter a search query</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="loading">🔍 Searching your bookmarks...</div>';

            try {
                const token = await getAuthToken();
                if (!token) {
                    resultsDiv.innerHTML = '<div class="error">❌ No authentication token found. Please log in to the extension first.</div>';
                    return;
                }

                const response = await fetch(`${API_URL}/api/posts/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        query: query,
                        limit: 10
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResults(data);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    resultsDiv.innerHTML = `<div class="error">❌ Search failed: ${errorData.message || response.statusText}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Search error: ${error.message}</div>`;
            }
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');

            if (data.totalResults === 0) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        ❌ No results found for "${data.query}"<br>
                        ${data.summary || 'Try a different search term or save more posts to expand your searchable content.'}
                    </div>
                `;
                return;
            }

            let html = `
                <div class="summary">
                    <h3>🧠 AI Summary</h3>
                    <p><strong>Query:</strong> "${data.query}"</p>
                    <p><strong>Results:</strong> ${data.totalResults} posts found</p>
                    <p>${data.summary.overview}</p>

                    ${data.summary.keyFindings.length > 0 ? `
                        <div class="key-findings">
                            <strong>Key Findings:</strong>
                            <ul>
                                ${data.summary.keyFindings.map(finding => `<li>${finding}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${data.summary.relatedTopics.length > 0 ? `
                        <p><strong>Related Topics:</strong> ${data.summary.relatedTopics.join(', ')}</p>
                    ` : ''}
                </div>
            `;

            html += '<h3>📋 Detailed Results</h3>';

            data.results.forEach((result, index) => {
                const post = result.post;
                const similarity = Math.round(result.similarity * 100);

                html += `
                    <div class="result-item">
                        <div class="result-header">
                            <div>
                                <span class="platform">${post.platform}</span>
                                <span style="margin-left: 8px; color: #666;">${post.authorName || 'Unknown Author'}</span>
                            </div>
                            <span class="similarity">${similarity}% match</span>
                        </div>
                        <div class="content">
                            ${post.content ? post.content.substring(0, 200) + (post.content.length > 200 ? '...' : '') : 'No content available'}
                        </div>
                        ${post.categories && post.categories.length > 0 ? `
                            <div style="margin-top: 8px;">
                                <small style="color: #666;">Categories: ${post.categories.join(', ')}</small>
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
        }

        // Allow Enter key to trigger search
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Auto-focus search input
        document.getElementById('searchInput').focus();
    </script>
</body>
</html>
