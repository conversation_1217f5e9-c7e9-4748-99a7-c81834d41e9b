# Ask My Bookmarks - Semantic Search Feature

## Overview

The "Ask My Bookmarks" feature enables users to search through their saved social media posts using natural language queries. Instead of traditional keyword matching, this feature uses AI-powered semantic search to understand the meaning and context of both the query and the saved content.

## Features

### 🔍 Natural Language Search
- Ask questions like "What did I save about AI prompts last week?"
- Search by concepts, themes, or topics rather than exact keywords
- Intelligent understanding of context and meaning

### 🤖 AI-Powered Summaries
- Get instant summaries of search results
- Key findings and insights from matching posts
- Related topics and suggested actions
- Smart categorization of results

### ⚡ Real-time Search
- Debounced input for smooth performance
- Loading animations with pulsating dots
- Instant results as you type

### 🎨 Beautiful UI
- Dark-mode first design with pastel highlights
- Smooth slide/fade animations
- Keyboard shortcuts (Cmd+K / Ctrl+K)
- Responsive design for all screen sizes

## How It Works

### Backend Architecture

1. **Embedding Generation**: When posts are saved, they're processed through OpenAI's text-embedding-3-small model to create vector representations
2. **Vector Storage**: Embedding vectors are stored in MongoDB alongside post metadata
3. **Similarity Search**: Search queries are converted to embeddings and compared using cosine similarity
4. **AI Summarization**: Results are processed through GPT-4o-mini to generate intelligent summaries

### Frontend Components

- **AskMyBookmarks**: Main search component with input field and suggestions
- **SearchResultsSummary**: Displays AI-generated summaries and detailed results
- **semanticSearchService**: Handles API communication and result processing

## API Endpoints

### POST /api/posts/search
Performs semantic search through user's saved posts.

**Request Body:**
```json
{
  "query": "AI tools for productivity",
  "limit": 10
}
```

**Response:**
```json
{
  "results": [
    {
      "post": {
        "id": "...",
        "content": "...",
        "platform": "X/Twitter",
        "authorName": "...",
        "savedAt": "...",
        "categories": [...],
        "tags": [...]
      },
      "similarity": 0.85
    }
  ],
  "summary": {
    "overview": "...",
    "keyFindings": [...],
    "relatedTopics": [...],
    "suggestedActions": [...],
    "totalPosts": 5
  },
  "totalResults": 5,
  "query": "AI tools for productivity"
}
```

### GET /api/posts/search/suggestions
Returns personalized search suggestions based on user's content.

**Response:**
```json
{
  "suggestions": [
    "AI tools and automation",
    "productivity workflows",
    "design inspiration",
    "business insights"
  ],
  "totalPosts": 150
}
```

## Usage

### Basic Search
1. Navigate to the dashboard
2. Use the search bar at the top (below platform filters)
3. Type your natural language query
4. View AI-generated summary and detailed results

### Keyboard Shortcuts
- **Cmd+K** (Mac) / **Ctrl+K** (Windows/Linux): Focus search bar
- **Escape**: Close search suggestions or clear focus

### Search Tips
- Use descriptive, natural language queries
- Ask about concepts, themes, or topics
- Include time references ("last week", "recently")
- Be specific about what you're looking for

## Examples

### Good Search Queries
- "AI prompts for writing"
- "productivity tips I saved last month"
- "design inspiration for mobile apps"
- "business advice from entrepreneurs"
- "coding tutorials and resources"

### Search Results
The system returns:
- **Similarity Score**: How closely each post matches your query
- **AI Summary**: Overview of what was found
- **Key Findings**: Important insights from the results
- **Related Topics**: Suggested areas to explore
- **Suggested Actions**: What you can do with the information

## Technical Requirements

### Backend Dependencies
- OpenAI API key for embeddings and summarization
- MongoDB with embedding vector storage
- Node.js with Express server

### Frontend Dependencies
- React 18+
- TypeScript
- Tailwind CSS
- Chrome Extension APIs

## Configuration

### Environment Variables
```bash
# Backend (.env)
OPENAI_API_KEY=your_openai_api_key
MONGO_URI=your_mongodb_connection_string

# Frontend (.env)
VITE_OPENAI_API_KEY=your_openai_api_key
```

### Similarity Threshold
The default similarity threshold is 0.7 (70% match). This can be adjusted in the backend search endpoint to be more or less strict.

## Performance Considerations

- **Embedding Generation**: Only performed once when posts are saved
- **Search Speed**: Vector similarity calculations are fast (< 100ms)
- **Result Caching**: Consider implementing Redis caching for frequent queries
- **Rate Limiting**: OpenAI API calls are rate-limited and debounced

## Future Enhancements

- [ ] Advanced filters (date range, platform, category)
- [ ] Search history and saved searches
- [ ] Collaborative search and sharing
- [ ] Multi-language support
- [ ] Voice search integration
- [ ] Search analytics and insights

## Troubleshooting

### No Results Found
- Ensure posts have been processed with embeddings
- Check if OpenAI API key is configured
- Verify user has saved posts with content

### Slow Search Performance
- Check MongoDB indexing on embedding vectors
- Monitor OpenAI API response times
- Consider implementing result caching

### API Errors
- Verify OpenAI API key validity and quota
- Check network connectivity
- Review server logs for detailed error messages
