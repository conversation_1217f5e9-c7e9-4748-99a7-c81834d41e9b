# Multilingual System Guide for Notely Social

This guide explains how the multilingual system works in Notely Social Chrome Extension and how to use it effectively.

## Overview

The extension now supports 5 languages:
- **English (en)** - Default
- **Turkish (tr)**
- **French (fr)**
- **German (de)**
- **Spanish (es)**

## Architecture

### Core Components

1. **Translation Files** (`src/translations.ts`)
   - Contains all translation keys and their values for each language
   - Organized by feature/component for easy maintenance

2. **Translation Utilities** (`src/utils/translation.ts`)
   - Lightweight translation function with caching
   - Locale detection and validation
   - Chrome storage integration

3. **React Hooks** (`src/hooks/useTranslation.ts`)
   - Easy-to-use hook for components
   - Provides `t()` function and locale management

4. **Context Providers** (`src/contexts/LocaleProvider.tsx`)
   - Manages global locale state
   - Handles locale persistence

5. **AI Prompt Service** (`src/services/aiPromptService.ts`)
   - Generates multilingual AI prompts
   - Ensures AI responses match user's language

## Usage

### In React Components

```tsx
import { useTranslation } from '../hooks/useTranslation';

function MyComponent() {
  const { t, locale, changeLocale } = useTranslation();
  
  return (
    <div>
      <h1>{t('dashboard.title')}</h1>
      <p>{t('dashboard.welcome')}</p>
      <button onClick={() => changeLocale('es')}>
        Switch to Spanish
      </button>
    </div>
  );
}
```

### Adding New Translation Keys

1. Add the key to `src/translations.ts`:

```typescript
"myFeature.newKey": {
  en: "English text",
  tr: "Turkish text",
  fr: "French text", 
  de: "German text",
  es: "Spanish text"
}
```

2. Use in components:

```tsx
const { t } = useTranslation();
return <span>{t('myFeature.newKey')}</span>;
```

### Language Selector Component

Use the built-in language selector:

```tsx
import LanguageSelector from '../components/LanguageSelector';

function Header() {
  return (
    <div>
      <LanguageSelector showLabel={true} />
    </div>
  );
}
```

## AI Multilingual Prompts

The AI system automatically generates prompts in the user's selected language:

```typescript
import { generateLocalizedPrompt, getCurrentLocale } from '../services/aiPromptService';

async function generateTags(content: string) {
  const locale = await getCurrentLocale();
  const prompt = generateLocalizedPrompt('tagging', locale, {
    postContent: content,
    maxTags: 5
  });
  
  // AI will respond in the user's language
  return await callAI(prompt);
}
```

## Supported Features

### UI Elements
- ✅ Dashboard interface
- ✅ Settings page
- ✅ Login/logout flows
- ✅ Post management
- ✅ Error messages
- ✅ Success notifications
- ✅ Search placeholders
- ✅ Button labels
- ✅ Tooltips

### AI Features
- ✅ Post categorization prompts
- ✅ Tag generation prompts
- ✅ Content insights prompts
- ✅ Fast take generation
- ✅ Snap note creation
- ✅ Content ideas generation

### Storage & Persistence
- ✅ Language preference saved to Chrome storage
- ✅ Automatic browser language detection
- ✅ Fallback to English for missing translations

## Implementation Details

### Locale Detection Priority

1. User's explicitly selected language (stored in Chrome storage)
2. Browser language (if supported)
3. English (fallback)

### Performance Optimizations

- **Translation Caching**: Frequently used translations are cached
- **Lazy Loading**: Only loads translations for current language
- **Bundle Size**: Minimal impact on extension size (~15KB for all languages)

### Fallback Strategy

- Missing translation keys fall back to English
- Invalid locales fall back to English
- Development warnings for missing translations

## Best Practices

### Translation Keys

1. **Use descriptive, hierarchical keys**:
   ```
   ✅ "dashboard.posts.noPosts"
   ❌ "text1"
   ```

2. **Group by feature/component**:
   ```
   ✅ "auth.login", "auth.logout", "auth.register"
   ❌ "login", "logout", "register"
   ```

3. **Use consistent naming**:
   ```
   ✅ "button.save", "button.cancel", "button.delete"
   ❌ "saveBtn", "cancelButton", "deleteAction"
   ```

### Component Integration

1. **Always wrap with LocaleProvider**:
   ```tsx
   <LocaleProvider>
     <App />
   </LocaleProvider>
   ```

2. **Use translation hook consistently**:
   ```tsx
   const { t } = useTranslation();
   // Use t() for all user-facing text
   ```

3. **Avoid hardcoded strings**:
   ```tsx
   ❌ <button>Save Post</button>
   ✅ <button>{t('post.save')}</button>
   ```

## Testing

### Manual Testing

1. Change language in settings
2. Verify all UI elements update
3. Test AI prompts in different languages
4. Check fallback behavior

### Automated Testing

```typescript
// Test translation function
import t from '../utils/translation';

test('translation fallback', () => {
  expect(t('nonexistent.key', 'es')).toBe('nonexistent.key');
  expect(t('dashboard.title', 'invalid')).toBe('Dashboard'); // English fallback
});
```

## Troubleshooting

### Common Issues

1. **Missing translations show as keys**
   - Add the missing key to `translations.ts`
   - Check for typos in key names

2. **Language not persisting**
   - Verify Chrome storage permissions
   - Check LocaleProvider is wrapping the component

3. **AI prompts in wrong language**
   - Ensure `getCurrentLocale()` is called before prompt generation
   - Check AI service is using `generateLocalizedPrompt()`

### Debug Mode

Enable translation debugging in development:

```typescript
// In translation.ts
if (process.env.NODE_ENV === 'development') {
  console.warn(`Missing translation for key: ${key} in locale: ${locale}`);
}
```

## Future Enhancements

- [ ] Add more languages (Italian, Portuguese, Japanese, etc.)
- [ ] Implement pluralization rules
- [ ] Add date/time localization
- [ ] Context-aware translations
- [ ] Translation management UI for admins

## Contributing

When adding new features:

1. Add translation keys for all user-facing text
2. Include translations for all 5 supported languages
3. Test with different locales
4. Update this documentation if needed

For translation contributions:
1. Native speakers preferred for accuracy
2. Maintain consistent tone and style
3. Consider cultural context, not just literal translation
4. Test translations in the actual UI context
