<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Notely Social - Save Your Social Media Posts</title>
  <meta name="description" content="Save, organize, and manage your social media posts with AI-powered insights" />
  <meta name="keywords" content="social media, posts, organization, AI, dashboard" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://notely.social/" />
  <meta property="og:title" content="Notely Social - Save Your Social Media Posts" />
  <meta property="og:description" content="Save, organize, and manage your social media posts with AI-powered insights" />
  <meta property="og:image" content="https://notely.social/notely-og.png" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://notely.social/" />
  <meta property="twitter:title" content="Notely Social - Save Your Social Media Posts" />
  <meta property="twitter:description" content="Save, organize, and manage your social media posts with AI-powered insights" />
  <meta property="twitter:image" content="https://notely.social/notely-og.png" />

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/notely.svg" />
  <link rel="icon" type="image/png" href="/icons/logo-32.png" />
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Standalone entry point -->
  <script type="module">
    import React from 'react';
    import ReactDOM from 'react-dom/client';
    import LandingPage from '/src/web/components/LandingPage';
    
    // Import styles
    import '/src/index.css';
    import '/src/styles/notely-theme.css';
    
    // Render directly without any router
    ReactDOM.createRoot(document.getElementById('root')).render(
      React.createElement(React.StrictMode, null, 
        React.createElement(LandingPage)
      )
    );
  </script>
</head>
<body>
  <div id="root"></div>
</body>
</html>
