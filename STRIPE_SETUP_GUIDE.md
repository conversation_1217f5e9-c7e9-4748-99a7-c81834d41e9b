# Stripe Integration Setup Guide

This guide will help you set up Stripe integration for <PERSON><PERSON>'s subscription system with upgrade/downgrade functionality.

## 🚀 Quick Setup

### Step 1: Set up Stripe Products and Prices

#### Option A: Automatic Setup (Recommended)
```bash
# Set your Stripe secret key
export STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here

# Run the setup script
npm run setup:stripe
```

#### Option B: Manual Setup
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Products** → **Add Product**
3. Create product: "Notely Premium"
4. Add two prices:
   - **Monthly**: $9.99/month recurring
   - **Yearly**: $99.99/year recurring
5. Copy the price IDs (start with `price_`)

### Step 2: Update Configuration Files

#### Frontend Configuration (`src/config/stripe.ts`)
```typescript
export const STRIPE_CONFIG = {
  TEST: {
    MONTHLY_PRICE_ID: 'price_1234567890abcdef', // Your monthly price ID
    YEARLY_PRICE_ID: 'price_0987654321fedcba',  // Your yearly price ID
  },
  // ... rest of config
};
```

#### Backend Environment Variables
Update your Railway environment variables:
```bash
railway variables set STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
railway variables set STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
railway variables set STRIPE_PRICE_ID_MONTHLY=price_1234567890abcdef
railway variables set STRIPE_PRICE_ID_YEARLY=price_0987654321fedcba
```

### Step 3: Set up Webhooks

1. In Stripe Dashboard, go to **Developers** → **Webhooks**
2. Add endpoint: `https://your-api-domain.com/auth/stripe/webhook`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
4. Copy the webhook secret and update `STRIPE_WEBHOOK_SECRET`

## 🎯 Features Implemented

### ✅ **Complete Subscription Management**

#### **For Free Users:**
- **Upgrade to Premium** → Opens Stripe checkout
- **Plan Selection** → Monthly ($9.99) or Yearly ($99.99)
- **Coupon Support** → Apply discount codes

#### **For Premium Users:**
- **View Current Plan** → Shows billing cycle and next payment
- **Change Plan** → Switch between monthly/yearly
- **Manage Billing** → Opens Stripe customer portal
- **Cancel Subscription** → Downgrade to free at period end
- **Reactivate** → Undo cancellation

#### **Plan Change Logic:**
- **Free → Premium** → Stripe checkout session
- **Premium → Free** → Cancel subscription (keeps access until period end)
- **Monthly ↔ Yearly** → Immediate plan change with prorations

### 🔧 **API Endpoints**

#### **Billing Routes (`/billing/`)**
- `POST /create-checkout-session` - Create Stripe checkout
- `POST /create-portal-session` - Open customer portal
- `POST /change-plan` - Change subscription plan
- `POST /cancel-subscription` - Cancel subscription
- `POST /reactivate-subscription` - Reactivate subscription
- `GET /subscription` - Get subscription details
- `GET /plans` - Get available pricing plans

#### **Authentication Routes (`/auth/`)**
- `GET /me` - Get user profile with subscription status
- `POST /stripe/create-checkout-session` - Legacy checkout (still supported)

### 🎨 **UI Components**

#### **Extension Settings Page**
- **Subscription Settings Section** → Complete plan management
- **Current Plan Display** → Shows status and billing info
- **Available Plans** → Cards with features and pricing
- **Upgrade/Downgrade Buttons** → Context-aware actions

#### **Dashboard Integration**
- **Upgrade Button** → For free users
- **Plan Status** → Shows current subscription
- **Settings Link** → Access full subscription management

## 🔄 **Subscription Flow**

### **Upgrade Flow (Free → Premium)**
1. User clicks "Upgrade to Premium"
2. Extension calls `/billing/create-checkout-session`
3. Stripe checkout opens in new tab
4. User completes payment
5. Webhook updates user plan to premium
6. Extension refreshes user profile

### **Downgrade Flow (Premium → Free)**
1. User clicks "Downgrade" on Free plan
2. Extension calls `/billing/cancel-subscription`
3. Subscription marked for cancellation at period end
4. User keeps premium access until billing cycle ends
5. Extension shows cancellation notice

### **Plan Change Flow (Monthly ↔ Yearly)**
1. User clicks "Switch" on different premium plan
2. Extension calls `/billing/change-plan`
3. Stripe updates subscription with prorations
4. User immediately gets new billing cycle
5. Extension refreshes subscription details

## 🛠️ **Testing**

### **Test the Complete Flow**
1. **Load extension** with updated build
2. **Login** with Google OAuth
3. **Go to Settings** → Should show subscription section
4. **Try upgrade** → Should open Stripe checkout (test mode)
5. **Complete test payment** → Use test card `4242 4242 4242 4242`
6. **Check plan change** → Should show premium status
7. **Test downgrade** → Should offer cancellation
8. **Test plan switching** → Monthly ↔ Yearly

### **Webhook Testing**
```bash
# Test webhook locally
stripe listen --forward-to localhost:3000/auth/stripe/webhook
```

## 🚨 **Troubleshooting**

### **Common Issues**

#### **"Price ID not configured" Error**
- Check environment variables are set correctly
- Verify price IDs match Stripe dashboard
- Restart Railway service after updating variables

#### **Webhook Signature Verification Failed**
- Verify `STRIPE_WEBHOOK_SECRET` is correct
- Check webhook endpoint URL is accessible
- Ensure webhook events are properly selected

#### **Checkout Session Creation Failed**
- Verify `STRIPE_SECRET_KEY` has correct permissions
- Check price IDs exist and are active
- Ensure customer creation is working

### **Debug Commands**
```bash
# Check Railway environment variables
railway variables

# Test webhook endpoint
curl -X POST https://your-api-domain.com/health

# Check Stripe configuration
stripe products list
stripe prices list
```

## 🎉 **Ready to Go!**

Your subscription system now supports:
- ✅ **Upgrade** (Free → Premium)
- ✅ **Downgrade** (Premium → Free)
- ✅ **Plan Changes** (Monthly ↔ Yearly)
- ✅ **Billing Management** (Stripe Customer Portal)
- ✅ **Real-time Updates** (Webhooks)
- ✅ **Error Handling** (Token expiration, API failures)

Both web and extension versions will work seamlessly with your Railway deployment! 🚀
