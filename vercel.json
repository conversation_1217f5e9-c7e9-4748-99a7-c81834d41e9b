{"buildCommand": "npm run build:web", "outputDirectory": "dist-web", "framework": "vite", "rewrites": [{"source": "/dashboard", "destination": "/index.html"}, {"source": "/login", "destination": "/index.html"}, {"source": "/settings", "destination": "/index.html"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}