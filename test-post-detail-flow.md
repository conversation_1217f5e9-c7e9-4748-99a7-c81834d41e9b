# Post Detail Flow Test Plan

## 1. Opening Post Detail
- [ ] Click "Edit" on a post in the dashboard
- [ ] Verify URL contains correct post ID
- [ ] Verify post details load correctly
- [ ] Check loading state is shown while fetching data
- [ ] Verify error handling if post ID is invalid

## 2. Categories Testing
- [ ] Verify existing categories are displayed
- [ ] Try adding a new category (max 3)
- [ ] Try removing a category
- [ ] Verify category suggestions work
- [ ] Test max limit enforcement
- [ ] Test duplicate prevention
- [ ] Verify changes are saved properly

## 3. Tags Testing
- [ ] Verify existing tags are displayed
- [ ] Try adding a new tag (max 5)
- [ ] Try removing a tag
- [ ] Verify tag suggestions work
- [ ] Test max limit enforcement
- [ ] Test duplicate prevention
- [ ] Verify changes are saved properly

## 4. Save Functionality
- [ ] Test saving with no changes (should be disabled)
- [ ] Test saving after category changes
- [ ] Test saving after tag changes
- [ ] Verify save button state changes appropriately
- [ ] Check loading state during save
- [ ] Verify success feedback
- [ ] Verify error handling

## 5. Navigation
- [ ] Test browser back button
- [ ] Test closing and reopening the detail view
- [ ] Verify unsaved changes warning (if implemented)
- [ ] Check navigation to dashboard

## 6. Cross-Platform Testing
Test with posts from each platform:
- [ ] Instagram
- [ ] Facebook
- [ ] Pinterest
- [ ] Reddit
- [ ] LinkedIn

## 7. Data Persistence
- [ ] Save changes and close detail view
- [ ] Reopen same post and verify changes persisted
- [ ] Check dashboard view reflects changes
- [ ] Verify changes persist after extension reload

## 8. Error Cases
- [ ] Test with invalid post ID
- [ ] Test with network offline
- [ ] Test with storage errors
- [ ] Verify error messages are user-friendly

## Test Results Template
For each test case:
1. Expected behavior:
2. Actual behavior:
3. Pass/Fail:
4. Notes:

## Issues Found
1. Issue:
   - Description:
   - Steps to reproduce:
   - Expected vs actual:
   - Severity:

## Improvements Needed
1. Improvement:
   - Description:
   - Priority:
   - Suggested implementation: 