<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Test page for Notely web content saver functionality">
    <title>Test Web Content Saver - Notely</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .test-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        .instructions {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bee5eb;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0c5460;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Notely Web Content Saver Test</h1>
        <p>Test the "Add to Notely" functionality on this page</p>
    </div>

    <div class="instructions">
        <h3>📋 How to Test</h3>
        <ol>
            <li><strong>Install the Extension:</strong> Load the built extension from the <code>dist/</code> folder</li>
            <li><strong>Test Text Selection:</strong> Select any text below and right-click to see "Add to Notely"</li>
            <li><strong>Test Image Saving:</strong> Right-click on the image below to save it</li>
            <li><strong>Test Page Saving:</strong> Right-click anywhere on the page (not on text/images) to save the entire page</li>
            <li><strong>Check Dashboard:</strong> Open the extension dashboard to see saved content under the "Web" filter</li>
        </ol>
    </div>

    <div class="content-section">
        <h2>📝 Sample Text Content</h2>
        <p>This is a sample paragraph that you can select and save to your mind. The web content saver should capture this text along with the page metadata including the URL, title, and timestamp.</p>

        <div class="highlight">
            <strong>Highlighted Information:</strong> This is important content that demonstrates how selected text is captured and saved. Try selecting this entire highlighted section and using the context menu to add it to Notely.
        </div>

        <p>Here's another paragraph with some <strong>bold text</strong> and <em>italic text</em>. The content saver should preserve the text content while capturing the context of where it came from.</p>
    </div>

    <div class="content-section">
        <h2>🖼️ Sample Image Content</h2>
        <p>Right-click on the image below to test image saving functionality:</p>

        <img src="https://picsum.photos/600/300?random=1"
             alt="Sample test image for Notely web content saver"
             class="test-image"
             title="Test Image - Right-click to save to Notely">

        <p><em>This is a sample image from Lorem Picsum. When you right-click and select "Add to Notely", it should save both the image and the page context.</em></p>
    </div>

    <div class="content-section">
        <h2>🌐 Page Metadata Test</h2>
        <p>This page includes various metadata elements that should be captured:</p>
        <ul>
            <li><strong>Title:</strong> Test Web Content Saver - Notely</li>
            <li><strong>Description:</strong> Test page for Notely web content saver functionality</li>
            <li><strong>URL:</strong> The current page URL</li>
            <li><strong>Favicon:</strong> Should be automatically detected</li>
            <li><strong>Domain:</strong> The hostname of this page</li>
        </ul>
    </div>

    <div class="content-section">
        <h2>✅ Expected Results</h2>
        <p>After testing the context menu functionality, you should see:</p>
        <ul>
            <li>Selected text saved with page context</li>
            <li>Images saved with their alt text and source URL</li>
            <li>Page content saved with title and description</li>
            <li>All items appearing in the dashboard under the "Web" platform filter</li>
            <li>Proper timestamps and URL references</li>
        </ul>
    </div>

    <div class="content-section">
        <h2>🔧 Troubleshooting</h2>
        <p>If the context menu doesn't appear:</p>
        <ul>
            <li>Check that the extension is loaded and enabled</li>
            <li>Verify the extension has the necessary permissions</li>
            <li>Check the browser console for any error messages</li>
            <li>Try refreshing the page after installing the extension</li>
        </ul>
    </div>

    <footer style="margin-top: 40px; padding: 20px; text-align: center; color: #666; border-top: 1px solid #eee;">
        <p>🧠 Notely Web Content Saver Test Page</p>
        <p>Use this page to test the "Add to Notely" context menu functionality</p>
    </footer>
</body>
</html>
