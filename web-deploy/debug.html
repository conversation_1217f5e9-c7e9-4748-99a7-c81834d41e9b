<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Notely Social - Debug Panel</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .controls {
      display: flex;
      margin-bottom: 20px;
      gap: 10px;
      align-items: center;
    }
    button {
      background-color: #0078d7;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    button:hover {
      background-color: #0063b1;
    }
    button.danger {
      background-color: #d73a49;
    }
    button.danger:hover {
      background-color: #cb2431;
    }
    .debug-section {
      margin-bottom: 30px;
    }
    .logs-container {
      max-height: 500px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f8f8f8;
    }
    .log-entry {
      padding: 8px 12px;
      border-bottom: 1px solid #eee;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .log-entry:last-child {
      border-bottom: none;
    }
    .error { color: #d73a49; background-color: #ffeef0; }
    .warn { color: #b08800; background-color: #fffbdd; }
    .info { color: #0366d6; background-color: #f1f8ff; }
    .debug { color: #6f42c1; background-color: #f5f0ff; }
    .log-timestamp {
      color: #666;
      margin-right: 8px;
    }
    .log-level {
      font-weight: bold;
      margin-right: 8px;
    }
    .log-context {
      font-weight: bold;
      margin-right: 8px;
    }
    .log-message {
      margin-right: 8px;
    }
    .log-data {
      margin-top: 4px;
      padding: 4px;
      border-radius: 2px;
      background-color: rgba(0, 0, 0, 0.05);
    }
    .flex-container {
      display: flex;
      gap: 20px;
    }
    .flex-column {
      flex: 1;
    }
    .tab-container {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin-bottom: 10px;
    }
    .tab {
      padding: 8px 16px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
    }
    .tab.active {
      border-bottom: 2px solid #0078d7;
      font-weight: bold;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .storage-item {
      margin-bottom: 10px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .storage-key {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .storage-value {
      word-break: break-all;
      white-space: pre-wrap;
      font-family: monospace;
      background-color: #f8f8f8;
      padding: 5px;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Notely Social - Debug Panel</h1>
    
    <div class="tab-container">
      <div class="tab active" data-tab="logs">Service Worker Logs</div>
      <div class="tab" data-tab="errors">Error Reports</div>
      <div class="tab" data-tab="storage">Storage Inspector</div>
    </div>
    
    <div class="tab-content active" id="logs-tab">
      <div class="controls">
        <button id="refresh-logs">Refresh Logs</button>
        <button id="clear-logs" class="danger">Clear Logs</button>
        <div>
          <label>
            <input type="checkbox" id="auto-refresh" checked>
            Auto-refresh (5s)
          </label>
        </div>
        <div>
          <label>
            <input type="checkbox" id="debug-enabled" checked>
            Enable Debug Logging
          </label>
        </div>
        <div>
          Filter:
          <label><input type="checkbox" checked class="log-filter" data-level="error"> Error</label>
          <label><input type="checkbox" checked class="log-filter" data-level="warn"> Warning</label>
          <label><input type="checkbox" checked class="log-filter" data-level="info"> Info</label>
          <label><input type="checkbox" checked class="log-filter" data-level="debug"> Debug</label>
        </div>
      </div>
      
      <div class="debug-section">
        <div class="logs-container" id="logs-container"></div>
      </div>
    </div>
    
    <div class="tab-content" id="errors-tab">
      <div class="controls">
        <button id="refresh-errors">Refresh Errors</button>
        <button id="clear-errors" class="danger">Clear Errors</button>
      </div>
      
      <div class="debug-section">
        <div class="logs-container" id="errors-container"></div>
      </div>
    </div>
    
    <div class="tab-content" id="storage-tab">
      <div class="controls">
        <button id="refresh-storage">Refresh Storage Data</button>
        <input type="text" id="storage-filter" placeholder="Filter by key...">
      </div>
      
      <div class="flex-container">
        <div class="flex-column">
          <h3>Local Storage</h3>
          <div id="local-storage-container"></div>
        </div>
        <div class="flex-column">
          <h3>Sync Storage</h3>
          <div id="sync-storage-container"></div>
        </div>
      </div>
    </div>
  </div>

  <script src="debug.js"></script>
</body>
</html>
