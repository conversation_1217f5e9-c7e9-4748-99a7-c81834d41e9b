<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Author Title Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-post {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .author-info {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-right: 12px;
        }
        .author-details h3 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
        }
        .author-details p {
            margin: 4px 0 0 0;
            font-size: 12px;
            color: #666;
        }
        .timestamp {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
            display: block;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        button {
            background: #0a66c2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>LinkedIn Author Title Extraction Test</h1>
    
    <div class="test-post">
        <h2>Test Case 1: Post with Author Title (Jan Mráz Style)</h2>
        <div class="author-info">
            <img src="https://via.placeholder.com/48" alt="Avatar" class="avatar">
            <div class="author-details">
                <h3 class="update-components-actor__name">Jan Mráz</h3>
                <p class="update-components-actor__description">UX UI Design Content Creator / Co-founder of Atheros / Design Lead</p>
                <span class="timestamp">1w • 🌍</span>
            </div>
        </div>
        <div class="update-components-text">
            <span>My team used to ask me to sign off every time they swiped their company card! And every time, without fail, I'd respond: "You're an Owner, not business. You make the decision!"</span>
        </div>
        <button onclick="testExtraction(this.parentElement)">Test Extraction</button>
        <div class="result" id="result1"></div>
    </div>

    <div class="test-post">
        <h2>Test Case 2: Post without Author Title</h2>
        <div class="author-info">
            <img src="https://via.placeholder.com/48" alt="Avatar" class="avatar">
            <div class="author-details">
                <h3 class="update-components-actor__name">John Doe</h3>
                <!-- No author title element -->
            </div>
        </div>
        <div class="update-components-text">
            <span>This is a test post without author title</span>
        </div>
        <button onclick="testExtraction(this.parentElement)">Test Extraction</button>
        <div class="result" id="result2"></div>
    </div>

    <div class="test-post">
        <h2>Test Case 3: Alternative Selector Structure</h2>
        <div class="author-info">
            <img src="https://via.placeholder.com/48" alt="Avatar" class="avatar">
            <div class="author-details">
                <h3 class="feed-shared-actor__name">Jane Smith</h3>
                <p class="feed-shared-actor__description">Product Manager at Tech Company</p>
            </div>
        </div>
        <div class="feed-shared-text">
            <span>Testing alternative LinkedIn selectors</span>
        </div>
        <button onclick="testExtraction(this.parentElement)">Test Extraction</button>
        <div class="result" id="result3"></div>
    </div>

    <script>
        function testExtraction(postElement) {
            const resultDiv = postElement.querySelector('.result');
            
            try {
                // Simulate the LinkedIn service extraction logic
                let authorElement = postElement.querySelector('.update-components-actor__name');
                if (!authorElement) {
                    authorElement = postElement.querySelector('.feed-shared-actor__name');
                }
                if (!authorElement) {
                    authorElement = postElement.querySelector('.update-components-actor__title');
                }

                const authorName = authorElement?.textContent?.trim() || '';

                // Extract author title/subtitle
                let authorTitleElement = null;
                const actorArea = postElement.querySelector('.update-components-actor, .feed-shared-actor, .author-info');
                
                if (actorArea) {
                    authorTitleElement = actorArea.querySelector('.update-components-actor__description');
                    if (!authorTitleElement) {
                        authorTitleElement = actorArea.querySelector('.feed-shared-actor__description');
                    }
                    if (!authorTitleElement) {
                        authorTitleElement = actorArea.querySelector('.update-components-actor__sub-description');
                    }
                    if (!authorTitleElement) {
                        authorTitleElement = actorArea.querySelector('.feed-shared-actor__sub-description');
                    }
                }

                const authorTitle = authorTitleElement?.textContent?.trim() || '';

                // Extract timestamp
                let timestampElement = postElement.querySelector('.timestamp');
                if (!timestampElement) {
                    timestampElement = postElement.querySelector('time[datetime]');
                }
                const timestamp = timestampElement?.textContent?.trim() || '';

                // Extract content
                let contentElement = postElement.querySelector('.update-components-text');
                if (!contentElement) {
                    contentElement = postElement.querySelector('.feed-shared-text');
                }
                const textContent = contentElement?.textContent?.trim() || '';

                // Display results
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>Extraction Results:</h4>
                    <p><strong>Author Name:</strong> "${authorName}"</p>
                    <p><strong>Author Title:</strong> "${authorTitle}"</p>
                    <p><strong>Timestamp:</strong> "${timestamp}"</p>
                    <p><strong>Content:</strong> "${textContent.substring(0, 100)}..."</p>
                    <p><strong>Status:</strong> ✅ Success</p>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>Extraction Error:</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Status:</strong> ❌ Failed</p>
                `;
            }
        }

        // Auto-test all cases on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.querySelectorAll('button').forEach(button => {
                    button.click();
                });
            }, 500);
        });
    </script>
</body>
</html>
