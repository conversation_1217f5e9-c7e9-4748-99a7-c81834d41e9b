<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Post Card Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f3f2ef;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .original, .generated {
            flex: 1;
        }
        .post-card {
            max-width: 552px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 14px;
            line-height: 20px;
            box-shadow: 0 0 0 1px rgba(0,0,0,0.15), 0 2px 3px rgba(0,0,0,0.2);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #0a66c2;
            padding-bottom: 10px;
        }
        button {
            background: #0a66c2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        .test-data {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>LinkedIn Post Card Generation Test</h1>
    
    <div class="test-data">
        <h3>Test Data (Jan Mráz Post):</h3>
        <pre id="testData"></pre>
    </div>

    <div class="comparison">
        <div class="original">
            <h2>Original LinkedIn Style</h2>
            <div class="post-card">
                <!-- Header -->
                <div style="padding: 12px 16px;">
                    <div style="display: flex; align-items: flex-start;">
                        <div style="width: 48px; height: 48px; border-radius: 50%; background: #0a66c2; margin-right: 8px; overflow: hidden; flex-shrink: 0;">
                            <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 20px;">J</div>
                        </div>
                        <div style="flex: 1; min-width: 0;">
                            <div style="display: flex; align-items: center; margin-bottom: 2px;">
                                <div style="font-weight: 600; color: #000000; font-size: 14px; line-height: 20px;">Jan Mráz</div>
                                <svg width="16" height="16" viewBox="0 0 16 16" style="margin-left: 4px;" fill="#0a66c2">
                                    <path d="M12.5 3.5h-9A1.5 1.5 0 002 5v6a1.5 1.5 0 001.5 1.5h9A1.5 1.5 0 0014 11V5a1.5 1.5 0 00-1.5-1.5zM3.5 5h9a.5.5 0 01.5.5v.5H3v-.5a.5.5 0 01.5-.5z"/>
                                </svg>
                            </div>
                            <div style="color: #666666; font-size: 12px; line-height: 16px; margin-bottom: 2px;">UX UI Design Content Creator / Co-founder of Atheros / Design Lead</div>
                            <div style="color: #666666; font-size: 12px; line-height: 16px; display: flex; align-items: center;">
                                <span>1w</span>
                                <span style="margin: 0 4px;">•</span>
                                <span style="font-size: 14px;">🌍</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div style="padding: 0 16px 16px 16px; color: #000000; white-space: pre-wrap;">My team used to ask me to sign off every time they swiped their company card! And every time, without fail, I'd respond: "You're an Owner, not business. You make the decision!"</div>
            </div>
        </div>

        <div class="generated">
            <h2>Generated by Notely</h2>
            <div id="generatedCard">
                <!-- Generated card will appear here -->
            </div>
            <button onclick="generateCard()">Generate Card</button>
        </div>
    </div>

    <script>
        // Test data matching the Jan Mráz post
        const testPost = {
            id: 'linkedin-test-123',
            platform: 'LinkedIn',
            author: 'Jan Mráz',
            authorName: 'Jan Mráz',
            authorTitle: 'UX UI Design Content Creator / Co-founder of Atheros / Design Lead',
            authorAvatar: '',
            content: 'My team used to ask me to sign off every time they swiped their company card! And every time, without fail, I\'d respond: "You\'re an Owner, not business. You make the decision!"',
            textContent: 'My team used to ask me to sign off every time they swiped their company card! And every time, without fail, I\'d respond: "You\'re an Owner, not business. You make the decision!"',
            timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
            savedAt: new Date().toISOString(),
            stats: {
                likes: 438,
                comments: 56,
                shares: 0
            }
        };

        // Display test data
        document.getElementById('testData').textContent = JSON.stringify(testPost, null, 2);

        // Simulate the createLinkedInPost function
        function createLinkedInPost(post, metrics) {
            const authorAvatar = post.authorAvatar || post.authorImage || '';
            const authorName = post.authorName || post.author || 'User';
            const authorTitle = post.authorTitle || '';
            const content = post.content || post.textContent || '';
            
            // Format timestamp to show relative time like LinkedIn
            const formatRelativeTime = (timestamp) => {
                try {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const diffMs = now.getTime() - date.getTime();
                    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                    const diffMinutes = Math.floor(diffMs / (1000 * 60));
                    
                    if (diffDays > 7) {
                        return `${Math.floor(diffDays / 7)}w`;
                    } else if (diffDays > 0) {
                        return `${diffDays}d`;
                    } else if (diffHours > 0) {
                        return `${diffHours}h`;
                    } else if (diffMinutes > 0) {
                        return `${diffMinutes}m`;
                    } else {
                        return 'now';
                    }
                } catch {
                    return '1h';
                }
            };
            
            const relativeTime = formatRelativeTime(post.timestamp || post.savedAt);

            return `
                <div style="
                    max-width: 552px;
                    background: #ffffff;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 0;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
                    font-size: 14px;
                    line-height: 20px;
                    box-shadow: 0 0 0 1px rgba(0,0,0,0.15), 0 2px 3px rgba(0,0,0,0.2);
                ">
                    <!-- Header -->
                    <div style="padding: 12px 16px;">
                        <div style="display: flex; align-items: flex-start;">
                            <div style="
                                width: 48px;
                                height: 48px;
                                border-radius: 50%;
                                background: #0a66c2;
                                margin-right: 8px;
                                overflow: hidden;
                                flex-shrink: 0;
                            ">
                                ${authorAvatar ? `<img src="${authorAvatar}" style="width: 100%; height: 100%; object-fit: cover;" />` :
                                  `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 20px;">${authorName.charAt(0).toUpperCase()}</div>`}
                            </div>
                            <div style="flex: 1; min-width: 0;">
                                <div style="display: flex; align-items: center; margin-bottom: 0;">
                                    <div style="font-weight: 600; color: #000000; font-size: 14px; line-height: 20px;">${authorName}</div>
                                    <div style="width: 16px; height: 16px; margin-left: 4px; background: #0a66c2; border-radius: 2px; display: flex; align-items: center; justify-content: center;">
                                        <div style="width: 10px; height: 8px; background: white; border-radius: 1px;"></div>
                                    </div>
                                </div>
                                ${authorTitle ? `<div style="color: #666666; font-size: 12px; line-height: 16px; margin-bottom: 0; margin-top: 0;">${authorTitle}</div>` : ''}
                                <div style="color: #666666; font-size: 12px; line-height: 16px; display: flex; align-items: center; margin-top: 2px;">
                                    <span>${relativeTime}</span>
                                    <span style="margin: 0 4px;">•</span>
                                    <span style="font-size: 14px;">🌍</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    ${content ? `<div style="padding: 0 16px 16px 16px; color: #000000; white-space: pre-wrap;">${content}</div>` : ''}
                </div>
            `;
        }

        function generateCard() {
            const generatedHTML = createLinkedInPost(testPost, {});
            document.getElementById('generatedCard').innerHTML = generatedHTML;
        }

        // Auto-generate on page load
        window.addEventListener('load', () => {
            setTimeout(generateCard, 500);
        });
    </script>
</body>
</html>
