<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Settings Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>This page will help you test the settings functionality. Follow these steps:</p>
        <ol>
            <li>Open the Chrome extension settings page</li>
            <li>Open the browser console (F12)</li>
            <li>Copy and paste the test script below into the console</li>
            <li>Run the tests and observe the results</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Quick Tests</h2>
        <button class="test-button" onclick="testStorageAccess()">Test Storage Access</button>
        <button class="test-button" onclick="testExtensionAPI()">Test Extension API</button>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Test Script (Copy to Console)</h2>
        <textarea id="test-script" readonly style="width: 100%; height: 200px; font-family: monospace;">
// Settings Test Script - Copy this to the settings page console

// Test if Chrome extension APIs are available
if (typeof chrome !== 'undefined' && chrome.storage) {
    console.log('✅ Chrome extension APIs available');
    
    // Test storage read
    chrome.storage.local.get(['platformIntegrations', 'saveMediaToCloud'], (result) => {
        console.log('📦 Current storage:', result);
    });
    
    chrome.storage.sync.get(['locale'], (result) => {
        console.log('🌐 Current locale:', result);
    });
    
    // Test if settings UI is loaded
    setTimeout(() => {
        const languageSelect = document.querySelector('select');
        const toggles = document.querySelectorAll('[role="switch"]');
        
        console.log('🎛️ Language selector found:', !!languageSelect);
        console.log('🔘 Platform toggles found:', toggles.length);
        
        if (languageSelect) {
            console.log('📝 Current language:', languageSelect.value);
        }
        
        if (toggles.length > 0) {
            console.log('🔧 Testing first toggle...');
            toggles[0].click();
            setTimeout(() => {
                console.log('✅ Toggle test completed');
            }, 500);
        }
    }, 1000);
    
} else {
    console.error('❌ Chrome extension APIs not available');
}
        </textarea>
        <button class="test-button" onclick="copyScript()">Copy Script</button>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output"></div>
    </div>

    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const line = `[${timestamp}] ${message}\n`;
            consoleOutput.textContent += line;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR: ' + args.join(' '), 'error');
        };
        
        function testStorageAccess() {
            const results = document.getElementById('test-results');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                results.innerHTML = '<div class="result success">✅ Chrome storage API is available</div>';
                
                // Test actual storage access
                chrome.storage.local.get(['platformIntegrations'], (result) => {
                    if (chrome.runtime.lastError) {
                        results.innerHTML += '<div class="result error">❌ Storage access failed: ' + chrome.runtime.lastError.message + '</div>';
                    } else {
                        results.innerHTML += '<div class="result success">✅ Storage read successful</div>';
                        results.innerHTML += '<div class="result info">📦 Platform integrations: ' + JSON.stringify(result.platformIntegrations || 'Not set') + '</div>';
                    }
                });
            } else {
                results.innerHTML = '<div class="result error">❌ Chrome storage API not available</div>';
            }
        }
        
        function testExtensionAPI() {
            const results = document.getElementById('test-results');
            
            const apis = [
                'chrome.storage',
                'chrome.runtime',
                'chrome.tabs'
            ];
            
            let html = '<h3>Extension API Availability:</h3>';
            
            apis.forEach(api => {
                const parts = api.split('.');
                let obj = window;
                let available = true;
                
                for (const part of parts) {
                    if (obj && obj[part]) {
                        obj = obj[part];
                    } else {
                        available = false;
                        break;
                    }
                }
                
                html += `<div class="result ${available ? 'success' : 'error'}">
                    ${available ? '✅' : '❌'} ${api}
                </div>`;
            });
            
            results.innerHTML = html;
        }
        
        function copyScript() {
            const script = document.getElementById('test-script');
            script.select();
            document.execCommand('copy');
            
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            button.style.background = '#28a745';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#007cba';
            }, 2000);
        }
        
        function clearConsole() {
            document.getElementById('console-output').textContent = '';
        }
        
        // Initial test
        console.log('🚀 Settings test page loaded');
        console.log('📋 Ready to test settings functionality');
    </script>
</body>
</html>
