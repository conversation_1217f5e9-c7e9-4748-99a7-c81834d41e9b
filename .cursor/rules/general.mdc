---
description: 
globs: 
alwaysApply: false
---
📌 Project Name: Notely.social
📌 Purpose: A Chrome extension that allows users to save, categorize, and manage posts from multiple social media platforms (Twitter/X, LinkedIn, Reddit, Instagram, Pinterest) in a unified dashboard with AI-enhanced features like categorization, tagging, and insights.

📌 Tech Stack:
React with TypeScript
Tailwind CSS for styling
Chrome Extension APIs
Vite for building
Firebase for backend/cloud storage
AI services for content analysis and enrichment

📌 Critical Files:
/src/dashboard/dashboard.tsx - Main dashboard UI and post management functionality
/src/components/CategoryTagManager.tsx - Manages categories and tags for posts
/src/storage.ts - Core storage functionality for posts, categories, and tags
/src/components/MindstreamWidgets.tsx - Dashboard widgets including category management
/src/background/background.ts - Extension background service worker