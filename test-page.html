<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for Notely Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-image {
            max-width: 300px;
            height: auto;
            margin: 10px 0;
        }
        .highlight {
            background-color: yellow;
            padding: 2px 4px;
        }
    </style>
</head>
<body>
    <h1>Test Page for Notely Extension</h1>
    <p>This page is designed to test the "Add to Notely" context menu functionality.</p>

    <div class="test-section">
        <h2>Text Selection Test</h2>
        <p>Select this text and right-click to see the "Add to Notely" option. This is a sample paragraph that contains some interesting content about web development and browser extensions. You can select any part of this text to test the text saving functionality.</p>
        <p class="highlight">This highlighted text is perfect for testing the selection feature.</p>
    </div>

    <div class="test-section">
        <h2>Image Test</h2>
        <p>Right-click on the image below to test image saving:</p>
        <img src="https://via.placeholder.com/300x200/4CAF50/white?text=Test+Image" 
             alt="Test image for Notely extension" 
             class="test-image">
        <p>This is a test image that should be saveable via the context menu.</p>
    </div>

    <div class="test-section">
        <h2>Page Save Test</h2>
        <p>Right-click anywhere on the page (not on text or images) to test the page saving functionality. This will save the entire page content to Notely.</p>
    </div>

    <div class="test-section">
        <h2>Instructions</h2>
        <ol>
            <li><strong>Text Selection:</strong> Select any text above and right-click to see "Add to Notely"</li>
            <li><strong>Image Save:</strong> Right-click on the test image to save it</li>
            <li><strong>Page Save:</strong> Right-click on empty space to save the entire page</li>
        </ol>
        <p>After using any of these options, you should see a notification confirming the save operation.</p>
    </div>
</body>
</html>
