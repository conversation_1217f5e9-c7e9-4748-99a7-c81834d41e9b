<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #444;
            border-radius: 4px;
            background: #333;
            color: #fff;
        }
        button {
            background: #0066cc;
            cursor: pointer;
        }
        button:hover {
            background: #0052a3;
        }
        .result {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .error {
            background: #660000;
            color: #ffcccc;
        }
        .success {
            background: #006600;
            color: #ccffcc;
        }
    </style>
</head>
<body>
    <h1>🔍 Search Debug Test</h1>
    
    <div class="container">
        <h2>Step 1: Get Auth Token</h2>
        <button onclick="getAuthToken()">Get Auth Token from Extension</button>
        <div id="tokenResult" class="result"></div>
    </div>

    <div class="container">
        <h2>Step 2: Test Search API</h2>
        <input type="text" id="searchQuery" placeholder="Enter search query (e.g., sony)" value="sony">
        <button onclick="testSearch()">Test Search</button>
        <div id="searchResult" class="result"></div>
    </div>

    <div class="container">
        <h2>Step 3: Test Search Info</h2>
        <button onclick="testSearchInfo()">Get Search Info</button>
        <div id="infoResult" class="result"></div>
    </div>

    <script>
        const API_URL = 'https://api.notely.social';
        let authToken = null;

        async function getAuthToken() {
            const resultDiv = document.getElementById('tokenResult');
            
            try {
                // Try to get token from extension
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage(
                        { action: 'GET_AUTH_TOKEN' },
                        (response) => {
                            if (chrome.runtime.lastError) {
                                resultDiv.innerHTML = `❌ Error: ${chrome.runtime.lastError.message}`;
                                resultDiv.className = 'result error';
                                return;
                            }
                            
                            if (response && response.token) {
                                authToken = response.token;
                                resultDiv.innerHTML = `✅ Token retrieved successfully!\nLength: ${authToken.length} characters`;
                                resultDiv.className = 'result success';
                            } else {
                                resultDiv.innerHTML = '❌ No token received from extension';
                                resultDiv.className = 'result error';
                            }
                        }
                    );
                } else {
                    resultDiv.innerHTML = '❌ Chrome extension API not available. Please open this page from the extension.';
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Error getting token: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testSearch() {
            const resultDiv = document.getElementById('searchResult');
            const query = document.getElementById('searchQuery').value;
            
            if (!authToken) {
                resultDiv.innerHTML = '❌ Please get auth token first';
                resultDiv.className = 'result error';
                return;
            }

            if (!query.trim()) {
                resultDiv.innerHTML = '❌ Please enter a search query';
                resultDiv.className = 'result error';
                return;
            }

            try {
                resultDiv.innerHTML = '🔄 Searching...';
                resultDiv.className = 'result';

                const response = await fetch(`${API_URL}/api/posts/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                    },
                    body: JSON.stringify({
                        query: query.trim(),
                        limit: 5
                    }),
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `✅ Search successful!\n\nResults: ${data.totalResults}\nSearch Type: ${data.searchType || 'unknown'}\n\nResponse:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ Search failed: ${data.message || response.statusText}\n\nFull response:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testSearchInfo() {
            const resultDiv = document.getElementById('infoResult');
            
            if (!authToken) {
                resultDiv.innerHTML = '❌ Please get auth token first';
                resultDiv.className = 'result error';
                return;
            }

            try {
                resultDiv.innerHTML = '🔄 Getting search info...';
                resultDiv.className = 'result';

                const response = await fetch(`${API_URL}/api/posts/search/test`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `✅ Search info retrieved!\n\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ Failed to get search info: ${data.message || response.statusText}\n\nFull response:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // Auto-get token on page load if extension is available
        window.addEventListener('load', () => {
            setTimeout(getAuthToken, 1000);
        });
    </script>
</body>
</html>
