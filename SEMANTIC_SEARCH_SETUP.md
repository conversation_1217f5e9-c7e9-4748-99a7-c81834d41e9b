# MongoDB Atlas Vector Search Integration

This document outlines the implementation of semantic search using MongoDB Atlas Vector Search for the Notely.social Chrome extension.

## Overview

The semantic search system enables users to find saved posts using natural language queries. It uses OpenAI's `text-embedding-3-small` model to generate embeddings and MongoDB Atlas Vector Search for efficient similarity matching.

## Architecture

### Components

1. **Vector Search Service** (`backend/src/services/vectorSearchService.ts`)
   - MongoDB Atlas vector search implementation
   - Hybrid search combining vector and text search
   - Search statistics and availability checking

2. **Enhanced Search API** (`backend/src/routes/postRoutes.ts`)
   - Updated `/api/posts/search` endpoint
   - Automatic fallback mechanisms
   - Performance monitoring

3. **Frontend Integration** (existing)
   - `AskMyBookmarks` component
   - `SearchResultsSummary` component
   - Semantic search service

## Setup Instructions

### 1. MongoDB Atlas Vector Search Index

**Required**: Create a vector search index in MongoDB Atlas

#### Option A: MongoDB Atlas UI
1. Log in to [MongoDB Atlas](https://cloud.mongodb.com)
2. Navigate to your cluster: `social-post-saver-cluster`
3. Go to "Search" tab
4. Click "Create Search Index"
5. Choose "JSON Editor"
6. Select database and `cloudposts` collection
7. Use this configuration:

```json
{
  "name": "vector_index",
  "type": "vectorSearch",
  "definition": {
    "fields": [
      {
        "type": "vector",
        "path": "embeddingVector",
        "numDimensions": 1536,
        "similarity": "cosine"
      },
      {
        "type": "filter",
        "path": "userId"
      },
      {
        "type": "filter", 
        "path": "platform"
      },
      {
        "type": "filter",
        "path": "categories"
      },
      {
        "type": "filter",
        "path": "tags"
      }
    ]
  }
}
```

#### Option B: Atlas CLI
```bash
# Run the setup script
node backend/scripts/setup-vector-search.js

# Use generated config file
atlas clusters search indexes create \
  --clusterName social-post-saver-cluster \
  --db YOUR_DATABASE_NAME \
  --collection cloudposts \
  --file backend/scripts/vector-search-index.json
```

### 2. Environment Variables

Ensure these are set in your `.env` file:

```env
OPENAI_API_KEY=your_openai_api_key
MONGODB_URI=your_mongodb_atlas_connection_string
```

### 3. Verification

Test the setup:

```bash
# Test search functionality
curl -X GET "https://api.notely.social/api/posts/search/test" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Expected response should include:
```json
{
  "vectorSearch": {
    "available": true,
    "indexName": "vector_index"
  }
}
```

## Features

### Search Types

1. **Vector Search** (Primary)
   - Uses MongoDB Atlas Vector Search
   - Fastest and most accurate
   - Requires vector search index

2. **Hybrid Search** (Fallback)
   - Combines vector and text search
   - Better coverage for edge cases
   - Automatic when vector results are sparse

3. **Cosine Similarity** (Fallback)
   - Client-side similarity calculation
   - Used when vector search unavailable
   - Slower but functional

4. **Text Search** (Final Fallback)
   - Traditional regex-based search
   - Always available
   - Used when no embeddings exist

### Performance Optimizations

- **Automatic Fallbacks**: Graceful degradation when services unavailable
- **Hybrid Search**: Combines vector and text for better coverage
- **Configurable Thresholds**: Adjustable similarity scores
- **Efficient Filtering**: User-scoped searches with platform/category filters

## API Endpoints

### POST /api/posts/search
Enhanced semantic search with multiple fallback mechanisms.

**Request:**
```json
{
  "query": "AI tools for productivity",
  "limit": 10,
  "minScore": 0.7
}
```

**Response:**
```json
{
  "results": [...],
  "summary": {
    "overview": "Found posts about AI productivity tools...",
    "keyFindings": [...],
    "relatedTopics": [...],
    "suggestedActions": [...]
  },
  "totalResults": 5,
  "query": "AI tools for productivity",
  "searchType": "vector",
  "stats": {
    "averageScore": 0.85,
    "vectorSearchAvailable": true
  }
}
```

### GET /api/posts/search/test
Test search functionality and system status.

### GET /api/posts/search/suggestions
Get AI-generated search suggestions based on user's content.

## Troubleshooting

### Vector Search Not Available
- Verify MongoDB Atlas cluster tier (M10+ required)
- Check vector search index exists and is active
- Ensure index name is exactly `vector_index`
- Wait 5-10 minutes after index creation

### Poor Search Results
- Check if posts have `embeddingVector` fields
- Verify OpenAI API key is working
- Try lowering `minScore` threshold
- Use hybrid search for better coverage

### Performance Issues
- Monitor MongoDB Atlas metrics
- Consider increasing `numCandidates` in vector search
- Optimize embedding generation for new posts
- Use appropriate similarity thresholds

## Monitoring

### Key Metrics
- Vector search availability
- Search response times
- Embedding coverage (posts with vectors)
- Search result quality scores

### Logging
Search operations are logged with:
- Search type used (vector/hybrid/cosine/text)
- Number of results found
- Average similarity scores
- Performance timing

## Future Enhancements

1. **Advanced Filtering**: Date ranges, interaction counts
2. **Personalized Search**: User behavior-based ranking
3. **Multi-modal Search**: Image and text combined
4. **Search Analytics**: User search pattern analysis
5. **Caching**: Redis-based result caching

## Dependencies

- MongoDB Atlas (M10+ cluster)
- OpenAI API (text-embedding-3-small)
- Node.js packages: `mongoose`, `openai`

## Security Considerations

- User-scoped searches (userId filtering)
- API key protection
- Rate limiting on search endpoints
- Input validation and sanitization
