<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twitter Thread Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #000;
            color: #fff;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        article[data-testid="tweet"] {
            border: 1px solid #2f3336;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            background-color: #000;
        }
        
        .tweet-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #1d9bf0;
            margin-right: 12px;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: bold;
            color: #fff;
        }
        
        .user-handle {
            color: #71767b;
            margin-left: 4px;
        }
        
        .tweet-content {
            margin-bottom: 12px;
            line-height: 1.5;
        }
        
        .tweet-actions {
            display: flex;
            justify-content: space-between;
            max-width: 425px;
        }
        
        .tweet-actions > div {
            display: flex;
            align-items: center;
            color: #71767b;
            cursor: pointer;
            padding: 8px;
            border-radius: 20px;
            transition: background-color 0.2s;
        }
        
        .tweet-actions > div:hover {
            background-color: rgba(29, 155, 240, 0.1);
        }
        
        .tweet-actions svg {
            width: 18px;
            height: 18px;
            margin-right: 8px;
        }
        
        time {
            color: #71767b;
            text-decoration: none;
        }
        
        .thread-indicator {
            background-color: #1d9bf0;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-top: 8px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Twitter Thread Test Page</h1>
        <p>This page simulates a Twitter thread to test the Notely thread saving functionality.</p>
        
        <!-- Tweet 1 - Thread starter -->
        <article data-testid="tweet">
            <div class="tweet-header">
                <div class="avatar"></div>
                <div class="user-info">
                    <span class="user-name" data-testid="User-Name">
                        <a href="/testuser">Test User</a>
                    </span>
                    <span class="user-handle">@testuser</span>
                    <span> · </span>
                    <time datetime="2024-01-15T10:00:00.000Z">
                        <a href="/testuser/status/1234567890123456789">2h</a>
                    </time>
                </div>
            </div>
            <div class="tweet-content">
                🧵 Thread: Let me explain why Twitter threads are so powerful for sharing complex ideas. Here's what makes them work so well... (1/5)
            </div>
            <div class="tweet-actions" role="group">
                <div data-testid="reply">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z"/>
                    </svg>
                    <span>12</span>
                </div>
                <div data-testid="retweet">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.79-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.79 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"/>
                    </svg>
                    <span>45</span>
                </div>
                <div data-testid="like">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91z"/>
                    </svg>
                    <span>234</span>
                </div>
            </div>
            <div class="thread-indicator">Thread 1/5</div>
        </article>

        <!-- Tweet 2 -->
        <article data-testid="tweet">
            <div class="tweet-header">
                <div class="avatar"></div>
                <div class="user-info">
                    <span class="user-name" data-testid="User-Name">
                        <a href="/testuser">Test User</a>
                    </span>
                    <span class="user-handle">@testuser</span>
                    <span> · </span>
                    <time datetime="2024-01-15T10:02:00.000Z">
                        <a href="/testuser/status/1234567890123456790">2h</a>
                    </time>
                </div>
            </div>
            <div class="tweet-content">
                First, threads allow you to break down complex topics into digestible chunks. Each tweet can focus on one key point, making it easier for readers to follow along and understand. (2/5)
            </div>
            <div class="tweet-actions" role="group">
                <div data-testid="reply">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z"/>
                    </svg>
                    <span>8</span>
                </div>
                <div data-testid="retweet">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.79-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.79 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"/>
                    </svg>
                    <span>32</span>
                </div>
                <div data-testid="like">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91z"/>
                    </svg>
                    <span>189</span>
                </div>
            </div>
            <div class="thread-indicator">Thread 2/5</div>
        </article>

        <!-- Tweet 3 -->
        <article data-testid="tweet">
            <div class="tweet-header">
                <div class="avatar"></div>
                <div class="user-info">
                    <span class="user-name" data-testid="User-Name">
                        <a href="/testuser">Test User</a>
                    </span>
                    <span class="user-handle">@testuser</span>
                    <span> · </span>
                    <time datetime="2024-01-15T10:04:00.000Z">
                        <a href="/testuser/status/1234567890123456791">2h</a>
                    </time>
                </div>
            </div>
            <div class="tweet-content">
                Second, threads create a narrative flow. You can build suspense, provide examples, and lead readers through your thought process step by step. It's like telling a story in bite-sized pieces. (3/5)
            </div>
            <div class="tweet-actions" role="group">
                <div data-testid="reply">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z"/>
                    </svg>
                    <span>15</span>
                </div>
                <div data-testid="retweet">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.79-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.79 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"/>
                    </svg>
                    <span>28</span>
                </div>
                <div data-testid="like">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91z"/>
                    </svg>
                    <span>156</span>
                </div>
            </div>
            <div class="thread-indicator">Thread 3/5</div>
        </article>

        <p style="margin-top: 40px; color: #71767b; text-align: center;">
            Load this page as a Chrome extension content script to test the thread saving functionality.
            You should see "Save Thread" buttons appear next to the tweet actions.
        </p>
    </div>
</body>
</html>
