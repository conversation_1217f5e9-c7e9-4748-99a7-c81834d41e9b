# Web Dashboard Deployment Guide

This guide explains how to deploy the Notely Social web dashboard to https://notely.social/dashboard

## 🌐 **What's Been Built**

A complete web version of the Notely Social dashboard that includes:

- ✅ **Responsive Web Interface** - Works on desktop and mobile
- ✅ **User Authentication** - Email/password + Google OAuth
- ✅ **Premium Features** - Billing management, subscription status
- ✅ **React Router** - Client-side routing for SPA experience
- ✅ **Notely Theme** - Consistent design with Chrome extension
- ✅ **Admin Support** - Role-based access for admin users

## 🏗️ **Architecture**

- **Frontend**: React + TypeScript + Tailwind CSS
- **Build Tool**: Vite with custom web configuration
- **Routing**: React Router for SPA navigation
- **Auth**: localStorage-based auth (compatible with backend API)
- **Deployment**: Static files ready for any hosting service

## 🚀 **Deployment Options**

### **Option 1: Vercel (Recommended)**

1. **Connect Repository**:
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy from project root
   vercel
   ```

2. **Configure Domain**:
   - Go to Vercel Dashboard
   - Add custom domain: `notely.social`
   - Set up DNS records as instructed

3. **Environment Variables** (if needed):
   - No environment variables required for frontend

### **Option 2: Netlify**

1. **Connect Repository**:
   - Go to [Netlify](https://netlify.com)
   - Connect your GitHub repository
   - Build command: `npm run build:web`
   - Publish directory: `dist-web`

2. **Configure Domain**:
   - Add custom domain in Netlify settings
   - Update DNS records

### **Option 3: Manual Deployment**

1. **Build the Project**:
   ```bash
   npm run build:web
   ```

2. **Upload Files**:
   - Upload entire `dist-web/` folder to your web server
   - Point domain to the uploaded directory

## 🔧 **Build Commands**

```bash
# Development server
npm run dev:web

# Build for production
npm run build:web

# Preview production build
npm run preview:web

# Clean build files
npm run clean
```

## 📁 **File Structure**

```
dist-web/
├── index.html          # Main entry point
├── assets/             # CSS and JS bundles
├── icons/              # App icons
├── notely.svg          # Logo
└── ...                 # Other static assets
```

## 🌍 **URL Structure**

- **Dashboard**: `https://notely.social/dashboard`
- **Login**: `https://notely.social/login`
- **Settings**: `https://notely.social/settings`
- **Root**: `https://notely.social/` (redirects to dashboard)

## 🔐 **Authentication Flow**

1. **Web Login**: Users can login with email/password or Google OAuth
2. **Token Storage**: JWT tokens stored in localStorage
3. **API Integration**: All requests go to `https://api.notely.social`
4. **Session Sync**: Web and Chrome extension share the same backend

## ⚙️ **Configuration Files**

### **Vercel Configuration** (`vercel.json`)
```json
{
  "buildCommand": "npm run build:web",
  "outputDirectory": "dist-web",
  "framework": "vite",
  "rewrites": [
    { "source": "/(.*)", "destination": "/index.html" }
  ]
}
```

### **Netlify Configuration** (`netlify.toml`)
```toml
[build]
  command = "npm run build:web"
  publish = "dist-web"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

## 🎯 **Features Available**

### **For All Users**:
- ✅ Login/Register with email or Google
- ✅ View account information
- ✅ Chrome extension download links
- ✅ Responsive design

### **For Premium Users**:
- ✅ Billing management
- ✅ Subscription status
- ✅ Cancel/reactivate subscription
- ✅ Premium badge display

### **For Admin Users**:
- ✅ Access to admin API endpoints
- ✅ User management capabilities
- ✅ Usage statistics (via API)

## 🔗 **Integration with Chrome Extension**

The web dashboard is designed to complement the Chrome extension:

1. **Shared Backend**: Both use the same API at `https://api.notely.social`
2. **Cross-Platform Login**: Login on web works for extension and vice versa
3. **Extension Promotion**: Web dashboard promotes Chrome extension installation
4. **Unified Experience**: Same design language and features

## 🚨 **Important Notes**

1. **Chrome Extension Storage**: The web version doesn't have access to Chrome extension storage, so saved posts are not visible on web (by design)

2. **Google OAuth**: The web version redirects to the backend OAuth flow, which should redirect back to the web dashboard after authentication

3. **API Compatibility**: All API calls are compatible with the existing backend

4. **Mobile Responsive**: The interface works well on mobile devices

## 🧪 **Testing**

1. **Local Testing**:
   ```bash
   npm run dev:web
   # Visit http://localhost:3001
   ```

2. **Production Testing**:
   ```bash
   npm run build:web
   npm run preview:web
   # Visit http://localhost:4173
   ```

3. **Test Scenarios**:
   - ✅ Login with email/password
   - ✅ Login with Google OAuth
   - ✅ Registration flow
   - ✅ Billing management (for premium users)
   - ✅ Responsive design on mobile
   - ✅ Navigation between pages

## 📞 **Support**

If you encounter any issues during deployment:

1. Check the build logs for errors
2. Verify all environment variables are set
3. Test the API endpoints are accessible
4. Ensure DNS records are properly configured

The web dashboard is now ready for deployment! 🎉
