[build]
  command = "npm run build:web"
  publish = "dist-web"

[[redirects]]
  from = "/dashboard"
  to = "/index.html"
  status = 200

[[redirects]]
  from = "/login"
  to = "/index.html"
  status = 200

[[redirects]]
  from = "/settings"
  to = "/index.html"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
